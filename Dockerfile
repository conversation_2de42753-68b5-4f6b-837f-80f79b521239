FROM node:22.14.0 AS frontend-builder

WORKDIR /app/frontend
COPY frontend/package*.json ./
RUN npm install
COPY frontend/ .
RUN npm run build

FROM python:3.12-bookworm AS builder

# 🚀 Optimized for size and build speed using techniques from
# https://indiebi.atlassian.net/wiki/spaces/DPT/pages/362512422/Optimizing+Dockerfiles+for+Python

ENV PIP_DISABLE_PIP_VERSION_CHECK=1 \
    PIP_NO_CACHE_DIR=1

WORKDIR /app

RUN pip install poetry && \
    poetry config virtualenvs.in-project true

COPY poetry.lock pyproject.toml /app/

RUN poetry install --no-interaction --no-root --no-cache --only main

FROM python:3.12-slim-bookworm

RUN groupadd --gid 1000 service && useradd --uid 1000 --gid 1000 -r -g service service

WORKDIR /app

COPY --from=frontend-builder --chown=service:service /app/static/frontend /app/static/frontend
COPY --from=builder --chown=service:service /app/.venv /app/.venv
COPY --chown=service:service dataoffice /app/dataoffice
COPY --chown=service:service static /app/static

USER service

ARG DOCKER_TAG
ARG DOCKER_BUILD_TIMESTAMP

ENV PATH=/app/.venv/bin:$PATH \
    PYTHONPATH=/app:$PYTHONPATH \
    BUILD_VERSION=$DOCKER_TAG \
    BUILD_TIMESTAMP=$DOCKER_BUILD_TIMESTAMP


CMD ["uvicorn", "dataoffice.main:app", "--host", "0.0.0.0", "--port", "8000", "--forwarded-allow-ips", "*", "--proxy-headers"]
