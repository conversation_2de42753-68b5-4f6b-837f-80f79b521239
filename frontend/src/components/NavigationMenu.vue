<template>
  <template v-for="item in items" :key="item.text">
    <li class="nav-item">
      <template v-if="item.children">
        <a
          class="nav-link dropdown-indicator"
          :href="'#' + generateId(item.text, parentId)"
          role="button"
          data-bs-toggle="collapse"
          aria-expanded="false"
          :aria-controls="generateId(item.text, parentId)"
        >
          <div class="d-flex align-items-center">
            <span v-if="item.icon" class="nav-link-icon">
              <span :class="item.icon"></span>
            </span>
            <span class="nav-link-text ps-1">{{ item.text }}</span>
          </div>
        </a>
        <ul class="nav collapse" :id="generateId(item.text, parentId)">
          <NavigationMenu :items="item.children" :parent-id="generateId(item.text, parentId)" />
        </ul>
      </template>
      <template v-else>
        <a
          class="nav-link"
          :href="item.href || '#'"
          :target="item.href ? '_blank' : undefined"
          :rel="item.href ? 'noopener noreferrer' : undefined"
        >
          <div class="d-flex align-items-center">
            <span v-if="item.icon" class="nav-link-icon">
              <span :class="item.icon"></span>
            </span>
            <span class="nav-link-text ps-1">{{ item.text }}</span>
          </div>
        </a>
      </template>
    </li>
  </template>
</template>

<script setup lang="ts">
interface MenuItem {
  text: string
  icon?: string
  href?: string
  children?: MenuItem[]
}

defineProps<{
  items: MenuItem[]
  parentId?: string
}>()

const generateId = (text: string, parentId?: string) => {
  return `${parentId ? parentId + '-' : ''}${text.toLowerCase().replace(/\s+/g, '-')}`
}
</script>
