<script setup lang="ts">
import { storeToRefs } from 'pinia'
import { useNotificationStore } from '@/stores/notification'

const notificationStore = useNotificationStore()
const { notifications } = storeToRefs(notificationStore)

const getIconClass = (type: string) => {
  switch (type) {
    case 'success':
      return 'fas fa-check-circle'
    case 'info':
      return 'fas fa-info-circle'
    case 'warning':
      return 'fas fa-exclamation-circle'
    case 'danger':
      return 'fas fa-times-circle'
    default:
      return 'fas fa-info-circle'
  }
}

const removeNotification = (id: string) => {
  notificationStore.removeNotification(id)
}
</script>

<template>
  <div class="notification-center">
    <div v-for="notification in notifications" :key="notification.id" class="mb-3">
      <div
        :class="[
          'alert',
          `alert-${notification.type}`,
          'border-0',
          'd-flex',
          'align-items-center',
          'fade',
          'show',
        ]"
        role="alert"
      >
        <div :class="['bg-' + notification.type, 'me-3', 'icon-item']">
          <span :class="[getIconClass(notification.type), 'text-white', 'fs-6']"></span>
        </div>
        <div class="flex-1">
          <h4 v-if="notification.title" class="alert-heading fw-semi-bold mb-1">
            {{ notification.title }}
          </h4>
          <p class="mb-0">{{ notification.message }}</p>
        </div>
        <button
          class="btn-close"
          type="button"
          @click="removeNotification(notification.id)"
          aria-label="Close"
        ></button>
      </div>
    </div>
  </div>
</template>

<style scoped>
.notification-center {
  position: fixed;
  top: 1rem;
  right: 1rem;
  z-index: var(--z-index-notification-center);
  max-width: 400px;
  width: 100%;
}
</style>
