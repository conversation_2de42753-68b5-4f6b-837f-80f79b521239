<template>
  <div class="loading-container">
    <div class="loader"></div>
  </div>
</template>

<script setup lang="ts"></script>

<style scoped>
.loading-container {
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
}

.loader {
  width: 50px;
  padding: 8px;
  aspect-ratio: 1;
  border-radius: 50%;
  background: #25b09b;

  --mask-pattern: conic-gradient(#0000 10%, #000), linear-gradient(#000 0 0) content-box;

  mask: var(--mask-pattern);
  /* stylelint-disable-next-line declaration-property-value-no-unknown */
  mask-composite: source-out;
  mask-composite: subtract;
  animation: l3 1s infinite linear;
}

@keyframes l3 {
  to {
    transform: rotate(1turn);
  }
}
</style>
