<template>
  <NavigationMenu :items="menuItems" />
</template>

<script setup lang="ts">
import NavigationMenu from './NavigationMenu.vue'

const menuItems = [
  {
    text: 'Login Links',
    icon: 'fas fa-link',
    children: [
      { text: 'Epic Games', href: 'https://dev.epicgames.com/portal/en-US/' },
      { text: 'GOG', href: 'https://partners.gog.com/charts/sales-summary' },
      { text: 'Humble', href: 'https://www.humblebundle.com/dashboard' },
      { text: 'Meta', href: 'https://developer.oculus.com/manage/' },
      { text: 'Microsoft', href: 'https://partner.microsoft.com/en-us/dashboard/home' },
      {
        text: 'Nintendo',
        children: [
          { text: 'Discounts', href: 'https://ncms3.mng.nintendo.net/ncms3/discount/search' },
          {
            text: 'Sales & Wishlists',
            href: 'https://sst.mng.nintendo.net/shoptools/switchLicenseeReports/titleReport',
          },
        ],
      },
      {
        text: 'Steam',
        children: [
          { text: 'Sales & Wishlists', href: 'https://partner.steampowered.com/login/' },
          { text: 'Discounts & Impressions', href: 'https://partner.steamgames.com/dashboard' },
        ],
      },
    ],
  },
]
</script>
