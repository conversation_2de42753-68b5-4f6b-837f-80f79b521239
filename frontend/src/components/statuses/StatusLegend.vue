<template>
  <div class="legend" :class="{ 'legend-collapsed': !fullLegend }">
    <div
      v-if="!fullLegend"
      class="legend-collapsed-content"
      @click="$emit('update:fullLegend', true)"
    >
      <span class="fas fa-info-circle me-2"></span>
      Show Legend
    </div>
    <div v-else class="legend-expanded-content">
      <div class="legend-header">
        <button class="btn-close-legend" @click="$emit('update:fullLegend', false)">
          <span class="fas fa-times"></span>
        </button>
      </div>
      <table class="legend-table">
        <thead>
          <tr>
            <th>Status</th>
            <th>Scrape</th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="(statusInfo, index) in statusExamples" :key="index">
            <td>
              <div class="status-container">
                <StatusIndicator :status-data="statusInfo" :is-interactive="false" />
              </div>
            </td>
            <td>{{ statusInfo.scrapeStatus }}</td>
          </tr>
        </tbody>
      </table>

      <h5 class="mt-4">Error Types</h5>
      <table class="legend-table">
        <tbody>
          <tr
            v-for="errorType in Object.values(ErrorType).filter(
              (errorType) => errorType !== ErrorType.NONE,
            )"
            :key="errorType"
          >
            <td>
              <span class="error-emoji">{{ getErrorEmoji(errorType) }}</span>
            </td>
            <td>{{ errorType }}</td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ErrorType, getErrorEmoji } from '@/types/errors'
import { ScrapeStatus, ReportStatus } from '@/types/statuses'
import type { StatusInfoWithoutData } from '@/types/statuses'
import StatusIndicator from '@/components/statuses/StatusIndicator.vue'

defineProps<{
  fullLegend: boolean
}>()

defineEmits<{
  'update:fullLegend': [value: boolean]
}>()

const statusExamples: StatusInfoWithoutData[] = [
  { scrapeStatus: ScrapeStatus.DISABLED },
  { scrapeStatus: ScrapeStatus.CONFIGURED },
  { scrapeStatus: ScrapeStatus.STARTED },
  { scrapeStatus: ScrapeStatus.SCHEDULED },
  { scrapeStatus: ScrapeStatus.STOPPED },
  { scrapeStatus: ScrapeStatus.FAILED, errorType: ErrorType.NONE },
  { scrapeStatus: ScrapeStatus.FINISHED, reportStatus: ReportStatus.NONE },
  { scrapeStatus: ScrapeStatus.MANUALLY_BLOCKED },
]
</script>

<style scoped>
.legend {
  position: fixed;
  bottom: 55px;
  right: 7px;
  width: 290px;
  padding: 1rem;
  padding-bottom: 0;
  z-index: var(--z-index-status-legend);
  max-height: 85vh;
  overflow-y: auto;
  background-color: rgb(0 0 0 / 80%);
  border-radius: 8px;
  box-shadow: 0 4px 12px rgb(0 0 0 / 50%);
}

.legend-collapsed {
  width: auto;
  padding: 0.75rem 1.5rem;
  cursor: pointer;
}

.legend-collapsed:hover {
  background-color: rgb(0 0 0 / 80%);
}

.legend-collapsed-content {
  color: white;
  font-size: 0.9rem;
  display: flex;
  align-items: center;
}

.legend-header {
  display: flex;
  justify-content: flex-end;
  align-items: flex-start;
  margin-bottom: 1rem;
  position: relative;
}

.btn-close-legend {
  position: absolute;
  top: -0.7rem;
  right: -0.5rem;
  background: none;
  border: none;
  color: #fff;
  cursor: pointer;
  padding: 0.5rem;
  font-size: 1rem;
  opacity: 0.7;
  z-index: var(--z-index-status-legend-close-button);
}

.btn-close-legend:hover {
  opacity: 1;
}

.legend h5 {
  margin-bottom: 1rem;
  font-weight: 600;
  color: white;
  border-bottom: 1px solid rgb(255 255 255 / 20%);
  padding-bottom: 0.4rem;
  font-size: 1.1rem;
}

.legend h5.mt-4 {
  margin-top: 1.5rem;
}

.legend-table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 1rem;
}

.legend-table th {
  font-weight: 600;
  font-size: 0.9rem;
  color: white;
  padding: 0.1rem 0.4rem;
}

.legend-table td {
  font-size: 0.85rem;
  padding: 0.1rem 0.5rem;
}

.legend-table td:first-child {
  width: 50px;
  text-align: center;
  padding: 0.1rem 0.5rem;
}

.legend-table tr:last-child th,
.legend-table tr:last-child td {
  border-bottom: none;
}

.status-container {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  transform: scale(0.8);
}

.error-emoji {
  font-size: 1.2rem;
}

:deep(.failed .symbol.error-emoji) {
  margin-left: -0.4rem;
}
</style>
