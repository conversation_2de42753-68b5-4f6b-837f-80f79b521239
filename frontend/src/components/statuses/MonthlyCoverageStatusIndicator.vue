<template>
  <div
    v-if="statusData"
    class="status-container"
    :class="{ interactive: isInteractive }"
    @mouseenter="isInteractive && handleMouseEnter()"
    @mouseleave="isInteractive && handleMouseLeave()"
  >
    <MonthlyCoverageStatusRing :organization-monthly-coverage="statusData" />
    <MonthlyCoverageStatusTooltip
      v-if="isInteractive && showTooltip"
      :organization-monthly-coverage="statusData"
    />
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import MonthlyCoverageStatusRing from '@/components/statuses/MonthlyCoverageStatusRing.vue'
import type { OrganizationMonthlyReportCoverage } from '@/domain/coverage/types'
import MonthlyCoverageStatusTooltip from '@/components/statuses/MonthlyCoverageStatusTooltip.vue'

interface Props {
  statusData: OrganizationMonthlyReportCoverage | undefined
  isInteractive?: boolean
}

withDefaults(defineProps<Props>(), { isInteractive: true })

const showTooltip = ref(false)
let tooltipTimer: number | null = null

const handleMouseEnter = () => {
  if (tooltipTimer !== null) {
    clearTimeout(tooltipTimer)
    tooltipTimer = null
  }

  tooltipTimer = window.setTimeout(() => {
    showTooltip.value = true
  }, 200)
}

const handleMouseLeave = () => {
  if (tooltipTimer !== null) {
    clearTimeout(tooltipTimer)
    tooltipTimer = null
  }

  showTooltip.value = false
}
</script>

<style scoped>
.status-container {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: var(--z-index-status-background);
}
</style>
