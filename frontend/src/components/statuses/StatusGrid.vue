<template>
  <SlantyTable
    v-if="tableData.length > 0 && columns.length > 0"
    :key="isTransposed ? 'transposed' : 'normal'"
    :columns="columns"
    :data="tableData"
    :isTransposed="isTransposed"
    @transpose="toggleTranspose"
  >
    <template #header="{ column, colIndex }">
      <div
        class="top-header-cell"
        :class="{ highlight: shouldBeHighlighted(-1, colIndex) }"
        @click="handleHeaderClick(-1, colIndex)"
        @mouseenter="handleHover(-1, colIndex)"
        @mouseleave="handleLeave"
      >
        <StatusGridControls
          v-if="colIndex === 0"
          :is-live-refresh-enabled="isLiveRefreshEnabled"
          :is-transposed="isTransposed"
          @toggle-live-refresh="(value) => $emit('toggleLiveRefresh', value)"
          @toggle-transpose="toggleTranspose"
        />

        <template v-else>
          {{ column.label }}
        </template>
      </div>
    </template>
    <template #default="{ value, row, column, rowIndex, colIndex }">
      <div
        v-if="colIndex === 0"
        class="left-header-cell"
        :class="{ highlight: shouldBeHighlighted(rowIndex, -1) }"
        @click="handleHeaderClick(rowIndex, -1)"
        @mouseenter="handleHover(rowIndex, -1)"
        @mouseleave="handleLeave"
      >
        {{ value }}
      </div>
      <div
        v-else
        class="status-cell"
        :class="{ highlight: shouldBeHighlighted(rowIndex, colIndex) }"
        @mouseenter="handleHover(rowIndex, colIndex)"
        @mouseleave="handleLeave"
      >
        <component
          :is="statusIndicatorComponent"
          :status-data="getStatusInfoForCell(row, column)"
          @select="handleCellSelect(rowIndex, colIndex)"
          @unselect="handleCellUnselect"
        />
      </div>
    </template>
  </SlantyTable>
  <div v-else class="no-data-message">
    <span class="fas fa-info-circle me-2"></span>
    No data available.
  </div>
</template>

<script setup lang="ts">
import { ref, computed, type Component } from 'vue'
import SlantyTable from '@/components/tables/SlantyTable.vue'
import StatusGridControls from '@/components/statuses/StatusGridControls.vue'
import type { Column, TableRow } from '@/types/table'
import { useStatusGridHighlightDecider } from '@/composables/useStatusGridHighlightDecider'

interface Props<T> {
  clients: string[]
  sources: string[]
  isTransposed: boolean
  isLiveRefreshEnabled: boolean
  getStatusInfo: (client: string, source: string) => T | undefined
  statusIndicatorComponent: Component
}

// eslint-disable-next-line @typescript-eslint/no-explicit-any
const props = defineProps<Props<any>>()
const emit = defineEmits<{
  (e: 'toggleTranspose'): void
  (e: 'toggleLiveRefresh', value: boolean): void
}>()

const hoveredCell = ref<{ row: number; col: number } | null>(null)
const selectedCell = ref<{ row: number; col: number } | null>(null)

const columns = computed<Column[]>(() => {
  if (props.isTransposed) {
    return [
      { key: 'source', label: '' },
      ...props.clients.map((client) => ({
        key: client,
        label: client,
      })),
    ]
  } else {
    return [
      { key: 'client', label: '' },
      ...props.sources.map((source) => ({
        key: source,
        label: source,
      })),
    ]
  }
})

const tableData = computed<TableRow[]>(() => {
  if (props.isTransposed) {
    return props.sources.map((source) => ({
      source,
      ...Object.fromEntries(
        props.clients.map((client) => [client, props.getStatusInfo(client, source)?.status]),
      ),
    }))
  } else {
    return props.clients.map((client) => ({
      client,
      ...Object.fromEntries(
        props.sources.map((source) => [source, props.getStatusInfo(client, source)?.status]),
      ),
    }))
  }
})

const rowCount = computed(() => tableData.value.length)
const colCount = computed(() => columns.value.length)

const { shouldBeHighlighted } = useStatusGridHighlightDecider({
  selectedCell,
  hoveredCell,
  rowCount,
  colCount,
})

const toggleTranspose = () => {
  emit('toggleTranspose')
}

const handleHover = (rowIndex: number, colIndex: number) => {
  hoveredCell.value = { row: rowIndex, col: colIndex }
}

const handleLeave = () => {
  hoveredCell.value = null
}

const handleHeaderClick = (rowIndex: number, colIndex: number) => {
  hoveredCell.value = { row: rowIndex, col: colIndex }
}

// eslint-disable-next-line @typescript-eslint/no-explicit-any
const getStatusInfoForCell = (row: TableRow, column: Column): any => {
  if (props.isTransposed) {
    return props.getStatusInfo(column.key, row.source || '')
  } else {
    return props.getStatusInfo(row.client || '', column.key)
  }
}

const handleCellSelect = (rowIndex: number, colIndex: number) => {
  selectedCell.value = { row: rowIndex, col: colIndex }
}

const handleCellUnselect = () => {
  selectedCell.value = null
}
</script>

<style scoped>
/* Table column widths */
:deep(.slanty-table th:first-child),
:deep(.slanty-table td:first-child) {
  font-weight: bold;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Status column width and cell styling */
:deep(.slanty-table th:not(:first-child)),
:deep(.slanty-table td:not(:first-child)) {
  width: 40px;
  min-width: 40px;
  max-width: 40px;
  height: 40px !important;
  padding: 0 !important;
  vertical-align: middle;
  box-sizing: border-box;
}

/* Ensure the table doesn't shrink below the minimum width */
:deep(.slanty-table-container) {
  min-width: 180px !important;
  padding-top: 70px;
}

.status-cell {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  cursor: pointer;
}

/* Extend hover area to include border */
.status-cell::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: calc(100% + 1px);
  height: calc(100% + 1px);
  z-index: var(--z-index-status-cell-hover);
}

.left-header-cell {
  width: calc(100% + 16px);
  height: 100%;
  display: flex;
  align-items: center;
  position: relative;
  margin: -8px;
  padding: 8px;
  box-sizing: border-box;
}

.top-header-cell {
  width: calc(100% + 16px);
  height: 100%;
  align-items: center;
  justify-content: center;
  position: relative;
  margin: -8px;
  padding: 8px;
  box-sizing: border-box;
}

:deep(.slanty-table th.rotate) {
  position: relative;
}

.status-cell.highlight,
.left-header-cell.highlight {
  background-color: var(--highlight-color);
  color: black;
}

:deep(th:not(:first-child) span:has(.top-header-cell.highlight)) {
  background-color: var(--highlight-color);
  padding-top: 1px;
  margin-left: -28px;
  padding-left: 28px;
  color: black;
}

:deep(.slanty-table th.rotate > div > span) {
  position: relative;
  z-index: var(--z-index-client-source-status-matrix-header-div-span);
}

:deep(.slanty-table th.rotate > div) {
  position: relative;
  z-index: var(--z-index-client-source-status-matrix-header-div);
}
</style>
