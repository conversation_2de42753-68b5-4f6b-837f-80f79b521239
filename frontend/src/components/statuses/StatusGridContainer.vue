<template>
  <div class="managed-clients">
    <LoadingIndicator v-if="isLoading" />
    <div
      v-else-if="clients && sources"
      class="table-container"
      :class="{ 'live-refresh': isLiveRefreshEnabled }"
    >
      <StatusGrid
        :clients="clients"
        :sources="sources"
        :isTransposed="isTransposed"
        :isLiveRefreshEnabled="isLiveRefreshEnabled"
        :getStatusInfo="getStatusInfo"
        :status-indicator-component="statusIndicatorComponent"
        @toggleTranspose="toggleTranspose"
        @toggleLiveRefresh="toggleLiveRefresh"
      />
      <component
        :is="statusLegendComponent"
        v-if="clients.length > 0 && sources.length > 0"
        v-model:fullLegend="fullLegend"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, type Component } from 'vue'
import StatusGrid from '@/components/statuses/StatusGrid.vue'
import LoadingIndicator from '@/components/LoadingIndicator.vue'
import { type UseStatusGridResult } from '@/composables/useStatusGrid'

interface Props<T> {
  clientStatusesComposable: UseStatusGridResult<T>
  statusIndicatorComponent: Component
  statusLegendComponent: Component
}

// eslint-disable-next-line @typescript-eslint/no-explicit-any
const props = defineProps<Props<any>>()

const { clients, sources, getStatusInfo, isLiveRefreshEnabled, toggleLiveRefresh } =
  props.clientStatusesComposable || {}

const isTransposed = ref(true)
const fullLegend = ref(false)
const isLoading = ref(true)

watch(
  [clients, sources],
  () => {
    if (clients?.value !== null && sources?.value !== null) {
      isLoading.value = false
    }
  },
  { immediate: true },
)

const toggleTranspose = () => {
  isTransposed.value = !isTransposed.value
}
</script>

<style scoped>
.managed-clients {
  padding: 2rem;
  padding-top: 0;
  height: 100%;

  --highlight-color: rgb(215 215 215);
}

.table-container {
  position: relative;
  height: calc(100% - 4rem);
  overflow: auto;
}
</style>
