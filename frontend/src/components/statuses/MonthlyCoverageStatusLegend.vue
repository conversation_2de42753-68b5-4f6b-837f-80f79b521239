<template>
  <div class="legend" :class="{ 'legend-collapsed': !fullLegend }">
    <div
      v-if="!fullLegend"
      class="legend-collapsed-content"
      @click="$emit('update:fullLegend', true)"
    >
      <span class="fas fa-info-circle me-2"></span>
      Show Legend
    </div>
    <div v-else class="legend-expanded-content">
      <div class="legend-header">
        <button class="btn-close-legend" @click="$emit('update:fullLegend', false)">
          <span class="fas fa-times"></span>
        </button>
      </div>
      <table class="legend-table">
        <thead>
          <tr>
            <th>Status</th>
            <th>Monthly Coverage</th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="(statusData, index) in statusExamples" :key="index">
            <td>
              <div class="status-container">
                <MonthlyCoverageStatusIndicator :status-data="statusData" :is-interactive="false" />
              </div>
            </td>
            <td>{{ statusData.status }}</td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</template>

<script setup lang="ts">
import { CoverageStatus, type OrganizationMonthlyReportCoverage } from '@/domain/coverage/types'
import { PlainYearMonth } from '@/libs/temporal'
import { createInMonthPeriods } from '@/domain/coverage/period'
import MonthlyCoverageStatusIndicator from '@/components/statuses/MonthlyCoverageStatusIndicator.vue'

defineProps<{
  fullLegend: boolean
}>()

defineEmits<{
  'update:fullLegend': [value: boolean]
}>()

const month = PlainYearMonth.from('2024-01')
const statusExamples = [
  {
    month,
    coverage: [],
    status: CoverageStatus.NO_COVERAGE,
  },
  {
    month,
    coverage: createInMonthPeriods(
      [
        ['2024-01-01', '2024-01-10'],
        ['2024-01-15', '2024-01-20'],
        ['2024-01-25', '2024-01-27'],
      ],
      PlainYearMonth.from('2024-01'),
    ),
    status: CoverageStatus.PARTIAL_COVERAGE,
  },
  {
    month,
    coverage: createInMonthPeriods([['2024-01-01', '2024-01-10']], month),
    status: CoverageStatus.MAXIMUM_PARTIAL_COVERAGE,
  },
  {
    month,
    coverage: createInMonthPeriods([['2024-01-01', '2024-01-31']], month),
    status: CoverageStatus.FULL_COVERAGE,
  },
] as OrganizationMonthlyReportCoverage[]
</script>

<style scoped>
.legend {
  position: fixed;
  bottom: 55px;
  right: 7px;
  width: 290px;
  padding: 1rem;
  padding-bottom: 0;
  z-index: var(--z-index-status-legend);
  max-height: 85vh;
  overflow-y: auto;
  background-color: rgb(0 0 0 / 80%);
  border-radius: 8px;
  box-shadow: 0 4px 12px rgb(0 0 0 / 50%);
}

.legend-collapsed {
  width: auto;
  padding: 0.75rem 1.5rem;
  cursor: pointer;
}

.legend-collapsed:hover {
  background-color: rgb(0 0 0 / 80%);
}

.legend-collapsed-content {
  color: white;
  font-size: 0.9rem;
  display: flex;
  align-items: center;
}

.legend-header {
  display: flex;
  justify-content: flex-end;
  align-items: flex-start;
  margin-bottom: 1rem;
  position: relative;
}

.btn-close-legend {
  position: absolute;
  top: -0.7rem;
  right: -0.5rem;
  background: none;
  border: none;
  color: #fff;
  cursor: pointer;
  padding: 0.5rem;
  font-size: 1rem;
  opacity: 0.7;
  z-index: var(--z-index-status-legend-close-button);
}

.btn-close-legend:hover {
  opacity: 1;
}

.legend-table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 1rem;
}

.legend-table th {
  font-weight: 600;
  font-size: 0.9rem;
  color: white;
  padding: 0.1rem 0.4rem;
}

.legend-table td {
  font-size: 0.85rem;
  padding: 0.1rem 0.5rem;
}

.legend-table td:first-child {
  width: 50px;
  text-align: center;
  padding: 0.1rem 0.5rem;
}

.legend-table tr:last-child th,
.legend-table tr:last-child td {
  border-bottom: none;
}

.status-container {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  transform: scale(0.8);
}

.error-emoji {
  font-size: 1.2rem;
}

:deep(.failed .symbol.error-emoji) {
  margin-left: -0.4rem;
}
</style>
