<template>
  <StatusTooltip :sections="sections" />
</template>

<script setup lang="ts">
import { computed } from 'vue'
import StatusTooltip from '@/components/statuses/StatusTooltip.vue'
import type { OrganizationMonthlyReportCoverage } from '@/domain/coverage/types'

const props = defineProps<{ organizationMonthlyCoverage: OrganizationMonthlyReportCoverage }>()

type Row = { label: string; value: string | number | null }
type Section = Row[]

const sections = computed<Section[]>(() => {
  const { status, month, coverage, organizationName, organizationId } =
    props.organizationMonthlyCoverage

  const coverageRows = coverage.map((c, index) => ({
    label: index === 0 ? 'Coverage' : '',
    value: `${c.dateFrom.toString()} - ${c.dateTo.toString()}`,
  }))

  return [
    [
      { label: 'Organization', value: organizationName },
      { label: 'Org ID', value: organizationId },
      { label: 'Month', value: month.toString() },
      { label: 'Status', value: status },
      ...coverageRows,
    ],
  ]
})
</script>
