<template>
  <div
    v-if="statusInfo"
    ref="containerRef"
    class="status-container"
    :class="{ interactive: isInteractive }"
    @mouseenter="isInteractive && handleMouseEnter()"
    @mouseleave="isInteractive && handleMouseLeave()"
    @click="isInteractive && handleClick($event)"
  >
    <StatusBackground :status-info="statusInfo" :should-pulse="shouldPulse" />
    <ReportsStatusRing :reports="statusInfo.data?.reports ?? []" :should-pulse="shouldPulse" />
    <StatusSymbol :status-info="statusInfo" />
    <Teleport to="body">
      <StatusInfoTooltip
        v-if="isInteractive && showTooltip"
        :status-info="statusInfo"
        :target-element="containerRef"
      />
    </Teleport>
    <Teleport to="body">
      <StatusContextMenu
        v-if="isInteractive && client && source && contextMenu.isSelected(client, source)"
        :x="contextMenu.state.x"
        :y="contextMenu.state.y"
        :client="client"
        :source="source"
        :status-info="statusInfo"
        @close="handleMenuClose"
      />
    </Teleport>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import StatusBackground from '@/components/statuses/StatusBackground.vue'
import StatusSymbol from '@/components/statuses/StatusSymbol.vue'
import StatusInfoTooltip from '@/components/statuses/StatusInfoTooltip.vue'
import StatusContextMenu from '@/components/statuses/StatusContextMenu.vue'
import ReportsStatusRing from '@/components/statuses/ReportsStatusRing.vue'
import type { StatusInfo, StatusInfoWithoutData } from '@/types/statuses'
import { useStatusContextMenu } from '@/composables/useStatusContextMenu'
import { shouldPulse as shouldPulseStatus } from '@/types/statuses'

interface InteractiveProps {
  statusData: StatusInfo | undefined
  isInteractive: true
}

interface NonInteractiveProps {
  statusData: StatusInfoWithoutData | undefined
  isInteractive: false
}

type Props =
  | InteractiveProps
  | NonInteractiveProps
  | {
      statusData: undefined
      isInteractive?: boolean
    }

const props = withDefaults(defineProps<Props>(), {
  isInteractive: true as const,
})

const statusInfo = computed(() => props.statusData)
const statusData = computed(() =>
  props.statusData && 'data' in props.statusData ? props.statusData.data : null,
)
const source = computed(() => statusData.value?.source)
const client = computed(() => statusData.value?.organizationName)

const emit = defineEmits<{
  (e: 'select'): void
  (e: 'unselect'): void
}>()

const contextMenu = useStatusContextMenu()

const containerRef = ref<HTMLElement>()
const showTooltip = ref(false)
let tooltipTimer: number | null = null

const handleMouseEnter = () => {
  if (tooltipTimer !== null) {
    clearTimeout(tooltipTimer)
    tooltipTimer = null
  }

  tooltipTimer = window.setTimeout(() => {
    showTooltip.value = true
  }, 200)
}

const handleMouseLeave = () => {
  if (tooltipTimer !== null) {
    clearTimeout(tooltipTimer)
    tooltipTimer = null
  }

  showTooltip.value = false
}

const handleClick = (event: MouseEvent) => {
  event.preventDefault()
  event.stopPropagation()

  if (client.value && source.value) {
    contextMenu.show(event.clientX, event.clientY, client.value, source.value)
    emit('select')
  }
}

const handleMenuClose = () => {
  contextMenu.hide()
  emit('unselect')
}

const shouldPulse = computed((): boolean => {
  return !!statusInfo.value && shouldPulseStatus(statusInfo.value.scrapeStatus)
})
</script>

<style scoped>
.status-container {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
