<template>
  <div class="dropdown show" :style="position">
    <div class="dropdown-menu dropdown-menu-end border py-2 show">
      <a
        class="dropdown-item"
        :href="lastOperationId ? generateLogsUrl(lastOperationId) : '#'"
        target="_blank"
        :class="{ disabled: !lastOperationId }"
        :style="{
          cursor: lastOperationId ? 'pointer' : 'not-allowed',
        }"
      >
        <span class="fas fa-list fa-fw me-2"></span>
        Elastic Logs
      </a>
      <a
        class="dropdown-item"
        :href="isHistoryAvailable ? generateHistoryUrl(source!, organizationId!) : '#'"
        :class="{ disabled: !isHistoryAvailable }"
        :style="{ cursor: isHistoryAvailable ? 'pointer' : 'not-allowed' }"
      >
        <span class="fas fa-history fa-fw me-2"></span>
        Show History
      </a>
      <a
        class="dropdown-item"
        :href="getManageReportsUrl(organizationId!, source!)"
        :class="{ disabled: !organizationId || !source }"
        :style="{ cursor: organizationId && source ? 'pointer' : 'not-allowed' }"
        target="_blank"
      >
        <span class="far fa-file-alt fa-fw me-2" style="padding-left: 1px"></span>
        Manage Reports
      </a>
      <template v-if="jiraTicketUrl">
        <div class="dropdown-divider"></div>
        <a class="dropdown-item text-warning" :href="jiraTicketUrl" target="_blank">
          <span class="fas fa-ticket-alt fa-fw me-2"></span>
          Create Jira Ticket
        </a>
      </template>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { StatusInfo } from '@/types/statuses'
import { ref, onMounted, onUnmounted, watch, computed } from 'vue'
import { useElastic } from '@/composables/useElastic'
import { useJira } from '@/composables/useJira'
import { useScraperOperationHistory } from '@/composables/useScraperOperationHistory'
import { useBackoffice } from '@/composables/useBackoffice'
import type { Source } from '@/domain/types'

const props = defineProps<{
  x: number
  y: number
  client: string
  source: Source
  statusInfo: StatusInfo | undefined
}>()

const emit = defineEmits<{
  (e: 'close'): void
  (e: 'action', action: string, client: string, source: string, statusInfo: StatusInfo): void
}>()

const position = ref({
  left: '0px',
  top: '0px',
})

const { generateLogsUrl } = useElastic()
const { generateJiraTicketUrl } = useJira()
const { generateHistoryUrl, generateExternalHistoryUrl } = useScraperOperationHistory()
const { getManageReportsUrl } = useBackoffice()
const jiraTicketUrl = computed(() => {
  if (
    !props.statusInfo ||
    !props.statusInfo.errorType ||
    !lastOperationId.value ||
    !organizationId.value
  )
    return undefined

  const logsUrl = generateLogsUrl(lastOperationId.value)
  const historyUrl = generateExternalHistoryUrl(props.source, organizationId.value)
  return generateJiraTicketUrl(
    props.source,
    props.statusInfo.errorType,
    props.client,
    logsUrl,
    historyUrl,
  )
})

const organizationId = computed(() => props.statusInfo?.data.organizationId)
const lastOperationId = computed(() => props.statusInfo?.data.lastOperationId)
const isHistoryAvailable = computed(() => props.source && organizationId.value)

// Function to update position and ensure menu stays within viewport
const updatePosition = () => {
  // Get viewport dimensions
  const viewportWidth = window.innerWidth
  const viewportHeight = window.innerHeight

  // Menu dimensions (approximate)
  const menuWidth = 160
  const menuHeight = 75

  // Calculate position to keep menu within viewport
  let left = props.x
  let top = props.y

  // Adjust horizontal position if menu would go off-screen
  if (left + menuWidth > viewportWidth) {
    left = viewportWidth - menuWidth
  }

  // Adjust vertical position if menu would go off-screen
  if (top + menuHeight > viewportHeight) {
    top = viewportHeight - menuHeight
  }

  // Ensure menu doesn't go off the top or left of the screen
  left = Math.max(0, left)
  top = Math.max(0, top)

  position.value = {
    left: `${left}px`,
    top: `${top}px`,
  }
}

// Update position when x or y changes
watch(() => props.x, updatePosition)
watch(() => props.y, updatePosition)

// Close menu when clicking outside
const handleClickOutside = (event: MouseEvent) => {
  const target = event.target as HTMLElement
  if (!target.closest('.dropdown')) {
    emit('close')
  }
}

// Add and remove event listeners
onMounted(() => {
  document.addEventListener('click', handleClickOutside)
  // Initialize position
  updatePosition()
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})
</script>

<style scoped>
.dropdown {
  position: fixed;
  z-index: var(--z-index-status-context-menu);
}

.dropdown-divider {
  margin: 0.3rem 0;
}
</style>
