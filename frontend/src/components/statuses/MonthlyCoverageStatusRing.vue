<template>
  <div class="status-ring" :class="organizationMonthlyCoverage.status.toLowerCase()">
    <svg viewBox="0 0 100 100" class="ring-svg">
      <circle class="ring-background" cx="50" cy="50" r="35" fill="none" stroke-width="15" />
      <circle
        v-for="(day, index) in dailyCoverage"
        :key="day.date.toString()"
        class="ring-segment"
        :class="{ 'is-covered': day.isCovered }"
        cx="50"
        cy="50"
        r="35"
        fill="none"
        stroke-width="15"
        :stroke-dasharray="`${segmentLength} ${circumference - segmentLength}`"
        :stroke-dashoffset="getSegmentOffset(index)"
      />
    </svg>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { type OrganizationMonthlyReportCoverage } from '@/domain/coverage/types'
import { getMonthDailyCoverage } from '@/domain/coverage/monthCoverage'

const props = defineProps<{
  organizationMonthlyCoverage: OrganizationMonthlyReportCoverage
}>()

const circumference = computed(() => 2 * Math.PI * 35)

const dailyCoverage = computed(() =>
  getMonthDailyCoverage(
    props.organizationMonthlyCoverage.month,
    props.organizationMonthlyCoverage.coverage,
  ),
)

const segmentLength = computed(() => {
  return circumference.value / dailyCoverage.value.length
})

const getSegmentOffset = (index: number) => {
  return -index * segmentLength.value
}
</script>

<style scoped>
.status-ring {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.ring-svg {
  position: absolute;
  width: 100%;
  height: 100%;
  transform: rotate(-90deg);
  z-index: var(--z-index-status-background);
}

.ring-background {
  stroke: #808080;
}

.ring-segment {
  stroke: #808080;
  transition: stroke 0.3s ease;
}

/* stylelint-disable-next-line selector-class-pattern */
.status-ring.full_coverage .ring-segment.is-covered {
  stroke: #36b37e;
}

/* stylelint-disable-next-line selector-class-pattern */
.status-ring.partial_coverage .ring-segment.is-covered {
  stroke: #ffa500;
}

/* stylelint-disable-next-line selector-class-pattern */
.status-ring.maximum_partial_coverage .ring-segment.is-covered {
  stroke: #00b8d9;
}
</style>
