<template>
  <div
    class="status-background"
    :class="[{ pulse: shouldPulse }, statusInfo.scrapeStatus.toLowerCase()]"
  >
    <div class="circle" :class="statusInfo.scrapeStatus.toLowerCase()"></div>
  </div>
</template>

<script setup lang="ts">
import type { StatusInfo } from '@/types/statuses'

defineProps<{
  statusInfo: StatusInfo
  shouldPulse: boolean
}>()
</script>

<style scoped>
.status-background {
  width: calc(100% - 6px);
  height: calc(100% - 6px);
  position: relative;
  margin: 2px;
  z-index: var(--z-index-status-background);
}

.live-refresh .status-background.pulse {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }

  50% {
    transform: scale(0.9);
  }

  100% {
    transform: scale(1);
  }
}

.circle {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.status-background.configured .circle {
  background-color: #00b8d9;
}

.status-background.deleted .circle {
  background-color: #d90000;
}

.status-background.disabled .circle {
  background-color: grey;
}

.status-background.failed .circle {
  background-color: #e63757;
}

.status-background.finished .circle {
  background-color: #36b37e;
}

/* stylelint-disable-next-line selector-class-pattern */
.status-background.manually_blocked .circle {
  background-color: #343434;
}

/* stylelint-disable-next-line selector-class-pattern */
.status-background.manually_unblocked .circle {
  background-color: #fff;
}

.status-background.scheduled .circle {
  background-color: #6554c0;
}

.status-background.started .circle {
  background-color: #fff;
}

.status-background.stopped .circle {
  background-color: #654114;
}
</style>
