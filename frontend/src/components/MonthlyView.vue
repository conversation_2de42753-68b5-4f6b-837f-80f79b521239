<template>
  <div>
    <div class="d-flex align-items-center justify-content-between p-3 rounded position-relative">
      <div class="d-flex align-items-center gap-2">
        <div class="btn-group" role="group" aria-label="Month navigation">
          <button class="btn btn-secondary" @click="prevMonth" aria-label="Previous month">
            <span aria-hidden="true">&lt;</span>
          </button>
          <button
            class="btn btn-secondary"
            @click="nextMonth"
            :disabled="!allowFuture && isCurrentMonth"
            aria-label="Next month"
          >
            <span aria-hidden="true">&gt;</span>
          </button>
        </div>
        <div class="btn-group" role="group" aria-label="Current month">
          <button class="btn btn-secondary" @click="goToCurrentMonth" :disabled="isCurrentMonth">
            Current month
          </button>
        </div>
      </div>
      <div class="position-absolute top-50 start-50 translate-middle">
        <span class="fs-4 fw-bold">{{ formattedMonth }}</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, defineProps, defineEmits, watch } from 'vue'
import { Temporal } from '@js-temporal/polyfill'

const props = defineProps({
  allowFuture: {
    type: Boolean,
    default: true,
  },
})

const emit = defineEmits<{
  'month-selected': [yearMonth: Temporal.PlainYearMonth, formattedMonth: string]
}>()

const now = Temporal.Now.plainDateISO()
const currentYearMonth = ref(Temporal.PlainYearMonth.from({ year: now.year, month: now.month }))
const selectedYearMonth = ref(currentYearMonth.value)

const formattedMonth = computed(() => {
  const monthName = new Date(
    selectedYearMonth.value.year,
    selectedYearMonth.value.month - 1,
  ).toLocaleString('en-US', { month: 'long' })
  return `${monthName} ${selectedYearMonth.value.year}`
})

const isCurrentMonth = computed(() => {
  return selectedYearMonth.value.equals(currentYearMonth.value)
})

function prevMonth() {
  selectedYearMonth.value = selectedYearMonth.value.subtract({ months: 1 })
}

function nextMonth() {
  if (!props.allowFuture && isCurrentMonth.value) return
  selectedYearMonth.value = selectedYearMonth.value.add({ months: 1 })
}

function goToCurrentMonth() {
  selectedYearMonth.value = Temporal.PlainYearMonth.from(currentYearMonth.value)
}

watch(
  selectedYearMonth,
  (newMonth) => {
    emit('month-selected', newMonth, formattedMonth.value)
  },
  { immediate: true },
)
</script>

<style scoped>
.bg-dark {
  background-color: #1a2332 !important;
}

.btn-group .btn {
  min-width: 48px;
}

.text-light {
  color: #e5eaf2 !important;
}

.position-absolute {
  pointer-events: none;
}
</style>
