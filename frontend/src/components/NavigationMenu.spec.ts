import { describe, it, expect } from 'vitest'
import { mount } from '@vue/test-utils'
import NavigationMenu from './NavigationMenu.vue'

describe('NavigationMenu', () => {
  const mockItems = [
    {
      text: 'Test Link',
      href: 'https://example.com',
      icon: 'fas fa-test',
    },
    {
      text: 'Parent',
      children: [
        {
          text: 'Child',
          href: 'https://child.com',
        },
      ],
    },
  ]

  it('renders menu items correctly', () => {
    const wrapper = mount(NavigationMenu, {
      props: {
        items: mockItems,
      },
    })

    const topLevelItems = wrapper
      .findAll('.nav-item')
      .filter((item) => !item.find('.nav.collapse').exists())
    expect(topLevelItems).toHaveLength(2)
    expect(wrapper.text()).toContain('Test Link')
    expect(wrapper.text()).toContain('Parent')
  })

  it('renders external links with correct attributes', () => {
    const wrapper = mount(NavigationMenu, {
      props: {
        items: [mockItems[0]],
      },
    })

    const link = wrapper.find('a.nav-link')
    expect(link.attributes('href')).toBe('https://example.com')
    expect(link.attributes('target')).toBe('_blank')
    expect(link.attributes('rel')).toBe('noopener noreferrer')
  })

  it('renders nested menu items correctly', () => {
    const wrapper = mount(NavigationMenu, {
      props: {
        items: [mockItems[1]],
      },
    })

    const parentLink = wrapper.find('a.dropdown-indicator')
    expect(parentLink.text()).toContain('Parent')

    const childLink = wrapper.find('ul.nav.collapse a.nav-link')
    expect(childLink.text()).toContain('Child')
    expect(childLink.attributes('href')).toBe('https://child.com')
  })

  it('generates correct IDs for nested items', () => {
    const wrapper = mount(NavigationMenu, {
      props: {
        items: [mockItems[1]],
      },
    })

    const parentId = wrapper.find('a.dropdown-indicator').attributes('href')
    const childContainer = wrapper.find('ul.nav.collapse')
    expect(childContainer.attributes('id')).toBe(parentId?.substring(1))
  })

  it('handles menu items without href', () => {
    const wrapper = mount(NavigationMenu, {
      props: {
        items: [
          {
            text: 'No Link',
            icon: 'fas fa-test',
          },
        ],
      },
    })

    const link = wrapper.find('a.nav-link')
    expect(link.exists()).toBe(true)
    expect(link.attributes('href')).toBe('#')
    expect(link.attributes('target')).toBeUndefined()
    expect(link.attributes('rel')).toBeUndefined()
    expect(link.text()).toContain('No Link')
  })
})
