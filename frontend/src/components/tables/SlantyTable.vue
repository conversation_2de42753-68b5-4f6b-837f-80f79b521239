<template>
  <div class="slanty-table-container">
    <table class="slanty-table">
      <thead>
        <tr>
          <th v-for="(column, index) in columns" :key="index" :class="{ rotate: index !== 0 }">
            <div>
              <span>
                <slot name="header" :column="column" :colIndex="index">
                  {{ column.label }}
                </slot>
              </span>
            </div>
          </th>
        </tr>
      </thead>
      <tbody>
        <tr v-for="(row, rowIndex) in data" :key="rowIndex">
          <td v-for="(column, colIndex) in columns" :key="colIndex">
            <slot
              :row="row"
              :column="column"
              :rowIndex="rowIndex"
              :colIndex="colIndex"
              :value="row[column.key]"
            ></slot>
          </td>
        </tr>
      </tbody>
    </table>
  </div>
</template>

<script setup lang="ts">
interface Column {
  key: string
  label: string
}

interface HeaderClasses {
  [index: number]: string
}

withDefaults(
  defineProps<{
    columns: Column[]
    data: Record<string, string | undefined>[]
    headerClasses?: HeaderClasses
  }>(),
  {
    headerClasses: () => ({}),
  },
)

defineOptions({
  name: 'SlantyTable',
  inheritAttrs: false,
})
</script>

<style scoped>
/* Reset table styles to ensure our styles take precedence */
.slanty-table-container {
  overflow: auto;
  margin: 0;
  padding-top: 90px; /* Space for rotated headers */
}

.slanty-table {
  border-collapse: collapse !important;

  --table-border-width: 1px;

  width: auto !important;
  border: none !important;
  background-color: transparent !important;
  table-layout: fixed !important;
}

.slanty-table th,
.slanty-table td {
  border: var(--table-border-width) solid #ddd !important;
  padding: 8px !important;
  text-align: center !important;
  background-color: transparent !important;
}

.slanty-table th {
  border-bottom: none !important;
  position: relative !important;
  padding: 0 !important;
  height: 100px !important;
}

.slanty-table th.rotate {
  white-space: nowrap !important;
  position: relative !important;
  height: 100px !important;
  border: none !important;
}

.slanty-table th.rotate > div {
  position: absolute !important;
  bottom: 0 !important;
  left: 0 !important;
  width: 100% !important;
  transform: translate(calc(100% - var(--table-border-width) / 2), var(--table-border-width))
    rotate(315deg) !important;
  transform-origin: 0% calc(100% - var(--table-border-width)) !important;
}

.slanty-table th.rotate > div > span {
  position: absolute !important;
  bottom: 0 !important;
  left: 0 !important;
  border-bottom: var(--table-border-width) solid #ddd !important;
  padding-right: 10px !important;
  padding-bottom: 4px !important;
}

/* Override any Bootstrap or theme styles that might be affecting the table */
.slanty-table thead th {
  border: none !important;
  background: none !important;
  box-shadow: none !important;
}
</style>
