import {
  type MonthlyReportsDataGridStatus,
  type OrganizationReportCoverage,
} from '@/domain/coverage/types'

import { getReportCoverage } from '@/api/reportCoverage'
import { PlainYearMonth } from '@/libs/temporal'
import { type OrganizationMonthlyReportCoverage, type Period } from '@/domain/coverage/types'
import { getMonthlyCoverageStatus, getMonthCoverage } from '@/domain/coverage/monthCoverage'
import type { Source } from '@/domain/types'

export function useMonthlyCoverage() {
  let coverage: OrganizationReportCoverage[] | null = null
  let lastFetchTime: number | null = null
  const CACHE_DURATION = 5 * 60 * 1000 // 5 minutes in milliseconds

  function makeGetMonthlyCoverageStatusesFunction(yearMonth: PlainYearMonth) {
    return async (): Promise<MonthlyReportsDataGridStatus[]> => {
      const coverage = await fetchCoverageIfNeeded()

      return coverage.flatMap((orgCoverage) =>
        Object.entries(orgCoverage.coverage).map(([source, periods]) => ({
          client: orgCoverage.organizationName,
          source: source as Source,
          status: createOrganizationMonthlyReportCoverage(yearMonth, orgCoverage, periods),
        })),
      )
    }
  }
  async function fetchCoverageIfNeeded(): Promise<OrganizationReportCoverage[]> {
    const now = Date.now()
    if (coverage && lastFetchTime && now - lastFetchTime < CACHE_DURATION) {
      return coverage
    }

    coverage = await getReportCoverage()
    lastFetchTime = now
    return coverage
  }

  function createOrganizationMonthlyReportCoverage(
    month: PlainYearMonth,
    { organizationId, organizationName }: OrganizationReportCoverage,
    periods: Period[],
  ): OrganizationMonthlyReportCoverage {
    return {
      organizationId,
      organizationName,
      month,
      coverage: getMonthCoverage(month, periods),
      status: getMonthlyCoverageStatus(month, periods),
    }
  }

  return {
    makeGetMonthlyCoverageStatusesFunction,
  }
}
