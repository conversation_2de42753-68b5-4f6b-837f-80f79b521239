import { unref, type Ref } from 'vue'

export interface CellState {
  row: number
  col: number
}

interface UseStatusGridHighlightDeciderProps {
  selectedCell: Ref<CellState | null>
  hoveredCell: Ref<CellState | null>
  rowCount: Ref<number>
  colCount: Ref<number>
}

interface UseStatusGridHighlightDeciderReturn {
  shouldBeHighlighted: (rowIndex: number, colIndex: number) => boolean
}

export function useStatusGridHighlightDecider(
  props: UseStatusGridHighlightDeciderProps,
): UseStatusGridHighlightDeciderReturn {
  const shouldBeHighlighted = (rowIndex: number, colIndex: number): boolean => {
    const selectedCell = unref(props.selectedCell)
    const hoveredCell = unref(props.hoveredCell)
    const rowCount = unref(props.rowCount)
    const colCount = unref(props.colCount)

    function isOutOfRange(rowIndex: number, colIndex: number): boolean {
      // Allow -1 for header cells
      if (rowIndex < -1 || rowIndex >= rowCount) return true
      if (colIndex < -1 || colIndex >= colCount) return true
      return false
    }

    if (isOutOfRange(rowIndex, colIndex)) {
      return false
    }

    const activeCell = selectedCell || hoveredCell
    if (!activeCell) return false

    // Highlight entire column if top header is selected
    if (activeCell.row === -1 && activeCell.col >= 0) {
      return colIndex === activeCell.col || (rowIndex === -1 && colIndex === activeCell.col)
    }
    // Highlight entire row if left header is selected
    if (activeCell.col === -1 && activeCell.row >= 0) {
      return rowIndex === activeCell.row || (colIndex === -1 && rowIndex === activeCell.row)
    }
    // ⅃-shape highlight for single cell
    if (activeCell.row >= 0 && activeCell.col >= 0) {
      const isInRow = rowIndex === activeCell.row && colIndex >= -1 && colIndex <= activeCell.col
      const isInCol = colIndex === activeCell.col && rowIndex >= -1 && rowIndex <= activeCell.row
      return isInRow || isInCol
    }
    return false
  }

  return {
    shouldBeHighlighted,
  }
}
