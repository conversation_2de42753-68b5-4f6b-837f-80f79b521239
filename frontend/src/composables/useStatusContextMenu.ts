import { reactive } from 'vue'

const state = reactive<{ visible: boolean; x: number; y: number; client: string; source: string }>({
  visible: false,
  x: 0,
  y: 0,
  client: '',
  source: '',
})
export function useStatusContextMenu() {
  const show = (x: number, y: number, client: string, source: string) => {
    Object.assign(state, { x, y, client, source, visible: true })
  }

  const hide = () => {
    state.visible = false
  }

  const isSelected = (client: string, source: string) => {
    return state.visible && state.client === client && state.source === source
  }

  return { state, show, hide, isSelected }
}
