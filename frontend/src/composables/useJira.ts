import { ref } from 'vue'
import { getMe } from '@/api/me'

// where to get other userids?
// https://indiebi.atlassian.net/rest/api/3/user/assignable/search?project=10605&startAt=0&maxResults=1000
//
// how to get project id?
// https://indiebi.atlassian.net/rest/api/3/project
//
// where to get issue type id?
// https://indiebi.atlassian.net/rest/api/3/issuetype

const reporterId = ref<string | undefined>(undefined)

const emailToJiraIdMap = {
  'adam.doma<PERSON><EMAIL>': '5c5c19dd95f91f3a04207ef9',
  '<EMAIL>': '5cfe268baf3a8f0c58e3e50d',
  '<EMAIL>': '5f50edf5fcaf93003b517826',
  '<EMAIL>': '61a73c43744c4d0069175340',
  '<EMAIL>': '6107b26fc51f3a0069ddaa01',
  '<EMAIL>': '61f8ed69c6bd1a00691cbd07',
  '<EMAIL>': '63da2962010d35637970dc68',
  '<EMAIL>': '624effa5247a4b006921dab3',
}

getMe().then((me) => {
  reporterId.value = emailToJiraIdMap[me.mail as keyof typeof emailToJiraIdMap]
})

export const useJira = () => {
  const generateJiraTicketUrl = (
    source: string,
    errorType: string,
    client: string,
    logsUrl: string,
    historyUrl: string,
  ) => {
    if (!reporterId.value) return undefined

    const description = encodeURIComponent(
      `[scraper operation history|${historyUrl}]\n[elastic logs|${logsUrl}]`,
    )

    return `https://indiebi.atlassian.net/secure/CreateIssueDetails!init.jspa?pid=10605&issuetype=10202&summary=${source} scrape is failing with ${errorType} for ${client}&reporter=${reporterId.value}&description=${description}`
  }

  return {
    generateJiraTicketUrl,
  }
}
