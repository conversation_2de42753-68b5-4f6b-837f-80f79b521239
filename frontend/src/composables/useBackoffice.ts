import { Env } from '@/types/envs'
import { useEnv } from './useEnv'
import type { Source } from '@/domain/types'

export const useBackoffice = () => {
  const env = useEnv()

  const getManageReportsUrl = (organizationId: string, source: Source) => {
    return encodeURI(
      `${getBackofficeMainUrl()}/admin/reports?organization_id=${organizationId}&sources=${source}`,
    )
  }

  const getBackofficeMainUrl = () => {
    switch (env.getCurrentEnv()) {
      case Env.PROD:
        return 'https://backoffice.indiebi.com'
      case Env.DEV:
      case Env.LOCAL:
        return 'https://backoffice.indiebi.dev'
    }
  }

  return {
    getManageReportsUrl,
  }
}
