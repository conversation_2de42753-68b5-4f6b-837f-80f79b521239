import { ref, reactive, type Ref } from 'vue'
import type { GridStatus } from '@/domain/grids/types'

export interface UseStatusGridResult<T> {
  clients: Ref<string[] | null>
  sources: Ref<string[] | null>
  isLiveRefreshEnabled: Ref<boolean>
  statusChangeTrigger: Ref<number>
  getStatusInfo: (client: string, source: string) => T | undefined
  toggleLiveRefresh: (enabled: boolean) => void
}

export function useStatusGrid<T>(
  getStatuses: () => Promise<GridStatus<T>[]>,
): UseStatusGridResult<T> {
  const statuses = reactive<Record<string, Record<string, T>>>({})
  const statusChangeTrigger = ref(0)
  const isLiveRefreshEnabled = ref(false)
  let refreshInterval: number | null = null
  const sources = ref<string[] | null>(null)
  const clients = ref<string[] | null>(null)

  const getStatusInfo = (client: string, source: string): T | undefined => {
    if (!statuses[client] || !statuses[client][source]) {
      return undefined
    }

    return statuses[client][source]
  }

  const initializeData = async () => {
    try {
      const allStatuses = await getStatuses()

      Object.keys(statuses).forEach((key) => {
        delete statuses[key]
      })

      allStatuses.forEach(({ client, source, status }) => {
        if (!statuses[client]) {
          statuses[client] = {}
        }

        statuses[client][source] = status
      })

      updateClientsAndSources()
      triggerStatusChange()
    } catch (error) {
      console.error('Error fetching scraper statuses:', error)
    }
  }

  const updateClientsAndSources = () => {
    clients.value = Object.keys(statuses).sort()
    sources.value = Array.from(
      new Set(Object.values(statuses).flatMap((status) => Object.keys(status))),
    ).sort()
  }

  const triggerStatusChange = () => {
    statusChangeTrigger.value++
  }

  const toggleLiveRefresh = (enabled: boolean) => {
    isLiveRefreshEnabled.value = enabled

    if (enabled) {
      if (refreshInterval === null) {
        refreshInterval = window.setInterval(initializeData, 2000)
      }
    } else {
      if (refreshInterval !== null) {
        clearInterval(refreshInterval)
        refreshInterval = null
      }
    }
  }

  initializeData()

  return {
    clients,
    sources,
    isLiveRefreshEnabled,
    statusChangeTrigger,
    getStatusInfo,
    toggleLiveRefresh,
  }
}
