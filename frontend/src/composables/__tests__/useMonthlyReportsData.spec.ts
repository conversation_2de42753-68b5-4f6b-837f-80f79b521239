import { describe, it, expect, vi, beforeEach } from 'vitest'
import { useMonthlyCoverage } from '../useMonthlyCoverage'
import * as reportCoverage from '@/api/reportCoverage'
import { PlainYearMonth } from '@/libs/temporal'
import { CoverageStatus } from '@/domain/coverage/types'
import { createPeriods, createInMonthPeriods } from '@/domain/coverage/period'
import type { Source } from '@/domain/types'

const yearMonth = PlainYearMonth.from({ year: 2025, month: 4 })

describe('useMonthlyReportsData', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('getMonthlyReportsDataStatuses', () => {
    it('get getMonthlyReportsDataStatuses for 2025-04 with no coverage', async () => {
      vi.spyOn(reportCoverage, 'getReportCoverage').mockResolvedValue([])

      const { makeGetMonthlyCoverageStatusesFunction } = useMonthlyCoverage()

      const getMonthlyReportsDataStatuses = makeGetMonthlyCoverageStatusesFunction(yearMonth)
      const statuses = await getMonthlyReportsDataStatuses()
      expect(statuses).toEqual([])
    })

    it('get getMonthlyReportsDataStatuses for 2025-04', async () => {
      vi.spyOn(reportCoverage, 'getReportCoverage').mockResolvedValue([
        {
          organizationId: 'o-testorgid',
          organizationName: 'Test Organization',
          coverage: {
            ['steam_sales' as Source]: createPeriods([['2025-01-15', '2025-04-07']]),
            ['playstation_sales' as Source]: createPeriods([
              ['2025-01-15', '2025-04-07'],
              ['2025-04-10', '2025-04-15'],
              ['2025-04-28', '2025-05-20'],
            ]),
          },
        },
      ])

      const { makeGetMonthlyCoverageStatusesFunction } = useMonthlyCoverage()

      const getMonthlyReportsDataStatuses = makeGetMonthlyCoverageStatusesFunction(yearMonth)
      const statuses = await getMonthlyReportsDataStatuses()
      expect(statuses).toEqual([
        {
          client: 'Test Organization',
          source: 'steam_sales',
          status: {
            organizationId: 'o-testorgid',
            organizationName: 'Test Organization',
            coverage: createInMonthPeriods([['2025-04-01', '2025-04-07']], yearMonth),
            month: yearMonth,
            status: CoverageStatus.PARTIAL_COVERAGE,
          },
        },
        {
          client: 'Test Organization',
          source: 'playstation_sales',
          status: {
            organizationId: 'o-testorgid',
            organizationName: 'Test Organization',
            coverage: createInMonthPeriods(
              [
                ['2025-04-01', '2025-04-07'],
                ['2025-04-10', '2025-04-15'],
                ['2025-04-28', '2025-04-30'],
              ],
              yearMonth,
            ),

            month: yearMonth,
            status: CoverageStatus.PARTIAL_COVERAGE,
          },
        },
      ])
    })
  })
})
