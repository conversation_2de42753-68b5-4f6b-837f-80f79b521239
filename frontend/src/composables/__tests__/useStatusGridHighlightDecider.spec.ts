import { describe, it, expect } from 'vitest'
import { useStatusGridHighlightDecider, type CellState } from '../useStatusGridHighlightDecider'
import { ref } from 'vue'

type TestCase = {
  name: string
  cell: CellState
  expectedHighlights: Array<{ row: number; col: number }>
}

const GRID_SIZE = 5
const rowCount = GRID_SIZE
const colCount = GRID_SIZE

const testCases: TestCase[] = [
  {
    name: 'applies ⅃-shaped highlight for single cell',
    cell: { row: 2, col: 3 },
    expectedHighlights: [
      // Row 2 from column 0 to 3 (including left header)
      { row: 2, col: -1 }, // left header
      { row: 2, col: 0 },
      { row: 2, col: 1 },
      { row: 2, col: 2 },
      { row: 2, col: 3 }, // the cell itself
      // Column 3 from row 0 to 2 (including top header)
      { row: 1, col: 3 },
      { row: 0, col: 3 },
      { row: -1, col: 3 }, // top header
    ],
  },
  {
    name: 'applies smaller ⅃-shaped highlight near top-left corner',
    cell: { row: 0, col: 0 },
    expectedHighlights: [
      { row: 0, col: -1 }, // top header
      { row: 0, col: 0 },
      { row: -1, col: 0 }, // left header
    ],
  },
  {
    name: 'applies big ⅃-shaped highlight near bottom-right corner',
    cell: { row: 4, col: 4 },
    expectedHighlights: [
      { row: 4, col: -1 }, // top header
      { row: 4, col: 0 },
      { row: 4, col: 1 },
      { row: 4, col: 2 },
      { row: 4, col: 3 },
      { row: 4, col: 4 }, // the cell itself
      // Column 4 from row 0 to 3 (including left header)
      { row: 3, col: 4 },
      { row: 2, col: 4 },
      { row: 1, col: 4 },
      { row: 0, col: 4 },
      { row: -1, col: 4 }, // left header
    ],
  },
  {
    name: 'highlights entire row when left header is selected',
    cell: { row: 1, col: -1 },
    expectedHighlights: [
      { row: 1, col: -1 }, // left header
      { row: 1, col: 0 },
      { row: 1, col: 1 },
      { row: 1, col: 2 },
      { row: 1, col: 3 },
      { row: 1, col: 4 },
    ],
  },
  {
    name: 'highlights entire column when top header is selected',
    cell: { row: -1, col: 4 },
    expectedHighlights: [
      { row: -1, col: 4 }, // top header
      { row: 0, col: 4 },
      { row: 1, col: 4 },
      { row: 2, col: 4 },
      { row: 3, col: 4 },
      { row: 4, col: 4 },
    ],
  },
]

describe('useStatusGridHighlightDecider', () => {
  describe.each(['selected', 'hovered'] as const)('when cell is %s', (state) => {
    testCases.forEach(({ name, cell, expectedHighlights }) => {
      it(`${name}`, () => {
        const selectedCell = state === 'selected' ? cell : null
        const hoveredCell = state === 'hovered' ? cell : null

        const { shouldBeHighlighted } = useStatusGridHighlightDecider({
          selectedCell: ref(selectedCell),
          hoveredCell: ref(hoveredCell),
          rowCount: ref(rowCount),
          colCount: ref(colCount),
        })

        for (let row = -1; row < colCount; row++) {
          for (let col = -1; col < rowCount; col++) {
            const rowOnListOfExpectedHighlight = expectedHighlights.some(
              (eh) => eh.row === row && eh.col === col,
            )
            expect(shouldBeHighlighted(row, col)).toBe(rowOnListOfExpectedHighlight)
          }
        }
      })
    })
  })

  it('should return false for coordinates outside grid', () => {
    const { shouldBeHighlighted } = useStatusGridHighlightDecider({
      selectedCell: ref({ row: 2, col: 2 }),
      hoveredCell: ref(null),
      rowCount: ref(rowCount),
      colCount: ref(colCount),
    })
    expect(shouldBeHighlighted(10, 10)).toBe(false)
    expect(shouldBeHighlighted(5, 5)).toBe(false)
    expect(shouldBeHighlighted(-2, 0)).toBe(false)
    expect(shouldBeHighlighted(0, -2)).toBe(false)
  })
})
