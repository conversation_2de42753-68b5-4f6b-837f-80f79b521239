export const useScraperOperationHistory = () => {
  const generateHistoryUrl = (source: string, organizationId: string) => {
    return `/scraper_operation_history/?tableState={"searchBuilder":{"criteria":[{"condition":"=","data":"Organization+ID","origData":"organization_id","type":"string","value":["${organizationId}"]},{"condition":"=","data":"Source","origData":"source","type":"string","value":["${source}"]}],"logic":"AND"}}`
  }

  const generateExternalHistoryUrl = (source: string, organizationId: string) => {
    return encodeURI(`${window.location.origin}${generateHistoryUrl(source, organizationId)}`)
  }

  return {
    generateHistoryUrl,
    generateExternalHistoryUrl,
  }
}
