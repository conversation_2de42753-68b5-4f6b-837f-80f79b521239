export const useElastic = () => {
  const generateLogsUrl = (operationId: string) => {
    return `https://indiebi.kb.westeurope.azure.elastic-cloud.com/s/dpt/app/discover#/?_g=(filters:!(),refreshInterval:(pause:!t,value:60000),time:(from:now-1y%2Fd,to:now))&_a=(breakdownField:log.level,columns:!(message),dataSource:(dataViewId:'logs-*',type:dataView),density:compact,filters:!((meta:(alias:!n,disabled:!f,index:'dataset-logs-*-*',key:service.name,negate:!f,params:(query:scraper-service),type:phrase),query:(match_phrase:(service.name:scraper-service))),(meta:(alias:!n,disabled:!f,index:'dataset-logs-*-*',key:s2.operation_id,negate:!f,params:(query:'${operationId}'),type:phrase),query:(match_phrase:(s2.operation_id:'${operationId}')))),headerRowHeight:-1,hideChart:!f,interval:auto,query:(language:kuery,query:''),rowHeight:-1,sort:!(!('@timestamp',desc)))`
  }

  return {
    generateLogsUrl,
  }
}
