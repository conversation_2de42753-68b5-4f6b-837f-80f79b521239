export enum ErrorType {
  CONFIGURATION_ISSUE = 'CONFIGURATION_ISSUE',
  DEPENDENCIES_SYNC_ERROR = 'DEPENDENCIES_SYNC_ERROR',
  INCORRECT_CREDENTIALS = 'INCORRECT_CREDENTIALS',
  MISSING_2FA = 'MISSING_2FA',
  MISSING_CAPTCHA = 'MISSING_CAPTCHA',
  MISSING_PERMISSIONS = 'MISSING_PERMISSIONS',
  RATE_LIMIT_EXCEEDED = 'RATE_LIMIT_EXCEEDED',
  SECRET_EXPIRED = 'SECRET_EXPIRED',
  SESSION_EXPIRED = 'SESSION_EXPIRED',
  STALE = 'STALE',
  STOPPED = 'STOPPED',
  TEMPORARY_PORTAL_ISSUE = 'TEMPORARY_PORTAL_ISSUE',
  UNEXPECTED_ERROR = 'UNEXPECTED_ERROR',
  NONE = 'NONE',
}

export interface ErrorStatus {
  code: ErrorType
  label: string
}

export const ERROR_STATUSES: ErrorStatus[] = [
  { code: ErrorType.CONFIGURATION_ISSUE, label: 'Configuration Issue' },
  { code: ErrorType.DEPENDENCIES_SYNC_ERROR, label: 'Dependencies Sync Error' },
  { code: ErrorType.INCORRECT_CREDENTIALS, label: 'Incorrect Credentials' },
  { code: ErrorType.MISSING_2FA, label: 'Missing 2FA' },
  { code: ErrorType.MISSING_CAPTCHA, label: 'Missing Captcha' },
  { code: ErrorType.MISSING_PERMISSIONS, label: 'Missing Permissions' },
  { code: ErrorType.RATE_LIMIT_EXCEEDED, label: 'Rate Limit Exceeded' },
  { code: ErrorType.SECRET_EXPIRED, label: 'Secret Expired' },
  { code: ErrorType.SESSION_EXPIRED, label: 'Session Expired' },
  { code: ErrorType.STALE, label: 'Stale' },
  { code: ErrorType.STOPPED, label: 'Stopped' },
  { code: ErrorType.TEMPORARY_PORTAL_ISSUE, label: 'Temporary Portal Issue' },
  { code: ErrorType.UNEXPECTED_ERROR, label: 'Unexpected Error' },
]

export const ERROR_EMOJIS: Record<ErrorType, string> = {
  [ErrorType.CONFIGURATION_ISSUE]: '⚙️',
  [ErrorType.DEPENDENCIES_SYNC_ERROR]: '🧲',
  [ErrorType.INCORRECT_CREDENTIALS]: '🔑',
  [ErrorType.MISSING_2FA]: '✉️',
  [ErrorType.MISSING_CAPTCHA]: '🤖',
  [ErrorType.MISSING_PERMISSIONS]: '🚫',
  [ErrorType.RATE_LIMIT_EXCEEDED]: '✋',
  [ErrorType.SECRET_EXPIRED]: '⌛',
  [ErrorType.SESSION_EXPIRED]: '🕒',
  [ErrorType.STALE]: '🧊',
  [ErrorType.STOPPED]: '🛑',
  [ErrorType.TEMPORARY_PORTAL_ISSUE]: '🌀',
  [ErrorType.UNEXPECTED_ERROR]: '💥',
  [ErrorType.NONE]: '',
}

export const getErrorEmoji = (errorType: ErrorType): string => {
  return ERROR_EMOJIS[errorType]
}
