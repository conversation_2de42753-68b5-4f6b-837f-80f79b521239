import { vi, describe, it, expect, afterEach } from 'vitest'
import { getReportCoverage } from './reportCoverage'
import { PlainDate } from '@/libs/temporal'
import type { OrganizationsReportCoverageResponse } from './types'
import type { Source } from '@/domain/types'

function mockFetchResponse(data: OrganizationsReportCoverageResponse) {
  global.fetch = vi.fn().mockResolvedValue({
    ok: true,
    json: () => Promise.resolve(data),
  })
}

describe('getReportCoverage', () => {
  afterEach(() => {
    vi.restoreAllMocks()
  })

  it('getReportCoverage returns dates as PlainDate objects', async () => {
    mockFetchResponse([
      {
        organizationId: 'org-1',
        organizationName: 'Test Organization',
        coverage: {
          ['steam_sales' as Source]: [{ dateFrom: '2024-01-01', dateTo: '2024-12-31' }],
          ['playstation_sales' as Source]: [
            { dateFrom: '2024-03-01', dateTo: '2024-03-31' },
            { dateFrom: '2024-04-15', dateTo: '2024-12-24' },
          ],
        },
      },
    ])

    expect(await getReportCoverage()).toEqual([
      {
        organizationId: 'org-1',
        organizationName: 'Test Organization',
        coverage: {
          steam_sales: [
            { dateFrom: PlainDate.from('2024-01-01'), dateTo: PlainDate.from('2024-12-31') },
          ],
          playstation_sales: [
            { dateFrom: PlainDate.from('2024-03-01'), dateTo: PlainDate.from('2024-03-31') },
            { dateFrom: PlainDate.from('2024-04-15'), dateTo: PlainDate.from('2024-12-24') },
          ],
        },
      },
    ])
  })
})
