import axios from 'axios'
import type { ManagedPartnerStatusResponse } from './types'
import type { GridStatus } from '@/domain/grids/types'
import { mapClientStatuses } from './utils'
import type { StatusInfo } from '@/types/statuses'

export async function getManagedPartnersStatuses(): Promise<GridStatus<StatusInfo>[]> {
  const response = await axios.get<ManagedPartnerStatusResponse[]>(
    '/api/managed-partners/statuses/',
  )
  return mapClientStatuses(response.data)
}
