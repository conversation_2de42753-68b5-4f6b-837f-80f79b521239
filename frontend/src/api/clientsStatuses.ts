import axios from 'axios'
import type { ClientStatusResponse } from './types'

import type { GridStatus } from '@/domain/grids/types'
import { mapClientStatuses } from './utils'
import type { StatusInfo } from '@/types/statuses'

export async function getClientsStatuses(): Promise<GridStatus<StatusInfo>[]> {
  const response = await axios.get<ClientStatusResponse[]>('/api/clients/statuses/')
  return mapClientStatuses(response.data)
}
