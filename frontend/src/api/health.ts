import { api } from './utils'
import type { AxiosResponse } from 'axios'

interface ServiceHealth {
  is_healthy: boolean
  error: string | null
}

export interface ServicesHealth {
  status: 'ok' | 'error'
  details: Record<string, ServiceHealth>
}

export interface HealthResponse {
  message: string
  session: Record<string, unknown>
  instance: {
    sysname: string
    nodename: string
    release: string
    version: string
    machine: string
  }
  version: string
  timestamp: string
  uptime: {
    seconds: number
    readable: string
  }
  services: ServicesHealth
}

export async function getHealth(): Promise<AxiosResponse<HealthResponse>> {
  return api.get('/health')
}
