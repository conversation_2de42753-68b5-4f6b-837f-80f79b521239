import { PlainDate } from '@/libs/temporal'
import type { OrganizationsReportCoverageResponse } from './types'
import type { OrganizationReportCoverage } from '@/domain/coverage/types'
import type { Source } from '@/domain/types'
import type { Period } from '@/domain/coverage/types'

export async function getReportCoverage(): Promise<OrganizationReportCoverage[]> {
  const response = await fetch('/api/managed-partners/report-coverage/')
  if (!response.ok) {
    throw new Error('Failed to fetch monthly report data')
  }
  const data = (await response.json()) as OrganizationsReportCoverageResponse

  return data.map(({ coverage, ...rest }) => ({
    ...rest,
    coverage: mapCoverageToPlainDateCoverage(coverage),
  }))
}

function mapCoverageToPlainDateCoverage(
  coverage: Record<Source, Array<{ dateFrom: string; dateTo: string }>>,
): Record<Source, Period[]> {
  return Object.fromEntries(
    Object.entries(coverage).map(([source, periods]) => [
      source,
      periods.map(({ dateFrom, dateTo }) => ({
        dateFrom: PlainDate.from(dateFrom),
        dateTo: PlainDate.from(dateTo),
      })),
    ]),
  )
}
