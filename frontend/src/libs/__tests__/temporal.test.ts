import { describe, it, expect } from 'vitest'
import { formatDateTimeISOWithSince, PlainDateTime } from '../temporal'

describe('formatDateTimeISOWithSince', () => {
  it('returns null for null input', () => {
    expect(formatDateTimeISOWithSince(null)).toBe(null)
  })

  it('formats timestamp with UTC suffix and relative time', () => {
    const someDate = new PlainDateTime(2024, 1, 15, 10, 25, 0)

    const result = formatDateTimeISOWithSince(someDate)

    expect(result).toMatch(/^2024-01-15 10:25:00 UTC \(\d+[dhm].*ago\)$/)
    expect(result).toContain('UTC')
    expect(result).toContain('ago')
  })

  it('includes proper time format', () => {
    const someDate = new PlainDateTime(2024, 1, 15, 10, 25, 30)

    const result = formatDateTimeISOWithSince(someDate)

    expect(result).toContain('2024-01-15 10:25:30 UTC')
  })

})
