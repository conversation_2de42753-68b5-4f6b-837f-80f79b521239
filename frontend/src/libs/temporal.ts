import { Temporal } from '@js-temporal/polyfill'

export const PlainDateTime = Temporal.PlainDateTime
export const PlainDate = Temporal.PlainDate
export const PlainYearMonth = Temporal.PlainYearMonth

export type PlainDateTime = Temporal.PlainDateTime
export type PlainDate = Temporal.PlainDate
export type PlainYearMonth = Temporal.PlainYearMonth

export function getToday(): PlainDate {
  return Temporal.Now.plainDateISO()
}

export function getNow(): PlainDateTime {
  return Temporal.Now.plainDateTimeISO()
}

export function formatDateTimeISO(date: PlainDateTime | null): string | null {
  if (!date) return null
  return date.toString().replace('T', ' ')
}

export function formatDateTimeISOWithSince(date: PlainDateTime | null): string | null {
  if (!date) return null

  const formatted = formatDateTimeISO(date)
  const now = getNow()
  const duration = now.since(date)

  const totalMinutes = duration.total('minutes')
  const totalHours = duration.total('hours')
  const totalDays = duration.total('days')

  let sinceText = ''

  if (totalDays >= 1) {
    const days = Math.floor(totalDays)
    const hours = Math.floor(totalHours % 24)

    if (hours > 0) {
      sinceText = `${days}d ${hours}h ago`
    } else {
      sinceText = `${days}d ago`
    }
  } else if (totalHours >= 1) {
    const hours = Math.floor(totalHours)
    const minutes = Math.floor(totalMinutes % 60)

    if (minutes > 0) {
      sinceText = `${hours}h ${minutes}m ago`
    } else {
      sinceText = `${hours}h ago`
    }
  } else {
    const minutes = Math.floor(totalMinutes)
    sinceText = `${minutes}m ago`
  }

  return `${formatted} UTC (${sinceText})`
}
