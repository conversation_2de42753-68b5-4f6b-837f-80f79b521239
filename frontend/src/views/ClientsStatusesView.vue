<template>
  <StatusGridContainer
    v-if="clientStatusesComposable && clientStatusesComposable.clients"
    :client-statuses-composable="clientStatusesComposable"
    :status-indicator-component="StatusIndicator"
    :status-legend-component="StatusLegend"
    key="clients"
  />
</template>

<script setup lang="ts">
import StatusGridContainer from '@/components/statuses/StatusGridContainer.vue'
import StatusIndicator from '@/components/statuses/StatusIndicator.vue'
import { useStatusGrid } from '@/composables/useStatusGrid'
import { getClientsStatuses } from '@/api/clientsStatuses'
import type { StatusInfo } from '@/types/statuses'
import StatusLegend from '@/components/statuses/StatusLegend.vue'

const clientStatusesComposable = useStatusGrid<StatusInfo>(getClientsStatuses)
</script>
