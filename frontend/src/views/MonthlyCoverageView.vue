<template>
  <MonthlyView class="mb-4" :allow-future="false" @month-selected="loadDataForSelectedMonth" />
  <StatusGridContainer
    v-if="clientStatusesComposable"
    :client-statuses-composable="clientStatusesComposable"
    :status-indicator-component="MonthlyCoverageStatusIndicator"
    :status-legend-component="MonthlyCoverageStatusLegend"
    :key="currentYearMonth?.toString()"
  />
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { PlainYearMonth } from '@/libs/temporal'
import MonthlyView from '../components/MonthlyView.vue'
import StatusGridContainer from '@/components/statuses/StatusGridContainer.vue'
import { useStatusGrid } from '@/composables/useStatusGrid'
import { useMonthlyCoverage } from '@/composables/useMonthlyCoverage'
import MonthlyCoverageStatusIndicator from '@/components/statuses/MonthlyCoverageStatusIndicator.vue'
import type { OrganizationMonthlyReportCoverage } from '@/domain/coverage/types'
import MonthlyCoverageStatusLegend from '@/components/statuses/MonthlyCoverageStatusLegend.vue'

const currentYearMonth = ref<PlainYearMonth | null>(null)
const { makeGetMonthlyCoverageStatusesFunction } = useMonthlyCoverage()

const clientStatusesComposable = computed(() => {
  if (!currentYearMonth.value) return null
  const getMonthlyCoverageStatuses = makeGetMonthlyCoverageStatusesFunction(currentYearMonth.value)
  return useStatusGrid<OrganizationMonthlyReportCoverage>(getMonthlyCoverageStatuses)
})

function loadDataForSelectedMonth(yearMonth: PlainYearMonth) {
  currentYearMonth.value = yearMonth
}
</script>
