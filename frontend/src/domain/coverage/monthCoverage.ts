import { PlainDate, PlainYearMonth, getToday } from '@/libs/temporal'
import { CoverageStatus, type InMonthPeriod, type Period } from './types'
import { mergeConsecutiveCoveragePeriods } from './period'

export function getMonthlyCoverageStatus(month: PlainYearMonth, periods: Period[]): CoverageStatus {
  if (periods.length === 0) {
    return CoverageStatus.NO_COVERAGE
  }

  if (isMonthFullyCovered(month, periods)) {
    return CoverageStatus.FULL_COVERAGE
  }

  if (isMonthMaximumPartiallyCovered(month, periods)) {
    return CoverageStatus.MAXIMUM_PARTIAL_COVERAGE
  }

  if (isMonthPartiallyCovered(month, periods)) {
    return CoverageStatus.PARTIAL_COVERAGE
  }

  return CoverageStatus.NO_COVERAGE
}

export function isMonthFullyCovered(month: PlainYearMonth, periods: Period[]): boolean {
  if (periods.length === 0) {
    return false
  }

  const mergedPeriods = mergeConsecutiveCoveragePeriods(periods)
  return mergedPeriods.some((period) => isPeriodFullyCoveringMonth(month, period))
}

export function isMonthPartiallyCovered(month: PlainYearMonth, periods: Period[]): boolean {
  if (periods.length === 0) {
    return false
  }

  if (isMonthFullyCovered(month, periods)) {
    return false
  }

  const mergedPeriods = mergeConsecutiveCoveragePeriods(periods)
  return mergedPeriods.some((period) => isPeriodOverlappingWithMonth(month, period))
}

export function isMonthMaximumPartiallyCovered(month: PlainYearMonth, periods: Period[]): boolean {
  if (periods.length === 0) {
    return false
  }

  if (isMonthFullyCovered(month, periods)) {
    return false
  }

  const mergedPeriods = mergeConsecutiveCoveragePeriods(periods)
  const monthStart = month.toPlainDate({ day: 1 })
  const today = getToday()

  return mergedPeriods.some((period) => {
    const isStartCovered = PlainDate.compare(period.dateFrom, monthStart) <= 0
    const isCurrentDateCovered = PlainDate.compare(period.dateTo, today) >= 0
    return isStartCovered && isCurrentDateCovered
  })
}

function isPeriodFullyCoveringMonth(month: PlainYearMonth, period: Period): boolean {
  const { monthStart, monthEnd } = getMonthBounds(month)
  return (
    PlainDate.compare(period.dateFrom, monthStart) <= 0 &&
    PlainDate.compare(period.dateTo, monthEnd) >= 0
  )
}

function isPeriodOverlappingWithMonth(month: PlainYearMonth, period: Period): boolean {
  const { monthStart, monthEnd } = getMonthBounds(month)
  return (
    PlainDate.compare(period.dateFrom, monthEnd) <= 0 &&
    PlainDate.compare(period.dateTo, monthStart) >= 0
  )
}

function getMonthBounds(month: PlainYearMonth) {
  return {
    monthStart: month.toPlainDate({ day: 1 }),
    monthEnd: month.toPlainDate({ day: month.daysInMonth }),
  }
}

export function getMonthCoverage(month: PlainYearMonth, periods: Period[]): InMonthPeriod[] {
  const { monthStart, monthEnd } = getMonthBounds(month)

  return periods
    .filter((period) => isPeriodOverlappingWithMonth(month, period))
    .map((period) => ({
      dateFrom: PlainDate.compare(period.dateFrom, monthStart) < 0 ? monthStart : period.dateFrom,
      dateTo: PlainDate.compare(period.dateTo, monthEnd) > 0 ? monthEnd : period.dateTo,
      month,
    }))
}

export function getMonthCoveragePercentage(periods: InMonthPeriod[]): number {
  if (periods.length === 0) {
    return 0
  }

  const coveredDays = periods.reduce(
    (sum, { dateFrom, dateTo }) => sum + dateTo.day - dateFrom.day + 1,
    0,
  )

  return Number((coveredDays / periods[0].month.daysInMonth).toFixed(2))
}

export interface DayCoverage {
  date: PlainDate
  isCovered: boolean
}

export function getMonthDailyCoverage(
  month: PlainYearMonth,
  periods: InMonthPeriod[],
): DayCoverage[] {
  const monthStart = month.toPlainDate({ day: 1 })
  const daysInMonth = month.daysInMonth

  return Array.from({ length: daysInMonth }, (_, index) => {
    const date = monthStart.add({ days: index })
    const isCovered = periods.some(
      (period) =>
        PlainDate.compare(date, period.dateFrom) >= 0 &&
        PlainDate.compare(date, period.dateTo) <= 0,
    )
    return { date, isCovered }
  })
}
