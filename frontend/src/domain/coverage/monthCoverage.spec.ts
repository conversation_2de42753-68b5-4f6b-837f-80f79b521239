import { describe, it, expect, vi, beforeEach } from 'vitest'
import {
  isMonthFullyCovered,
  isMonthPartiallyCovered,
  getMonthCoverage,
  getMonthCoveragePercentage,
  getMonthlyCoverageStatus,
  isMonthMaximumPartiallyCovered,
  getMonthDailyCoverage,
  type DayCoverage,
} from './monthCoverage'
import { PlainDate, PlainYearMonth } from '@/libs/temporal'
import * as temporal from '@/libs/temporal'
import type { Period, InMonthPeriod } from './types'
import { createPeriods, createInMonthPeriods as _createInMonthPeriods } from './period'
import { CoverageStatus } from './types'

const month = PlainYearMonth.from('2024-05')

const createInMonthPeriods = (periods: string[][], defaultMonth: PlainYearMonth = month) =>
  _createInMonthPeriods(periods, defaultMonth)

describe('isMonthFullyCovered', () => {
  it('returns false when no coverage periods provided', () => {
    const periods: Period[] = []
    expect(isMonthFullyCovered(month, periods)).toBe(false)
  })

  it('returns true when single period fully covers the month', () => {
    const periods = createPeriods([['2024-05-01', '2024-05-31']])
    expect(isMonthFullyCovered(month, periods)).toBe(true)
  })

  it('returns true when single period is wider than the month', () => {
    const periods = createPeriods([['2024-04-01', '2024-06-30']])
    expect(isMonthFullyCovered(month, periods)).toBe(true)
  })

  it('returns false when single period starts after the month starts', () => {
    const periods = createPeriods([['2024-05-10', '2024-05-31']])
    expect(isMonthFullyCovered(month, periods)).toBe(false)
  })

  it('returns false when single period ends before the month ends', () => {
    const periods = createPeriods([['2024-05-01', '2024-05-20']])
    expect(isMonthFullyCovered(month, periods)).toBe(false)
  })

  it('returns false when single period has no overlap', () => {
    const periods = createPeriods([['2024-06-01', '2024-06-30']])
    expect(isMonthFullyCovered(month, periods)).toBe(false)
  })

  it('returns true when multiple periods together cover the full month', () => {
    const periods = createPeriods([
      ['2024-03-01', '2024-05-15'],
      ['2024-05-16', '2024-07-31'],
    ])
    expect(isMonthFullyCovered(month, periods)).toBe(true)
  })

  it('returns false when multiple periods have gaps and do not cover full month', () => {
    const periods = createPeriods([
      ['2024-05-01', '2024-05-10'],
      ['2024-05-20', '2024-05-31'],
    ])
    expect(isMonthFullyCovered(month, periods)).toBe(false)
  })

  it('returns true when one of multiple periods fully covers the month', () => {
    const periods = createPeriods([
      ['2024-03-01', '2024-03-31'],
      ['2024-05-01', '2024-05-31'],
      ['2024-07-01', '2024-07-31'],
    ])
    expect(isMonthFullyCovered(month, periods)).toBe(true)
  })
})

describe('isMonthPartiallyCovered', () => {
  it('returns false when no coverage periods provided', () => {
    const periods: Period[] = []
    expect(isMonthPartiallyCovered(month, periods)).toBe(false)
  })

  it('returns true when single period partially covers the month (starts before, ends inside)', () => {
    const periods = createPeriods([['2024-04-20', '2024-05-10']])
    expect(isMonthPartiallyCovered(month, periods)).toBe(true)
  })

  it('returns true when single period partially covers the month (starts inside, ends after)', () => {
    const periods = createPeriods([['2024-05-20', '2024-06-10']])
    expect(isMonthPartiallyCovered(month, periods)).toBe(true)
  })

  it('returns false when single period fully covers the month', () => {
    const periods = createPeriods([['2024-05-01', '2024-05-31']])
    expect(isMonthPartiallyCovered(month, periods)).toBe(false)
  })

  it('returns false when single period has no overlap', () => {
    const periods = createPeriods([['2024-06-01', '2024-06-30']])
    expect(isMonthPartiallyCovered(month, periods)).toBe(false)
  })

  it('returns false when single period ends before the month starts', () => {
    const periods = createPeriods([['2024-04-01', '2024-04-30']])
    expect(isMonthPartiallyCovered(month, periods)).toBe(false)
  })

  it('returns true when multiple periods together partially cover the month', () => {
    const periods = createPeriods([
      ['2024-05-01', '2024-05-10'],
      ['2024-05-20', '2024-05-25'],
    ])
    expect(isMonthPartiallyCovered(month, periods)).toBe(true)
  })

  it('returns false when multiple periods together fully cover the month', () => {
    const periods = createPeriods([
      ['2024-05-01', '2024-05-15'],
      ['2024-05-16', '2024-05-31'],
    ])
    expect(isMonthPartiallyCovered(month, periods)).toBe(false)
  })

  it('returns true when some periods have partial overlap and some have no overlap', () => {
    const periods = createPeriods([
      ['2024-03-01', '2024-03-31'],
      ['2024-05-15', '2024-05-25'],
      ['2024-07-01', '2024-07-31'],
    ])
    expect(isMonthPartiallyCovered(month, periods)).toBe(true)
  })
})

describe('isMonthMaximumPartiallyCovered', () => {
  const mockToday = PlainDate.from('2025-06-06')
  const month = PlainYearMonth.from('2025-06')

  beforeEach(() => {
    vi.spyOn(temporal, 'getToday').mockReturnValue(mockToday)
  })

  it('returns false when no periods provided', () => {
    const periods: Period[] = []
    expect(isMonthMaximumPartiallyCovered(month, periods)).toBe(false)
  })

  it('returns true when coverage is from start of month to current date', () => {
    const periods = createPeriods([['2025-06-01', '2025-06-06']])
    expect(isMonthMaximumPartiallyCovered(month, periods)).toBe(true)
  })

  it('returns true when coverage is from start of month to current date with multiple periods', () => {
    const periods = createPeriods([
      ['2025-06-01', '2025-06-03'],
      ['2025-06-04', '2025-06-06'],
    ])
    expect(isMonthMaximumPartiallyCovered(month, periods)).toBe(true)
  })

  it('returns false when coverage is from start of month but has gaps', () => {
    const periods = createPeriods([
      ['2025-06-01', '2025-06-03'],
      ['2025-06-05', '2025-06-06'],
    ])
    expect(isMonthMaximumPartiallyCovered(month, periods)).toBe(false)
  })

  it('returns false when coverage is from start of month but ends before current date', () => {
    const periods = createPeriods([['2025-06-01', '2025-06-05']])
    expect(isMonthMaximumPartiallyCovered(month, periods)).toBe(false)
  })

  it('returns false when coverage starts after month start but ends at current date', () => {
    const periods = createPeriods([['2025-06-02', '2025-06-06']])
    expect(isMonthMaximumPartiallyCovered(month, periods)).toBe(false)
  })

  it('returns false when coverage is fully covering the month', () => {
    const periods = createPeriods([['2025-06-01', '2025-06-30']])
    expect(isMonthMaximumPartiallyCovered(month, periods)).toBe(false)
  })

  it('returns false when coverage has no overlap with month', () => {
    const periods = createPeriods([['2025-05-01', '2025-05-31']])
    expect(isMonthMaximumPartiallyCovered(month, periods)).toBe(false)
  })
})

describe('getMonthlyCoverageStatus', () => {
  const mockedToday = PlainDate.from('2025-06-06')
  const month = PlainYearMonth.from('2025-06')

  beforeEach(() => {
    vi.spyOn(temporal, 'getToday').mockReturnValue(mockedToday)
  })

  it('returns FULL_COVERAGE when month is fully covered', () => {
    const periods = createPeriods([['2025-06-01', '2025-06-30']])
    expect(getMonthlyCoverageStatus(month, periods)).toBe(CoverageStatus.FULL_COVERAGE)
  })

  it('returns NO_COVERAGE when no periods provided', () => {
    const periods: Period[] = []
    expect(getMonthlyCoverageStatus(month, periods)).toBe(CoverageStatus.NO_COVERAGE)
  })

  it('returns MAXIMUM_PARTIAL_COVERAGE when coverage is from start of month to current date', () => {
    const periods = createPeriods([['2025-06-01', '2025-06-06']])
    expect(getMonthlyCoverageStatus(month, periods)).toBe(CoverageStatus.MAXIMUM_PARTIAL_COVERAGE)
  })

  it('returns PARTIAL_COVERAGE when month is partially covered', () => {
    const periods = createPeriods([['2025-06-10', '2025-06-20']])
    expect(getMonthlyCoverageStatus(month, periods)).toBe(CoverageStatus.PARTIAL_COVERAGE)
  })
})

describe('getMonthCoverage', () => {
  const month = PlainYearMonth.from('2025-04')

  it('returns empty array when no periods provided', () => {
    const periods: Period[] = []
    expect(getMonthCoverage(month, periods)).toEqual([])
  })

  it('returns empty array when periods do not overlap with month', () => {
    const periods = createPeriods([
      ['2025-01-01', '2025-03-31'],
      ['2025-05-01', '2025-06-30'],
    ])
    expect(getMonthCoverage(month, periods)).toEqual([])
  })

  it('clips period that starts before month and ends within month', () => {
    const periods = createPeriods([['2025-01-15', '2025-04-07']])
    const expected = createInMonthPeriods([['2025-04-01', '2025-04-07']])
    expect(getMonthCoverage(month, periods)).toEqual(expected)
  })

  it('clips period that starts within month and ends after month', () => {
    const periods = createPeriods([['2025-04-28', '2025-05-20']])
    const expected = createInMonthPeriods([['2025-04-28', '2025-04-30']])
    expect(getMonthCoverage(month, periods)).toEqual(expected)
  })

  it('returns period unchanged when it is fully within month', () => {
    const periods = createPeriods([['2025-04-10', '2025-04-15']])
    const expected = createInMonthPeriods([['2025-04-10', '2025-04-15']])
    expect(getMonthCoverage(month, periods)).toEqual(expected)
  })

  it('clips period that spans entire month and beyond', () => {
    const periods = createPeriods([['2025-03-15', '2025-05-15']])
    const expected = createInMonthPeriods([['2025-04-01', '2025-04-30']])
    expect(getMonthCoverage(month, periods)).toEqual(expected)
  })

  it('handles multiple periods with different overlaps', () => {
    const periods = createPeriods([
      ['2025-01-15', '2025-04-07'],
      ['2025-04-10', '2025-04-15'],
      ['2025-04-28', '2025-05-20'],
    ])
    const expected = createInMonthPeriods([
      ['2025-04-01', '2025-04-07'],
      ['2025-04-10', '2025-04-15'],
      ['2025-04-28', '2025-04-30'],
    ])
    expect(getMonthCoverage(month, periods)).toEqual(expected)
  })

  it('handles mix of overlapping and non-overlapping periods', () => {
    const periods = createPeriods([
      ['2025-01-01', '2025-02-28'],
      ['2025-04-05', '2025-04-10'],
      ['2025-06-01', '2025-07-31'],
    ])
    const expected = createInMonthPeriods([['2025-04-05', '2025-04-10']])
    expect(getMonthCoverage(month, periods)).toEqual(expected)
  })

  it('works correctly with February month (28 days)', () => {
    const februaryMonth = PlainYearMonth.from('2025-02')
    const periods = createPeriods([
      ['2025-01-15', '2025-02-15'],
      ['2025-02-20', '2025-03-10'],
    ])
    const expected = createInMonthPeriods(
      [
        ['2025-02-01', '2025-02-15'],
        ['2025-02-20', '2025-02-28'],
      ],
      februaryMonth,
    )
    expect(getMonthCoverage(februaryMonth, periods)).toEqual(expected)
  })

  it('works correctly with leap year February (29 days)', () => {
    const leapFebruaryMonth = PlainYearMonth.from('2024-02')
    const periods = createPeriods([['2024-02-20', '2024-03-10']])
    const expected = createInMonthPeriods([['2024-02-20', '2024-02-29']], leapFebruaryMonth)
    expect(getMonthCoverage(leapFebruaryMonth, periods)).toEqual(expected)
  })
})

describe('getMonthCoveragePercentage', () => {
  it('returns 0 when no periods provided', () => {
    const periods: InMonthPeriod[] = []
    expect(getMonthCoveragePercentage(periods)).toBe(0)
  })

  it('returns 1 when month is fully covered by single period', () => {
    const periods = createInMonthPeriods([['2024-05-01', '2024-05-31']])
    expect(getMonthCoveragePercentage(periods)).toBe(1)
  })

  it('returns 1 when month is fully covered by multiple periods', () => {
    const periods = createInMonthPeriods([
      ['2024-05-01', '2024-05-15'],
      ['2024-05-16', '2024-05-31'],
    ])
    expect(getMonthCoveragePercentage(periods)).toBe(1)
  })

  it('returns 0.48 when exactly half of the month is covered', () => {
    const periods = createInMonthPeriods([['2024-05-01', '2024-05-15']])
    expect(getMonthCoveragePercentage(periods)).toBeCloseTo(0.48)
  })

  it('returns 0.32 when one third of the month is covered', () => {
    const periods = createInMonthPeriods([['2024-05-01', '2024-05-10']])
    expect(getMonthCoveragePercentage(periods)).toBeCloseTo(0.32)
  })

  it('returns 0.68 when two thirds of the month is covered by multiple periods', () => {
    const periods = createInMonthPeriods([
      ['2024-05-01', '2024-05-10'],
      ['2024-05-21', '2024-05-31'],
    ])
    expect(getMonthCoveragePercentage(periods)).toBe(0.68)
  })

  it('works correctly with February month (28 days)', () => {
    const februaryMonth = PlainYearMonth.from('2024-02')
    const periods = createInMonthPeriods([['2024-02-01', '2024-02-14']], februaryMonth)
    expect(getMonthCoveragePercentage(periods)).toBe(0.48)
  })

  it('works correctly with leap year February (29 days)', () => {
    const leapFebruaryMonth = PlainYearMonth.from('2024-02')
    const periods = createInMonthPeriods([['2024-02-01', '2024-02-15']], leapFebruaryMonth)
    expect(getMonthCoveragePercentage(periods)).toBe(0.52)
  })
})

describe('getMonthDailyCoverage', () => {
  function expectDailyCoverage(result: DayCoverage[], expectedCoveredDays: string[]) {
    expect(result).toHaveLength(month.daysInMonth)
    const toMonthDay = (day: string) => `${month.toString()}-${day}`

    for (let i = 0; i < month.daysInMonth; i++) {
      const date = month.toPlainDate({ day: i + 1 })
      const day = result[i]
      const shouldBeCovered = expectedCoveredDays.map(toMonthDay).includes(date.toString())

      expect(day.date.toString()).toBe(date.toString())
      expect(day.isCovered).toBe(shouldBeCovered)
    }
  }

  it('returns array of all days with no coverage when no periods provided', () => {
    expectDailyCoverage(getMonthDailyCoverage(month, []), [])
  })

  it('returns correct coverage for single period', () => {
    const periods = createInMonthPeriods([['2024-05-05', '2024-05-10']], month)
    const result = getMonthDailyCoverage(month, periods)
    const expectedCoveredDays = ['05', '06', '07', '08', '09', '10']

    expectDailyCoverage(result, expectedCoveredDays)
  })

  it('returns correct coverage for multiple  periods', () => {
    const periods = createInMonthPeriods(
      [
        ['2024-05-01', '2024-05-03'],
        ['2024-05-07', '2024-05-09'],
        ['2024-05-12', '2024-05-13'],
      ],
      month,
    )
    const result = getMonthDailyCoverage(month, periods)
    const expectedCoveredDays = ['01', '02', '03', '07', '08', '09', '12', '13']

    expectDailyCoverage(result, expectedCoveredDays)
  })
})
