import { PlainDate, PlainYearMonth } from '@/libs/temporal'
import type { InMonthPeriod, Period } from './types'

export function mergeConsecutiveCoveragePeriods(periods: Period[]): Period[] {
  if (periods.length <= 1) {
    return [...periods]
  }

  return sortPeriodsByStartDate(periods).reduce(mergePeriodWithAccumulator, [] as Period[])
}

function sortPeriodsByStartDate(periods: Period[]): Period[] {
  return [...periods].sort((a, b) => PlainDate.compare(a.dateFrom, b.dateFrom))
}

function mergePeriodWithAccumulator(merged: Period[], current: Period): Period[] {
  if (merged.length === 0) {
    return [current]
  }

  const lastPeriod = merged[merged.length - 1]

  if (arePeriodsConsecutiveOrOverlapping(lastPeriod, current)) {
    const mergedPeriod = mergeTwoPeriods(lastPeriod, current)
    return [...merged.slice(0, -1), mergedPeriod]
  }

  return [...merged, current]
}

function arePeriodsConsecutiveOrOverlapping(first: Period, second: Period): boolean {
  const nextDayAfterFirst = first.dateTo.add({ days: 1 })
  return PlainDate.compare(second.dateFrom, nextDayAfterFirst) <= 0
}

function mergeTwoPeriods(first: Period, second: Period): Period {
  return {
    dateFrom: first.dateFrom,
    dateTo: PlainDate.compare(first.dateTo, second.dateTo) > 0 ? first.dateTo : second.dateTo,
  }
}

export function createPeriods(periods: string[][]): Period[] {
  return periods.map(([from, to]) => ({
    dateFrom: PlainDate.from(from),
    dateTo: PlainDate.from(to),
  }))
}

export function createInMonthPeriods(
  periods: string[][],
  defaultMonth: PlainYearMonth,
): InMonthPeriod[] {
  return createPeriods(periods).map((period) => ({
    ...period,
    month: defaultMonth,
  }))
}
