import { describe, it, expect } from 'vitest'
import { mergeConsecutiveCoveragePeriods } from './period'
import type { Period } from './types'
import { createPeriods } from './period'

describe('mergeConsecutiveCoveragePeriods', () => {
  it('returns empty array when given empty array', () => {
    const periods: Period[] = []
    expect(mergeConsecutiveCoveragePeriods(periods)).toEqual([])
  })

  it('returns single period unchanged when given single period', () => {
    const periods = createPeriods([['2024-01-01', '2024-01-31']])
    expect(mergeConsecutiveCoveragePeriods(periods)).toEqual(periods)
  })

  it('merges two consecutive periods', () => {
    const periods = createPeriods([
      ['2024-01-01', '2024-02-12'],
      ['2024-02-13', '2024-04-15'],
    ])
    const expected = createPeriods([['2024-01-01', '2024-04-15']])
    expect(mergeConsecutiveCoveragePeriods(periods)).toEqual(expected)
  })

  it('does not merge periods with gaps between them', () => {
    const periods = createPeriods([
      ['2024-01-01', '2024-01-31'],
      ['2024-03-01', '2024-03-31'],
    ])
    expect(mergeConsecutiveCoveragePeriods(periods)).toEqual(periods)
  })

  it('merges multiple consecutive periods', () => {
    const periods = createPeriods([
      ['2024-01-01', '2024-01-31'],
      ['2024-02-01', '2024-02-29'],
      ['2024-03-01', '2024-03-31'],
    ])
    const expected = createPeriods([['2024-01-01', '2024-03-31']])
    expect(mergeConsecutiveCoveragePeriods(periods)).toEqual(expected)
  })

  it('handles mixed consecutive and non-consecutive periods', () => {
    const periods = createPeriods([
      ['2024-01-01', '2024-01-31'],
      ['2024-02-01', '2024-02-29'],
      ['2024-04-01', '2024-04-30'],
      ['2024-05-01', '2024-05-31'],
    ])
    const expected = createPeriods([
      ['2024-01-01', '2024-02-29'],
      ['2024-04-01', '2024-05-31'],
    ])
    expect(mergeConsecutiveCoveragePeriods(periods)).toEqual(expected)
  })

  it('sorts periods by start date before merging', () => {
    const periods = createPeriods([
      ['2024-03-01', '2024-03-31'],
      ['2024-01-01', '2024-01-31'],
      ['2024-02-01', '2024-02-29'],
    ])
    const expected = createPeriods([['2024-01-01', '2024-03-31']])
    expect(mergeConsecutiveCoveragePeriods(periods)).toEqual(expected)
  })

  it('handles overlapping periods by merging them', () => {
    const periods = createPeriods([
      ['2024-01-01', '2024-02-15'],
      ['2024-02-10', '2024-03-31'],
    ])
    const expected = createPeriods([['2024-01-01', '2024-03-31']])
    expect(mergeConsecutiveCoveragePeriods(periods)).toEqual(expected)
  })
})
