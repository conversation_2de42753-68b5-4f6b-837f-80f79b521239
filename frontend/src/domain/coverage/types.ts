import type { PlainDate, PlainYearMonth } from '@/libs/temporal'
import type { Source } from '@/domain/types'
import type { GridStatus } from '../grids/types'

export interface Period {
  dateFrom: PlainDate
  dateTo: PlainDate
}

export type InMonthPeriod = Period & {
  month: PlainYearMonth
}

export interface OrganizationReportCoverage {
  organizationId: string
  organizationName: string
  coverage: Record<Source, Period[]>
}

export interface OrganizationMonthlyReportCoverage {
  organizationId: string
  organizationName: string
  coverage: InMonthPeriod[]
  month: PlainYearMonth
  status: CoverageStatus
}

export enum CoverageStatus {
  FULL_COVERAGE = 'FULL_COVERAGE',
  PARTIAL_COVERAGE = 'PARTIAL_COVERAGE',
  MAXIMUM_PARTIAL_COVERAGE = 'MAXIMUM_PARTIAL_COVERAGE',
  NO_COVERAGE = 'NO_COVERAGE',
}

export type MonthlyReportsDataGridStatus = GridStatus<OrganizationMonthlyReportCoverage>
