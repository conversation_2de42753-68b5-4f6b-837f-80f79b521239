import { getNow, type PlainDateTime } from '@/libs/temporal'

export function formatDateTimeISO(date: PlainDateTime | null): string | null {
  if (!date) return null
  return date.toString().replace('T', ' ')
}

export function formatTimeSince(date: PlainDateTime): string {
  const now = getNow()
  const duration = now.since(date)

  const totalMinutes = duration.total('minutes')
  const totalHours = duration.total('hours')
  const totalDays = duration.total('days')

  if (totalDays >= 1) {
    const days = Math.floor(totalDays)
    const hours = Math.floor(totalHours % 24)

    if (hours > 0) {
      return `${days}d ${hours}h ago`
    } else {
      return `${days}d ago`
    }
  } else if (totalHours >= 1) {
    const hours = Math.floor(totalHours)
    const minutes = Math.floor(totalMinutes % 60)

    if (minutes > 0) {
      return `${hours}h ${minutes}m ago`
    } else {
      return `${hours}h ago`
    }
  } else {
    const minutes = Math.floor(totalMinutes)
    return `${minutes}m ago`
  }
}

export function formatDateTimeISOWithSince(date: PlainDateTime | null): string | null {
  if (!date) return null

  const formatted = formatDateTimeISO(date)
  const sinceText = formatTimeSince(date)

  return `${formatted} UTC (${sinceText})`
}
