import { describe, it, expect } from 'vitest'
import { formatDateTimeISO, formatDateTimeISOWithSince } from './dateTime'
import { PlainDateTime } from '@/libs/temporal'

describe('formatDateTimeISO', () => {
  it('returns null for null input', () => {
    expect(formatDateTimeISO(null)).toBe(null)
  })

  it('formats PlainDateTime to ISO string with space instead of T', () => {
    const date = new PlainDateTime(2024, 1, 15, 10, 25, 30)
    
    const result = formatDateTimeISO(date)
    
    expect(result).toBe('2024-01-15 10:25:30')
  })
})

describe('formatDateTimeISOWithSince', () => {
  it('returns null for null input', () => {
    expect(formatDateTimeISOWithSince(null)).toBe(null)
  })

  it('formats timestamp with UTC suffix and relative time', () => {
    const someDate = new PlainDateTime(2024, 1, 15, 10, 25, 0)
    
    const result = formatDateTimeISOWithSince(someDate)
    
    expect(result).toMatch(/^2024-01-15 10:25:00 UTC \(\d+[dhm].*ago\)$/)
    expect(result).toContain('UTC')
    expect(result).toContain('ago')
  })

  it('includes proper time format', () => {
    const someDate = new PlainDateTime(2024, 1, 15, 10, 25, 30)
    
    const result = formatDateTimeISOWithSince(someDate)
    
    expect(result).toContain('2024-01-15 10:25:30 UTC')
  })
})
