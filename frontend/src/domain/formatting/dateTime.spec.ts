import { describe, it, expect } from 'vitest'
import { formatDateTimeISO, formatDateTimeISOWithSince, formatTimeSince } from './dateTime'
import { PlainDateTime } from '@/libs/temporal'

describe('formatDateTimeISO', () => {
  it('returns null for null input', () => {
    expect(formatDateTimeISO(null)).toBe(null)
  })

  it('formats PlainDateTime to ISO string with space instead of T', () => {
    const date = new PlainDateTime(2024, 1, 15, 10, 25, 30)

    const result = formatDateTimeISO(date)

    expect(result).toBe('2024-01-15 10:25:30')
  })
})

describe('formatTimeSince', () => {
  it('formats minutes only for times under an hour', () => {
    expect(formatTimeSince(5, 0.083, 0)).toBe('5m ago')
    expect(formatTimeSince(30, 0.5, 0)).toBe('30m ago')
    expect(formatTimeSince(0, 0, 0)).toBe('0m ago')
  })

  it('formats hours and minutes for times under a day', () => {
    expect(formatTimeSince(150, 2.5, 0)).toBe('2h 30m ago')
    expect(formatTimeSince(180, 3, 0)).toBe('3h ago')
    expect(formatTimeSince(60, 1, 0)).toBe('1h ago')
  })

  it('formats days and hours for times over a day', () => {
    expect(formatTimeSince(1770, 29.5, 1.23)).toBe('1d 5h ago')
    expect(formatTimeSince(2880, 48, 2)).toBe('2d ago')
    expect(formatTimeSince(4320, 72, 3)).toBe('3d ago')
  })
})

describe('formatDateTimeISOWithSince', () => {
  it('returns null for null input', () => {
    expect(formatDateTimeISOWithSince(null)).toBe(null)
  })

  it('formats timestamp with UTC suffix and relative time', () => {
    const someDate = new PlainDateTime(2024, 1, 15, 10, 25, 0)

    const result = formatDateTimeISOWithSince(someDate)

    expect(result).toMatch(/^2024-01-15 10:25:00 UTC \(\d+[dhm].*ago\)$/)
    expect(result).toContain('UTC')
    expect(result).toContain('ago')
  })
})
