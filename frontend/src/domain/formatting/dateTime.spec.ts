import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { formatDateTimeISO, formatDateTimeISOWithSince, formatTimeSince } from './dateTime'
import { PlainDateTime } from '@/libs/temporal'
import * as temporal from '@/libs/temporal'

describe('formatDateTimeISO', () => {
  it('returns null for null input', () => {
    expect(formatDateTimeISO(null)).toBe(null)
  })

  it('formats PlainDateTime to ISO string with space instead of T', () => {
    const date = new PlainDateTime(2024, 1, 15, 10, 25, 30)

    const result = formatDateTimeISO(date)

    expect(result).toBe('2024-01-15 10:25:30')
  })
})

describe('formatTimeSince', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('formats minutes only for times under an hour', () => {
    const now = new PlainDateTime(2024, 1, 15, 10, 30, 0)
    const fiveMinutesAgo = new PlainDateTime(2024, 1, 15, 10, 25, 0)

    vi.spyOn(temporal, 'getNow').mockReturnValue(now)

    const result = formatTimeSince(fiveMinutesAgo)

    expect(result).toBe('5m ago')
  })

  it('formats hours and minutes for times under a day', () => {
    const now = new PlainDateTime(2024, 1, 15, 10, 30, 0)
    const twoHoursThirtyMinutesAgo = new PlainDateTime(2024, 1, 15, 8, 0, 0)

    vi.spyOn(temporal, 'getNow').mockReturnValue(now)

    const result = formatTimeSince(twoHoursThirtyMinutesAgo)

    expect(result).toBe('2h 30m ago')
  })

  it('formats only hours when minutes are zero', () => {
    const now = new PlainDateTime(2024, 1, 15, 10, 30, 0)
    const twoHoursAgo = new PlainDateTime(2024, 1, 15, 8, 30, 0)

    vi.spyOn(temporal, 'getNow').mockReturnValue(now)

    const result = formatTimeSince(twoHoursAgo)

    expect(result).toBe('2h ago')
  })

  it('formats days and hours for times over a day', () => {
    const now = new PlainDateTime(2024, 1, 15, 10, 30, 0)
    const oneDayFiveHoursAgo = new PlainDateTime(2024, 1, 14, 5, 30, 0)

    vi.spyOn(temporal, 'getNow').mockReturnValue(now)

    const result = formatTimeSince(oneDayFiveHoursAgo)

    expect(result).toBe('1d 5h ago')
  })

  it('formats only days when hours are zero', () => {
    const now = new PlainDateTime(2024, 1, 15, 10, 30, 0)
    const twoDaysAgo = new PlainDateTime(2024, 1, 13, 10, 30, 0)

    vi.spyOn(temporal, 'getNow').mockReturnValue(now)

    const result = formatTimeSince(twoDaysAgo)

    expect(result).toBe('2d ago')
  })

  it('handles zero minutes correctly', () => {
    const now = new PlainDateTime(2024, 1, 15, 10, 30, 0)
    const justNow = new PlainDateTime(2024, 1, 15, 10, 30, 0)

    vi.spyOn(temporal, 'getNow').mockReturnValue(now)

    const result = formatTimeSince(justNow)

    expect(result).toBe('0m ago')
  })
})

describe('formatDateTimeISOWithSince', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('returns null for null input', () => {
    expect(formatDateTimeISOWithSince(null)).toBe(null)
  })

  it('formats timestamp with UTC suffix and relative time', () => {
    const now = new PlainDateTime(2024, 1, 15, 10, 30, 0)
    const fiveMinutesAgo = new PlainDateTime(2024, 1, 15, 10, 25, 0)

    vi.spyOn(temporal, 'getNow').mockReturnValue(now)

    const result = formatDateTimeISOWithSince(fiveMinutesAgo)

    expect(result).toBe('2024-01-15 10:25:00 UTC (5m ago)')
  })
})
