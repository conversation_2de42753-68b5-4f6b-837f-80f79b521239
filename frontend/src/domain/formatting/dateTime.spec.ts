import { describe, it, expect, vi, beforeEach } from 'vitest'
import { formatDateTimeISO, formatDateTimeISOWithSince, formatTimeSince } from './dateTime'
import { PlainDateTime } from '@/libs/temporal'
import * as temporal from '@/libs/temporal'

describe('dateTime formatting', () => {
  const now = new PlainDateTime(2024, 1, 15, 10, 30, 0)

  beforeEach(() => {
    vi.clearAllMocks()
    vi.spyOn(temporal, 'getNow').mockReturnValue(now)
  })

  describe.each([
    [new PlainDateTime(2024, 1, 15, 10, 25, 0), '5m ago', 'minutes only for times under an hour'],
    [
      new PlainDateTime(2024, 1, 15, 8, 0, 0),
      '2h 30m ago',
      'hours and minutes for times under a day',
    ],
    [new PlainDateTime(2024, 1, 15, 8, 30, 0), '2h ago', 'only hours when minutes are zero'],
    [new PlainDateTime(2024, 1, 14, 5, 30, 0), '1d 5h ago', 'days and hours for times over a day'],
    [new PlainDateTime(2024, 1, 13, 10, 30, 0), '2d ago', 'only days when hours are zero'],
    [new PlainDateTime(2024, 1, 15, 10, 30, 0), '0m ago', 'zero minutes correctly'],
  ])('formatTimeSince(%s)', (date, expected, description) => {
    it(`formats ${description}`, () => {
      expect(formatTimeSince(date)).toBe(expected)
    })
  })

  describe('formatDateTimeISOWithSince', () => {
    it('formatDateTimeISOWithSince returns null for null input', () => {
      expect(formatDateTimeISOWithSince(null)).toBe(null)
    })

    it('formatDateTimeISOWithSince formats timestamp with UTC suffix and relative time', () => {
      expect(formatDateTimeISOWithSince(new PlainDateTime(2024, 1, 15, 10, 25, 0))).toBe(
        '2024-01-15 10:25:00 UTC (5m ago)',
      )
    })
  })

  describe('formatDateTimeISO', () => {
    it('formatDateTimeISO returns null for null input', () => {
      expect(formatDateTimeISO(null)).toBe(null)
    })

    it('formatDateTimeISO formats PlainDateTime to ISO string with space instead of T', () => {
      expect(formatDateTimeISO(new PlainDateTime(2024, 1, 15, 10, 25, 30))).toBe(
        '2024-01-15 10:25:30',
      )
    })
  })
})
