import { describe, it, expect, vi, beforeEach } from 'vitest'
import { formatDateTimeISO, formatDateTimeISOWithSince, formatTimeSince } from './dateTime'
import { PlainDateTime } from '@/libs/temporal'
import * as temporal from '@/libs/temporal'

describe('dateTime formatting', () => {
  const now = new PlainDateTime(2024, 1, 15, 10, 30, 0)

  beforeEach(() => {
    vi.clearAllMocks()
    vi.spyOn(temporal, 'getNow').mockReturnValue(now)
  })

  describe('formatTimeSince', () => {
    it('formatTimeSince formats minutes only for times under an hour', () => {
      expect(formatTimeSince(new PlainDateTime(2024, 1, 15, 10, 25, 0))).toBe('5m ago')
    })

    it('formatTimeSince formats hours and minutes for times under a day', () => {
      expect(formatTimeSince(new PlainDateTime(2024, 1, 15, 8, 0, 0))).toBe('2h 30m ago')
    })

    it('formatTimeSince formats only hours when minutes are zero', () => {
      expect(formatTimeSince(new PlainDateTime(2024, 1, 15, 8, 30, 0))).toBe('2h ago')
    })

    it('formatTimeSince formats days and hours for times over a day', () => {
      expect(formatTimeSince(new PlainDateTime(2024, 1, 14, 5, 30, 0))).toBe('1d 5h ago')
    })

    it('formatTimeSince formats only days when hours are zero', () => {
      expect(formatTimeSince(new PlainDateTime(2024, 1, 13, 10, 30, 0))).toBe('2d ago')
    })

    it('formatTimeSince handles zero minutes correctly', () => {
      expect(formatTimeSince(new PlainDateTime(2024, 1, 15, 10, 30, 0))).toBe('0m ago')
    })
  })

  describe('formatDateTimeISOWithSince', () => {
    it('formatDateTimeISOWithSince returns null for null input', () => {
      expect(formatDateTimeISOWithSince(null)).toBe(null)
    })

    it('formatDateTimeISOWithSince formats timestamp with UTC suffix and relative time', () => {
      expect(formatDateTimeISOWithSince(new PlainDateTime(2024, 1, 15, 10, 25, 0))).toBe(
        '2024-01-15 10:25:00 UTC (5m ago)',
      )
    })
  })
})

it('formatDateTimeISO returns null for null input', () => {
  expect(formatDateTimeISO(null)).toBe(null)
})

it('formatDateTimeISO formats PlainDateTime to ISO string with space instead of T', () => {
  expect(formatDateTimeISO(new PlainDateTime(2024, 1, 15, 10, 25, 30))).toBe('2024-01-15 10:25:30')
})
