import { describe, it, expect, beforeEach, vi } from 'vitest'
import { setActive<PERSON><PERSON>, createP<PERSON> } from 'pinia'
import { useNotificationStore } from './notification'

describe('Notification Store', () => {
  beforeEach(() => {
    setActivePinia(createPinia())
    vi.useFakeTimers()
  })

  it('should add and remove notification', () => {
    const store = useNotificationStore()
    store.success('Test message')

    expect(store.notifications).toHaveLength(1)
    expect(store.notifications[0].message).toBe('Test message')
    expect(store.notifications[0].type).toBe('success')

    store.removeNotification(store.notifications[0].id)
    expect(store.notifications).toHaveLength(0)
  })

  it('should auto-remove notification after timeout', () => {
    const store = useNotificationStore()
    store.success('Test message', undefined, 1000)

    expect(store.notifications).toHaveLength(1)
    vi.advanceTimersByTime(1000)
    expect(store.notifications).toHaveLength(0)
  })

  it('should not auto-remove notification when timeout is 0', () => {
    const store = useNotificationStore()
    store.success('Test message', undefined, 0)

    expect(store.notifications).toHaveLength(1)
    vi.advanceTimersByTime(5000)
    expect(store.notifications).toHaveLength(1)
  })

  it('should not add duplicate singleton notifications', () => {
    const store = useNotificationStore()
    const message = 'Test message'
    const title = 'Test title'
    const singletonKey = 'test-key'

    store.warning(message, title, 0, singletonKey)
    expect(store.notifications).toHaveLength(1)

    store.warning(message, title, 0, singletonKey)
    expect(store.notifications).toHaveLength(1)
  })

  it('should allow different singleton notifications with different keys', () => {
    const store = useNotificationStore()
    const message = 'Test message'
    const title = 'Test title'

    store.warning(message, title, 0, 'key1')
    store.warning(message, title, 0, 'key2')

    expect(store.notifications).toHaveLength(2)
  })

  it('should remove singleton from active singletons when notification is removed', () => {
    const store = useNotificationStore()
    const singletonKey = 'test-key'

    store.warning('Test message', 'Test title', 0, singletonKey)
    expect(store.notifications).toHaveLength(1)

    store.removeNotification(store.notifications[0].id)
    expect(store.notifications).toHaveLength(0)

    store.warning('Test message', 'Test title', 0, singletonKey)
    expect(store.notifications).toHaveLength(1)
  })

  it('should generate singleton key from type, title and message if not provided', () => {
    const store = useNotificationStore()
    const message = 'Test message'
    const title = 'Test title'

    store.warning(message, title, 0)
    expect(store.notifications).toHaveLength(1)

    store.warning(message, title, 0)
    expect(store.notifications).toHaveLength(2)
  })

  it('should support all notification types', () => {
    const store = useNotificationStore()
    const message = 'Test message'

    store.success(message)
    store.info(message)
    store.warning(message)
    store.danger(message)

    expect(store.notifications).toHaveLength(4)
    expect(store.notifications.map((n) => n.type)).toEqual(['success', 'info', 'warning', 'danger'])
  })
})
