import { defineStore } from 'pinia'
import { ref } from 'vue'

export type NotificationType = 'success' | 'info' | 'warning' | 'danger'

export interface Notification {
  id: string
  type: NotificationType
  title?: string
  message: string
  timeout?: number
  singletonKey?: string
}

export type NotificationStore = ReturnType<typeof useNotificationStore>

export const useNotificationStore = defineStore('notification', () => {
  const notifications = ref<Notification[]>([])
  const activeSingletons = ref<Set<string>>(new Set())

  const addNotification = (notification: Omit<Notification, 'id'>) => {
    if (notification.singletonKey) {
      if (activeSingletons.value.has(notification.singletonKey)) {
        return
      }
      activeSingletons.value.add(notification.singletonKey)
    }

    const id = crypto.randomUUID()
    const newNotification = { ...notification, id }
    notifications.value.push(newNotification)

    if (notification.timeout !== 0) {
      setTimeout(() => {
        removeNotification(id)
      }, notification.timeout || 5000)
    }
  }

  const removeNotification = (id: string) => {
    const index = notifications.value.findIndex((n) => n.id === id)
    if (index !== -1) {
      const notification = notifications.value[index]
      if (notification.singletonKey) {
        activeSingletons.value.delete(notification.singletonKey)
      }
      notifications.value.splice(index, 1)
    }
  }

  const removeByKey = (singletonKey: string) => {
    const notification = notifications.value.find((n) => n.singletonKey === singletonKey)
    if (notification) {
      removeNotification(notification.id)
    }
  }

  const success = (message: string, title?: string, timeout?: number, singletonKey?: string) => {
    addNotification({ type: 'success', message, title, timeout, singletonKey })
  }

  const info = (message: string, title?: string, timeout?: number, singletonKey?: string) => {
    addNotification({ type: 'info', message, title, timeout, singletonKey })
  }

  const warning = (message: string, title?: string, timeout?: number, singletonKey?: string) => {
    addNotification({ type: 'warning', message, title, timeout, singletonKey })
  }

  const danger = (message: string, title?: string, timeout?: number, singletonKey?: string) => {
    addNotification({ type: 'danger', message, title, timeout, singletonKey })
  }

  return {
    notifications,
    addNotification,
    removeNotification,
    removeByKey,
    success,
    info,
    warning,
    danger,
  }
})
