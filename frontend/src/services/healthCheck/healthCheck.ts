import { useNotificationStore } from '@/stores/notification'
import { getHealth } from '@/api/health'
import { ConfigurationError, ConnectionError, DnsError, type HealthError } from './errors'
import type { AxiosResponse } from 'axios'
import type { HealthResponse } from '@/api/health'

export class HealthCheckService {
  constructor(
    private readonly errors: HealthError[] = [DnsError, ConnectionError, ConfigurationError],
  ) {}
  private checkInterval: number | null = null
  private readonly CHECK_INTERVAL_MS = 10_000

  private async checkService(): Promise<{
    response?: AxiosResponse<HealthResponse>
    error?: unknown
  }> {
    try {
      const response = await getHealth()
      return { response }
    } catch (error: unknown) {
      return { error }
    }
  }

  private async checkAllServices() {
    const { response, error } = await this.checkService()
    this.handleHealthCheckResult(response, error)
  }
  private handleHealthCheckResult(response?: AxiosResponse<HealthResponse>, error?: unknown): void {
    const notifications = useNotificationStore()
    this.errors.forEach(({ title, timeout, key, matches, getMessage }) => {
      if (matches(response, error)) {
        notifications.warning(getMessage(response, error), title, timeout, key)
      } else {
        notifications.removeByKey(key)
      }
    })
  }

  startMonitoring() {
    if (this.checkInterval === null) {
      this.checkAllServices()
      this.checkInterval = window.setInterval(() => this.checkAllServices(), this.CHECK_INTERVAL_MS)
    }
  }

  stopMonitoring() {
    if (this.checkInterval !== null) {
      window.clearInterval(this.checkInterval)
      this.checkInterval = null
    }
  }
}
