import { AxiosError, type AxiosResponse } from 'axios'

import type { HealthResponse } from '@/api/health'

export type HealthError = {
  readonly key: string
  readonly title: string
  readonly timeout: number
  matches(response?: AxiosResponse<HealthResponse>, error?: unknown): boolean
  getMessage(response?: AxiosResponse<HealthResponse>, error?: unknown): string
}

export const ConnectionError: HealthError = {
  key: 'backend-connection-error',
  title: 'Connection Problem',
  timeout: 0,

  matches(response: AxiosResponse<HealthResponse>, error: unknown): boolean {
    if (error instanceof AxiosError) {
      return error.code === 'ERR_NETWORK' || error.message === 'Network Error'
    }
    return false
  },

  getMessage(): string {
    const url = window.location.hostname
    if (url.includes('localhost')) {
      return `Cannot connect to backend. Please check if your server is running.`
    }
    return `Cannot connect to ${url}/health. Check your internet connection and VPN.`
  },
}

export const DnsError: HealthError = {
  key: 'dns-error',
  title: 'DNS Problem',
  timeout: 0,

  matches(response?: AxiosResponse<HealthResponse>): boolean {
    if (!response) return false

    return Object.values(response.data.services.details).some(
      ({ is_healthy, error }) =>
        !is_healthy && error?.includes('nodename nor servname provided, or not known'),
    )
  },

  getMessage(): string {
    return 'Backend server cannot resolve domain names. Please check your VPN connection.'
  },
}

const missingUrlMessage =
  "Request error: Request URL is missing an 'http://' or 'https://' protocol."

export const ConfigurationError: HealthError = {
  key: 'service-config-error',
  title: 'Configuration Problem',
  timeout: 0,

  matches(response: AxiosResponse<HealthResponse>): boolean {
    if (!response || !response.data.services.details) return false

    return Object.values(response.data.services.details).some(
      (service) => !service.is_healthy && service.error === missingUrlMessage,
    )
  },

  getMessage(response: AxiosResponse<HealthResponse>): string {
    if (!response.data.services.details) return ''
    const servicesWithErrors = Object.entries(response.data.services.details)
      .filter(([, service]) => !service.is_healthy && service.error === missingUrlMessage)
      .map(([name]) => name)
      .join(', ')
    return `Service configuration error in: ${servicesWithErrors}. Please check your environment variables.`
  },
}
