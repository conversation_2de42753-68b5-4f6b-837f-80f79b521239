/* eslint-disable @typescript-eslint/no-explicit-any */
import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { HealthCheckService } from './healthCheck'
import { getHealth } from '@/api/health'
import { useNotificationStore } from '@/stores/notification'
import type { AxiosResponse } from 'axios'
import type { HealthResponse } from '@/api/health'
import { setActivePinia, createPinia } from 'pinia'
import type { NotificationStore } from '@/stores/notification'

vi.mock('@/api/health')
vi.mock('@/stores/notification')

describe('HealthCheckService', () => {
  beforeEach(() => {
    setActivePinia(createPinia())
    vi.clearAllMocks()
    vi.useFakeTimers()
  })

  afterEach(() => {
    vi.useRealTimers()
  })

  const interval = 10_000

  const createMockNotificationStore = () =>
    ({ warning: vi.fn(), removeByKey: vi.fn() }) as any as NotificationStore

  const createMockHealthResponse = (status: 'ok' | 'error') =>
    ({
      data: { services: { status } } as HealthResponse,
    }) as any as AxiosResponse<HealthResponse>

  describe('monitoring & interval', () => {
    let healthCheckService: HealthCheckService

    beforeEach(() => {
      vi.mocked(useNotificationStore).mockReturnValue(createMockNotificationStore())
      vi.mocked(getHealth).mockResolvedValue(createMockHealthResponse('ok'))
      healthCheckService = new HealthCheckService([])
    })

    it('should start monitoring when startMonitoring is called', () => {
      healthCheckService.startMonitoring()
      expect(getHealth).toHaveBeenCalledTimes(1)
    })

    it('should not start multiple monitoring intervals', () => {
      healthCheckService.startMonitoring()
      healthCheckService.startMonitoring()
      healthCheckService.startMonitoring()

      expect(getHealth).toHaveBeenCalledTimes(1)
    })

    it('should stop monitoring when stopMonitoring is called', async () => {
      healthCheckService.startMonitoring()
      healthCheckService.stopMonitoring()

      await vi.advanceTimersByTimeAsync(interval * 42)
      expect(getHealth).toHaveBeenCalledTimes(1)
    })

    it('should check health every 10 seconds', async () => {
      healthCheckService.startMonitoring()
      await vi.advanceTimersByTimeAsync(interval)
      await vi.advanceTimersByTimeAsync(interval - 1)
      expect(getHealth).toHaveBeenCalledTimes(2)
      await vi.advanceTimersByTimeAsync(1)
      expect(getHealth).toHaveBeenCalledTimes(3) // Initial + 2 intervals
    })
  })

  describe('handling responses', () => {
    const alwaysMatchError = {
      title: 'title',
      timeout: 60_000,
      key: 'test-error',
      matches: () => true,
      getMessage: () => 'msg',
    }

    const neverMatchError = {
      title: 'title',
      timeout: 60_000,
      key: 'test-error',
      matches: () => false,
      getMessage: () => 'msg',
    }

    let store: NotificationStore

    beforeEach(() => {
      store = createMockNotificationStore()
      vi.mocked(useNotificationStore).mockReturnValue(store)
    })

    it('should handle API errors gracefully', async () => {
      vi.mocked(getHealth).mockRejectedValue(new Error())
      new HealthCheckService([alwaysMatchError]).startMonitoring()
      await vi.advanceTimersByTimeAsync(interval)

      expect(store.warning).toHaveBeenCalledWith('msg', 'title', 60_000, 'test-error')
    })

    it('should handle successful health check', async () => {
      vi.mocked(getHealth).mockResolvedValue(createMockHealthResponse('ok'))
      new HealthCheckService([neverMatchError]).startMonitoring()
      await vi.advanceTimersByTimeAsync(interval)

      expect(store.removeByKey).toHaveBeenCalledWith('test-error')
    })

    it('should handle failed health check', async () => {
      vi.mocked(getHealth).mockResolvedValue(createMockHealthResponse('error'))
      new HealthCheckService([alwaysMatchError]).startMonitoring()
      await vi.advanceTimersByTimeAsync(interval)

      expect(store.warning).toHaveBeenCalledWith('msg', 'title', 60_000, 'test-error')
    })
  })
})
