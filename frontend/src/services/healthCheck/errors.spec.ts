import { describe, it, expect, vi, afterEach } from 'vitest'
import { DnsError, ConnectionError, ConfigurationError } from './errors'
import type { HealthResponse, ServicesHealth } from '@/api/health'
import { AxiosError, type AxiosResponse } from 'axios'

const createResponse = (services: ServicesHealth): AxiosResponse<HealthResponse> => {
  return { data: { services } } as AxiosResponse<HealthResponse>
}

describe('HealthCheck Errors', () => {
  const healthyResponse = createResponse({
    status: 'ok',
    details: {
      userService: { is_healthy: true, error: null },
      reportService: { is_healthy: true, error: null },
    },
  })

  const configurationErrorResponse = createResponse({
    status: 'error',
    details: {
      userService: {
        is_healthy: false,
        error: "Request error: Request URL is missing an 'http://' or 'https://' protocol.",
      },
      reportService: { is_healthy: true, error: null },
    },
  })

  const dnsErrorResponse = createResponse({
    status: 'error',
    details: {
      userService: {
        is_healthy: false,
        error: 'nodename nor servname provided, or not known',
      },
      reportService: { is_healthy: true, error: null },
    },
  })

  describe('DnsError', () => {
    it('should match DNS error in response', () => {
      expect(DnsError.matches(dnsErrorResponse, undefined)).toBe(true)
    })

    it('should not match healthy response', () => {
      expect(DnsError.matches(healthyResponse, undefined)).toBe(false)
    })

    it('should not match configuration error', () => {
      expect(DnsError.matches(configurationErrorResponse, undefined)).toBe(false)
    })

    it('should return correct notification message', () => {
      expect(DnsError.getMessage(undefined, undefined)).toBe(
        'Backend server cannot resolve domain names. Please check your VPN connection.',
      )
    })
  })

  describe('ConnectionError', () => {
    afterEach(() => {
      vi.unstubAllGlobals()
    })

    it('should match network error', () => {
      const networkError = new AxiosError('Network Error', 'ERR_NETWORK')
      expect(ConnectionError.matches(undefined, networkError)).toBe(true)
    })

    it('should not match healthy response', () => {
      expect(ConnectionError.matches(healthyResponse, undefined)).toBe(false)
    })

    it('should not match configuration error', () => {
      expect(ConnectionError.matches(configurationErrorResponse, undefined)).toBe(false)
    })

    it('should return correct notification message for localhost', () => {
      vi.stubGlobal('window', { location: { hostname: 'localhost' } })
      expect(ConnectionError.getMessage(undefined, undefined)).toBe(
        'Cannot connect to backend. Please check if your server is running.',
      )
    })

    it('should return correct notification message for remote server', () => {
      vi.stubGlobal('window', { location: { hostname: 'dataoffice.indiebi.dev' } })
      expect(ConnectionError.getMessage(undefined, undefined)).toBe(
        'Cannot connect to dataoffice.indiebi.dev/health. Check your internet connection and VPN.',
      )
    })
  })

  describe('ConfigurationError', () => {
    it('should match configuration error in response', () => {
      expect(ConfigurationError.matches(configurationErrorResponse, undefined)).toBe(true)
    })

    it('should not match healthy response', () => {
      expect(ConfigurationError.matches(healthyResponse, undefined)).toBe(false)
    })

    it('should return correct notification message', () => {
      expect(ConfigurationError.getMessage(configurationErrorResponse, undefined)).toBe(
        'Service configuration error in: userService. Please check your environment variables.',
      )
    })
  })
})
