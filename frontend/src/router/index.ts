import { createRouter, createWebHistory } from 'vue-router'

const router = createRouter({
  history: createWebHistory('/new/'),
  routes: [
    {
      path: '/clients-statuses',
      name: 'clients-statuses',
      component: () => import('../views/ClientsStatusesView.vue'),
    },
    {
      path: '/managed-partners-statuses',
      name: 'managed-partners-statuses',
      // route level code-splitting
      // this generates a separate chunk (ManagedPartnersStatusesView.[hash].js) for this route
      // which is lazy-loaded when the route is visited.
      component: () => import('../views/ManagedPartnersStatusesView.vue'),
    },
    {
      path: '/managed-partners-monthly-coverage',
      name: 'managed-partners-monthly-coverage',
      component: () => import('../views/MonthlyCoverageView.vue'),
    },
  ],
})

export default router
