<!doctype html>
<html data-bs-theme="light" lang="en-US" dir="ltr">
  <head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <script>
      window.onload = function () {
        if (window.location.href.startsWith('https://dataoffice.indiebi.com/')) {
          document.documentElement.setAttribute('data-env', 'prod')
          var favicon = document.querySelector('link[rel="icon"]')
          favicon.href = '/static/favicon_prod.png'
        }
        if (window.location.href.startsWith('https://dataoffice.indiebi.dev/')) {
          document.documentElement.setAttribute('data-env', 'dev')
          var favicon = document.querySelector('link[rel="icon"]')
          favicon.href = '/static/favicon_dev.png'
        }
        if (window.location.href.startsWith('http://localhost')) {
          document.documentElement.setAttribute('data-env', 'local')
          var favicon = document.querySelector('link[rel="icon"]')
          favicon.href = '/static/favicon_local.png'
        }
      }
    </script>

    <!-- ===============================================-->
    <!--    Document Title-->
    <!-- ===============================================-->
    <title>Home - Dataoffice</title>

    <!-- ===============================================-->
    <!--    Favicons-->
    <!-- ===============================================-->
    <link rel="icon" type="image/png" href="/static/favicon_local.png" />
    <meta name="theme-color" content="#ffffff" />
    <script src="theme/assets/js/config.js"></script>
    <script src="theme/vendors/simplebar/simplebar.min.js"></script>

    <!-- ===============================================-->
    <!--    Stylesheets-->
    <!-- ===============================================-->
    <link rel="preconnect" href="https://fonts.gstatic.com" />
    <link
      href="https://fonts.googleapis.com/css?family=Open+Sans:300,400,500,600,700%7cPoppins:300,400,500,600,700,800,900&amp;display=swap"
      rel="stylesheet"
    />
    <link href="theme/vendors/simplebar/simplebar.min.css" rel="stylesheet" />
    <link
      href="theme/vendors/bootstrap/bootstrap-icons.css"
      rel="stylesheet"
      id="bootstrap-icons"
    />
    <link href="theme/assets/css/theme-rtl.css" rel="stylesheet" id="style-rtl" />
    <link href="theme/assets/css/theme.css" rel="stylesheet" id="style-default" />
    <link href="theme/assets/css/user-rtl.css" rel="stylesheet" id="user-style-rtl" />
    <link href="theme/assets/css/user.css" rel="stylesheet" id="user-style-default" />
    <link href="static/dataoffice.css" rel="stylesheet" id="user-style-default" />
    <script>
      var isRTL = JSON.parse(localStorage.getItem('isRTL'))
      if (isRTL) {
        var linkDefault = document.getElementById('style-default')
        var userLinkDefault = document.getElementById('user-style-default')
        linkDefault.setAttribute('disabled', true)
        userLinkDefault.setAttribute('disabled', true)
        document.querySelector('html').setAttribute('dir', 'rtl')
      } else {
        var linkRTL = document.getElementById('style-rtl')
        var userLinkRTL = document.getElementById('user-style-rtl')
        linkRTL.setAttribute('disabled', true)
        userLinkRTL.setAttribute('disabled', true)
      }
    </script>
  </head>

  <body>
    <!-- ===============================================-->
    <!--    Main Content-->
    <!-- ===============================================-->
    <main class="main" id="top">
      <div class="container-fluid" data-layout="container">
        <script>
          var isFluid = JSON.parse(localStorage.getItem('isFluid'))
          if (isFluid) {
            var container = document.querySelector('[data-layout]')
            container.classList.remove('container')
            container.classList.add('container-fluid')
          }
        </script>
        <script>
          var navbarStyle = localStorage.getItem('navbarStyle')
          if (navbarStyle && navbarStyle !== 'transparent') {
            document.querySelector('.navbar-vertical').classList.add(`navbar-${navbarStyle}`)
          }
        </script>
        <div id="app"></div>
        <script type="module" src="/src/main.ts"></script>
      </div>
    </main>
    <!-- ===============================================-->
    <!--    End of Main Content-->
    <!-- ===============================================-->

    <!-- ===============================================-->
    <!--    JavaScripts-->
    <!-- ===============================================-->
    <!-- <script src="/static/htmx.1.9.12.min.js"></script> -->
    <script src="theme/vendors/popper/popper.min.js"></script>
    <script src="theme/vendors/bootstrap/bootstrap.min.js"></script>
    <script src="theme/vendors/anchorjs/anchor.min.js"></script>
    <script src="theme/vendors/is/is.min.js"></script>
    <script src="theme/vendors/echarts/echarts.min.js"></script>
    <script src="theme/vendors/fontawesome/all.min.js"></script>
    <script src="theme/vendors/lodash/lodash.min.js"></script>
    <script src="https://polyfill.io/v3/polyfill.min.js?features=window.scroll"></script>
    <script src="theme/vendors/list.js/list.min.js"></script>
    <script src="theme/assets/js/theme.js"></script>
  </body>
</html>
