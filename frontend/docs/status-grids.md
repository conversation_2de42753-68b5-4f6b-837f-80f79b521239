# Status Grids

## Data flow
```mermaid
flowchart TD
    Service[get_clients_statuses_service]:::backend --> Endpoint1[get_clients_statuses]:::backend
    Service --> Endpoint2[get_managed_partners_statuses]:::backend
    Endpoint1 --> URL1[/api/clients/statuses/]:::url
    Endpoint2 --> URL2[/api/managed-partners/statuses/]:::url
    URL1 --> API1[getClientsStatuses]:::frontend
    URL2 --> API2[getManagedPartnersStatuses]:::frontend
    API1 --> Composable[useStatusGrid]:::frontend
    API2 --> Composable
    Composable --> Container[StatusGridContainer]:::frontend
    Container --> View1[AllClientsStatusView]:::frontend
    Container --> View2[ManagedPartnersStatusView]:::frontend

    classDef frontend fill:#42b883,stroke:#35495e,color:white
    classDef backend fill:#3776ab,stroke:#2b5b84,color:white
    classDef url fill:#666666,stroke:#444444,color:white
```

## Components Hierarchy
```mermaid
flowchart TD
    View1[AllClientsStatusView]:::frontend --> Container
    View2[ManagedPartnersStatusView]:::frontend --> Container[StatusGridContainer]:::frontend
    Container --> Grid[StatusGrid]:::frontend
    Grid --> Indicator[StatusIndicator]:::frontend
    Indicator --> Background[StatusBackground]:::frontend
    Indicator --> Symbol[StatusSymbol]:::frontend
    Indicator --> Tooltip[StatusTooltip]:::frontend
    Container --> Legend[StatusLegend]:::frontend
    Legend --> Indicator

    classDef frontend fill:#42b883,stroke:#35495e,color:white
```
