{"name": "dataoffice", "version": "0.0.0", "private": true, "type": "module", "engines": {"node": "22.14.0", "npm": ">=10.9.2"}, "scripts": {"dev": "vite", "build": "run-p type-check \"build-only {@}\" --", "preview": "vite preview", "test:unit": "vitest run --reporter verbose", "test:unit:watch": "vitest", "test:e2e": "playwright test", "build-only": "vite build", "lint": "eslint . --fix", "lint:css": "stylelint './src/**/*.{vue,css,scss}' --fix", "format": "prettier --write src/", "type-check": "vue-tsc --build", "ci:lint": "eslint .", "ci:lint:css": "stylelint './src/**/*.{vue,css,scss}'", "ci:format": "prettier --check src/", "ci:type-check": "vue-tsc --build"}, "dependencies": {"@js-temporal/polyfill": "^0.5.1", "@types/list.js": "^2.3.1", "axios": "^1.8.4", "list.js": "^2.3.1", "pinia": "^3.0.1", "vue": "^3.5.13", "vue-router": "^4.5.0"}, "devDependencies": {"@playwright/test": "^1.51.1", "@tsconfig/node22": "^22.0.1", "@types/jsdom": "^21.1.7", "@types/node": "^22.14.0", "@vitejs/plugin-vue": "^5.2.3", "@vitest/eslint-plugin": "^1.1.39", "@vue/eslint-config-prettier": "^10.2.0", "@vue/eslint-config-typescript": "^14.5.0", "@vue/test-utils": "^2.4.6", "@vue/tsconfig": "^0.7.0", "eslint": "^9.22.0", "eslint-plugin-playwright": "^2.2.0", "eslint-plugin-vue": "~10.0.0", "jiti": "^2.4.2", "jsdom": "^26.0.0", "npm-run-all2": "^7.0.2", "postcss-html": "^1.8.0", "prettier": "3.5.3", "stylelint": "^16.19.1", "stylelint-config-recommended-vue": "^1.6.0", "stylelint-config-standard": "^38.0.0", "stylelint-declaration-strict-value": "^1.10.11", "typescript": "~5.8.0", "vite": "^6.2.4", "vite-plugin-vue-devtools": "^7.7.2", "vitest": "^3.1.1", "vue-tsc": "^2.2.8"}}