module.exports = {
  extends: ['stylelint-config-standard', 'stylelint-config-recommended-vue'],
  plugins: ['stylelint-declaration-strict-value'],
  rules: {
    'scale-unlimited/declaration-strict-value': [
      ['z-index'],
      {
        disableFix: true,
        message:
          'All z-index values must use CSS variables defined in z-index-hierarchy.css (instead of raw “${value}”).',
      },
    ],
  },
}
