/*!
 * Copyright (c) 2018 <PERSON> <<EMAIL>>
 * 
 * Permission is hereby granted, free of charge, to any person obtaining
 * a copy of this software and associated documentation files (the
 * "Software"), to deal in the Software without restriction, including
 * without limitation the rights to use, copy, modify, merge, publish,
 * distribute, sublicense, and/or sell copies of the Software, and to
 * permit persons to whom the Software is furnished to do so, subject to
 * the following conditions:
 * 
 * The above copyright notice and this permission notice shall be
 * included in all copies or substantial portions of the Software.
 * 
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND,
 * EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND
 * NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE
 * LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION
 * OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION
 * WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
 */
!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?module.exports=e():"function"==typeof define&&define.amd?define(e):t.validator=e()}(this,function(){"use strict";function i(t){return(i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function u(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){if("undefined"==typeof Symbol||!(Symbol.iterator in Object(t)))return;var r=[],n=!0,i=!1,a=void 0;try{for(var o,s=t[Symbol.iterator]();!(n=(o=s.next()).done)&&(r.push(o.value),!e||r.length!==e);n=!0);}catch(t){i=!0,a=t}finally{try{n||null==s.return||s.return()}finally{if(i)throw a}}return r}(t,e)||c(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function r(t){return function(t){if(Array.isArray(t))return n(t)}(t)||function(t){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(t))return Array.from(t)}(t)||c(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function c(t,e){if(t){if("string"==typeof t)return n(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?n(t,e):void 0}}function n(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function d(t){if(!("string"==typeof t||t instanceof String)){var e=i(t);throw null===t?e="null":"object"===e&&(e=t.constructor.name),new TypeError("Expected a string but received a ".concat(e))}}function a(t){return d(t),t=Date.parse(t),isNaN(t)?null:new Date(t)}for(var t,o={"en-US":/^[A-Z]+$/i,"az-AZ":/^[A-VXYZÇƏĞİıÖŞÜ]+$/i,"bg-BG":/^[А-Я]+$/i,"cs-CZ":/^[A-ZÁČĎÉĚÍŇÓŘŠŤÚŮÝŽ]+$/i,"da-DK":/^[A-ZÆØÅ]+$/i,"de-DE":/^[A-ZÄÖÜß]+$/i,"el-GR":/^[Α-ώ]+$/i,"es-ES":/^[A-ZÁÉÍÑÓÚÜ]+$/i,"fa-IR":/^[ابپتثجچحخدذرزژسشصضطظعغفقکگلمنوهی]+$/i,"fr-FR":/^[A-ZÀÂÆÇÉÈÊËÏÎÔŒÙÛÜŸ]+$/i,"it-IT":/^[A-ZÀÉÈÌÎÓÒÙ]+$/i,"nb-NO":/^[A-ZÆØÅ]+$/i,"nl-NL":/^[A-ZÁÉËÏÓÖÜÚ]+$/i,"nn-NO":/^[A-ZÆØÅ]+$/i,"hu-HU":/^[A-ZÁÉÍÓÖŐÚÜŰ]+$/i,"pl-PL":/^[A-ZĄĆĘŚŁŃÓŻŹ]+$/i,"pt-PT":/^[A-ZÃÁÀÂÄÇÉÊËÍÏÕÓÔÖÚÜ]+$/i,"ru-RU":/^[А-ЯЁ]+$/i,"sl-SI":/^[A-ZČĆĐŠŽ]+$/i,"sk-SK":/^[A-ZÁČĎÉÍŇÓŠŤÚÝŽĹŔĽÄÔ]+$/i,"sr-RS@latin":/^[A-ZČĆŽŠĐ]+$/i,"sr-RS":/^[А-ЯЂЈЉЊЋЏ]+$/i,"sv-SE":/^[A-ZÅÄÖ]+$/i,"th-TH":/^[ก-๐\s]+$/i,"tr-TR":/^[A-ZÇĞİıÖŞÜ]+$/i,"uk-UA":/^[А-ЩЬЮЯЄIЇҐі]+$/i,"vi-VN":/^[A-ZÀÁẠẢÃÂẦẤẬẨẪĂẰẮẶẲẴĐÈÉẸẺẼÊỀẾỆỂỄÌÍỊỈĨÒÓỌỎÕÔỒỐỘỔỖƠỜỚỢỞỠÙÚỤỦŨƯỪỨỰỬỮỲÝỴỶỸ]+$/i,"ku-IQ":/^[ئابپتجچحخدرڕزژسشعغفڤقکگلڵمنوۆھەیێيطؤثآإأكضصةظذ]+$/i,ar:/^[ءآأؤإئابةتثجحخدذرزسشصضطظعغفقكلمنهوىيًٌٍَُِّْٰ]+$/,he:/^[א-ת]+$/,fa:/^['آاءأؤئبپتثجچحخدذرزژسشصضطظعغفقکگلمنوهةی']+$/i},s={"en-US":/^[0-9A-Z]+$/i,"az-AZ":/^[0-9A-VXYZÇƏĞİıÖŞÜ]+$/i,"bg-BG":/^[0-9А-Я]+$/i,"cs-CZ":/^[0-9A-ZÁČĎÉĚÍŇÓŘŠŤÚŮÝŽ]+$/i,"da-DK":/^[0-9A-ZÆØÅ]+$/i,"de-DE":/^[0-9A-ZÄÖÜß]+$/i,"el-GR":/^[0-9Α-ω]+$/i,"es-ES":/^[0-9A-ZÁÉÍÑÓÚÜ]+$/i,"fr-FR":/^[0-9A-ZÀÂÆÇÉÈÊËÏÎÔŒÙÛÜŸ]+$/i,"it-IT":/^[0-9A-ZÀÉÈÌÎÓÒÙ]+$/i,"hu-HU":/^[0-9A-ZÁÉÍÓÖŐÚÜŰ]+$/i,"nb-NO":/^[0-9A-ZÆØÅ]+$/i,"nl-NL":/^[0-9A-ZÁÉËÏÓÖÜÚ]+$/i,"nn-NO":/^[0-9A-ZÆØÅ]+$/i,"pl-PL":/^[0-9A-ZĄĆĘŚŁŃÓŻŹ]+$/i,"pt-PT":/^[0-9A-ZÃÁÀÂÄÇÉÊËÍÏÕÓÔÖÚÜ]+$/i,"ru-RU":/^[0-9А-ЯЁ]+$/i,"sl-SI":/^[0-9A-ZČĆĐŠŽ]+$/i,"sk-SK":/^[0-9A-ZÁČĎÉÍŇÓŠŤÚÝŽĹŔĽÄÔ]+$/i,"sr-RS@latin":/^[0-9A-ZČĆŽŠĐ]+$/i,"sr-RS":/^[0-9А-ЯЂЈЉЊЋЏ]+$/i,"sv-SE":/^[0-9A-ZÅÄÖ]+$/i,"th-TH":/^[ก-๙\s]+$/i,"tr-TR":/^[0-9A-ZÇĞİıÖŞÜ]+$/i,"uk-UA":/^[0-9А-ЩЬЮЯЄIЇҐі]+$/i,"ku-IQ":/^[٠١٢٣٤٥٦٧٨٩0-9ئابپتجچحخدرڕزژسشعغفڤقکگلڵمنوۆھەیێيطؤثآإأكضصةظذ]+$/i,"vi-VN":/^[0-9A-ZÀÁẠẢÃÂẦẤẬẨẪĂẰẮẶẲẴĐÈÉẸẺẼÊỀẾỆỂỄÌÍỊỈĨÒÓỌỎÕÔỒỐỘỔỖƠỜỚỢỞỠÙÚỤỦŨƯỪỨỰỬỮỲÝỴỶỸ]+$/i,ar:/^[٠١٢٣٤٥٦٧٨٩0-9ءآأؤإئابةتثجحخدذرزسشصضطظعغفقكلمنهوىيًٌٍَُِّْٰ]+$/,he:/^[0-9א-ת]+$/,fa:/^['0-9آاءأؤئبپتثجچحخدذرزژسشصضطظعغفقکگلمنوهةی۱۲۳۴۵۶۷۸۹۰']+$/i},l={"en-US":".",ar:"٫"},e=["AU","GB","HK","IN","NZ","ZA","ZM"],f=0;f<e.length;f++)t="en-".concat(e[f]),o[t]=o["en-US"],s[t]=s["en-US"],l[t]=l["en-US"];for(var p,$=["AE","BH","DZ","EG","IQ","JO","KW","LB","LY","MA","QM","QA","SA","SD","SY","TN","YE"],A=0;A<$.length;A++)p="ar-".concat($[A]),o[p]=o.ar,s[p]=s.ar,l[p]=l.ar;for(var h,g=["IR","AF"],m=0;m<g.length;m++)h="fa-".concat(g[m]),s[h]=s.fa,l[h]=l.ar;for(var v=["ar-EG","ar-LB","ar-LY"],I=["bg-BG","cs-CZ","da-DK","de-DE","el-GR","en-ZM","es-ES","fr-CA","fr-FR","id-ID","it-IT","ku-IQ","hu-HU","nb-NO","nn-NO","nl-NL","pl-PL","pt-PT","ru-RU","sl-SI","sr-RS@latin","sr-RS","sv-SE","tr-TR","uk-UA","vi-VN"],S=0;S<v.length;S++)l[v[S]]=l["en-US"];for(var Z=0;Z<I.length;Z++)l[I[Z]]=",";function E(t,e){d(t),e=e||{};var r=new RegExp("^(?:[-+])?(?:[0-9]+)?(?:\\".concat(e.locale?l[e.locale]:".","[0-9]*)?(?:[eE][\\+\\-]?(?:[0-9]+))?$"));if(""===t||"."===t||"-"===t||"+"===t)return!1;var n=parseFloat(t.replace(",","."));return r.test(t)&&(!e.hasOwnProperty("min")||n>=e.min)&&(!e.hasOwnProperty("max")||n<=e.max)&&(!e.hasOwnProperty("lt")||n<e.lt)&&(!e.hasOwnProperty("gt")||n>e.gt)}o["fr-CA"]=o["fr-FR"],s["fr-CA"]=s["fr-FR"],o["pt-BR"]=o["pt-PT"],s["pt-BR"]=s["pt-PT"],l["pt-BR"]=l["pt-PT"],o["pl-Pl"]=o["pl-PL"],s["pl-Pl"]=s["pl-PL"],l["pl-Pl"]=l["pl-PL"],o["fa-AF"]=o.fa;var C=Object.keys(l);function F(t){return E(t)?parseFloat(t):NaN}function _(t){return"object"===i(t)&&null!==t?t="function"==typeof t.toString?t.toString():"[object Object]":(null==t||isNaN(t)&&!t.length)&&(t=""),String(t)}function M(t,e){var r,n=0<arguments.length&&void 0!==t?t:{},i=1<arguments.length?e:void 0;for(r in i)void 0===n[r]&&(n[r]=i[r]);return n}var R={ignoreCase:!1};function b(t,e){var r;d(t),e="object"===i(e)?(r=e.min||0,e.max):(r=e,arguments[2]);t=encodeURI(t).split(/%..|./).length-1;return r<=t&&(void 0===e||t<=e)}var L={require_tld:!0,allow_underscores:!1,allow_trailing_dot:!1,allow_numeric_tld:!1};function N(t,e){d(t),(e=M(e,L)).allow_trailing_dot&&"."===t[t.length-1]&&(t=t.substring(0,t.length-1));var r=t.split("."),t=r[r.length-1];if(e.require_tld){if(r.length<2)return!1;if(!/^([a-z\u00a1-\uffff]{2,}|xn[a-z0-9-]{2,})$/i.test(t))return!1;if(/[\s\u2002-\u200B\u202F\u205F\u3000\uFEFF\uDB40\uDC20\u00A9\uFFFD]/.test(t))return!1}return!(!e.allow_numeric_tld&&/^\d+$/.test(t))&&r.every(function(t){return!(63<t.length)&&(!!/^[a-z_\u00a1-\uffff0-9-]+$/i.test(t)&&(!/[\uff01-\uff5e]/.test(t)&&(!/^-|-$/.test(t)&&!(!e.allow_underscores&&/_/.test(t)))))})}var D=/^(([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])\.){3}([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])$/,T=/^[0-9A-F]{1,4}$/i;function w(t){var e=1<arguments.length&&void 0!==arguments[1]?arguments[1]:"";if(d(t),!(e=String(e)))return w(t,4)||w(t,6);if("4"===e)return!!D.test(t)&&t.split(".").sort(function(t,e){return t-e})[3]<=255;if("6"!==e)return!1;e=[t];if(t.includes("%")){if(2!==(e=t.split("%")).length)return!1;if(!e[0].includes(":"))return!1;if(""===e[1])return!1}var r=e[0].split(":"),n=!1,i=w(r[r.length-1],4),e=i?7:8;if(r.length>e)return!1;if("::"===t)return!0;"::"===t.substr(0,2)?(r.shift(),r.shift(),n=!0):"::"===t.substr(t.length-2)&&(r.pop(),r.pop(),n=!0);for(var a=0;a<r.length;++a)if(""===r[a]&&0<a&&a<r.length-1){if(n)return!1;n=!0}else if(!(i&&a===r.length-1||T.test(r[a])))return!1;return n?1<=r.length:r.length===e}var y={allow_display_name:!1,require_display_name:!1,allow_utf8_local_part:!0,require_tld:!0,blacklisted_chars:"",ignore_max_length:!1},B=/^([^\x00-\x1F\x7F-\x9F\cX]+)<(.+)>$/i,U=/^[a-z\d!#\$%&'\*\+\-\/=\?\^_`{\|}~]+$/i,x=/^[a-z\d]+$/,P=/^([\s\x01-\x08\x0b\x0c\x0e-\x1f\x7f\x21\x23-\x5b\x5d-\x7e]|(\\[\x01-\x09\x0b\x0c\x0d-\x7f]))*$/i,G=/^[a-z\d!#\$%&'\*\+\-\/=\?\^_`{\|}~\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF]+$/i,O=/^([\s\x01-\x08\x0b\x0c\x0e-\x1f\x7f\x21\x23-\x5b\x5d-\x7e\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF]|(\\[\x01-\x09\x0b\x0c\x0d-\x7f\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF]))*$/i;var Y={protocols:["http","https","ftp"],require_tld:!0,require_protocol:!1,require_host:!0,require_port:!1,require_valid_protocol:!0,allow_underscores:!1,allow_trailing_dot:!1,allow_protocol_relative_urls:!1,validate_length:!0},k=/^\[([^\]]+)\](?::([0-9]+))?$/;function H(t,e){for(var r,n=0;n<e.length;n++){var i=e[n];if(t===i||(r=i,"[object RegExp]"===Object.prototype.toString.call(r)&&i.test(t)))return 1}}var K=/^([0-9a-fA-F][0-9a-fA-F]:){5}([0-9a-fA-F][0-9a-fA-F])$/,V=/^([0-9a-fA-F]){12}$/,W=/^([0-9a-fA-F][0-9a-fA-F]-){5}([0-9a-fA-F][0-9a-fA-F])$/,z=/^([0-9a-fA-F][0-9a-fA-F]\s){5}([0-9a-fA-F][0-9a-fA-F])$/,j=/^([0-9a-fA-F]{4}).([0-9a-fA-F]{4}).([0-9a-fA-F]{4})$/;var J=/^\d{1,2}$/;var X={format:"YYYY/MM/DD",delimiters:["/","-"],strictMode:!1};function q(e,r){if(r=M("string"==typeof r?{format:r}:r,X),"string"==typeof e&&/(^(y{4}|y{2})[\/-](m{1,2})[\/-](d{1,2})$)|(^(m{1,2})[\/-](d{1,2})[\/-]((y{4}|y{2})$))|(^(d{1,2})[\/-](m{1,2})[\/-]((y{4}|y{2})$))/gi.test(r.format)){var t=r.delimiters.find(function(t){return-1!==r.format.indexOf(t)}),n=r.strictMode?t:r.delimiters.find(function(t){return-1!==e.indexOf(t)}),i={},a=function(t,e){var r;if("undefined"==typeof Symbol||null==t[Symbol.iterator]){if(Array.isArray(t)||(r=c(t))||e&&t&&"number"==typeof t.length){r&&(t=r);var n=0,e=function(){};return{s:e,n:function(){return n>=t.length?{done:!0}:{done:!1,value:t[n++]}},e:function(t){throw t},f:e}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var i,a=!0,o=!1;return{s:function(){r=t[Symbol.iterator]()},n:function(){var t=r.next();return a=t.done,t},e:function(t){o=!0,i=t},f:function(){try{a||null==r.return||r.return()}finally{if(o)throw i}}}}(function(t,e){for(var r=[],n=Math.min(t.length,e.length),i=0;i<n;i++)r.push([t[i],e[i]]);return r}(e.split(n),r.format.toLowerCase().split(t)));try{for(a.s();!(s=a.n()).done;){var o=u(s.value,2),s=o[0],o=o[1];if(s.length!==o.length)return!1;i[o.charAt(0)]=s}}catch(t){a.e(t)}finally{a.f()}return new Date("".concat(i.m,"/").concat(i.d,"/").concat(i.y)).getDate()===+i.d}return!r.strictMode&&("[object Date]"===Object.prototype.toString.call(e)&&isFinite(e))}var Q=/^[A-z]{2,4}([_-]([A-z]{4}|[\d]{3}))?([_-]([A-z]{2}|[\d]{3}))?$/;var tt=Object.keys(o);var et=Object.keys(s),rt=/^[0-9]+$/;var nt={AM:/^[A-Z]{2}\d{7}$/,AR:/^[A-Z]{3}\d{6}$/,AT:/^[A-Z]\d{7}$/,AU:/^[A-Z]\d{7}$/,BE:/^[A-Z]{2}\d{6}$/,BG:/^\d{9}$/,BY:/^[A-Z]{2}\d{7}$/,CA:/^[A-Z]{2}\d{6}$/,CH:/^[A-Z]\d{7}$/,CN:/^[GE]\d{8}$/,CY:/^[A-Z](\d{6}|\d{8})$/,CZ:/^\d{8}$/,DE:/^[CFGHJKLMNPRTVWXYZ0-9]{9}$/,DK:/^\d{9}$/,DZ:/^\d{9}$/,EE:/^([A-Z]\d{7}|[A-Z]{2}\d{7})$/,ES:/^[A-Z0-9]{2}([A-Z0-9]?)\d{6}$/,FI:/^[A-Z]{2}\d{7}$/,FR:/^\d{2}[A-Z]{2}\d{5}$/,GB:/^\d{9}$/,GR:/^[A-Z]{2}\d{7}$/,HR:/^\d{9}$/,HU:/^[A-Z]{2}(\d{6}|\d{7})$/,IE:/^[A-Z0-9]{2}\d{7}$/,IN:/^[A-Z]{1}-?\d{7}$/,IS:/^(A)\d{7}$/,IT:/^[A-Z0-9]{2}\d{7}$/,JP:/^[A-Z]{2}\d{7}$/,KR:/^[MS]\d{8}$/,LT:/^[A-Z0-9]{8}$/,LU:/^[A-Z0-9]{8}$/,LV:/^[A-Z0-9]{2}\d{7}$/,MT:/^\d{7}$/,NL:/^[A-Z]{2}[A-Z0-9]{6}\d$/,PO:/^[A-Z]{2}\d{7}$/,PT:/^[A-Z]\d{6}$/,RO:/^\d{8,9}$/,RU:/^\d{2}\d{2}\d{6}$/,SE:/^\d{8}$/,SL:/^(P)[A-Z]\d{7}$/,SK:/^[0-9A-Z]\d{7}$/,TR:/^[A-Z]\d{8}$/,UA:/^[A-Z]{2}\d{6}$/,US:/^\d{9}$/};var it=/^(?:[-+]?(?:0|[1-9][0-9]*))$/,at=/^[-+]?[0-9]+$/;function ot(t,e){d(t);var r=(e=e||{}).hasOwnProperty("allow_leading_zeroes")&&!e.allow_leading_zeroes?it:at,n=!e.hasOwnProperty("min")||t>=e.min,i=!e.hasOwnProperty("max")||t<=e.max,a=!e.hasOwnProperty("lt")||t<e.lt,e=!e.hasOwnProperty("gt")||t>e.gt;return r.test(t)&&n&&i&&a&&e}var st=/^[0-9]{15}$/,ct=/^\d{2}-\d{6}-\d{6}-\d{1}$/;var lt=/^[\x00-\x7F]+$/;var ut=/[^\u0020-\u007E\uFF61-\uFF9F\uFFA0-\uFFDC\uFFE8-\uFFEE0-9a-zA-Z]/;var dt=/[\u0020-\u007E\uFF61-\uFF9F\uFFA0-\uFFDC\uFFE8-\uFFEE0-9a-zA-Z]/;var ft=/[^\x00-\x7F]/;var pt,$t,At=($t="i",pt=(pt=["^(0|[1-9]\\d*)\\.(0|[1-9]\\d*)\\.(0|[1-9]\\d*)","(?:-((?:0|[1-9]\\d*|\\d*[a-z-][0-9a-z-]*)(?:\\.(?:0|[1-9]\\d*|\\d*[a-z-][0-9a-z-]*))*))","?(?:\\+([0-9a-z-]+(?:\\.[0-9a-z-]+)*))?$"]).join(""),new RegExp(pt,$t));var ht=/[\uD800-\uDBFF][\uDC00-\uDFFF]/;function gt(t,e){return t.some(function(t){return e===t})}var mt={force_decimal:!1,decimal_digits:"1,",locale:"en-US"},vt=["","-","+"];var It=/^(0x|0h)?[0-9A-F]+$/i;function St(t){return d(t),It.test(t)}var Zt=/^(0o)?[0-7]+$/i;var Et=/^#?([0-9A-F]{3}|[0-9A-F]{4}|[0-9A-F]{6}|[0-9A-F]{8})$/i;var Ct=/^rgb\((([0-9]|[1-9][0-9]|1[0-9][0-9]|2[0-4][0-9]|25[0-5]),){2}([0-9]|[1-9][0-9]|1[0-9][0-9]|2[0-4][0-9]|25[0-5])\)$/,Ft=/^rgba\((([0-9]|[1-9][0-9]|1[0-9][0-9]|2[0-4][0-9]|25[0-5]),){3}(0?\.\d|1(\.0)?|0(\.0)?)\)$/,_t=/^rgb\((([0-9]%|[1-9][0-9]%|100%),){2}([0-9]%|[1-9][0-9]%|100%)\)/,Mt=/^rgba\((([0-9]%|[1-9][0-9]%|100%),){3}(0?\.\d|1(\.0)?|0(\.0)?)\)/;var Rt=/^(hsl)a?\(\s*((\+|\-)?([0-9]+(\.[0-9]+)?(e(\+|\-)?[0-9]+)?|\.[0-9]+(e(\+|\-)?[0-9]+)?))(deg|grad|rad|turn|\s*)(\s*,\s*(\+|\-)?([0-9]+(\.[0-9]+)?(e(\+|\-)?[0-9]+)?|\.[0-9]+(e(\+|\-)?[0-9]+)?)%){2}\s*(,\s*((\+|\-)?([0-9]+(\.[0-9]+)?(e(\+|\-)?[0-9]+)?|\.[0-9]+(e(\+|\-)?[0-9]+)?)%?)\s*)?\)$/i,bt=/^(hsl)a?\(\s*((\+|\-)?([0-9]+(\.[0-9]+)?(e(\+|\-)?[0-9]+)?|\.[0-9]+(e(\+|\-)?[0-9]+)?))(deg|grad|rad|turn|\s)(\s*(\+|\-)?([0-9]+(\.[0-9]+)?(e(\+|\-)?[0-9]+)?|\.[0-9]+(e(\+|\-)?[0-9]+)?)%){2}\s*(\/\s*((\+|\-)?([0-9]+(\.[0-9]+)?(e(\+|\-)?[0-9]+)?|\.[0-9]+(e(\+|\-)?[0-9]+)?)%?)\s*)?\)$/i;var Lt=/^[A-Z]{2}[0-9A-Z]{3}\d{2}\d{5}$/;var Nt={AD:/^(AD[0-9]{2})\d{8}[A-Z0-9]{12}$/,AE:/^(AE[0-9]{2})\d{3}\d{16}$/,AL:/^(AL[0-9]{2})\d{8}[A-Z0-9]{16}$/,AT:/^(AT[0-9]{2})\d{16}$/,AZ:/^(AZ[0-9]{2})[A-Z0-9]{4}\d{20}$/,BA:/^(BA[0-9]{2})\d{16}$/,BE:/^(BE[0-9]{2})\d{12}$/,BG:/^(BG[0-9]{2})[A-Z]{4}\d{6}[A-Z0-9]{8}$/,BH:/^(BH[0-9]{2})[A-Z]{4}[A-Z0-9]{14}$/,BR:/^(BR[0-9]{2})\d{23}[A-Z]{1}[A-Z0-9]{1}$/,BY:/^(BY[0-9]{2})[A-Z0-9]{4}\d{20}$/,CH:/^(CH[0-9]{2})\d{5}[A-Z0-9]{12}$/,CR:/^(CR[0-9]{2})\d{18}$/,CY:/^(CY[0-9]{2})\d{8}[A-Z0-9]{16}$/,CZ:/^(CZ[0-9]{2})\d{20}$/,DE:/^(DE[0-9]{2})\d{18}$/,DK:/^(DK[0-9]{2})\d{14}$/,DO:/^(DO[0-9]{2})[A-Z]{4}\d{20}$/,EE:/^(EE[0-9]{2})\d{16}$/,EG:/^(EG[0-9]{2})\d{25}$/,ES:/^(ES[0-9]{2})\d{20}$/,FI:/^(FI[0-9]{2})\d{14}$/,FO:/^(FO[0-9]{2})\d{14}$/,FR:/^(FR[0-9]{2})\d{10}[A-Z0-9]{11}\d{2}$/,GB:/^(GB[0-9]{2})[A-Z]{4}\d{14}$/,GE:/^(GE[0-9]{2})[A-Z0-9]{2}\d{16}$/,GI:/^(GI[0-9]{2})[A-Z]{4}[A-Z0-9]{15}$/,GL:/^(GL[0-9]{2})\d{14}$/,GR:/^(GR[0-9]{2})\d{7}[A-Z0-9]{16}$/,GT:/^(GT[0-9]{2})[A-Z0-9]{4}[A-Z0-9]{20}$/,HR:/^(HR[0-9]{2})\d{17}$/,HU:/^(HU[0-9]{2})\d{24}$/,IE:/^(IE[0-9]{2})[A-Z0-9]{4}\d{14}$/,IL:/^(IL[0-9]{2})\d{19}$/,IQ:/^(IQ[0-9]{2})[A-Z]{4}\d{15}$/,IR:/^(IR[0-9]{2})0\d{2}0\d{18}$/,IS:/^(IS[0-9]{2})\d{22}$/,IT:/^(IT[0-9]{2})[A-Z]{1}\d{10}[A-Z0-9]{12}$/,JO:/^(JO[0-9]{2})[A-Z]{4}\d{22}$/,KW:/^(KW[0-9]{2})[A-Z]{4}[A-Z0-9]{22}$/,KZ:/^(KZ[0-9]{2})\d{3}[A-Z0-9]{13}$/,LB:/^(LB[0-9]{2})\d{4}[A-Z0-9]{20}$/,LC:/^(LC[0-9]{2})[A-Z]{4}[A-Z0-9]{24}$/,LI:/^(LI[0-9]{2})\d{5}[A-Z0-9]{12}$/,LT:/^(LT[0-9]{2})\d{16}$/,LU:/^(LU[0-9]{2})\d{3}[A-Z0-9]{13}$/,LV:/^(LV[0-9]{2})[A-Z]{4}[A-Z0-9]{13}$/,MC:/^(MC[0-9]{2})\d{10}[A-Z0-9]{11}\d{2}$/,MD:/^(MD[0-9]{2})[A-Z0-9]{20}$/,ME:/^(ME[0-9]{2})\d{18}$/,MK:/^(MK[0-9]{2})\d{3}[A-Z0-9]{10}\d{2}$/,MR:/^(MR[0-9]{2})\d{23}$/,MT:/^(MT[0-9]{2})[A-Z]{4}\d{5}[A-Z0-9]{18}$/,MU:/^(MU[0-9]{2})[A-Z]{4}\d{19}[A-Z]{3}$/,NL:/^(NL[0-9]{2})[A-Z]{4}\d{10}$/,NO:/^(NO[0-9]{2})\d{11}$/,PK:/^(PK[0-9]{2})[A-Z0-9]{4}\d{16}$/,PL:/^(PL[0-9]{2})\d{24}$/,PS:/^(PS[0-9]{2})[A-Z0-9]{4}\d{21}$/,PT:/^(PT[0-9]{2})\d{21}$/,QA:/^(QA[0-9]{2})[A-Z]{4}[A-Z0-9]{21}$/,RO:/^(RO[0-9]{2})[A-Z]{4}[A-Z0-9]{16}$/,RS:/^(RS[0-9]{2})\d{18}$/,SA:/^(SA[0-9]{2})\d{2}[A-Z0-9]{18}$/,SC:/^(SC[0-9]{2})[A-Z]{4}\d{20}[A-Z]{3}$/,SE:/^(SE[0-9]{2})\d{20}$/,SI:/^(SI[0-9]{2})\d{15}$/,SK:/^(SK[0-9]{2})\d{20}$/,SM:/^(SM[0-9]{2})[A-Z]{1}\d{10}[A-Z0-9]{12}$/,SV:/^(SV[0-9]{2})[A-Z0-9]{4}\d{20}$/,TL:/^(TL[0-9]{2})\d{19}$/,TN:/^(TN[0-9]{2})\d{20}$/,TR:/^(TR[0-9]{2})\d{5}[A-Z0-9]{17}$/,UA:/^(UA[0-9]{2})\d{6}[A-Z0-9]{19}$/,VA:/^(VA[0-9]{2})\d{18}$/,VG:/^(VG[0-9]{2})[A-Z0-9]{4}\d{16}$/,XK:/^(XK[0-9]{2})\d{16}$/};var Dt=/^[A-z]{4}[A-z]{2}\w{2}(\w{3})?$/;var Tt=/^[a-f0-9]{32}$/;var wt={md5:32,md4:32,sha1:40,sha256:64,sha384:96,sha512:128,ripemd128:32,ripemd160:40,tiger128:32,tiger160:40,tiger192:48,crc32:8,crc32b:8};var yt=/[^A-Z0-9+\/=]/i,Bt=/^[A-Z0-9_\-]*$/i,Ut={urlSafe:!1};function xt(t,e){d(t),e=M(e,Ut);var r=t.length;if(e.urlSafe)return Bt.test(t);if(r%4!=0||yt.test(t))return!1;e=t.indexOf("=");return-1===e||e===r-1||e===r-2&&"="===t[r-1]}var Pt={allow_primitives:!1};var Gt={ignore_whitespace:!1};var Ot={3:/^[0-9A-F]{8}-[0-9A-F]{4}-3[0-9A-F]{3}-[0-9A-F]{4}-[0-9A-F]{12}$/i,4:/^[0-9A-F]{8}-[0-9A-F]{4}-4[0-9A-F]{3}-[89AB][0-9A-F]{3}-[0-9A-F]{12}$/i,5:/^[0-9A-F]{8}-[0-9A-F]{4}-5[0-9A-F]{3}-[89AB][0-9A-F]{3}-[0-9A-F]{12}$/i,all:/^[0-9A-F]{8}-[0-9A-F]{4}-[0-9A-F]{4}-[0-9A-F]{4}-[0-9A-F]{12}$/i};var Yt=/^(?:4[0-9]{12}(?:[0-9]{3,6})?|5[1-5][0-9]{14}|(222[1-9]|22[3-9][0-9]|2[3-6][0-9]{2}|27[01][0-9]|2720)[0-9]{12}|6(?:011|5[0-9][0-9])[0-9]{12,15}|3[47][0-9]{13}|3(?:0[0-5]|[68][0-9])[0-9]{11}|(?:2131|1800|35\d{3})\d{11}|6[27][0-9]{14})$/;var kt={ES:function(t){d(t);var e={X:0,Y:1,Z:2},r=t.trim().toUpperCase();if(!/^[0-9X-Z][0-9]{7}[TRWAGMYFPDXBNJZSQVHLCKE]$/.test(r))return!1;t=r.slice(0,-1).replace(/[X,Y,Z]/g,function(t){return e[t]});return r.endsWith(["T","R","W","A","G","M","Y","F","P","D","X","B","N","J","Z","S","Q","V","H","L","C","K","E"][t%23])},IN:function(t){var r=[[0,1,2,3,4,5,6,7,8,9],[1,2,3,4,0,6,7,8,9,5],[2,3,4,0,1,7,8,9,5,6],[3,4,0,1,2,8,9,5,6,7],[4,0,1,2,3,9,5,6,7,8],[5,9,8,7,6,0,4,3,2,1],[6,5,9,8,7,1,0,4,3,2],[7,6,5,9,8,2,1,0,4,3],[8,7,6,5,9,3,2,1,0,4],[9,8,7,6,5,4,3,2,1,0]],n=[[0,1,2,3,4,5,6,7,8,9],[1,5,7,6,2,8,3,0,9,4],[5,8,0,3,7,9,6,1,4,2],[8,9,1,6,0,4,3,5,2,7],[9,4,5,3,1,2,6,8,7,0],[4,2,8,6,5,7,3,9,0,1],[2,7,9,3,8,0,6,4,1,5],[7,0,4,6,9,1,3,2,5,8]],t=t.trim();if(!/^[1-9]\d{3}\s?\d{4}\s?\d{4}$/.test(t))return!1;var i=0;return t.replace(/\s/g,"").split("").map(Number).reverse().forEach(function(t,e){i=r[i][n[e%8][t]]}),0===i},IT:function(t){return 9===t.length&&("CA00000AA"!==t&&-1<t.search(/C[A-Z][0-9]{5}[A-Z]{2}/i))},NO:function(t){var e=t.trim();if(isNaN(Number(e)))return!1;if(11!==e.length)return!1;if("00000000000"===e)return!1;var r=e.split("").map(Number),t=(11-(3*r[0]+7*r[1]+6*r[2]+ +r[3]+8*r[4]+9*r[5]+4*r[6]+5*r[7]+2*r[8])%11)%11,e=(11-(5*r[0]+4*r[1]+3*r[2]+2*r[3]+7*r[4]+6*r[5]+5*r[6]+4*r[7]+3*r[8]+2*t)%11)%11;return t===r[9]&&e===r[10]},"he-IL":function(t){t=t.trim();if(!/^\d{9}$/.test(t))return!1;for(var e,r=t,n=0,i=0;i<r.length;i++)n+=9<(e=Number(r[i])*(i%2+1))?e-9:e;return n%10==0},"ar-TN":function(t){t=t.trim();return!!/^\d{8}$/.test(t)},"zh-CN":function(t){function n(t){return r.includes(t)}function i(t){var e=parseInt(t.substring(0,4),10),r=parseInt(t.substring(4,6),10),n=parseInt(t.substring(6),10);return!((t=new Date(e,r-1,n))>new Date)&&(t.getFullYear()===e&&t.getMonth()===r-1&&t.getDate()===n)}function a(t){return function(t){for(var e=t.substring(0,17),r=0,n=0;n<17;n++)r+=parseInt(e.charAt(n),10)*parseInt(o[n],10);return s[r%11]}(t)===t.charAt(17).toUpperCase()}var e,r=["11","12","13","14","15","21","22","23","31","32","33","34","35","36","37","41","42","43","44","45","46","50","51","52","53","54","61","62","63","64","65","71","81","82","91"],o=["7","9","10","5","8","4","2","1","6","3","7","9","10","5","8","4","2"],s=["1","0","X","9","8","7","6","5","4","3","2"];return!!/^\d{15}|(\d{17}(\d|x|X))$/.test(e=t)&&(15===e.length?function(t){var e=/^[1-9]\d{7}((0[1-9])|(1[0-2]))((0[1-9])|([1-2][0-9])|(3[0-1]))\d{3}$/.test(t);if(!e)return!1;var r=t.substring(0,2);if(!(e=n(r)))return!1;t="19".concat(t.substring(6,12));return!!(e=i(t))}:function(t){var e=/^[1-9]\d{5}[1-9]\d{3}((0[1-9])|(1[0-2]))((0[1-9])|([1-2][0-9])|(3[0-1]))\d{3}(\d|x|X)$/.test(t);if(!e)return!1;var r=t.substring(0,2);if(!(e=n(r)))return!1;r=t.substring(6,14);return!!(e=i(r))&&a(t)})(e)},"zh-TW":function(t){var n={A:10,B:11,C:12,D:13,E:14,F:15,G:16,H:17,I:34,J:18,K:19,L:20,M:21,N:22,O:35,P:23,Q:24,R:25,S:26,T:27,U:28,V:29,W:32,X:30,Y:31,Z:33},t=t.trim().toUpperCase();return!!/^[A-Z][0-9]{9}$/.test(t)&&Array.from(t).reduce(function(t,e,r){if(0!==r)return 9===r?(10-t%10-Number(e))%10==0:t+Number(e)*(9-r);e=n[e];return e%10*9+Math.floor(e/10)},0)}};var Ht=8,Kt=/^(\d{8}|\d{13})$/;function Vt(r){var t=10-r.slice(0,-1).split("").map(function(t,e){return Number(t)*(t=r.length,e=e,t===Ht?e%2==0?3:1:e%2==0?1:3)}).reduce(function(t,e){return t+e},0)%10;return t<10?t:0}var Wt=/^[A-Z]{2}[0-9A-Z]{9}[0-9]$/;var zt=/^(?:[0-9]{9}X|[0-9]{10})$/,jt=/^(?:[0-9]{13})$/,Jt=[1,3];function Xt(t){for(var e=10,r=0;r<t.length-1;r++)e=(parseInt(t[r],10)+e)%10==0?9:(parseInt(t[r],10)+e)%10*2%11;return(e=1===e?0:11-e)===parseInt(t[10],10)}function qt(t){for(var e,r=0,n=!1,i=t.length-1;0<=i;i--)r+=n?9<(e=2*parseInt(t[i],10))?e.toString().split("").map(function(t){return parseInt(t,10)}).reduce(function(t,e){return t+e},0):e:parseInt(t[i],10),n=!n;return r%10==0}function Qt(t,e){for(var r=0,n=0;n<t.length;n++)r+=t[n]*(e-n);return r}var te={andover:["10","12"],atlanta:["60","67"],austin:["50","53"],brookhaven:["01","02","03","04","05","06","11","13","14","16","21","22","23","25","34","51","52","54","55","56","57","58","59","65"],cincinnati:["30","32","35","36","37","38","61"],fresno:["15","24"],internet:["20","26","27","45","46","47"],kansas:["40","44"],memphis:["94","95"],ogden:["80","90"],philadelphia:["33","39","41","42","43","46","48","62","63","64","66","68","71","72","73","74","75","76","77","81","82","83","84","85","86","87","88","91","92","93","98","99"],sba:["31"]};function ee(t){for(var e=!1,r=!1,n=0;n<3;n++)if(!e&&/[AEIOU]/.test(t[n]))e=!0;else if(!r&&e&&"X"===t[n])r=!0;else if(0<n){if(e&&!r&&!/[AEIOU]/.test(t[n]))return;if(r&&!/X/.test(t[n]))return}return 1}var re={"bg-BG":/^\d{10}$/,"cs-CZ":/^\d{6}\/{0,1}\d{3,4}$/,"de-AT":/^\d{9}$/,"de-DE":/^[1-9]\d{10}$/,"dk-DK":/^\d{6}-{0,1}\d{4}$/,"el-CY":/^[09]\d{7}[A-Z]$/,"el-GR":/^([0-4]|[7-9])\d{8}$/,"en-GB":/^\d{10}$|^(?!GB|NK|TN|ZZ)(?![DFIQUV])[A-Z](?![DFIQUVO])[A-Z]\d{6}[ABCD ]$/i,"en-IE":/^\d{7}[A-W][A-IW]{0,1}$/i,"en-US":/^\d{2}[- ]{0,1}\d{7}$/,"es-ES":/^(\d{0,8}|[XYZKLM]\d{7})[A-HJ-NP-TV-Z]$/i,"et-EE":/^[1-6]\d{6}(00[1-9]|0[1-9][0-9]|[1-6][0-9]{2}|70[0-9]|710)\d$/,"fi-FI":/^\d{6}[-+A]\d{3}[0-9A-FHJ-NPR-Y]$/i,"fr-BE":/^\d{11}$/,"fr-FR":/^[0-3]\d{12}$|^[0-3]\d\s\d{2}(\s\d{3}){3}$/,"fr-LU":/^\d{13}$/,"hr-HR":/^\d{11}$/,"hu-HU":/^8\d{9}$/,"it-IT":/^[A-Z]{6}[L-NP-V0-9]{2}[A-EHLMPRST][L-NP-V0-9]{2}[A-ILMZ][L-NP-V0-9]{3}[A-Z]$/i,"lv-LV":/^\d{6}-{0,1}\d{5}$/,"mt-MT":/^\d{3,7}[APMGLHBZ]$|^([1-8])\1\d{7}$/i,"nl-NL":/^\d{9}$/,"pl-PL":/^\d{10,11}$/,"pt-PT":/^\d{9}$/,"ro-RO":/^\d{13}$/,"sk-SK":/^\d{6}\/{0,1}\d{3,4}$/,"sl-SI":/^[1-9]\d{7}$/,"sv-SE":/^(\d{6}[-+]{0,1}\d{4}|(18|19|20)\d{6}[-+]{0,1}\d{4})$/};re["lb-LU"]=re["fr-LU"],re["lt-LT"]=re["et-EE"],re["nl-BE"]=re["fr-BE"];var ne={"bg-BG":function(t){var e=t.slice(0,2),r=parseInt(t.slice(2,4),10),e=40<r?(r-=40,"20".concat(e)):20<r?(r-=20,"18".concat(e)):"19".concat(e);if(r<10&&(r="0".concat(r)),!q("".concat(e,"/").concat(r,"/").concat(t.slice(4,6)),"YYYY/MM/DD"))return!1;for(var n=t.split("").map(function(t){return parseInt(t,10)}),i=[2,4,8,5,10,9,7,3,6],a=0,o=0;o<i.length;o++)a+=n[o]*i[o];return(a=a%11==10?0:a%11)===n[9]},"cs-CZ":function(t){t=t.replace(/\W/,"");var e=parseInt(t.slice(0,2),10);if(10===t.length)e=(e<54?"20":"19").concat(e);else{if("000"===t.slice(6))return!1;if(!(e<54))return!1;e="19".concat(e)}3===e.length&&(e=[e.slice(0,2),"0",e.slice(2)].join(""));var r=parseInt(t.slice(2,4),10);if(50<r&&(r-=50),20<r){if(parseInt(e,10)<2004)return!1;r-=20}if(r<10&&(r="0".concat(r)),!q("".concat(e,"/").concat(r,"/").concat(t.slice(4,6)),"YYYY/MM/DD"))return!1;if(10===t.length&&parseInt(t,10)%11!=0){r=parseInt(t.slice(0,9),10)%11;if(!(parseInt(e,10)<1986&&10==r))return!1;if(0!==parseInt(t.slice(9),10))return!1}return!0},"de-AT":qt,"de-DE":function(t){for(var e=t.split("").map(function(t){return parseInt(t,10)}),r=[],n=0;n<e.length-1;n++){r.push("");for(var i=0;i<e.length-1;i++)e[n]===e[i]&&(r[n]+=i)}if(2!==(r=r.filter(function(t){return 1<t.length})).length&&3!==r.length)return!1;if(3===r[0].length){for(var a=r[0].split("").map(function(t){return parseInt(t,10)}),o=0,s=0;s<a.length-1;s++)a[s]+1===a[s+1]&&(o+=1);if(2===o)return!1}return Xt(t)},"dk-DK":function(t){t=t.replace(/\W/,"");var e=parseInt(t.slice(4,6),10);switch(t.slice(6,7)){case"0":case"1":case"2":case"3":e="19".concat(e);break;case"4":case"9":e=(e<37?"20":"19").concat(e);break;default:if(e<37)e="20".concat(e);else{if(!(58<e))return!1;e="18".concat(e)}}if(3===e.length&&(e=[e.slice(0,2),"0",e.slice(2)].join("")),!q("".concat(e,"/").concat(t.slice(2,4),"/").concat(t.slice(0,2)),"YYYY/MM/DD"))return!1;for(var r=t.split("").map(function(t){return parseInt(t,10)}),n=0,i=4,a=0;a<9;a++)n+=r[a]*i,1===--i&&(i=7);return 1!==(n%=11)&&(0===n?0===r[9]:r[9]===11-n)},"el-CY":function(t){for(var e=t.slice(0,8).split("").map(function(t){return parseInt(t,10)}),r=0,n=1;n<e.length;n+=2)r+=e[n];for(var i=0;i<e.length;i+=2)e[i]<2?r+=1-e[i]:(r+=2*(e[i]-2)+5,4<e[i]&&(r+=2));return String.fromCharCode(r%26+65)===t.charAt(8)},"el-GR":function(t){for(var e=t.split("").map(function(t){return parseInt(t,10)}),r=0,n=0;n<8;n++)r+=e[n]*Math.pow(2,8-n);return r%11===e[8]},"en-IE":function(t){var e=Qt(t.split("").slice(0,7).map(function(t){return parseInt(t,10)}),8);return 9===t.length&&"W"!==t[8]&&(e+=9*(t[8].charCodeAt(0)-64)),0===(e%=23)?"W"===t[7].toUpperCase():t[7].toUpperCase()===String.fromCharCode(64+e)},"en-US":function(t){return-1!==function(){var t,e=[];for(t in te)te.hasOwnProperty(t)&&e.push.apply(e,r(te[t]));return e}().indexOf(t.substr(0,2))},"es-ES":function(t){var e=t.toUpperCase().split("");if(isNaN(parseInt(e[0],10))&&1<e.length){var r=0;switch(e[0]){case"Y":r=1;break;case"Z":r=2}e.splice(0,1,r)}else for(;e.length<9;)e.unshift(0);return e=e.join(""),t=parseInt(e.slice(0,8),10)%23,e[8]===["T","R","W","A","G","M","Y","F","P","D","X","B","N","J","Z","S","Q","V","H","L","C","K","E"][t]},"et-EE":function(t){var e=t.slice(1,3);switch(t.slice(0,1)){case"1":case"2":e="18".concat(e);break;case"3":case"4":e="19".concat(e);break;default:e="20".concat(e)}if(!q("".concat(e,"/").concat(t.slice(3,5),"/").concat(t.slice(5,7)),"YYYY/MM/DD"))return!1;for(var r=t.split("").map(function(t){return parseInt(t,10)}),n=0,i=1,a=0;a<10;a++)n+=r[a]*i,10===(i+=1)&&(i=1);if(n%11==10){i=3;for(var o=n=0;o<10;o++)n+=r[o]*i,10===(i+=1)&&(i=1);if(n%11==10)return 0===r[10]}return n%11===r[10]},"fi-FI":function(t){var e=t.slice(4,6);switch(t.slice(6,7)){case"+":e="18".concat(e);break;case"-":e="19".concat(e);break;default:e="20".concat(e)}if(!q("".concat(e,"/").concat(t.slice(2,4),"/").concat(t.slice(0,2)),"YYYY/MM/DD"))return!1;var r=parseInt(t.slice(0,6)+t.slice(7,10),10)%31;return r<10?r===parseInt(t.slice(10),10):["A","B","C","D","E","F","H","J","K","L","M","N","P","R","S","T","U","V","W","X","Y"][r-=10]===t.slice(10)},"fr-BE":function(t){if(("00"!==t.slice(2,4)||"00"!==t.slice(4,6))&&!q("".concat(t.slice(0,2),"/").concat(t.slice(2,4),"/").concat(t.slice(4,6)),"YY/MM/DD"))return!1;var e=97-parseInt(t.slice(0,9),10)%97,r=parseInt(t.slice(9,11),10);return e===r||97-parseInt("2".concat(t.slice(0,9)),10)%97===r},"fr-FR":function(t){return t=t.replace(/\s/g,""),parseInt(t.slice(0,10),10)%511===parseInt(t.slice(10,13),10)},"fr-LU":function(t){return!!q("".concat(t.slice(0,4),"/").concat(t.slice(4,6),"/").concat(t.slice(6,8)),"YYYY/MM/DD")&&(!!qt(t.slice(0,12))&&function(t){for(var e=[[0,1,2,3,4,5,6,7,8,9],[1,2,3,4,0,6,7,8,9,5],[2,3,4,0,1,7,8,9,5,6],[3,4,0,1,2,8,9,5,6,7],[4,0,1,2,3,9,5,6,7,8],[5,9,8,7,6,0,4,3,2,1],[6,5,9,8,7,1,0,4,3,2],[7,6,5,9,8,2,1,0,4,3],[8,7,6,5,9,3,2,1,0,4],[9,8,7,6,5,4,3,2,1,0]],r=[[0,1,2,3,4,5,6,7,8,9],[1,5,7,6,2,8,3,0,9,4],[5,8,0,3,7,9,6,1,4,2],[8,9,1,6,0,4,3,5,2,7],[9,4,5,3,1,2,6,8,7,0],[4,2,8,6,5,7,3,9,0,1],[2,7,9,3,8,0,6,4,1,5],[7,0,4,6,9,1,3,2,5,8]],n=t.split("").reverse().join(""),i=0,a=0;a<n.length;a++)i=e[i][r[a%8][parseInt(n[a],10)]];return 0===i}("".concat(t.slice(0,11)).concat(t[12])))},"hr-HR":Xt,"hu-HU":function(t){for(var e=t.split("").map(function(t){return parseInt(t,10)}),r=8,n=1;n<9;n++)r+=e[n]*(n+1);return r%11===e[9]},"it-IT":function(t){var e=t.toUpperCase().split("");if(!ee(e.slice(0,3)))return!1;if(!ee(e.slice(3,6)))return!1;for(var r={L:"0",M:"1",N:"2",P:"3",Q:"4",R:"5",S:"6",T:"7",U:"8",V:"9"},n=0,i=[6,7,9,10,12,13,14];n<i.length;n++){var a=i[n];e[a]in r&&e.splice(a,1,r[e[a]])}var o={A:"01",B:"02",C:"03",D:"04",E:"05",H:"06",L:"07",M:"08",P:"09",R:"10",S:"11",T:"12"}[e[8]];if(40<(t=parseInt(e[9]+e[10],10))&&(t-=40),t<10&&(t="0".concat(t)),!q("".concat(e[6]).concat(e[7],"/").concat(o,"/").concat(t),"YY/MM/DD"))return!1;for(var s=0,c=1;c<e.length-1;c+=2){var l=parseInt(e[c],10);isNaN(l)&&(l=e[c].charCodeAt(0)-65),s+=l}for(var u={A:1,B:0,C:5,D:7,E:9,F:13,G:15,H:17,I:19,J:21,K:2,L:4,M:18,N:20,O:11,P:3,Q:6,R:8,S:12,T:14,U:16,V:10,W:22,X:25,Y:24,Z:23,0:1,1:0},d=0;d<e.length-1;d+=2){var f,p=0;e[d]in u?p=u[e[d]]:(p=2*(f=parseInt(e[d],10))+1,4<f&&(p+=2)),s+=p}return String.fromCharCode(65+s%26)===e[15]},"lv-LV":function(t){var e=(t=t.replace(/\W/,"")).slice(0,2);if("32"===e)return!0;if("00"!==t.slice(2,4)){var r=t.slice(4,6);switch(t[6]){case"0":r="18".concat(r);break;case"1":r="19".concat(r);break;default:r="20".concat(r)}if(!q("".concat(r,"/").concat(t.slice(2,4),"/").concat(e),"YYYY/MM/DD"))return!1}for(var n=1101,i=[1,6,3,7,9,10,5,8,4,2],a=0;a<t.length-1;a++)n-=parseInt(t[a],10)*i[a];return parseInt(t[10],10)===n%11},"mt-MT":function(t){if(9!==t.length){for(var e=t.toUpperCase().split("");e.length<8;)e.unshift(0);switch(t[7]){case"A":case"P":if(0===parseInt(e[6],10))return!1;break;default:var r=parseInt(e.join("").slice(0,5),10);if(32e3<r)return!1;if(r===parseInt(e.join("").slice(5,7),10))return!1}}return!0},"nl-NL":function(t){return Qt(t.split("").slice(0,8).map(function(t){return parseInt(t,10)}),9)%11===parseInt(t[8],10)},"pl-PL":function(t){if(10===t.length){for(var e=[6,5,7,2,3,4,5,6,7],r=0,n=0;n<e.length;n++)r+=parseInt(t[n],10)*e[n];return 10!==(r%=11)&&r===parseInt(t[9],10)}var i=t.slice(0,2),a=parseInt(t.slice(2,4),10);if(80<a?(i="18".concat(i),a-=80):60<a?(i="22".concat(i),a-=60):40<a?(i="21".concat(i),a-=40):20<a?(i="20".concat(i),a-=20):i="19".concat(i),a<10&&(a="0".concat(a)),!q("".concat(i,"/").concat(a,"/").concat(t.slice(4,6)),"YYYY/MM/DD"))return!1;for(var o=0,s=1,c=0;c<t.length-1;c++)o+=parseInt(t[c],10)*s%10,10<(s+=2)?s=1:5===s&&(s+=2);return(o=10-o%10)===parseInt(t[10],10)},"pt-PT":function(t){var e=11-Qt(t.split("").slice(0,8).map(function(t){return parseInt(t,10)}),9)%11;return 9<e?0===parseInt(t[8],10):e===parseInt(t[8],10)},"ro-RO":function(t){if("9000"===t.slice(0,4))return!0;var e=t.slice(1,3);switch(t[0]){case"1":case"2":e="19".concat(e);break;case"3":case"4":e="18".concat(e);break;case"5":case"6":e="20".concat(e)}var r="".concat(e,"/").concat(t.slice(3,5),"/").concat(t.slice(5,7));if(8===r.length){if(!q(r,"YY/MM/DD"))return!1}else if(!q(r,"YYYY/MM/DD"))return!1;for(var n=t.split("").map(function(t){return parseInt(t,10)}),i=[2,7,9,1,4,6,3,5,8,2,7,9],a=0,o=0;o<i.length;o++)a+=n[o]*i[o];return a%11==10?1===n[12]:n[12]===a%11},"sk-SK":function(t){if(9===t.length){if("000"===(t=t.replace(/\W/,"")).slice(6))return!1;var e=parseInt(t.slice(0,2),10);if(53<e)return!1;e=(e<10?"190":"19").concat(e);var r=parseInt(t.slice(2,4),10);if(50<r&&(r-=50),r<10&&(r="0".concat(r)),!q("".concat(e,"/").concat(r,"/").concat(t.slice(4,6)),"YYYY/MM/DD"))return!1}return!0},"sl-SI":function(t){var e=11-Qt(t.split("").slice(0,7).map(function(t){return parseInt(t,10)}),8)%11;return 10==e?0===parseInt(t[7],10):e===parseInt(t[7],10)},"sv-SE":function(t){var e=t.slice(0);11<t.length&&(e=e.slice(2));var r="",n=e.slice(2,4),i=parseInt(e.slice(4,6),10);if(11<t.length)r=t.slice(0,4);else if(r=t.slice(0,2),11===t.length&&i<60){var a=(new Date).getFullYear().toString(),e=parseInt(a.slice(0,2),10),a=parseInt(a,10);if("-"===t[6])r=(parseInt("".concat(e).concat(r),10)>a?"".concat(e-1):"".concat(e)).concat(r);else if(r="".concat(e-1).concat(r),a-parseInt(r,10)<100)return!1}if(60<i&&(i-=60),i<10&&(i="0".concat(i)),8===(i="".concat(r,"/").concat(n,"/").concat(i)).length){if(!q(i,"YY/MM/DD"))return!1}else if(!q(i,"YYYY/MM/DD"))return!1;return qt(t.replace(/\W/,""))}};ne["lb-LU"]=ne["fr-LU"],ne["lt-LT"]=ne["et-EE"],ne["nl-BE"]=ne["fr-BE"];var ie=/[-\\\/!@#$%\^&\*\(\)\+\=\[\]]+/g,ae={"de-AT":ie,"de-DE":/[\/\\]/g,"fr-BE":ie};ae["nl-BE"]=ae["fr-BE"];var oe={"am-AM":/^(\+?374|0)((10|[9|7][0-9])\d{6}$|[2-4]\d{7}$)/,"ar-AE":/^((\+?971)|0)?5[024568]\d{7}$/,"ar-BH":/^(\+?973)?(3|6)\d{7}$/,"ar-DZ":/^(\+?213|0)(5|6|7)\d{8}$/,"ar-LB":/^(\+?961)?((3|81)\d{6}|7\d{7})$/,"ar-EG":/^((\+?20)|0)?1[0125]\d{8}$/,"ar-IQ":/^(\+?964|0)?7[0-9]\d{8}$/,"ar-JO":/^(\+?962|0)?7[789]\d{7}$/,"ar-KW":/^(\+?965)[569]\d{7}$/,"ar-LY":/^((\+?218)|0)?(9[1-6]\d{7}|[1-8]\d{7,9})$/,"ar-MA":/^(?:(?:\+|00)212|0)[5-7]\d{8}$/,"ar-SA":/^(!?(\+?966)|0)?5\d{8}$/,"ar-SY":/^(!?(\+?963)|0)?9\d{8}$/,"ar-TN":/^(\+?216)?[2459]\d{7}$/,"az-AZ":/^(\+994|0)(5[015]|7[07]|99)\d{7}$/,"bs-BA":/^((((\+|00)3876)|06))((([0-3]|[5-6])\d{6})|(4\d{7}))$/,"be-BY":/^(\+?375)?(24|25|29|33|44)\d{7}$/,"bg-BG":/^(\+?359|0)?8[789]\d{7}$/,"bn-BD":/^(\+?880|0)1[13456789][0-9]{8}$/,"ca-AD":/^(\+376)?[346]\d{5}$/,"cs-CZ":/^(\+?420)? ?[1-9][0-9]{2} ?[0-9]{3} ?[0-9]{3}$/,"da-DK":/^(\+?45)?\s?\d{2}\s?\d{2}\s?\d{2}\s?\d{2}$/,"de-DE":/^(\+49)?0?[1|3]([0|5][0-45-9]\d|6([23]|0\d?)|7([0-57-9]|6\d))\d{7}$/,"de-AT":/^(\+43|0)\d{1,4}\d{3,12}$/,"de-CH":/^(\+41|0)(7[5-9])\d{1,7}$/,"de-LU":/^(\+352)?((6\d1)\d{6})$/,"el-GR":/^(\+?30|0)?(69\d{8})$/,"en-AU":/^(\+?61|0)4\d{8}$/,"en-GB":/^(\+?44|0)7\d{9}$/,"en-GG":/^(\+?44|0)1481\d{6}$/,"en-GH":/^(\+233|0)(20|50|24|54|27|57|26|56|23|28)\d{7}$/,"en-HK":/^(\+?852[-\s]?)?[456789]\d{3}[-\s]?\d{4}$/,"en-MO":/^(\+?853[-\s]?)?[6]\d{3}[-\s]?\d{4}$/,"en-IE":/^(\+?353|0)8[356789]\d{7}$/,"en-IN":/^(\+?91|0)?[6789]\d{9}$/,"en-KE":/^(\+?254|0)(7|1)\d{8}$/,"en-MT":/^(\+?356|0)?(99|79|77|21|27|22|25)[0-9]{6}$/,"en-MU":/^(\+?230|0)?\d{8}$/,"en-NG":/^(\+?234|0)?[789]\d{9}$/,"en-NZ":/^(\+?64|0)[28]\d{7,9}$/,"en-PK":/^((\+92)|(0092))-{0,1}\d{3}-{0,1}\d{7}$|^\d{11}$|^\d{4}-\d{7}$/,"en-PH":/^(09|\+639)\d{9}$/,"en-RW":/^(\+?250|0)?[7]\d{8}$/,"en-SG":/^(\+65)?[689]\d{7}$/,"en-SL":/^(?:0|94|\+94)?(7(0|1|2|5|6|7|8)( |-)?\d)\d{6}$/,"en-TZ":/^(\+?255|0)?[67]\d{8}$/,"en-UG":/^(\+?256|0)?[7]\d{8}$/,"en-US":/^((\+1|1)?( |-)?)?(\([2-9][0-9]{2}\)|[2-9][0-9]{2})( |-)?([2-9][0-9]{2}( |-)?[0-9]{4})$/,"en-ZA":/^(\+?27|0)\d{9}$/,"en-ZM":/^(\+?26)?09[567]\d{7}$/,"en-ZW":/^(\+263)[0-9]{9}$/,"es-AR":/^\+?549(11|[2368]\d)\d{8}$/,"es-BO":/^(\+?591)?(6|7)\d{7}$/,"es-CO":/^(\+?57)?([1-8]{1}|3[0-9]{2})?[2-9]{1}\d{6}$/,"es-CL":/^(\+?56|0)[2-9]\d{1}\d{7}$/,"es-CR":/^(\+506)?[2-8]\d{7}$/,"es-DO":/^(\+?1)?8[024]9\d{7}$/,"es-HN":/^(\+?504)?[9|8]\d{7}$/,"es-EC":/^(\+?593|0)([2-7]|9[2-9])\d{7}$/,"es-ES":/^(\+?34)?[6|7]\d{8}$/,"es-PE":/^(\+?51)?9\d{8}$/,"es-MX":/^(\+?52)?(1|01)?\d{10,11}$/,"es-PA":/^(\+?507)\d{7,8}$/,"es-PY":/^(\+?595|0)9[9876]\d{7}$/,"es-UY":/^(\+598|0)9[1-9][\d]{6}$/,"et-EE":/^(\+?372)?\s?(5|8[1-4])\s?([0-9]\s?){6,7}$/,"fa-IR":/^(\+?98[\-\s]?|0)9[0-39]\d[\-\s]?\d{3}[\-\s]?\d{4}$/,"fi-FI":/^(\+?358|0)\s?(4(0|1|2|4|5|6)?|50)\s?(\d\s?){4,8}\d$/,"fj-FJ":/^(\+?679)?\s?\d{3}\s?\d{4}$/,"fo-FO":/^(\+?298)?\s?\d{2}\s?\d{2}\s?\d{2}$/,"fr-FR":/^(\+?33|0)[67]\d{8}$/,"fr-GF":/^(\+?594|0|00594)[67]\d{8}$/,"fr-GP":/^(\+?590|0|00590)[67]\d{8}$/,"fr-MQ":/^(\+?596|0|00596)[67]\d{8}$/,"fr-RE":/^(\+?262|0|00262)[67]\d{8}$/,"he-IL":/^(\+972|0)([23489]|5[012345689]|77)[1-9]\d{6}$/,"hu-HU":/^(\+?36)(20|30|70)\d{7}$/,"id-ID":/^(\+?62|0)8(1[123456789]|2[1238]|3[1238]|5[12356789]|7[78]|9[56789]|8[123456789])([\s?|\d]{5,11})$/,"it-IT":/^(\+?39)?\s?3\d{2} ?\d{6,7}$/,"it-SM":/^((\+378)|(0549)|(\+390549)|(\+3780549))?6\d{5,9}$/,"ja-JP":/^(\+81[ \-]?(\(0\))?|0)[6789]0[ \-]?\d{4}[ \-]?\d{4}$/,"ka-GE":/^(\+?995)?(5|79)\d{7}$/,"kk-KZ":/^(\+?7|8)?7\d{9}$/,"kl-GL":/^(\+?299)?\s?\d{2}\s?\d{2}\s?\d{2}$/,"ko-KR":/^((\+?82)[ \-]?)?0?1([0|1|6|7|8|9]{1})[ \-]?\d{3,4}[ \-]?\d{4}$/,"lt-LT":/^(\+370|8)\d{8}$/,"ms-MY":/^(\+?6?01){1}(([0145]{1}(\-|\s)?\d{7,8})|([236789]{1}(\s|\-)?\d{7}))$/,"nb-NO":/^(\+?47)?[49]\d{7}$/,"ne-NP":/^(\+?977)?9[78]\d{8}$/,"nl-BE":/^(\+?32|0)4?\d{8}$/,"nl-NL":/^(((\+|00)?31\(0\))|((\+|00)?31)|0)6{1}\d{8}$/,"nn-NO":/^(\+?47)?[49]\d{7}$/,"pl-PL":/^(\+?48)? ?[5-8]\d ?\d{3} ?\d{2} ?\d{2}$/,"pt-BR":/^((\+?55\ ?[1-9]{2}\ ?)|(\+?55\ ?\([1-9]{2}\)\ ?)|(0[1-9]{2}\ ?)|(\([1-9]{2}\)\ ?)|([1-9]{2}\ ?))((\d{4}\-?\d{4})|(9[2-9]{1}\d{3}\-?\d{4}))$/,"pt-PT":/^(\+?351)?9[1236]\d{7}$/,"ro-RO":/^(\+?4?0)\s?7\d{2}(\/|\s|\.|\-)?\d{3}(\s|\.|\-)?\d{3}$/,"ru-RU":/^(\+?7|8)?9\d{9}$/,"sl-SI":/^(\+386\s?|0)(\d{1}\s?\d{3}\s?\d{2}\s?\d{2}|\d{2}\s?\d{3}\s?\d{3})$/,"sk-SK":/^(\+?421)? ?[1-9][0-9]{2} ?[0-9]{3} ?[0-9]{3}$/,"sq-AL":/^(\+355|0)6[789]\d{6}$/,"sr-RS":/^(\+3816|06)[- \d]{5,9}$/,"sv-SE":/^(\+?46|0)[\s\-]?7[\s\-]?[02369]([\s\-]?\d){7}$/,"th-TH":/^(\+66|66|0)\d{9}$/,"tr-TR":/^(\+?90|0)?5\d{9}$/,"uk-UA":/^(\+?38|8)?0\d{9}$/,"uz-UZ":/^(\+?998)?(6[125-79]|7[1-69]|88|9\d)\d{7}$/,"vi-VN":/^(\+?84|0)((3([2-9]))|(5([2689]))|(7([0|6-9]))|(8([1-6|89]))|(9([0-9])))([0-9]{7})$/,"zh-CN":/^((\+|00)86)?1([3568][0-9]|4[579]|6[67]|7[01235678]|9[012356789])[0-9]{8}$/,"zh-TW":/^(\+?886\-?|0)?9\d{8}$/};oe["en-CA"]=oe["en-US"],oe["fr-CA"]=oe["en-CA"],oe["fr-BE"]=oe["nl-BE"],oe["zh-HK"]=oe["en-HK"],oe["zh-MO"]=oe["en-MO"],oe["ga-IE"]=oe["en-IE"];var se=Object.keys(oe),ce=/^(0x)[0-9a-f]{40}$/i;var le={symbol:"$",require_symbol:!1,allow_space_after_symbol:!1,symbol_after_digits:!1,allow_negatives:!0,parens_for_negatives:!1,negative_sign_before_digits:!1,negative_sign_after_digits:!1,allow_negative_sign_placeholder:!1,thousands_separator:",",decimal_separator:".",allow_decimal:!0,require_decimal:!1,digits_after_decimal:[2],allow_space_after_digits:!1};var ue=/^(bc1|[13])[a-zA-HJ-NP-Z0-9]{25,39}$/;var de=/^([\+-]?\d{4}(?!\d{2}\b))((-?)((0[1-9]|1[0-2])(\3([12]\d|0[1-9]|3[01]))?|W([0-4]\d|5[0-3])(-?[1-7])?|(00[1-9]|0[1-9]\d|[12]\d{2}|3([0-5]\d|6[1-6])))([T\s]((([01]\d|2[0-3])((:?)[0-5]\d)?|24:?00)([\.,]\d+(?!:))?)?(\17[0-5]\d([\.,]\d+)?)?([zZ]|([\+-])([01]\d|2[0-3]):?([0-5]\d)?)?)?)?$/,fe=/^([\+-]?\d{4}(?!\d{2}\b))((-?)((0[1-9]|1[0-2])(\3([12]\d|0[1-9]|3[01]))?|W([0-4]\d|5[0-3])(-?[1-7])?|(00[1-9]|0[1-9]\d|[12]\d{2}|3([0-5]\d|6[1-6])))([T]((([01]\d|2[0-3])((:?)[0-5]\d)?|24:?00)([\.,]\d+(?!:))?)?(\17[0-5]\d([\.,]\d+)?)?([zZ]|([\+-])([01]\d|2[0-3]):?([0-5]\d)?)?)?)?$/;var pe=/([01][0-9]|2[0-3])/,$e=/[0-5][0-9]/,ie=new RegExp("[-+]".concat(pe.source,":").concat($e.source)),ie=new RegExp("([zZ]|".concat(ie.source,")")),pe=new RegExp("".concat(pe.source,":").concat($e.source,":").concat(/([0-5][0-9]|60)/.source).concat(/(\.[0-9]+)?/.source)),$e=new RegExp("".concat(/[0-9]{4}/.source,"-").concat(/(0[1-9]|1[0-2])/.source,"-").concat(/([12]\d|0[1-9]|3[01])/.source)),pe=new RegExp("".concat(pe.source).concat(ie.source)),Ae=new RegExp("".concat($e.source,"[ tT]").concat(pe.source));var he=["AD","AE","AF","AG","AI","AL","AM","AO","AQ","AR","AS","AT","AU","AW","AX","AZ","BA","BB","BD","BE","BF","BG","BH","BI","BJ","BL","BM","BN","BO","BQ","BR","BS","BT","BV","BW","BY","BZ","CA","CC","CD","CF","CG","CH","CI","CK","CL","CM","CN","CO","CR","CU","CV","CW","CX","CY","CZ","DE","DJ","DK","DM","DO","DZ","EC","EE","EG","EH","ER","ES","ET","FI","FJ","FK","FM","FO","FR","GA","GB","GD","GE","GF","GG","GH","GI","GL","GM","GN","GP","GQ","GR","GS","GT","GU","GW","GY","HK","HM","HN","HR","HT","HU","ID","IE","IL","IM","IN","IO","IQ","IR","IS","IT","JE","JM","JO","JP","KE","KG","KH","KI","KM","KN","KP","KR","KW","KY","KZ","LA","LB","LC","LI","LK","LR","LS","LT","LU","LV","LY","MA","MC","MD","ME","MF","MG","MH","MK","ML","MM","MN","MO","MP","MQ","MR","MS","MT","MU","MV","MW","MX","MY","MZ","NA","NC","NE","NF","NG","NI","NL","NO","NP","NR","NU","NZ","OM","PA","PE","PF","PG","PH","PK","PL","PM","PN","PR","PS","PT","PW","PY","QA","RE","RO","RS","RU","RW","SA","SB","SC","SD","SE","SG","SH","SI","SJ","SK","SL","SM","SN","SO","SR","SS","ST","SV","SX","SY","SZ","TC","TD","TF","TG","TH","TJ","TK","TL","TM","TN","TO","TR","TT","TV","TW","TZ","UA","UG","UM","US","UY","UZ","VA","VC","VE","VG","VI","VN","VU","WF","WS","YE","YT","ZA","ZM","ZW"];var ge=["AFG","ALA","ALB","DZA","ASM","AND","AGO","AIA","ATA","ATG","ARG","ARM","ABW","AUS","AUT","AZE","BHS","BHR","BGD","BRB","BLR","BEL","BLZ","BEN","BMU","BTN","BOL","BES","BIH","BWA","BVT","BRA","IOT","BRN","BGR","BFA","BDI","KHM","CMR","CAN","CPV","CYM","CAF","TCD","CHL","CHN","CXR","CCK","COL","COM","COG","COD","COK","CRI","CIV","HRV","CUB","CUW","CYP","CZE","DNK","DJI","DMA","DOM","ECU","EGY","SLV","GNQ","ERI","EST","ETH","FLK","FRO","FJI","FIN","FRA","GUF","PYF","ATF","GAB","GMB","GEO","DEU","GHA","GIB","GRC","GRL","GRD","GLP","GUM","GTM","GGY","GIN","GNB","GUY","HTI","HMD","VAT","HND","HKG","HUN","ISL","IND","IDN","IRN","IRQ","IRL","IMN","ISR","ITA","JAM","JPN","JEY","JOR","KAZ","KEN","KIR","PRK","KOR","KWT","KGZ","LAO","LVA","LBN","LSO","LBR","LBY","LIE","LTU","LUX","MAC","MKD","MDG","MWI","MYS","MDV","MLI","MLT","MHL","MTQ","MRT","MUS","MYT","MEX","FSM","MDA","MCO","MNG","MNE","MSR","MAR","MOZ","MMR","NAM","NRU","NPL","NLD","NCL","NZL","NIC","NER","NGA","NIU","NFK","MNP","NOR","OMN","PAK","PLW","PSE","PAN","PNG","PRY","PER","PHL","PCN","POL","PRT","PRI","QAT","REU","ROU","RUS","RWA","BLM","SHN","KNA","LCA","MAF","SPM","VCT","WSM","SMR","STP","SAU","SEN","SRB","SYC","SLE","SGP","SXM","SVK","SVN","SLB","SOM","ZAF","SGS","SSD","ESP","LKA","SDN","SUR","SJM","SWZ","SWE","CHE","SYR","TWN","TJK","TZA","THA","TLS","TGO","TKL","TON","TTO","TUN","TUR","TKM","TCA","TUV","UGA","UKR","ARE","GBR","USA","UMI","URY","UZB","VUT","VEN","VNM","VGB","VIR","WLF","ESH","YEM","ZMB","ZWE"];var me=/^[A-Z2-7]+=*$/;var ve=/^[A-HJ-NP-Za-km-z1-9]*$/;var Ie=/^[a-z]+\/[a-z0-9\-\+]+$/i,Se=/^[a-z\-]+=[a-z0-9\-]+$/i,Ze=/^[a-z0-9!\$&'\(\)\*\+,;=\-\._~:@\/\?%\s]*$/i;var Ee=/^magnet:\?xt=urn:[a-z0-9]+:[a-z0-9]{32,40}&dn=.+&tr=.+$/i;var Ce=/^(application|audio|font|image|message|model|multipart|text|video)\/[a-zA-Z0-9\.\-\+]{1,100}$/i,Fe=/^text\/[a-zA-Z0-9\.\-\+]{1,100};\s?charset=("[a-zA-Z0-9\.\-\+\s]{0,70}"|[a-zA-Z0-9\.\-\+]{0,70})(\s?\([a-zA-Z0-9\.\-\+\s]{1,20}\))?$/i,_e=/^multipart\/[a-zA-Z0-9\.\-\+]{1,100}(;\s?(boundary|charset)=("[a-zA-Z0-9\.\-\+\s]{0,70}"|[a-zA-Z0-9\.\-\+]{0,70})(\s?\([a-zA-Z0-9\.\-\+\s]{1,20}\))?){0,2}$/i;var Me=/^\(?[+-]?(90(\.0+)?|[1-8]?\d(\.\d+)?)$/,Re=/^\s?[+-]?(180(\.0+)?|1[0-7]\d(\.\d+)?|\d{1,2}(\.\d+)?)\)?$/,be=/^(([1-8]?\d)\D+([1-5]?\d|60)\D+([1-5]?\d|60)(\.\d+)?|90\D+0\D+0)\D+[NSns]?$/i,Le=/^\s*([1-7]?\d{1,2}\D+([1-5]?\d|60)\D+([1-5]?\d|60)(\.\d+)?|180\D+0\D+0)\D+[EWew]?$/i,Ne={checkDMS:!1};var ie=/^\d{4}$/,$e=/^\d{5}$/,pe=/^\d{6}$/,De={AD:/^AD\d{3}$/,AT:ie,AU:ie,AZ:/^AZ\d{4}$/,BE:ie,BG:ie,BR:/^\d{5}-\d{3}$/,BY:/2[1-4]{1}\d{4}$/,CA:/^[ABCEGHJKLMNPRSTVXY]\d[ABCEGHJ-NPRSTV-Z][\s\-]?\d[ABCEGHJ-NPRSTV-Z]\d$/i,CH:ie,CN:/^(0[1-7]|1[012356]|2[0-7]|3[0-6]|4[0-7]|5[1-7]|6[1-7]|7[1-5]|8[1345]|9[09])\d{4}$/,CZ:/^\d{3}\s?\d{2}$/,DE:$e,DK:ie,DO:$e,DZ:$e,EE:$e,ES:/^(5[0-2]{1}|[0-4]{1}\d{1})\d{3}$/,FI:$e,FR:/^\d{2}\s?\d{3}$/,GB:/^(gir\s?0aa|[a-z]{1,2}\d[\da-z]?\s?(\d[a-z]{2})?)$/i,GR:/^\d{3}\s?\d{2}$/,HR:/^([1-5]\d{4}$)/,HT:/^HT\d{4}$/,HU:ie,ID:$e,IE:/^(?!.*(?:o))[A-z]\d[\dw]\s\w{4}$/i,IL:/^(\d{5}|\d{7})$/,IN:/^((?!10|29|35|54|55|65|66|86|87|88|89)[1-9][0-9]{5})$/,IR:/\b(?!(\d)\1{3})[13-9]{4}[1346-9][013-9]{5}\b/,IS:/^\d{3}$/,IT:$e,JP:/^\d{3}\-\d{4}$/,KE:$e,LI:/^(948[5-9]|949[0-7])$/,LT:/^LT\-\d{5}$/,LU:ie,LV:/^LV\-\d{4}$/,MX:$e,MT:/^[A-Za-z]{3}\s{0,1}\d{4}$/,MY:$e,NL:/^\d{4}\s?[a-z]{2}$/i,NO:ie,NP:/^(10|21|22|32|33|34|44|45|56|57)\d{3}$|^(977)$/i,NZ:ie,PL:/^\d{2}\-\d{3}$/,PR:/^00[679]\d{2}([ -]\d{4})?$/,PT:/^\d{4}\-\d{3}?$/,RO:pe,RU:pe,SA:$e,SE:/^[1-9]\d{2}\s?\d{2}$/,SG:pe,SI:ie,SK:/^\d{3}\s?\d{2}$/,TH:$e,TN:ie,TW:/^\d{3}(\d{2})?$/,UA:$e,US:/^\d{5}(-\d{4})?$/,ZA:ie,ZM:$e},$e=Object.keys(De);function Te(t,e){d(t);e=e?new RegExp("^[".concat(e.replace(/[.*+?^${}()|[\]\\]/g,"\\$&"),"]+"),"g"):/^\s+/g;return t.replace(e,"")}function we(t,e){d(t);e=e?new RegExp("[".concat(e.replace(/[.*+?^${}()|[\]\\]/g,"\\$&"),"]+$"),"g"):/\s+$/g;return t.replace(e,"")}function ye(t,e){return d(t),t.replace(new RegExp("[".concat(e,"]+"),"g"),"")}var Be={all_lowercase:!0,gmail_lowercase:!0,gmail_remove_dots:!0,gmail_remove_subaddress:!0,gmail_convert_googlemaildotcom:!0,outlookdotcom_lowercase:!0,outlookdotcom_remove_subaddress:!0,yahoo_lowercase:!0,yahoo_remove_subaddress:!0,yandex_lowercase:!0,icloud_lowercase:!0,icloud_remove_subaddress:!0},Ue=["icloud.com","me.com"],xe=["hotmail.at","hotmail.be","hotmail.ca","hotmail.cl","hotmail.co.il","hotmail.co.nz","hotmail.co.th","hotmail.co.uk","hotmail.com","hotmail.com.ar","hotmail.com.au","hotmail.com.br","hotmail.com.gr","hotmail.com.mx","hotmail.com.pe","hotmail.com.tr","hotmail.com.vn","hotmail.cz","hotmail.de","hotmail.dk","hotmail.es","hotmail.fr","hotmail.hu","hotmail.id","hotmail.ie","hotmail.in","hotmail.it","hotmail.jp","hotmail.kr","hotmail.lv","hotmail.my","hotmail.ph","hotmail.pt","hotmail.sa","hotmail.sg","hotmail.sk","live.be","live.co.uk","live.com","live.com.ar","live.com.mx","live.de","live.es","live.eu","live.fr","live.it","live.nl","msn.com","outlook.at","outlook.be","outlook.cl","outlook.co.il","outlook.co.nz","outlook.co.th","outlook.com","outlook.com.ar","outlook.com.au","outlook.com.br","outlook.com.gr","outlook.com.pe","outlook.com.tr","outlook.com.vn","outlook.cz","outlook.de","outlook.dk","outlook.es","outlook.fr","outlook.hu","outlook.id","outlook.ie","outlook.in","outlook.it","outlook.jp","outlook.kr","outlook.lv","outlook.my","outlook.ph","outlook.pt","outlook.sa","outlook.sg","outlook.sk","passport.com"],Pe=["rocketmail.com","yahoo.ca","yahoo.co.uk","yahoo.com","yahoo.de","yahoo.fr","yahoo.in","yahoo.it","ymail.com"],Ge=["yandex.ru","yandex.ua","yandex.kz","yandex.com","yandex.by","ya.ru"];function Oe(t){return 1<t.length?t:""}var Ye=/^[^\s-_](?!.*?[-_]{2,})([a-z0-9-\\]{1,})[^\s]*[^-_\s]$/;var ke=/^[A-Z]$/,He=/^[a-z]$/,Ke=/^[0-9]$/,Ve=/^[-#!$%^&*()_+|~=`{}\[\]:";'<>?,.\/ ]$/,We={minLength:8,minLowercase:1,minUppercase:1,minNumbers:1,minSymbols:1,returnScore:!1,pointsPerUnique:1,pointsPerRepeat:.5,pointsForContainingLower:10,pointsForContainingUpper:10,pointsForContainingNumber:10,pointsForContainingSymbol:10};function ze(t){var e,r,n=(e=t,r={},Array.from(e).forEach(function(t){r[t]?r[t]+=1:r[t]=1}),r),i={length:t.length,uniqueChars:Object.keys(n).length,uppercaseCount:0,lowercaseCount:0,numberCount:0,symbolCount:0};return Object.keys(n).forEach(function(t){ke.test(t)?i.uppercaseCount+=n[t]:He.test(t)?i.lowercaseCount+=n[t]:Ke.test(t)?i.numberCount+=n[t]:Ve.test(t)&&(i.symbolCount+=n[t])}),i}var je={GB:/^GB((\d{3} \d{4} ([0-8][0-9]|9[0-6]))|(\d{9} \d{3})|(((GD[0-4])|(HA[5-9]))[0-9]{2}))$/};return{version:"13.5.2",toDate:a,toFloat:F,toInt:function(t,e){return d(t),parseInt(t,e||10)},toBoolean:function(t,e){return d(t),e?"1"===t||/^true$/i.test(t):"0"!==t&&!/^false$/i.test(t)&&""!==t},equals:function(t,e){return d(t),t===e},contains:function(t,e,r){return d(t),(r=M(r,R)).ignoreCase?0<=t.toLowerCase().indexOf(_(e).toLowerCase()):0<=t.indexOf(_(e))},matches:function(t,e,r){return d(t),"[object RegExp]"!==Object.prototype.toString.call(e)&&(e=new RegExp(e,r)),e.test(t)},isEmail:function(t,e){if(d(t),(e=M(e,y)).require_display_name||e.allow_display_name){var r=t.match(B);if(r){var n=u(r,3),i=n[1];if(t=n[2],i.endsWith(" ")&&(i=i.substr(0,i.length-1)),!function(t){var e=t.match(/^"(.+)"$/i);if((t=e?e[1]:t).trim()){if(/[\.";<>]/.test(t)){if(!e)return;if(!(t.split('"').length===t.split('\\"').length))return}return 1}}(i))return!1}else if(e.require_display_name)return!1}if(!e.ignore_max_length&&254<t.length)return!1;if(n=t.split("@"),i=n.pop(),t=n.join("@"),n=i.toLowerCase(),e.domain_specific_validation&&("gmail.com"===n||"googlemail.com"===n)){n=(t=t.toLowerCase()).split("+")[0];if(!b(n.replace(".",""),{min:6,max:30}))return!1;for(var a=n.split("."),o=0;o<a.length;o++)if(!x.test(a[o]))return!1}if(!(!1!==e.ignore_max_length||b(t,{max:64})&&b(i,{max:254})))return!1;if(!N(i,{require_tld:e.require_tld})){if(!e.allow_ip_domain)return!1;if(!w(i)){if(!i.startsWith("[")||!i.endsWith("]"))return!1;i=i.substr(1,i.length-2);if(0===i.length||!w(i))return!1}}if('"'===t[0])return t=t.slice(1,t.length-1),(e.allow_utf8_local_part?O:P).test(t);for(var s=e.allow_utf8_local_part?G:U,c=t.split("."),l=0;l<c.length;l++)if(!s.test(c[l]))return!1;return!e.blacklisted_chars||-1===t.search(new RegExp("[".concat(e.blacklisted_chars,"]+"),"g"))},isURL:function(t,e){if(d(t),!t||/[\s<>]/.test(t))return!1;if(0===t.indexOf("mailto:"))return!1;if((e=M(e,Y)).validate_length&&2083<=t.length)return!1;var r,n,i,a=t.split("#");if(1<(a=(t=(a=(t=a.shift()).split("?")).shift()).split("://")).length){if(i=a.shift().toLowerCase(),e.require_valid_protocol&&-1===e.protocols.indexOf(i))return!1}else{if(e.require_protocol)return!1;if("//"===t.substr(0,2)){if(!e.allow_protocol_relative_urls)return!1;a[0]=t.substr(2)}}if(""===(t=a.join("://")))return!1;if(""===(t=(a=t.split("/")).shift())&&!e.require_host)return!0;if(1<(a=t.split("@")).length){if(e.disallow_auth)return!1;if(-1===(o=a.shift()).indexOf(":")||0<=o.indexOf(":")&&2<o.split(":").length)return!1}i=n=null;var o=(t=a.join("@")).match(k);if(o?(r="",i=o[1],n=o[2]||null):(r=(a=t.split(":")).shift(),a.length&&(n=a.join(":"))),null!==n){if(a=parseInt(n,10),!/^[0-9]+$/.test(n)||a<=0||65535<a)return!1}else if(e.require_port)return!1;return!!(w(r)||N(r,e)||i&&w(i,6))&&(r=r||i,!(e.host_whitelist&&!H(r,e.host_whitelist))&&(!e.host_blacklist||!H(r,e.host_blacklist)))},isMACAddress:function(t,e){return d(t),e&&e.no_colons?V.test(t):K.test(t)||W.test(t)||z.test(t)||j.test(t)},isIP:w,isIPRange:function(t){return d(t),2===(t=t.split("/")).length&&(!!J.test(t[1])&&(!(1<t[1].length&&t[1].startsWith("0"))&&(w(t[0],4)&&t[1]<=32&&0<=t[1])))},isFQDN:N,isBoolean:function(t){return d(t),0<=["true","false","1","0"].indexOf(t)},isIBAN:function(t){return d(t),(e=(r=(e=t).replace(/[\s\-]+/gi,"").toUpperCase()).slice(0,2).toUpperCase())in Nt&&Nt[e].test(r)&&1===((t=(t=t).replace(/[^A-Z0-9]+/gi,"").toUpperCase()).slice(4)+t.slice(0,4)).replace(/[A-Z]/g,function(t){return t.charCodeAt(0)-55}).match(/\d{1,7}/g).reduce(function(t,e){return Number(t+e)%97},"");var e,r},isBIC:function(t){return d(t),Dt.test(t)},isAlpha:function(t){var e=1<arguments.length&&void 0!==arguments[1]?arguments[1]:"en-US",r=2<arguments.length&&void 0!==arguments[2]?arguments[2]:{};if(d(t),r=r.ignore)if(r instanceof RegExp)t=t.replace(r,"");else{if("string"!=typeof r)throw new Error("ignore should be instance of a String or RegExp");t=t.replace(new RegExp("[".concat(r.replace(/[-[\]{}()*+?.,\\^$|#\\s]/g,"\\$&"),"]"),"g"),"")}if(e in o)return o[e].test(t);throw new Error("Invalid locale '".concat(e,"'"))},isAlphaLocales:tt,isAlphanumeric:function(t){var e=1<arguments.length&&void 0!==arguments[1]?arguments[1]:"en-US";if(d(t),e in s)return s[e].test(t);throw new Error("Invalid locale '".concat(e,"'"))},isAlphanumericLocales:et,isNumeric:function(t,e){return d(t),(e&&e.no_symbols?rt:new RegExp("^[+-]?([0-9]*[".concat((e||{}).locale?l[e.locale]:".","])?[0-9]+$"))).test(t)},isPassportNumber:function(t,e){return d(t),t=t.replace(/\s/g,"").toUpperCase(),e.toUpperCase()in nt&&nt[e].test(t)},isPort:function(t){return ot(t,{min:0,max:65535})},isLowercase:function(t){return d(t),t===t.toLowerCase()},isUppercase:function(t){return d(t),t===t.toUpperCase()},isAscii:function(t){return d(t),lt.test(t)},isFullWidth:function(t){return d(t),ut.test(t)},isHalfWidth:function(t){return d(t),dt.test(t)},isVariableWidth:function(t){return d(t),ut.test(t)&&dt.test(t)},isMultibyte:function(t){return d(t),ft.test(t)},isSemVer:function(t){return d(t),At.test(t)},isSurrogatePair:function(t){return d(t),ht.test(t)},isInt:ot,isIMEI:function(t,e){d(t);var r=st;if((e=e||{}).allow_hyphens&&(r=ct),!r.test(t))return!1;t=t.replace(/-/g,"");for(var n=0,i=2,a=0;a<14;a++){var o=t.substring(14-a-1,14-a),o=parseInt(o,10)*i;n+=10<=o?o%10+1:o,1===i?i+=1:--i}return(10-n%10)%10===parseInt(t.substring(14,15),10)},isFloat:E,isFloatLocales:C,isDecimal:function(t,e){if(d(t),(e=M(e,mt)).locale in l)return!gt(vt,t.replace(/ /g,""))&&(r=e,new RegExp("^[-+]?([0-9]+)?(\\".concat(l[r.locale],"[0-9]{").concat(r.decimal_digits,"})").concat(r.force_decimal?"":"?","$")).test(t));var r;throw new Error("Invalid locale '".concat(e.locale,"'"))},isHexadecimal:St,isOctal:function(t){return d(t),Zt.test(t)},isDivisibleBy:function(t,e){return d(t),F(t)%parseInt(e,10)==0},isHexColor:function(t){return d(t),Et.test(t)},isRgbColor:function(t){var e=!(1<arguments.length&&void 0!==arguments[1])||arguments[1];return d(t),e?Ct.test(t)||Ft.test(t)||_t.test(t)||Mt.test(t):Ct.test(t)||Ft.test(t)},isHSL:function(t){return d(t),Rt.test(t)||bt.test(t)},isISRC:function(t){return d(t),Lt.test(t)},isMD5:function(t){return d(t),Tt.test(t)},isHash:function(t,e){return d(t),new RegExp("^[a-fA-F0-9]{".concat(wt[e],"}$")).test(t)},isJWT:function(t){d(t);var e=t.split(".");return!(3<(t=e.length)||t<2)&&e.reduce(function(t,e){return t&&xt(e,{urlSafe:!0})},!0)},isJSON:function(t,e){d(t);try{e=M(e,Pt);var r=[];e.allow_primitives&&(r=[null,!1,!0]);var n=JSON.parse(t);return r.includes(n)||!!n&&"object"===i(n)}catch(t){}return!1},isEmpty:function(t,e){return d(t),0===((e=M(e,Gt)).ignore_whitespace?t.trim():t).length},isLength:function(t,e){var r,n;return d(t),n="object"===i(e)?(r=e.min||0,e.max):(r=e||0,arguments[2]),e=t.match(/[\uD800-\uDBFF][\uDC00-\uDFFF]/g)||[],e=t.length-e.length,r<=e&&(void 0===n||e<=n)},isLocale:function(t){return d(t),"en_US_POSIX"===t||"ca_ES_VALENCIA"===t||Q.test(t)},isByteLength:b,isUUID:function(t){var e=1<arguments.length&&void 0!==arguments[1]?arguments[1]:"all";return d(t),(e=Ot[e])&&e.test(t)},isMongoId:function(t){return d(t),St(t)&&24===t.length},isAfter:function(t){var e=1<arguments.length&&void 0!==arguments[1]?arguments[1]:String(new Date);return d(t),e=a(e),!!((t=a(t))&&e&&e<t)},isBefore:function(t){var e=1<arguments.length&&void 0!==arguments[1]?arguments[1]:String(new Date);return d(t),e=a(e),!!((t=a(t))&&e&&t<e)},isIn:function(t,e){if(d(t),"[object Array]"!==Object.prototype.toString.call(e))return"object"===i(e)?e.hasOwnProperty(t):!(!e||"function"!=typeof e.indexOf)&&0<=e.indexOf(t);var r,n=[];for(r in e)!{}.hasOwnProperty.call(e,r)||(n[r]=_(e[r]));return 0<=n.indexOf(t)},isCreditCard:function(t){d(t);var e=t.replace(/[- ]+/g,"");if(!Yt.test(e))return!1;for(var r,n,i=0,a=e.length-1;0<=a;a--)r=e.substring(a,a+1),r=parseInt(r,10),i+=n&&10<=(r*=2)?r%10+1:r,n=!n;return!(i%10!=0||!e)},isIdentityCard:function(t,e){if(d(t),e in kt)return kt[e](t);if("any"!==e)throw new Error("Invalid locale '".concat(e,"'"));for(var r in kt)if(kt.hasOwnProperty(r))if((0,kt[r])(t))return!0;return!1},isEAN:function(t){d(t);var e=Number(t.slice(-1));return Kt.test(t)&&e===Vt(t)},isISIN:function(t){if(d(t),!Wt.test(t))return!1;for(var e,r=t.replace(/[A-Z]/g,function(t){return parseInt(t,36)}),n=0,i=!0,a=r.length-2;0<=a;a--)e=r.substring(a,a+1),e=parseInt(e,10),n+=i&&10<=(e*=2)?e+1:e,i=!i;return parseInt(t.substr(t.length-1),10)===(1e4-n)%10},isISBN:function t(e){var r=1<arguments.length&&void 0!==arguments[1]?arguments[1]:"";if(d(e),!(r=String(r)))return t(e,10)||t(e,13);var n,i=e.replace(/[\s-]+/g,""),a=0;if("10"===r){if(!zt.test(i))return!1;for(n=0;n<9;n++)a+=(n+1)*i.charAt(n);if("X"===i.charAt(9)?a+=100:a+=10*i.charAt(9),a%11==0)return!!i}else if("13"===r){if(!jt.test(i))return!1;for(n=0;n<12;n++)a+=Jt[n%2]*i.charAt(n);if(i.charAt(12)-(10-a%10)%10==0)return!!i}return!1},isISSN:function(t){var e=1<arguments.length&&void 0!==arguments[1]?arguments[1]:{};d(t);var r="^\\d{4}-?\\d{3}[\\dX]$",r=e.require_hyphen?r.replace("?",""):r;if(!(r=e.case_sensitive?new RegExp(r):new RegExp(r,"i")).test(t))return!1;for(var n=t.replace("-","").toUpperCase(),i=0,a=0;a<n.length;a++){var o=n[a];i+=("X"===o?10:+o)*(8-a)}return i%11==0},isMobilePhone:function(e,t,r){if(d(e),r&&r.strictMode&&!e.startsWith("+"))return!1;if(Array.isArray(t))return t.some(function(t){if(oe.hasOwnProperty(t)&&oe[t].test(e))return!0;return!1});if(t in oe)return oe[t].test(e);if(t&&"any"!==t)throw new Error("Invalid locale '".concat(t,"'"));for(var n in oe)if(oe.hasOwnProperty(n))if(oe[n].test(e))return!0;return!1},isMobilePhoneLocales:se,isPostalCode:function(t,e){if(d(t),e in De)return De[e].test(t);if("any"!==e)throw new Error("Invalid locale '".concat(e,"'"));for(var r in De)if(De.hasOwnProperty(r))if(De[r].test(t))return!0;return!1},isPostalCodeLocales:$e,isEthereumAddress:function(t){return d(t),ce.test(t)},isCurrency:function(t,e){return d(t),function(t){var r="\\d{".concat(t.digits_after_decimal[0],"}");t.digits_after_decimal.forEach(function(t,e){0!==e&&(r="".concat(r,"|\\d{").concat(t,"}"))});var e="(".concat(t.symbol.replace(/\W/,function(t){return"\\".concat(t)}),")").concat(t.require_symbol?"":"?"),n="[1-9]\\d{0,2}(\\".concat(t.thousands_separator,"\\d{3})*"),i="(".concat(["0","[1-9]\\d*",n].join("|"),")?"),n="(\\".concat(t.decimal_separator,"(").concat(r,"))").concat(t.require_decimal?"":"?"),n=i+(t.allow_decimal||t.require_decimal?n:"");return t.allow_negatives&&!t.parens_for_negatives&&(t.negative_sign_after_digits?n+="-?":t.negative_sign_before_digits&&(n="-?"+n)),t.allow_negative_sign_placeholder?n="( (?!\\-))?".concat(n):t.allow_space_after_symbol?n=" ?".concat(n):t.allow_space_after_digits&&(n+="( (?!$))?"),t.symbol_after_digits?n+=e:n=e+n,t.allow_negatives&&(t.parens_for_negatives?n="(\\(".concat(n,"\\)|").concat(n,")"):t.negative_sign_before_digits||t.negative_sign_after_digits||(n="-?"+n)),new RegExp("^(?!-? )(?=.*\\d)".concat(n,"$"))}(e=M(e,le)).test(t)},isBtcAddress:function(t){return d(t),ue.test(t)},isISO8601:function(t){var e=1<arguments.length&&void 0!==arguments[1]?arguments[1]:{};d(t);var r=(e.strictSeparator?fe:de).test(t);return r&&e.strict?function(t){var e=t.match(/^(\d{4})-?(\d{3})([ T]{1}\.*|$)/);if(e){var r=Number(e[1]),n=Number(e[2]);return r%4==0&&r%100!=0||r%400==0?n<=366:n<=365}var i=t.match(/(\d{4})-?(\d{0,2})-?(\d*)/).map(Number),e=i[1],r=i[2],n=i[3],t=r&&"0".concat(r).slice(-2),i=n&&"0".concat(n).slice(-2),i=new Date("".concat(e,"-").concat(t||"01","-").concat(i||"01"));return!r||!n||i.getUTCFullYear()===e&&i.getUTCMonth()+1===r&&i.getUTCDate()===n}(t):r},isRFC3339:function(t){return d(t),Ae.test(t)},isISO31661Alpha2:function(t){return d(t),gt(he,t.toUpperCase())},isISO31661Alpha3:function(t){return d(t),gt(ge,t.toUpperCase())},isBase32:function(t){return d(t),!(t.length%8!=0||!me.test(t))},isBase58:function(t){return d(t),!!ve.test(t)},isBase64:xt,isDataURI:function(t){d(t);var e=t.split(",");if(e.length<2)return!1;var r=e.shift().trim().split(";");if("data:"!==(t=r.shift()).substr(0,5))return!1;if(""!==(t=t.substr(5))&&!Ie.test(t))return!1;for(var n=0;n<r.length;n++)if((n!==r.length-1||"base64"!==r[n].toLowerCase())&&!Se.test(r[n]))return!1;for(var i=0;i<e.length;i++)if(!Ze.test(e[i]))return!1;return!0},isMagnetURI:function(t){return d(t),Ee.test(t.trim())},isMimeType:function(t){return d(t),Ce.test(t)||Fe.test(t)||_e.test(t)},isLatLong:function(t,e){return d(t),e=M(e,Ne),!!t.includes(",")&&(!((t=t.split(","))[0].startsWith("(")&&!t[1].endsWith(")")||t[1].endsWith(")")&&!t[0].startsWith("("))&&(e.checkDMS?be.test(t[0])&&Le.test(t[1]):Me.test(t[0])&&Re.test(t[1])))},ltrim:Te,rtrim:we,trim:function(t,e){return we(Te(t,e),e)},escape:function(t){return d(t),t.replace(/&/g,"&amp;").replace(/"/g,"&quot;").replace(/'/g,"&#x27;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/\//g,"&#x2F;").replace(/\\/g,"&#x5C;").replace(/`/g,"&#96;")},unescape:function(t){return d(t),t.replace(/&amp;/g,"&").replace(/&quot;/g,'"').replace(/&#x27;/g,"'").replace(/&lt;/g,"<").replace(/&gt;/g,">").replace(/&#x2F;/g,"/").replace(/&#x5C;/g,"\\").replace(/&#96;/g,"`")},stripLow:function(t,e){return d(t),ye(t,e?"\\x00-\\x09\\x0B\\x0C\\x0E-\\x1F\\x7F":"\\x00-\\x1F\\x7F")},whitelist:function(t,e){return d(t),t.replace(new RegExp("[^".concat(e,"]+"),"g"),"")},blacklist:ye,isWhitelisted:function(t,e){d(t);for(var r=t.length-1;0<=r;r--)if(-1===e.indexOf(t[r]))return!1;return!0},normalizeEmail:function(t,e){e=M(e,Be);var r=t.split("@"),t=r.pop();if((r=[r.join("@"),t])[1]=r[1].toLowerCase(),"gmail.com"===r[1]||"googlemail.com"===r[1]){if(e.gmail_remove_subaddress&&(r[0]=r[0].split("+")[0]),e.gmail_remove_dots&&(r[0]=r[0].replace(/\.+/g,Oe)),!r[0].length)return!1;(e.all_lowercase||e.gmail_lowercase)&&(r[0]=r[0].toLowerCase()),r[1]=e.gmail_convert_googlemaildotcom?"gmail.com":r[1]}else if(0<=Ue.indexOf(r[1])){if(e.icloud_remove_subaddress&&(r[0]=r[0].split("+")[0]),!r[0].length)return!1;(e.all_lowercase||e.icloud_lowercase)&&(r[0]=r[0].toLowerCase())}else if(0<=xe.indexOf(r[1])){if(e.outlookdotcom_remove_subaddress&&(r[0]=r[0].split("+")[0]),!r[0].length)return!1;(e.all_lowercase||e.outlookdotcom_lowercase)&&(r[0]=r[0].toLowerCase())}else if(0<=Pe.indexOf(r[1])){if(e.yahoo_remove_subaddress&&(t=r[0].split("-"),r[0]=1<t.length?t.slice(0,-1).join("-"):t[0]),!r[0].length)return!1;(e.all_lowercase||e.yahoo_lowercase)&&(r[0]=r[0].toLowerCase())}else 0<=Ge.indexOf(r[1])?((e.all_lowercase||e.yandex_lowercase)&&(r[0]=r[0].toLowerCase()),r[1]="yandex.ru"):e.all_lowercase&&(r[0]=r[0].toLowerCase());return r.join("@")},toString:toString,isSlug:function(t){return d(t),Ye.test(t)},isStrongPassword:function(t){var e=1<arguments.length&&void 0!==arguments[1]?arguments[1]:null;d(t);var r,n,i=ze(t);return(e=M(e||{},We)).returnScore?(r=e,n=0,n+=(t=i).uniqueChars*r.pointsPerUnique,n+=(t.length-t.uniqueChars)*r.pointsPerRepeat,0<t.lowercaseCount&&(n+=r.pointsForContainingLower),0<t.uppercaseCount&&(n+=r.pointsForContainingUpper),0<t.numberCount&&(n+=r.pointsForContainingNumber),0<t.symbolCount&&(n+=r.pointsForContainingSymbol),n):i.length>=e.minLength&&i.lowercaseCount>=e.minLowercase&&i.uppercaseCount>=e.minUppercase&&i.numberCount>=e.minNumbers&&i.symbolCount>=e.minSymbols},isTaxID:function(t){var e=1<arguments.length&&void 0!==arguments[1]?arguments[1]:"en-US";if(d(t),t=t.slice(0),e in re)return e in ae&&(t=t.replace(ae[e],"")),!!re[e].test(t)&&(!(e in ne)||ne[e](t));throw new Error("Invalid locale '".concat(e,"'"))},isDate:q,isVAT:function(t,e){if(d(t),d(e),e in je)return je[e].test(t);throw new Error("Invalid country code: '".concat(e,"'"))}}});