{"version": 3, "sources": ["theme/mixins/_button.scss", "theme/_variables.scss"], "names": [], "mappings": "AAAA,+EAAA;AACA,+EAAA;AACA,+EAAA;ACFA,oBAAA", "file": "user.css", "sourcesContent": ["/* -------------------------------------------------------------------------- */\r\n/*                                Falcon <PERSON>                               */\r\n/* -------------------------------------------------------------------------- */\r\n@mixin falcon-button-variant($color) {\r\n  &, &.show {\r\n    color: var(--#{$prefix}btn-falcon-#{$color}-color);\r\n    background-color: var(--#{$prefix}btn-falcon-background);\r\n    // @if $enable-gradients {\r\n    //   background-image: var(--#{$prefix}gradient);\r\n    // }\r\n    border-color: var(--#{$prefix}btn-falcon-background);\r\n    box-shadow: var(--#{$prefix}btn-falcon-box-shadow);\r\n\r\n  }\r\n\r\n  // &.show {\r\n  //   color: var(--#{$prefix}btn-falcon-#{$color}-color);\r\n  //   background-color: var(--#{$prefix}btn-falcon-background);\r\n  //   border-color: var(--#{$prefix}btn-falcon-background);\r\n  //   box-shadow: var(--#{$prefix}btn-falcon-box-shadow);\r\n  // }\r\n\r\n  @include hover-focus {\r\n    color: var(--#{$prefix}btn-falcon-#{$color}-hover-color);\r\n    background-color: var(--#{$prefix}btn-falcon-background);\r\n    border-color: var(--#{$prefix}btn-falcon-background);\r\n\r\n    &:not(.disabled):not(:disabled) {\r\n      color: var(--#{$prefix}btn-falcon-#{$color}-hover-color);\r\n      box-shadow: var(--#{$prefix}btn-falcon-hover-box-shadow);\r\n    }\r\n  }\r\n  &:active {\r\n    box-shadow: none !important;\r\n    color: var(--#{$prefix}btn-falcon-#{$color}-active-color);\r\n  }\r\n\r\n  .btn-check:checked+&,\r\n  :not(.btn-check)+&:active,\r\n  &:first-child:active,\r\n  &.active {\r\n    color: var(--#{$prefix}btn-falcon-#{$color}-color);\r\n    background-color: var(--#{$prefix}btn-falcon-#{$color}-active-background);\r\n    // Remove CSS gradients if they're enabled\r\n    // background-image: if($enable-gradients, none, null);\r\n    border-color: var(--#{$prefix}btn-falcon-background);\r\n\r\n    // &:focus {\r\n    //   @if $enable-shadows {\r\n    //     @include box-shadow($btn-active-box-shadow, 0 0 0 $btn-focus-width rgba(mix($color, $border, 15%), .5));\r\n    //   } @else {\r\n    //     // Avoid using mixin so we can pass custom focus shadow properly\r\n    //     box-shadow: 0 0 0 $btn-focus-width rgba(mix($color, $border, 15%), .5);\r\n    //   }\r\n    // }\r\n  }\r\n\r\n  &:disabled,\r\n  &.disabled {\r\n    color: var(--#{$prefix}btn-disabled-color);\r\n    background-color: var(--#{$prefix}btn-falcon-background);\r\n    // Remove CSS gradients if they're enabled\r\n    background-image: if($enable-gradients, none, null);\r\n    box-shadow: var(--#{$prefix}btn-falcon-box-shadow) !important;\r\n  }\r\n}", "/* prettier-ignore */\r\n@use 'sass:math';\r\n@use \"sass:string\";\r\n$prefix: 'falcon-';\r\n\r\n// Grid containers\r\n//\r\n// Define the maximum width of `.container` for different screen sizes.\r\n// scss-docs-start container-max-widths\r\n\r\n// scss-docs-start container-max-widths\r\n$container-max-widths: (\r\n  sm: 540px,\r\n  md: 720px,\r\n  lg: 960px,\r\n  xl: 1140px,\r\n  xxl: 1480px,\r\n) !default;\r\n// scss-docs-end container-max-widths\r\n\r\n// scss-docs-start grid-breakpoints\r\n$grid-breakpoints: (\r\n  xs: 0,\r\n  sm: 576px,\r\n  md: 768px,\r\n  lg: 992px,\r\n  xl: 1200px,\r\n  xxl: 1540px,\r\n) !default;\r\n// scss-docs-end grid-breakpoints\r\n\r\n// scss-docs-start spacer-variables-maps\r\n$spacer: 1rem !default;\r\n$spacer-x1: 1.25rem;\r\n$spacers: (\r\n  0: 0,\r\n  1: $spacer * 0.25,\r\n  2: $spacer * 0.5,\r\n  3: $spacer,\r\n  4: $spacer * 1.8,\r\n  5: $spacer * 3,\r\n  6: $spacer * 4,\r\n  7: $spacer * 5,\r\n  8: $spacer * 7.5,\r\n  9: $spacer * 10,\r\n  10: $spacer * 12.5,\r\n  11: $spacer * 15,\r\n  x1: $spacer-x1\r\n) !default;\r\n// scss-docs-end spacer-variables-maps\r\n\r\n// Customize the light and dark text colors for use in our color contrast function.\r\n$color-contrast-dark: $gray-800 !default;\r\n\r\n// Min contrast ratio\r\n$min-contrast-ratio: 2 !default;\r\n\r\n// Grid columns\r\n//\r\n// Set the number of columns and specify the width of the gutters.\r\n$grid-gutter-width: 2rem !default;\r\n\r\n// Border\r\n// \r\n// Define border radius styles and more.\r\n$border-color: $gray-300 !default;\r\n$border-width: 1px !default;\r\n\r\n// scss-docs-start border-styles\r\n$border-styles : (\r\n  dotted: 'dotted !important',\r\n  dashed: 'dashed !important',\r\n  none: 'none !important',\r\n  hidden: 'hidden !important',\r\n) !default;\r\n// scss-docs-end border-styles\r\n\r\n$disabled-border: transparent !default;\r\n\r\n// scss-docs-start border-radius-variables\r\n$border-radiuses: (\r\n  null: var(--#{$prefix}border-radius),\r\n  0: 0,\r\n  1: var(--#{$prefix}border-radius-sm),\r\n  2: var(--#{$prefix}border-radius),\r\n  3: var(--#{$prefix}border-radius-lg),\r\n  4: var(--#{$prefix}border-radius-xl),\r\n  5: var(--#{$prefix}border-radius-xxl),\r\n  circle: 50%,\r\n  pill: var(--#{$prefix}border-radius-pill)\r\n) !default;\r\n// scss-docs-end border-radius-variables\r\n\r\n$border-radius: 0.25rem !default;\r\n$border-radius-lg: 0.375rem !default;\r\n\r\n// Opacity\r\n// \r\n// scss-docs-start opacity-map\r\n$opacities : (\r\n  0: 0,\r\n  25: 0.25,\r\n  50: 0.5,\r\n  75: 0.75,\r\n  85: 0.85,\r\n  100: 1,\r\n) !default;\r\n// scss-docs-end opacity-map\r\n\r\n// Position\r\n//\r\n// Define the edge positioning anchors of the position utilities.\r\n\r\n// scss-docs-start position-map\r\n$positions : (\r\n  static,\r\n  absolute,\r\n  relative,\r\n  fixed,\r\n  sticky,\r\n) !default;\r\n// scss-docs-end position-map\r\n\r\n// Options\r\n//\r\n// Quickly modify global styling by enabling or disabling optional features.\r\n\r\n$enable-shadows: true !default;\r\n$enable-gradients: false !default;\r\n$enable-negative-margins: true !default;\r\n$enable-dark-mode: true !default;\r\n\r\n$component-active-bg: $primary !default;\r\n\r\n// scss-docs-start box-shadow-variables\r\n$box-shadow: 0 7px 14px 0 rgba(65, 69, 88, 0.1), 0 3px 6px 0 rgba(0, 0, 0, 0.07) !default;\r\n$box-shadow-sm:  0 .125rem .25rem rgba($black, .075) !default;\r\n$box-shadow-lg: 0 1rem 4rem rgba($black, 0.175) !default;\r\n$box-shadow-inset: inset 0 1px 2px rgba($black, .075) !default;\r\n// scss-docs-end box-shadow-variables\r\n\r\n// Links\r\n//\r\n// Style anchor elements.\r\n$link-decoration: none !default;\r\n$link-hover-decoration: underline !default;\r\n$link-shade-percentage: 20% !default;\r\n\r\n\r\n// Typography\r\n//\r\n// Font, line-height, and color for body text, headings, and more.\r\n\r\n// scss-docs-start font-variables\r\n$font-family-sans-serif: 'Poppins',\r\n  -apple-system,\r\n  BlinkMacSystemFont,\r\n  'Segoe UI',\r\n  Roboto,\r\n  'Helvetica Neue',\r\n  Arial,\r\n  sans-serif,\r\n  'Apple Color Emoji',\r\n  'Segoe UI Emoji',\r\n  'Segoe UI Symbol' !default;\r\n$font-family-monospace: 'SFMono-Regular',\r\n  Menlo,\r\n  Monaco,\r\n  Consolas,\r\n  'Liberation Mono',\r\n  'Courier New',\r\n  monospace !default;\r\n$font-family-base: 'Open Sans',\r\n  -apple-system,\r\n  BlinkMacSystemFont,\r\n  'Segoe UI',\r\n  Roboto,\r\n  'Helvetica Neue',\r\n  Arial,\r\n  sans-serif,\r\n  'Apple Color Emoji',\r\n  'Segoe UI Emoji',\r\n  'Segoe UI Symbol' !default;\r\n\r\n$type-scale: 1.2 !default;\r\n$font-size-base: 1rem !default;\r\n$font-sizes: ( ) !default;\r\n$font-sizes: map-merge((\r\n    11: math.div(1, pow($type-scale, 2)) * $font-size-base, //11.11\r\n    10: math.div(1, $type-scale) * $font-size-base, //13.33\r\n    9: $font-size-base, //16\r\n    8: pow($type-scale, 1) * $font-size-base, // 19.2\r\n    7: pow($type-scale, 2) * $font-size-base, // 23.04\r\n    6: pow($type-scale, 3) * $font-size-base, // 27.65\r\n    5: pow($type-scale, 4) * $font-size-base, // 33.18\r\n    4: pow($type-scale, 5) * $font-size-base, // 39.81\r\n    3: pow($type-scale, 6) * $font-size-base, // 47.78\r\n    2: pow($type-scale, 7) * $font-size-base, // 57.33\r\n    1: pow($type-scale, 8) * $font-size-base, // 68.79\r\n  ),\r\n  $font-sizes\r\n);\r\n\r\n$font-size-sm: $font-size-base * 0.875 !default;\r\n$font-size-lg: $font-size-base * 1.2 !default;\r\n\r\n$font-weight-thin: 100 !default;\r\n$font-weight-lighter: 200 !default;\r\n$font-weight-light: 300 !default;\r\n$font-weight-normal: 400 !default;\r\n$font-weight-medium: 500 !default;\r\n$font-weight-semi-bold: 600 !default;\r\n$font-weight-bold: 700 !default;\r\n$font-weight-bolder: 800 !default;\r\n$font-weight-black: 900 !default;\r\n\r\n$font-weights : (\r\n  thin: $font-weight-thin,\r\n  lighter: $font-weight-lighter,\r\n  light: $font-weight-light,\r\n  normal: $font-weight-normal,\r\n  medium: $font-weight-medium,\r\n  semi-bold: $font-weight-semi-bold,\r\n  bold: $font-weight-bold,\r\n  bolder: $font-weight-bolder,\r\n  black: $font-weight-black\r\n) !default;\r\n\r\n$h1-font-size: map_get($font-sizes, 4) !default;\r\n$h2-font-size: map_get($font-sizes, 5) !default;\r\n$h3-font-size: map_get($font-sizes, 6) !default;\r\n$h4-font-size: map_get($font-sizes, 7) !default;\r\n$h5-font-size: map_get($font-sizes, 8) !default;\r\n$h6-font-size: map_get($font-sizes, 10) !default;\r\n\r\n$headings-font-family: var(--#{$prefix}font-sans-serif) !default;\r\n$headings-font-weight: $font-weight-medium !default;\r\n$headings-color: var(--#{$prefix}secondary-color) !default;\r\n// scss-docs-end font-variables\r\n\r\n// scss-docs-start display-headings\r\n$display-font-sizes: (\r\n  1: map_get($font-sizes, 1),\r\n  2: map_get($font-sizes, 2),\r\n  3: map_get($font-sizes, 3),\r\n  4: map_get($font-sizes, 4),\r\n  5: map_get($font-sizes, 5),\r\n  6: map_get($font-sizes, 6),\r\n) !default;\r\n\r\n$display-font-weight: $font-weight-black !default;\r\n$display-line-height: 1 !default;\r\n// scss-docs-end display-headings\r\n\r\n// scss-docs-start type-variables\r\n$lead-font-size: $font-size-lg !default;\r\n$lead-font-weight: $font-weight-normal !default;\r\n\r\n$small-font-size: 75% !default;\r\n\r\n$blockquote-font-size: $font-size-lg !default;\r\n\r\n$hr-color: var(--#{$prefix}border-color) !default;\r\n$hr-opacity: var(--#{$prefix}hr-opacity) !default;\r\n// scss-docs-end type-variables\r\n\r\n\r\n// Cards\r\n// \r\n// scss-docs-start card-variables\r\n$card-spacer-y: 1.25rem !default;\r\n$card-spacer-x: $spacer-x1 !default;\r\n$card-border-width: 0px !default;\r\n$card-border-radius: $border-radius-lg !default;\r\n$card-border-color: rgba(var(--#{$prefix}emphasis-color-rgb), 0.125) !default;\r\n$card-cap-bg: rgba($body-emphasis-color, 0) !default;\r\n$card-cap-padding-y: $spacer !default;\r\n$card-cap-padding-x: 1.25rem !default;\r\n$card-bg: $white !default;\r\n$card-bg-dark: tint-color($gray-1100, 2.9%) !default;\r\n$card-title-color: $headings-color !default;\r\n// scss-docs-end card-variables\r\n\r\n\r\n// Accordion\r\n// \r\n// scss-docs-start accordion-variables\r\n$accordion-icon-active-color: $body-color !default;\r\n$accordion-bg: var(--#{$prefix}emphasis-bg) !default;\r\n$accordion-border-color: rgba(var(--#{$prefix}emphasis-color-rgb), 0.125) !default;\r\n$accordion-button-active-color: shade-color($primary, 10%) !default;\r\n$accordion-button-active-bg: var(--#{$prefix}emphasis-bg) !default;\r\n$accordion-button-color: var(--#{$prefix}accordion-btn-color-global) !default;\r\n// scss-docs-end accordion-variables\r\n\r\n\r\n// Tables\r\n//\r\n// Customizes the `.table` component with basic values, each used across all table variations.\r\n\r\n// scss-docs-start table-variables\r\n$table-cell-padding-y: 0.75rem !default;\r\n$table-cell-padding-x: 0.75rem !default;\r\n\r\n$table-bg: transparent !default;\r\n$table-color: $gray-700 !default;\r\n$table-border-color: var(--#{$prefix}body-bg) !default;\r\n\r\n$table-active-color: var(--#{$prefix}body-color) !default;\r\n\r\n$table-hover-color: var(--#{$prefix}body-color) !default;\r\n\r\n$table-striped-order: even !default;\r\n$table-striped-bg: var(--#{$prefix}gray-100) !default;\r\n\r\n$table-group-separator-color: inherit !default;\r\n\r\n$table-caption-color: $gray-500 !default;\r\n\r\n$table-bg-scale: -80% !default;\r\n// scss-docs-end table-variables\r\n\r\n\r\n// Buttons + Forms\r\n//\r\n// Shared variables that are reassigned to `$input-` and `$btn-` specific variables.\r\n\r\n// scss-docs-start btn-falcon-variables\r\n$btn-falcon-box-shadow: 0 0 0 1px rgba(43, 45, 80, 0.1), 0 2px 5px 0 rgba(43, 45, 80, 0.08), 0 1px 1.5px 0 rgba($black, 0.07), 0 1px 2px 0 rgba($black, 0.08) !default;\r\n$btn-falcon-hover-box-shadow: 0 0 0 1px rgba(43, 45, 80, 0.1), 0 2px 5px 0 rgba(43, 45, 80, 0.1), 0 3px 9px 0 rgba(43, 45, 80, 0.08), 0 1px 1.5px 0 rgba($black, 0.08), 0 1px 2px 0 rgba($black, 0.08) !default;\r\n// scss-docs-end btn-falcon-variables\r\n\r\n// scss-docs-start input-btn-variables\r\n$input-btn-padding-y: 0.3125rem !default;\r\n$input-btn-padding-x: 1rem !default;\r\n\r\n$input-btn-padding-y-sm: 0.1875rem !default;\r\n$input-btn-padding-x-sm: 0.75rem !default;\r\n\r\n$input-btn-padding-y-lg: 0.375rem !default;\r\n$input-btn-padding-x-lg: 1.25rem !default;\r\n\r\n$input-plaintext-color: $gray-700 !default;\r\n// scss-docs-end input-btn-variables\r\n\r\n// scss-docs-start btn-variables\r\n$btn-font-weight: $font-weight-medium !default;\r\n$btn-focus-width: 0 !default;\r\n\r\n$btn-color: $gray-700 !default;\r\n\r\n$btn-disabled-opacity: .50 !default;\r\n// scss-docs-end btn-variables\r\n\r\n\r\n// Forms\r\n// \r\n// scss-docs-start form-input-variables\r\n$input-bg: var(--#{$prefix}quaternary-bg) !default;\r\n$input-disabled-bg: var(--#{$prefix}gray-200) !default;\r\n\r\n$input-color: var(--#{$prefix}gray-900) !default;\r\n$input-border-color: var(--#{$prefix}gray-300) !default;\r\n\r\n$input-focus-border-color: var(--#{$prefix}input-focus-border-color-global) !default;\r\n\r\n$input-placeholder-color: var(--#{$prefix}input-placeholder-color-global) !default;\r\n\r\n$input-group-addon-bg: var(--#{$prefix}gray-200) !default;\r\n// scss-docs-end form-input-variables\r\n\r\n// scss-docs-start form-text-variables\r\n$form-text-color: $gray-500 !default;\r\n// scss-docs-end form-text-variables\r\n\r\n// scss-docs-start form-label-variables\r\n$form-label-font-size: map_get($font-sizes, 10) !default;\r\n$form-label-font-weight: $font-weight-medium !default;\r\n$form-label-margin-bottom: map-get($spacers, 2) !default;\r\n// scss-docs-end form-label-variables\r\n\r\n// scss-docs-start form-switch-variables\r\n$form-switch-color: $gray-500 !default;\r\n// scss-docs-end form-switch-variables\r\n\r\n// scss-docs-start form-check-variables\r\n$form-check-margin-bottom: 0.34375rem !default;\r\n$form-check-input-bg: transparent !default;\r\n$form-check-input-border: 1px solid var(--#{$prefix}gray-400) !default;\r\n// scss-docs-end form-check-variables\r\n\r\n// scss-docs-start form-select-variables\r\n$form-select-disabled-bg: var(--#{$prefix}gray-200) !default;\r\n// scss-docs-end form-select-variables\r\n\r\n// scss-docs-start form-file-variables\r\n$form-file-button-bg: $gray-900 !default;\r\n$form-file-button-hover-bg: $gray-900 !default;\r\n$form-file-button-color: $gray-300 !default;\r\n// scss-docs-start form-file-variables\r\n\r\n// scss-docs-start form-range-variables\r\n$form-range-track-bg: var(--#{$prefix}gray-300) !default;\r\n$form-range-thumb-active-bg: lighten($component-active-bg, 35%) !default;\r\n$form-range-thumb-disabled-bg: $gray-500 !default;\r\n// scss-docs-end form-range-variables\r\n\r\n\r\n// Z-index master list\r\n// \r\n// scss-docs-start zindex-stack\r\n$zindex-sticky: 1015 !default;\r\n// scss-docs-end zindex-stack\r\n\r\n\r\n// Navbar\r\n\r\n// scss-docs-start navbar-variables\r\n$navbar-padding-y: map_get($spacers, 2) !default;\r\n$navbar-padding-x: $spacer !default;\r\n\r\n$top-nav-height: var(--#{$prefix}top-nav-height) !default;\r\n$standard-nav-height: 3.5625rem !default;\r\n\r\n$navbar-brand-font-size: map_get($font-sizes, 6) !default;\r\n$navbar-brand-font-weight: $font-weight-bolder !default;\r\n\r\n$navbar-light-color: #{rgba(var(--#{$prefix}emphasis-color-rgb), .55)} !default;\r\n$navbar-light-hover-color: #{rgba(var(--#{$prefix}emphasis-color-rgb), .7)} !default;\r\n$navbar-light-active-color: #{rgba(var(--#{$prefix}emphasis-color-rgb), .9)} !default;\r\n$navbar-light-disabled-color: #{rgba(var(--#{$prefix}emphasis-color-rgb), .3)} !default;\r\n$navbar-light-toggler-border-color: #{rgba(var(--#{$prefix}emphasis-color-rgb), .1)} !default;\r\n$navbar-light-toggler-icon-bg: str-replace(url(\"data:image/svg+xml;charset=utf8,%3Csvg viewBox='0 0 30 30' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath stroke='#9da9bb' stroke-width='2' stroke-linecap='round' stroke-miterlimit='10' d='M0 6h30M0 14h30M0 22h30'/%3E%3C/svg%3E\"),\r\n    '#',\r\n    '%23'\r\n  ) !default;\r\n\r\n$navbar-dark-toggler-icon-bg: str-replace(url(\"data:image/svg+xml;charset=utf8,%3Csvg viewBox='0 0 30 30' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath stroke='#9da9bb' stroke-width='2' stroke-linecap='round' stroke-miterlimit='10' d='M0 6h30M0 14h30M0 22h30'/%3E%3C/svg%3E\"),\r\n    '#',\r\n    '%23'\r\n  ) !default;\r\n// scss-docs-end navbar-variables\r\n\r\n// Navbar glass\r\n// \r\n$bg-navbar-glass: var(--#{$prefix}bg-navbar-glass) !default;\r\n\r\n\r\n// Navs\r\n\r\n// scss-docs-start nav-variables\r\n$nav-link-color: null !default;\r\n$nav-link-hover-color: null !default;\r\n$nav-link-disabled-color: var(--#{$prefix}gray-600) !default;\r\n\r\n$nav-tabs-border-color: var(--#{$prefix}border-color) !default;\r\n$nav-tabs-link-hover-border-color: $gray-200 $gray-200 $nav-tabs-border-color !default;\r\n// scss-docs-end nav-variables\r\n\r\n\r\n// Navbar vertical\r\n// \r\n// scss-docs-start navbar-vertical-variables\r\n$navbar-vertical-breakpoints: mapReverse($grid-breakpoints) !default;\r\n$navbar-vertical-width: 12.625rem !default;\r\n$navbar-vertical-icon-width: 1.5rem !default;\r\n$navbar-vertical-collapsed-width: 3.125rem !default;\r\n$navbar-vertical-hover-width: 12.625rem !default;\r\n$navbar-vertical-variation-width: 14.625rem !default;\r\n$navbar-vertical-variation-collapsed-width: 4.125rem !default;\r\n$navbar-vertical-link-font-size: $font-size-sm !default;\r\n$navbar-vertical-dropdown-font-size: map-get($font-sizes, 10) !default;\r\n$navbar-vertical-collapsed-hover-shadow: 0.625rem 0 0.625rem -0.5625rem rgba($black, 0.2) !default;\r\n// scss-docs-end navbar-vertical-variables\r\n\r\n\r\n// Navbar vertical styles\r\n// \r\n// scss-docs-start navbar-vertical-default-variables\r\n$navbar-vertical-default-bg-color: var(--#{$prefix}bg-navbar-glass) !default;\r\n$navbar-vertical-default-link-color: #{$gray-700} !default;\r\n$navbar-vertical-default-link-hover-color: #{$gray-1000} !default;\r\n$navbar-vertical-default-link-active-color: #{$primary} !default;\r\n$navbar-vertical-default-link-disable-color: #{$gray-400} !default;\r\n$navbar-vertical-default-hr-color: #{rgba($black, 0.08)} !default;\r\n$navbar-vertical-default-scrollbar-color: #{rgba($gray-600, 0.3)} !default;\r\n$navbar-vertical-default-label-color: var(--#{$prefix}gray-500) !default;\r\n// scss-docs-end navbar-vertical-default-variables\r\n\r\n// scss-docs-start navbar-vertical-inverted-variables\r\n$navbar-vertical-inverted-bg-color: $gray-1000 !default;\r\n$navbar-vertical-inverted-link-color: $gray-500 !default;\r\n$navbar-vertical-inverted-link-hover-color: $gray-200 !default;\r\n$navbar-vertical-inverted-link-active-color: $navbar-vertical-inverted-link-hover-color !default;\r\n$navbar-vertical-inverted-link-disable-color: $gray-700 !default;\r\n$navbar-vertical-inverted-hr-color: rgba($white, 0.08) !default;\r\n$navbar-vertical-inverted-scrollbar-color: $gray-400 !default;\r\n$navbar-vertical-inverted-label-color: $gray-700 !default;\r\n// scss-docs-end navbar-vertical-inverted-variables\r\n\r\n// scss-docs-start navbar-vertical-vibrant-variables\r\n$navbar-vertical-vibrant-bg-image: linear-gradient(-45deg,\r\n    rgba(0, 160, 255, 0.86),\r\n    #0048a2),\r\n  url(../img/generic/bg-navbar.png) !default;\r\n$navbar-vertical-vibrant-link-color: rgba($white, 0.75) !default;\r\n$navbar-vertical-vibrant-link-hover-color: $white !default;\r\n$navbar-vertical-vibrant-link-active-color: $navbar-vertical-vibrant-link-hover-color !default;\r\n$navbar-vertical-vibrant-link-disable-color: rgba($white, 0.45) !default;\r\n$navbar-vertical-vibrant-hr-color: rgba($white, 0.2) !default;\r\n$navbar-vertical-vibrant-scrollbar-color: $gray-400 !default;\r\n$navbar-vertical-vibrant-label-color: rgba($white, 0.4) !default;\r\n// scss-docs-end navbar-vertical-vibrant-variables\r\n\r\n// scss-docs-start navbar-vertical-card-variables\r\n$navbar-vertical-card-shadow: $box-shadow !default;\r\n$navbar-vertical-card-bg-color: $card-bg !default;\r\n$navbar-vertical-card-link-color: var(--#{$prefix}gray-700) !default;\r\n$navbar-vertical-card-link-hover-color: var(--#{$prefix}gray-900) !default;\r\n$navbar-vertical-card-link-active-color: var(--#{$prefix}primary) !default;\r\n$navbar-vertical-card-link-disable-color: var(--#{$prefix}gray-400) !default;\r\n$navbar-vertical-card-hr-color: rgba($black, 0.08) !default;\r\n$navbar-vertical-card-scrollbar-color: rgba($gray-600, 0.3) !default;\r\n$navbar-vertical-card-label-color: var(--#{$prefix}gray-500) !default;\r\n// scss-docs-end navbar-vertical-card-variables\r\n\r\n\r\n// Dropdowns\r\n//\r\n// Dropdown menu container and contents.\r\n\r\n// scss-docs-start dropdown-variables\r\n$dropdown-font-size: map_get($font-sizes, 10) !default;\r\n$dropdown-padding-y: map_get($spacers, 3) !default;\r\n$dropdown-item-padding-y: map_get($spacers, 1) !default;\r\n$dropdown-item-padding-x: map_get($spacers, 3) !default;\r\n$dropdown-box-shadow:  var(--#{$prefix}box-shadow) !default;\r\n\r\n$dropdown-bg: var(--#{$prefix}dropdown-bg-global) !default;\r\n$dropdown-color: var(--#{$prefix}gray-300) !default;\r\n$dropdown-divider-bg: var(--#{$prefix}border-color) !default;\r\n$dropdown-border-color: var(--#{$prefix}border-color) !default;\r\n\r\n$dropdown-link-color: var(--#{$prefix}gray-900) !default;\r\n$dropdown-link-hover-color: var(--#{$prefix}dropdown-link-hover-color-global) !default;\r\n$dropdown-link-hover-bg: var(--#{$prefix}dropdown-link-hover-bg-global) !default;\r\n\r\n$dropdown-link-disabled-color: var(--#{$prefix}gray-600) !default;\r\n$dropdown-header-color: var(--#{$prefix}gray-600) !default;\r\n// scss-docs-end dropdown-variables\r\n\r\n\r\n// Pagination\r\n// \r\n// scss-docs-start pagination-variables\r\n$pagination-padding-y: 0.5rem !default;\r\n\r\n$pagination-color: var(--#{$prefix}emphasis-color) !default;\r\n$pagination-bg: var(--#{$prefix}quaternary-bg) !default;\r\n$pagination-border-color: var(--#{$prefix}gray-200) !default;\r\n\r\n$pagination-focus-color: var(--#{$prefix}gray-700) !default;\r\n$pagination-focus-bg: var(--#{$prefix}gray-100) !default;\r\n$pagination-focus-box-shadow: none !default;\r\n\r\n$pagination-hover-color: $white !default;\r\n$pagination-hover-bg: $primary !default;\r\n$pagination-hover-border-color: $primary !default;\r\n\r\n$pagination-disabled-color: $pagination-focus-color !default;\r\n$pagination-disabled-bg: $pagination-focus-bg !default;\r\n$pagination-disabled-border-color: $pagination-border-color !default;\r\n// scss-docs-end pagination-variables\r\n\r\n\r\n// Tooltips\r\n// \r\n// scss-docs-start tooltip-variables\r\n$tooltip-padding-y: 0.5rem !default;\r\n$tooltip-font-size: map_get($font-sizes, 10) !default;\r\n$tooltip-bg: $body-emphasis-color !default;\r\n$tooltip-color: $white !default;\r\n// scss-docs-end tooltip-variables\r\n\r\n\r\n// Badges\r\n// \r\n// scss-docs-start badge-variables\r\n$badge-font-weight: $font-weight-semi-bold !default;\r\n$badge-padding-y: 0.355555em !default;\r\n$badge-padding-x: 0.711111em !default;\r\n// scss-docs-end badge-variables\r\n\r\n\r\n// Modals\r\n// \r\n// scss-docs-start modal-variables\r\n$modal-content-border-radius: $border-radius-lg !default;\r\n$modal-content-bg: var(--#{$prefix}emphasis-bg) !default;\r\n// scss-docs-end modal-variables\r\n\r\n\r\n// List group\r\n// \r\n// scss-docs-start list-group-variables\r\n$list-group-color: var(--#{$prefix}gray-800) !default;\r\n$list-group-bg: var(--#{$prefix}list-group-bg-global) !default;\r\n$list-group-border-color: var(--#{$prefix}border-color) !default;\r\n\r\n$list-group-action-hover-color: var(--#{$prefix}gray-700) !default;\r\n$list-group-hover-bg: var(--#{$prefix}body-bg) !default;\r\n\r\n$list-group-disabled-color: var(--#{$prefix}gray-600) !default;\r\n$list-group-disabled-bg: var(--#{$prefix}card-cap-bg) !default;\r\n\r\n$list-group-action-active-bg: var(--#{$prefix}gray-200) !default;\r\n// scss-docs-end list-group-variables\r\n\r\n\r\n// Image thumbnails\r\n// \r\n// scss-docs-start thumbnail-variables\r\n$thumbnail-bg: var(--#{$prefix}thumbnail-bg-global) !default;\r\n$thumbnail-border-width: 3px !default;\r\n$thumbnail-border-color: var(--#{$prefix}thumbnail-bg-global) !default;\r\n// scss-docs-end thumbnail-variables\r\n\r\n\r\n// Figures\r\n\r\n// scss-docs-start figure-variables\r\n$figure-caption-color: var(--#{$prefix}gray-600) !default;\r\n// scss-docs-end figure-variables\r\n\r\n\r\n// Breadcrumbs\r\n// \r\n// scss-docs-start breadcrumb-variables\r\n$breadcrumb-padding-x: 0 !default;\r\n$breadcrumb-margin-bottom: 0 !default;\r\n$breadcrumb-bg: 'transparent' !default;\r\n$breadcrumb-divider: quote('/') !default;\r\n$breadcrumb-divider-color: $gray-600 !default;\r\n$breadcrumb-active-color: $gray-600 !default;\r\n// scss-docs-end breadcrumb-variables\r\n\r\n\r\n// Carousel\r\n// \r\n// scss-docs-start carousel-variables\r\n$carousel-transition-duration: 0.8s !default;\r\n// scss-docs-end carousel-variables\r\n\r\n\r\n// Spinners\r\n// \r\n// scss-docs-start spinner-variables\r\n$spinner-width-sm: 1.35rem !default;\r\n$spinner-height-sm: $spinner-width-sm !default;\r\n// scss-docs-end spinner-variables\r\n\r\n\r\n// Popovers\r\n// \r\n// scss-docs-start popover-variables\r\n$popover-bg:  var(--#{$prefix}quaternary-bg) !default;\r\n$popover-border-color: rgba(var(--#{$prefix}emphasis-color-rgb), .2) !default;\r\n$popover-header-bg: var(--#{$prefix}popover-header-bg-global) !default;\r\n$popover-header-color: $headings-color !default;\r\n$popover-body-color: $body-color !default;\r\n$popover-box-shadow: var(--#{$prefix}box-shadow) !default;\r\n$popover-arrow-color: var(--#{$prefix}quaternary-bg) !default;\r\n$popover-arrow-outer-color: rgba(var(--#{$prefix}emphasis-color-rgb), .25) !default;\r\n// scss-docs-end popover-variables\r\n\r\n\r\n// Toasts\r\n// \r\n// scss-docs-start toast-variables\r\n$toast-background-color: var(--#{$prefix}toast-bg-global) !default;\r\n$toast-header-color: var(--#{$prefix}gray-600) !default;\r\n$toast-header-background-color: var(--#{$prefix}toast-bg-global) !default;\r\n$toast-header-border-color: rgba($black, 0.05) !default;\r\n$toast-color: var(--#{$prefix}gray-600) !default;\r\n// scss-docs-end toast-variables\r\n\r\n\r\n// Progress bars\r\n// \r\n// scss-docs-start progress-variables\r\n$progress-bg: var(--#{$prefix}gray-200) !default;\r\n// scss-docs-end progress-variables\r\n\r\n\r\n// Offcanvas\r\n// \r\n// scss-docs-start offcanvas-variables\r\n$offcanvas-bg-color: var(--#{$prefix}quaternary-bg) !default;\r\n// scss-docs-end offcanvas-variables\r\n\r\n\r\n// \r\n// Falcon Specific\r\n// \r\n\r\n$transparent-50: rgba(var(--#{$prefix}quaternary-bg-rgb), 0.5) !default;\r\n\r\n// Treeview\r\n// \r\n// scss-docs-start treeview-variables\r\n$treeview-transition-collapse: height .15s ease !default;\r\n$treeview-row-bg-odd : var(--#{$prefix}gray-200) !default;\r\n$treeview-row-bg-even : var(--#{$prefix}gray-100) !default;\r\n$treeview-text-color : var(--#{$prefix}gray-600) !default;\r\n// scss-docs-end treeview-variables\r\n\r\n\r\n// Viewport Heights & Widths\r\n// \r\n// scss-docs-start viewport-heights-map\r\n$viewport-heights: (\r\n  25: 25vh,\r\n  50: 50vh,\r\n  75: 75vh,\r\n  100: 100vh,\r\n) !default;\r\n// scss-docs-end viewport-heights-map\r\n\r\n// scss-docs-start viewport-widths-map\r\n$viewport-widths: (\r\n  25: 25vw,\r\n  50: 50vw,\r\n  75: 75vw,\r\n  100: 100vw,\r\n) !default;\r\n// scss-docs-end viewport-widths-map\r\n\r\n// scss-docs-start sizes-map\r\n$sizes: (\r\n  25: 25%,\r\n  50: 50%,\r\n  75: 75%,\r\n  100: 100%,\r\n  auto: auto,\r\n) !default;\r\n// scss-docs-end sizes-map\r\n\r\n\r\n// Calendar\r\n// \r\n// scss-docs-start calendar-variables\r\n$calendar-color: var(--#{$prefix}danger) !default;\r\n$calendar-color-rgb: var(--#{$prefix}danger-rgb) !default;\r\n// scss-docs-end calendar-variables\r\n\r\n\r\n// Footer\r\n// \r\n// scss-docs-start footer-variables\r\n$footer-height: 3.9875rem !default;\r\n$responsive-footer-height: 0.625rem !default;\r\n// scss-docs-end footer-variables\r\n\r\n\r\n// Avatars dimensions\r\n// \r\n// scss-docs-start avatars-dimension-map\r\n$avatars-dimension: (\r\n  's': toRem(20rem),\r\n  'm': toRem(24rem),\r\n  'l': toRem(28rem),\r\n  'xl': toRem(32rem),\r\n  '2xl': toRem(40rem),\r\n  '3xl': toRem(56rem),\r\n  '4xl': toRem(98rem),\r\n  '5xl': toRem(168rem),\r\n) !default;\r\n// scss-docs-end avatars-dimension-map\r\n\r\n\r\n// Scrollbar\r\n// \r\n// scss-docs-start scrollbar-variables\r\n$scrollbar-bg: var(--#{$prefix}scrollbar-bg) !default;\r\n$simplebar-bg: var(--#{$prefix}simplebar-bg) !default;\r\n// scss-docs-end scrollbar-variables\r\n\r\n\r\n// Timeline\r\n// \r\n// scss-docs-start timeline-variables\r\n$timeline-vertical-breakpoint: lg !default;\r\n$timeline-zigzag-breakpoint: lg !default;\r\n// scss-docs-end timeline-variables\r\n\r\n\r\n// \r\n// Plugins color variables\r\n// \r\n\r\n$data-table-pagination-button-color: $body-emphasis-color !default;"]}