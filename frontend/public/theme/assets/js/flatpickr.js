/*
 * ATTENTION: The "eval" devtool has been used (maybe by default in mode: "development").
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
/******/ (function() { // webpackBootstrap
/******/ 	var __webpack_modules__ = ({

/***/ "./node_modules/flatpickr/dist/esm/index.js":
/*!**************************************************!*\
  !*** ./node_modules/flatpickr/dist/esm/index.js ***!
  \**************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _types_options__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./types/options */ \"./node_modules/flatpickr/dist/esm/types/options.js\");\n/* harmony import */ var _l10n_default__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./l10n/default */ \"./node_modules/flatpickr/dist/esm/l10n/default.js\");\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./utils */ \"./node_modules/flatpickr/dist/esm/utils/index.js\");\n/* harmony import */ var _utils_dom__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./utils/dom */ \"./node_modules/flatpickr/dist/esm/utils/dom.js\");\n/* harmony import */ var _utils_dates__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./utils/dates */ \"./node_modules/flatpickr/dist/esm/utils/dates.js\");\n/* harmony import */ var _utils_formatting__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./utils/formatting */ \"./node_modules/flatpickr/dist/esm/utils/formatting.js\");\n/* harmony import */ var _utils_polyfills__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./utils/polyfills */ \"./node_modules/flatpickr/dist/esm/utils/polyfills.js\");\n/* harmony import */ var _utils_polyfills__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(_utils_polyfills__WEBPACK_IMPORTED_MODULE_6__);\nvar __assign = (undefined && undefined.__assign) || function () {\n    __assign = Object.assign || function(t) {\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\n            s = arguments[i];\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))\n                t[p] = s[p];\n        }\n        return t;\n    };\n    return __assign.apply(this, arguments);\n};\nvar __spreadArrays = (undefined && undefined.__spreadArrays) || function () {\n    for (var s = 0, i = 0, il = arguments.length; i < il; i++) s += arguments[i].length;\n    for (var r = Array(s), k = 0, i = 0; i < il; i++)\n        for (var a = arguments[i], j = 0, jl = a.length; j < jl; j++, k++)\n            r[k] = a[j];\n    return r;\n};\n\n\n\n\n\n\n\nvar DEBOUNCED_CHANGE_MS = 300;\nfunction FlatpickrInstance(element, instanceConfig) {\n    var self = {\n        config: __assign(__assign({}, _types_options__WEBPACK_IMPORTED_MODULE_0__.defaults), flatpickr.defaultConfig),\n        l10n: _l10n_default__WEBPACK_IMPORTED_MODULE_1__[\"default\"],\n    };\n    self.parseDate = (0,_utils_dates__WEBPACK_IMPORTED_MODULE_4__.createDateParser)({ config: self.config, l10n: self.l10n });\n    self._handlers = [];\n    self.pluginElements = [];\n    self.loadedPlugins = [];\n    self._bind = bind;\n    self._setHoursFromDate = setHoursFromDate;\n    self._positionCalendar = positionCalendar;\n    self.changeMonth = changeMonth;\n    self.changeYear = changeYear;\n    self.clear = clear;\n    self.close = close;\n    self.onMouseOver = onMouseOver;\n    self._createElement = _utils_dom__WEBPACK_IMPORTED_MODULE_3__.createElement;\n    self.createDay = createDay;\n    self.destroy = destroy;\n    self.isEnabled = isEnabled;\n    self.jumpToDate = jumpToDate;\n    self.updateValue = updateValue;\n    self.open = open;\n    self.redraw = redraw;\n    self.set = set;\n    self.setDate = setDate;\n    self.toggle = toggle;\n    function setupHelperFunctions() {\n        self.utils = {\n            getDaysInMonth: function (month, yr) {\n                if (month === void 0) { month = self.currentMonth; }\n                if (yr === void 0) { yr = self.currentYear; }\n                if (month === 1 && ((yr % 4 === 0 && yr % 100 !== 0) || yr % 400 === 0))\n                    return 29;\n                return self.l10n.daysInMonth[month];\n            },\n        };\n    }\n    function init() {\n        self.element = self.input = element;\n        self.isOpen = false;\n        parseConfig();\n        setupLocale();\n        setupInputs();\n        setupDates();\n        setupHelperFunctions();\n        if (!self.isMobile)\n            build();\n        bindEvents();\n        if (self.selectedDates.length || self.config.noCalendar) {\n            if (self.config.enableTime) {\n                setHoursFromDate(self.config.noCalendar ? self.latestSelectedDateObj : undefined);\n            }\n            updateValue(false);\n        }\n        setCalendarWidth();\n        var isSafari = /^((?!chrome|android).)*safari/i.test(navigator.userAgent);\n        if (!self.isMobile && isSafari) {\n            positionCalendar();\n        }\n        triggerEvent(\"onReady\");\n    }\n    function getClosestActiveElement() {\n        var _a;\n        return (((_a = self.calendarContainer) === null || _a === void 0 ? void 0 : _a.getRootNode())\n            .activeElement || document.activeElement);\n    }\n    function bindToInstance(fn) {\n        return fn.bind(self);\n    }\n    function setCalendarWidth() {\n        var config = self.config;\n        if (config.weekNumbers === false && config.showMonths === 1) {\n            return;\n        }\n        else if (config.noCalendar !== true) {\n            window.requestAnimationFrame(function () {\n                if (self.calendarContainer !== undefined) {\n                    self.calendarContainer.style.visibility = \"hidden\";\n                    self.calendarContainer.style.display = \"block\";\n                }\n                if (self.daysContainer !== undefined) {\n                    var daysWidth = (self.days.offsetWidth + 1) * config.showMonths;\n                    self.daysContainer.style.width = daysWidth + \"px\";\n                    self.calendarContainer.style.width =\n                        daysWidth +\n                            (self.weekWrapper !== undefined\n                                ? self.weekWrapper.offsetWidth\n                                : 0) +\n                            \"px\";\n                    self.calendarContainer.style.removeProperty(\"visibility\");\n                    self.calendarContainer.style.removeProperty(\"display\");\n                }\n            });\n        }\n    }\n    function updateTime(e) {\n        if (self.selectedDates.length === 0) {\n            var defaultDate = self.config.minDate === undefined ||\n                (0,_utils_dates__WEBPACK_IMPORTED_MODULE_4__.compareDates)(new Date(), self.config.minDate) >= 0\n                ? new Date()\n                : new Date(self.config.minDate.getTime());\n            var defaults = (0,_utils_dates__WEBPACK_IMPORTED_MODULE_4__.getDefaultHours)(self.config);\n            defaultDate.setHours(defaults.hours, defaults.minutes, defaults.seconds, defaultDate.getMilliseconds());\n            self.selectedDates = [defaultDate];\n            self.latestSelectedDateObj = defaultDate;\n        }\n        if (e !== undefined && e.type !== \"blur\") {\n            timeWrapper(e);\n        }\n        var prevValue = self._input.value;\n        setHoursFromInputs();\n        updateValue();\n        if (self._input.value !== prevValue) {\n            self._debouncedChange();\n        }\n    }\n    function ampm2military(hour, amPM) {\n        return (hour % 12) + 12 * (0,_utils__WEBPACK_IMPORTED_MODULE_2__.int)(amPM === self.l10n.amPM[1]);\n    }\n    function military2ampm(hour) {\n        switch (hour % 24) {\n            case 0:\n            case 12:\n                return 12;\n            default:\n                return hour % 12;\n        }\n    }\n    function setHoursFromInputs() {\n        if (self.hourElement === undefined || self.minuteElement === undefined)\n            return;\n        var hours = (parseInt(self.hourElement.value.slice(-2), 10) || 0) % 24, minutes = (parseInt(self.minuteElement.value, 10) || 0) % 60, seconds = self.secondElement !== undefined\n            ? (parseInt(self.secondElement.value, 10) || 0) % 60\n            : 0;\n        if (self.amPM !== undefined) {\n            hours = ampm2military(hours, self.amPM.textContent);\n        }\n        var limitMinHours = self.config.minTime !== undefined ||\n            (self.config.minDate &&\n                self.minDateHasTime &&\n                self.latestSelectedDateObj &&\n                (0,_utils_dates__WEBPACK_IMPORTED_MODULE_4__.compareDates)(self.latestSelectedDateObj, self.config.minDate, true) ===\n                    0);\n        var limitMaxHours = self.config.maxTime !== undefined ||\n            (self.config.maxDate &&\n                self.maxDateHasTime &&\n                self.latestSelectedDateObj &&\n                (0,_utils_dates__WEBPACK_IMPORTED_MODULE_4__.compareDates)(self.latestSelectedDateObj, self.config.maxDate, true) ===\n                    0);\n        if (self.config.maxTime !== undefined &&\n            self.config.minTime !== undefined &&\n            self.config.minTime > self.config.maxTime) {\n            var minBound = (0,_utils_dates__WEBPACK_IMPORTED_MODULE_4__.calculateSecondsSinceMidnight)(self.config.minTime.getHours(), self.config.minTime.getMinutes(), self.config.minTime.getSeconds());\n            var maxBound = (0,_utils_dates__WEBPACK_IMPORTED_MODULE_4__.calculateSecondsSinceMidnight)(self.config.maxTime.getHours(), self.config.maxTime.getMinutes(), self.config.maxTime.getSeconds());\n            var currentTime = (0,_utils_dates__WEBPACK_IMPORTED_MODULE_4__.calculateSecondsSinceMidnight)(hours, minutes, seconds);\n            if (currentTime > maxBound && currentTime < minBound) {\n                var result = (0,_utils_dates__WEBPACK_IMPORTED_MODULE_4__.parseSeconds)(minBound);\n                hours = result[0];\n                minutes = result[1];\n                seconds = result[2];\n            }\n        }\n        else {\n            if (limitMaxHours) {\n                var maxTime = self.config.maxTime !== undefined\n                    ? self.config.maxTime\n                    : self.config.maxDate;\n                hours = Math.min(hours, maxTime.getHours());\n                if (hours === maxTime.getHours())\n                    minutes = Math.min(minutes, maxTime.getMinutes());\n                if (minutes === maxTime.getMinutes())\n                    seconds = Math.min(seconds, maxTime.getSeconds());\n            }\n            if (limitMinHours) {\n                var minTime = self.config.minTime !== undefined\n                    ? self.config.minTime\n                    : self.config.minDate;\n                hours = Math.max(hours, minTime.getHours());\n                if (hours === minTime.getHours() && minutes < minTime.getMinutes())\n                    minutes = minTime.getMinutes();\n                if (minutes === minTime.getMinutes())\n                    seconds = Math.max(seconds, minTime.getSeconds());\n            }\n        }\n        setHours(hours, minutes, seconds);\n    }\n    function setHoursFromDate(dateObj) {\n        var date = dateObj || self.latestSelectedDateObj;\n        if (date && date instanceof Date) {\n            setHours(date.getHours(), date.getMinutes(), date.getSeconds());\n        }\n    }\n    function setHours(hours, minutes, seconds) {\n        if (self.latestSelectedDateObj !== undefined) {\n            self.latestSelectedDateObj.setHours(hours % 24, minutes, seconds || 0, 0);\n        }\n        if (!self.hourElement || !self.minuteElement || self.isMobile)\n            return;\n        self.hourElement.value = (0,_utils__WEBPACK_IMPORTED_MODULE_2__.pad)(!self.config.time_24hr\n            ? ((12 + hours) % 12) + 12 * (0,_utils__WEBPACK_IMPORTED_MODULE_2__.int)(hours % 12 === 0)\n            : hours);\n        self.minuteElement.value = (0,_utils__WEBPACK_IMPORTED_MODULE_2__.pad)(minutes);\n        if (self.amPM !== undefined)\n            self.amPM.textContent = self.l10n.amPM[(0,_utils__WEBPACK_IMPORTED_MODULE_2__.int)(hours >= 12)];\n        if (self.secondElement !== undefined)\n            self.secondElement.value = (0,_utils__WEBPACK_IMPORTED_MODULE_2__.pad)(seconds);\n    }\n    function onYearInput(event) {\n        var eventTarget = (0,_utils_dom__WEBPACK_IMPORTED_MODULE_3__.getEventTarget)(event);\n        var year = parseInt(eventTarget.value) + (event.delta || 0);\n        if (year / 1000 > 1 ||\n            (event.key === \"Enter\" && !/[^\\d]/.test(year.toString()))) {\n            changeYear(year);\n        }\n    }\n    function bind(element, event, handler, options) {\n        if (event instanceof Array)\n            return event.forEach(function (ev) { return bind(element, ev, handler, options); });\n        if (element instanceof Array)\n            return element.forEach(function (el) { return bind(el, event, handler, options); });\n        element.addEventListener(event, handler, options);\n        self._handlers.push({\n            remove: function () { return element.removeEventListener(event, handler, options); },\n        });\n    }\n    function triggerChange() {\n        triggerEvent(\"onChange\");\n    }\n    function bindEvents() {\n        if (self.config.wrap) {\n            [\"open\", \"close\", \"toggle\", \"clear\"].forEach(function (evt) {\n                Array.prototype.forEach.call(self.element.querySelectorAll(\"[data-\" + evt + \"]\"), function (el) {\n                    return bind(el, \"click\", self[evt]);\n                });\n            });\n        }\n        if (self.isMobile) {\n            setupMobile();\n            return;\n        }\n        var debouncedResize = (0,_utils__WEBPACK_IMPORTED_MODULE_2__.debounce)(onResize, 50);\n        self._debouncedChange = (0,_utils__WEBPACK_IMPORTED_MODULE_2__.debounce)(triggerChange, DEBOUNCED_CHANGE_MS);\n        if (self.daysContainer && !/iPhone|iPad|iPod/i.test(navigator.userAgent))\n            bind(self.daysContainer, \"mouseover\", function (e) {\n                if (self.config.mode === \"range\")\n                    onMouseOver((0,_utils_dom__WEBPACK_IMPORTED_MODULE_3__.getEventTarget)(e));\n            });\n        bind(self._input, \"keydown\", onKeyDown);\n        if (self.calendarContainer !== undefined) {\n            bind(self.calendarContainer, \"keydown\", onKeyDown);\n        }\n        if (!self.config.inline && !self.config.static)\n            bind(window, \"resize\", debouncedResize);\n        if (window.ontouchstart !== undefined)\n            bind(window.document, \"touchstart\", documentClick);\n        else\n            bind(window.document, \"mousedown\", documentClick);\n        bind(window.document, \"focus\", documentClick, { capture: true });\n        if (self.config.clickOpens === true) {\n            bind(self._input, \"focus\", self.open);\n            bind(self._input, \"click\", self.open);\n        }\n        if (self.daysContainer !== undefined) {\n            bind(self.monthNav, \"click\", onMonthNavClick);\n            bind(self.monthNav, [\"keyup\", \"increment\"], onYearInput);\n            bind(self.daysContainer, \"click\", selectDate);\n        }\n        if (self.timeContainer !== undefined &&\n            self.minuteElement !== undefined &&\n            self.hourElement !== undefined) {\n            var selText = function (e) {\n                return (0,_utils_dom__WEBPACK_IMPORTED_MODULE_3__.getEventTarget)(e).select();\n            };\n            bind(self.timeContainer, [\"increment\"], updateTime);\n            bind(self.timeContainer, \"blur\", updateTime, { capture: true });\n            bind(self.timeContainer, \"click\", timeIncrement);\n            bind([self.hourElement, self.minuteElement], [\"focus\", \"click\"], selText);\n            if (self.secondElement !== undefined)\n                bind(self.secondElement, \"focus\", function () { return self.secondElement && self.secondElement.select(); });\n            if (self.amPM !== undefined) {\n                bind(self.amPM, \"click\", function (e) {\n                    updateTime(e);\n                });\n            }\n        }\n        if (self.config.allowInput) {\n            bind(self._input, \"blur\", onBlur);\n        }\n    }\n    function jumpToDate(jumpDate, triggerChange) {\n        var jumpTo = jumpDate !== undefined\n            ? self.parseDate(jumpDate)\n            : self.latestSelectedDateObj ||\n                (self.config.minDate && self.config.minDate > self.now\n                    ? self.config.minDate\n                    : self.config.maxDate && self.config.maxDate < self.now\n                        ? self.config.maxDate\n                        : self.now);\n        var oldYear = self.currentYear;\n        var oldMonth = self.currentMonth;\n        try {\n            if (jumpTo !== undefined) {\n                self.currentYear = jumpTo.getFullYear();\n                self.currentMonth = jumpTo.getMonth();\n            }\n        }\n        catch (e) {\n            e.message = \"Invalid date supplied: \" + jumpTo;\n            self.config.errorHandler(e);\n        }\n        if (triggerChange && self.currentYear !== oldYear) {\n            triggerEvent(\"onYearChange\");\n            buildMonthSwitch();\n        }\n        if (triggerChange &&\n            (self.currentYear !== oldYear || self.currentMonth !== oldMonth)) {\n            triggerEvent(\"onMonthChange\");\n        }\n        self.redraw();\n    }\n    function timeIncrement(e) {\n        var eventTarget = (0,_utils_dom__WEBPACK_IMPORTED_MODULE_3__.getEventTarget)(e);\n        if (~eventTarget.className.indexOf(\"arrow\"))\n            incrementNumInput(e, eventTarget.classList.contains(\"arrowUp\") ? 1 : -1);\n    }\n    function incrementNumInput(e, delta, inputElem) {\n        var target = e && (0,_utils_dom__WEBPACK_IMPORTED_MODULE_3__.getEventTarget)(e);\n        var input = inputElem ||\n            (target && target.parentNode && target.parentNode.firstChild);\n        var event = createEvent(\"increment\");\n        event.delta = delta;\n        input && input.dispatchEvent(event);\n    }\n    function build() {\n        var fragment = window.document.createDocumentFragment();\n        self.calendarContainer = (0,_utils_dom__WEBPACK_IMPORTED_MODULE_3__.createElement)(\"div\", \"flatpickr-calendar\");\n        self.calendarContainer.tabIndex = -1;\n        if (!self.config.noCalendar) {\n            fragment.appendChild(buildMonthNav());\n            self.innerContainer = (0,_utils_dom__WEBPACK_IMPORTED_MODULE_3__.createElement)(\"div\", \"flatpickr-innerContainer\");\n            if (self.config.weekNumbers) {\n                var _a = buildWeeks(), weekWrapper = _a.weekWrapper, weekNumbers = _a.weekNumbers;\n                self.innerContainer.appendChild(weekWrapper);\n                self.weekNumbers = weekNumbers;\n                self.weekWrapper = weekWrapper;\n            }\n            self.rContainer = (0,_utils_dom__WEBPACK_IMPORTED_MODULE_3__.createElement)(\"div\", \"flatpickr-rContainer\");\n            self.rContainer.appendChild(buildWeekdays());\n            if (!self.daysContainer) {\n                self.daysContainer = (0,_utils_dom__WEBPACK_IMPORTED_MODULE_3__.createElement)(\"div\", \"flatpickr-days\");\n                self.daysContainer.tabIndex = -1;\n            }\n            buildDays();\n            self.rContainer.appendChild(self.daysContainer);\n            self.innerContainer.appendChild(self.rContainer);\n            fragment.appendChild(self.innerContainer);\n        }\n        if (self.config.enableTime) {\n            fragment.appendChild(buildTime());\n        }\n        (0,_utils_dom__WEBPACK_IMPORTED_MODULE_3__.toggleClass)(self.calendarContainer, \"rangeMode\", self.config.mode === \"range\");\n        (0,_utils_dom__WEBPACK_IMPORTED_MODULE_3__.toggleClass)(self.calendarContainer, \"animate\", self.config.animate === true);\n        (0,_utils_dom__WEBPACK_IMPORTED_MODULE_3__.toggleClass)(self.calendarContainer, \"multiMonth\", self.config.showMonths > 1);\n        self.calendarContainer.appendChild(fragment);\n        var customAppend = self.config.appendTo !== undefined &&\n            self.config.appendTo.nodeType !== undefined;\n        if (self.config.inline || self.config.static) {\n            self.calendarContainer.classList.add(self.config.inline ? \"inline\" : \"static\");\n            if (self.config.inline) {\n                if (!customAppend && self.element.parentNode)\n                    self.element.parentNode.insertBefore(self.calendarContainer, self._input.nextSibling);\n                else if (self.config.appendTo !== undefined)\n                    self.config.appendTo.appendChild(self.calendarContainer);\n            }\n            if (self.config.static) {\n                var wrapper = (0,_utils_dom__WEBPACK_IMPORTED_MODULE_3__.createElement)(\"div\", \"flatpickr-wrapper\");\n                if (self.element.parentNode)\n                    self.element.parentNode.insertBefore(wrapper, self.element);\n                wrapper.appendChild(self.element);\n                if (self.altInput)\n                    wrapper.appendChild(self.altInput);\n                wrapper.appendChild(self.calendarContainer);\n            }\n        }\n        if (!self.config.static && !self.config.inline)\n            (self.config.appendTo !== undefined\n                ? self.config.appendTo\n                : window.document.body).appendChild(self.calendarContainer);\n    }\n    function createDay(className, date, _dayNumber, i) {\n        var dateIsEnabled = isEnabled(date, true), dayElement = (0,_utils_dom__WEBPACK_IMPORTED_MODULE_3__.createElement)(\"span\", className, date.getDate().toString());\n        dayElement.dateObj = date;\n        dayElement.$i = i;\n        dayElement.setAttribute(\"aria-label\", self.formatDate(date, self.config.ariaDateFormat));\n        if (className.indexOf(\"hidden\") === -1 &&\n            (0,_utils_dates__WEBPACK_IMPORTED_MODULE_4__.compareDates)(date, self.now) === 0) {\n            self.todayDateElem = dayElement;\n            dayElement.classList.add(\"today\");\n            dayElement.setAttribute(\"aria-current\", \"date\");\n        }\n        if (dateIsEnabled) {\n            dayElement.tabIndex = -1;\n            if (isDateSelected(date)) {\n                dayElement.classList.add(\"selected\");\n                self.selectedDateElem = dayElement;\n                if (self.config.mode === \"range\") {\n                    (0,_utils_dom__WEBPACK_IMPORTED_MODULE_3__.toggleClass)(dayElement, \"startRange\", self.selectedDates[0] &&\n                        (0,_utils_dates__WEBPACK_IMPORTED_MODULE_4__.compareDates)(date, self.selectedDates[0], true) === 0);\n                    (0,_utils_dom__WEBPACK_IMPORTED_MODULE_3__.toggleClass)(dayElement, \"endRange\", self.selectedDates[1] &&\n                        (0,_utils_dates__WEBPACK_IMPORTED_MODULE_4__.compareDates)(date, self.selectedDates[1], true) === 0);\n                    if (className === \"nextMonthDay\")\n                        dayElement.classList.add(\"inRange\");\n                }\n            }\n        }\n        else {\n            dayElement.classList.add(\"flatpickr-disabled\");\n        }\n        if (self.config.mode === \"range\") {\n            if (isDateInRange(date) && !isDateSelected(date))\n                dayElement.classList.add(\"inRange\");\n        }\n        if (self.weekNumbers &&\n            self.config.showMonths === 1 &&\n            className !== \"prevMonthDay\" &&\n            i % 7 === 6) {\n            self.weekNumbers.insertAdjacentHTML(\"beforeend\", \"<span class='flatpickr-day'>\" + self.config.getWeek(date) + \"</span>\");\n        }\n        triggerEvent(\"onDayCreate\", dayElement);\n        return dayElement;\n    }\n    function focusOnDayElem(targetNode) {\n        targetNode.focus();\n        if (self.config.mode === \"range\")\n            onMouseOver(targetNode);\n    }\n    function getFirstAvailableDay(delta) {\n        var startMonth = delta > 0 ? 0 : self.config.showMonths - 1;\n        var endMonth = delta > 0 ? self.config.showMonths : -1;\n        for (var m = startMonth; m != endMonth; m += delta) {\n            var month = self.daysContainer.children[m];\n            var startIndex = delta > 0 ? 0 : month.children.length - 1;\n            var endIndex = delta > 0 ? month.children.length : -1;\n            for (var i = startIndex; i != endIndex; i += delta) {\n                var c = month.children[i];\n                if (c.className.indexOf(\"hidden\") === -1 && isEnabled(c.dateObj))\n                    return c;\n            }\n        }\n        return undefined;\n    }\n    function getNextAvailableDay(current, delta) {\n        var givenMonth = current.className.indexOf(\"Month\") === -1\n            ? current.dateObj.getMonth()\n            : self.currentMonth;\n        var endMonth = delta > 0 ? self.config.showMonths : -1;\n        var loopDelta = delta > 0 ? 1 : -1;\n        for (var m = givenMonth - self.currentMonth; m != endMonth; m += loopDelta) {\n            var month = self.daysContainer.children[m];\n            var startIndex = givenMonth - self.currentMonth === m\n                ? current.$i + delta\n                : delta < 0\n                    ? month.children.length - 1\n                    : 0;\n            var numMonthDays = month.children.length;\n            for (var i = startIndex; i >= 0 && i < numMonthDays && i != (delta > 0 ? numMonthDays : -1); i += loopDelta) {\n                var c = month.children[i];\n                if (c.className.indexOf(\"hidden\") === -1 &&\n                    isEnabled(c.dateObj) &&\n                    Math.abs(current.$i - i) >= Math.abs(delta))\n                    return focusOnDayElem(c);\n            }\n        }\n        self.changeMonth(loopDelta);\n        focusOnDay(getFirstAvailableDay(loopDelta), 0);\n        return undefined;\n    }\n    function focusOnDay(current, offset) {\n        var activeElement = getClosestActiveElement();\n        var dayFocused = isInView(activeElement || document.body);\n        var startElem = current !== undefined\n            ? current\n            : dayFocused\n                ? activeElement\n                : self.selectedDateElem !== undefined && isInView(self.selectedDateElem)\n                    ? self.selectedDateElem\n                    : self.todayDateElem !== undefined && isInView(self.todayDateElem)\n                        ? self.todayDateElem\n                        : getFirstAvailableDay(offset > 0 ? 1 : -1);\n        if (startElem === undefined) {\n            self._input.focus();\n        }\n        else if (!dayFocused) {\n            focusOnDayElem(startElem);\n        }\n        else {\n            getNextAvailableDay(startElem, offset);\n        }\n    }\n    function buildMonthDays(year, month) {\n        var firstOfMonth = (new Date(year, month, 1).getDay() - self.l10n.firstDayOfWeek + 7) % 7;\n        var prevMonthDays = self.utils.getDaysInMonth((month - 1 + 12) % 12, year);\n        var daysInMonth = self.utils.getDaysInMonth(month, year), days = window.document.createDocumentFragment(), isMultiMonth = self.config.showMonths > 1, prevMonthDayClass = isMultiMonth ? \"prevMonthDay hidden\" : \"prevMonthDay\", nextMonthDayClass = isMultiMonth ? \"nextMonthDay hidden\" : \"nextMonthDay\";\n        var dayNumber = prevMonthDays + 1 - firstOfMonth, dayIndex = 0;\n        for (; dayNumber <= prevMonthDays; dayNumber++, dayIndex++) {\n            days.appendChild(createDay(\"flatpickr-day \" + prevMonthDayClass, new Date(year, month - 1, dayNumber), dayNumber, dayIndex));\n        }\n        for (dayNumber = 1; dayNumber <= daysInMonth; dayNumber++, dayIndex++) {\n            days.appendChild(createDay(\"flatpickr-day\", new Date(year, month, dayNumber), dayNumber, dayIndex));\n        }\n        for (var dayNum = daysInMonth + 1; dayNum <= 42 - firstOfMonth &&\n            (self.config.showMonths === 1 || dayIndex % 7 !== 0); dayNum++, dayIndex++) {\n            days.appendChild(createDay(\"flatpickr-day \" + nextMonthDayClass, new Date(year, month + 1, dayNum % daysInMonth), dayNum, dayIndex));\n        }\n        var dayContainer = (0,_utils_dom__WEBPACK_IMPORTED_MODULE_3__.createElement)(\"div\", \"dayContainer\");\n        dayContainer.appendChild(days);\n        return dayContainer;\n    }\n    function buildDays() {\n        if (self.daysContainer === undefined) {\n            return;\n        }\n        (0,_utils_dom__WEBPACK_IMPORTED_MODULE_3__.clearNode)(self.daysContainer);\n        if (self.weekNumbers)\n            (0,_utils_dom__WEBPACK_IMPORTED_MODULE_3__.clearNode)(self.weekNumbers);\n        var frag = document.createDocumentFragment();\n        for (var i = 0; i < self.config.showMonths; i++) {\n            var d = new Date(self.currentYear, self.currentMonth, 1);\n            d.setMonth(self.currentMonth + i);\n            frag.appendChild(buildMonthDays(d.getFullYear(), d.getMonth()));\n        }\n        self.daysContainer.appendChild(frag);\n        self.days = self.daysContainer.firstChild;\n        if (self.config.mode === \"range\" && self.selectedDates.length === 1) {\n            onMouseOver();\n        }\n    }\n    function buildMonthSwitch() {\n        if (self.config.showMonths > 1 ||\n            self.config.monthSelectorType !== \"dropdown\")\n            return;\n        var shouldBuildMonth = function (month) {\n            if (self.config.minDate !== undefined &&\n                self.currentYear === self.config.minDate.getFullYear() &&\n                month < self.config.minDate.getMonth()) {\n                return false;\n            }\n            return !(self.config.maxDate !== undefined &&\n                self.currentYear === self.config.maxDate.getFullYear() &&\n                month > self.config.maxDate.getMonth());\n        };\n        self.monthsDropdownContainer.tabIndex = -1;\n        self.monthsDropdownContainer.innerHTML = \"\";\n        for (var i = 0; i < 12; i++) {\n            if (!shouldBuildMonth(i))\n                continue;\n            var month = (0,_utils_dom__WEBPACK_IMPORTED_MODULE_3__.createElement)(\"option\", \"flatpickr-monthDropdown-month\");\n            month.value = new Date(self.currentYear, i).getMonth().toString();\n            month.textContent = (0,_utils_formatting__WEBPACK_IMPORTED_MODULE_5__.monthToStr)(i, self.config.shorthandCurrentMonth, self.l10n);\n            month.tabIndex = -1;\n            if (self.currentMonth === i) {\n                month.selected = true;\n            }\n            self.monthsDropdownContainer.appendChild(month);\n        }\n    }\n    function buildMonth() {\n        var container = (0,_utils_dom__WEBPACK_IMPORTED_MODULE_3__.createElement)(\"div\", \"flatpickr-month\");\n        var monthNavFragment = window.document.createDocumentFragment();\n        var monthElement;\n        if (self.config.showMonths > 1 ||\n            self.config.monthSelectorType === \"static\") {\n            monthElement = (0,_utils_dom__WEBPACK_IMPORTED_MODULE_3__.createElement)(\"span\", \"cur-month\");\n        }\n        else {\n            self.monthsDropdownContainer = (0,_utils_dom__WEBPACK_IMPORTED_MODULE_3__.createElement)(\"select\", \"flatpickr-monthDropdown-months\");\n            self.monthsDropdownContainer.setAttribute(\"aria-label\", self.l10n.monthAriaLabel);\n            bind(self.monthsDropdownContainer, \"change\", function (e) {\n                var target = (0,_utils_dom__WEBPACK_IMPORTED_MODULE_3__.getEventTarget)(e);\n                var selectedMonth = parseInt(target.value, 10);\n                self.changeMonth(selectedMonth - self.currentMonth);\n                triggerEvent(\"onMonthChange\");\n            });\n            buildMonthSwitch();\n            monthElement = self.monthsDropdownContainer;\n        }\n        var yearInput = (0,_utils_dom__WEBPACK_IMPORTED_MODULE_3__.createNumberInput)(\"cur-year\", { tabindex: \"-1\" });\n        var yearElement = yearInput.getElementsByTagName(\"input\")[0];\n        yearElement.setAttribute(\"aria-label\", self.l10n.yearAriaLabel);\n        if (self.config.minDate) {\n            yearElement.setAttribute(\"min\", self.config.minDate.getFullYear().toString());\n        }\n        if (self.config.maxDate) {\n            yearElement.setAttribute(\"max\", self.config.maxDate.getFullYear().toString());\n            yearElement.disabled =\n                !!self.config.minDate &&\n                    self.config.minDate.getFullYear() === self.config.maxDate.getFullYear();\n        }\n        var currentMonth = (0,_utils_dom__WEBPACK_IMPORTED_MODULE_3__.createElement)(\"div\", \"flatpickr-current-month\");\n        currentMonth.appendChild(monthElement);\n        currentMonth.appendChild(yearInput);\n        monthNavFragment.appendChild(currentMonth);\n        container.appendChild(monthNavFragment);\n        return {\n            container: container,\n            yearElement: yearElement,\n            monthElement: monthElement,\n        };\n    }\n    function buildMonths() {\n        (0,_utils_dom__WEBPACK_IMPORTED_MODULE_3__.clearNode)(self.monthNav);\n        self.monthNav.appendChild(self.prevMonthNav);\n        if (self.config.showMonths) {\n            self.yearElements = [];\n            self.monthElements = [];\n        }\n        for (var m = self.config.showMonths; m--;) {\n            var month = buildMonth();\n            self.yearElements.push(month.yearElement);\n            self.monthElements.push(month.monthElement);\n            self.monthNav.appendChild(month.container);\n        }\n        self.monthNav.appendChild(self.nextMonthNav);\n    }\n    function buildMonthNav() {\n        self.monthNav = (0,_utils_dom__WEBPACK_IMPORTED_MODULE_3__.createElement)(\"div\", \"flatpickr-months\");\n        self.yearElements = [];\n        self.monthElements = [];\n        self.prevMonthNav = (0,_utils_dom__WEBPACK_IMPORTED_MODULE_3__.createElement)(\"span\", \"flatpickr-prev-month\");\n        self.prevMonthNav.innerHTML = self.config.prevArrow;\n        self.nextMonthNav = (0,_utils_dom__WEBPACK_IMPORTED_MODULE_3__.createElement)(\"span\", \"flatpickr-next-month\");\n        self.nextMonthNav.innerHTML = self.config.nextArrow;\n        buildMonths();\n        Object.defineProperty(self, \"_hidePrevMonthArrow\", {\n            get: function () { return self.__hidePrevMonthArrow; },\n            set: function (bool) {\n                if (self.__hidePrevMonthArrow !== bool) {\n                    (0,_utils_dom__WEBPACK_IMPORTED_MODULE_3__.toggleClass)(self.prevMonthNav, \"flatpickr-disabled\", bool);\n                    self.__hidePrevMonthArrow = bool;\n                }\n            },\n        });\n        Object.defineProperty(self, \"_hideNextMonthArrow\", {\n            get: function () { return self.__hideNextMonthArrow; },\n            set: function (bool) {\n                if (self.__hideNextMonthArrow !== bool) {\n                    (0,_utils_dom__WEBPACK_IMPORTED_MODULE_3__.toggleClass)(self.nextMonthNav, \"flatpickr-disabled\", bool);\n                    self.__hideNextMonthArrow = bool;\n                }\n            },\n        });\n        self.currentYearElement = self.yearElements[0];\n        updateNavigationCurrentMonth();\n        return self.monthNav;\n    }\n    function buildTime() {\n        self.calendarContainer.classList.add(\"hasTime\");\n        if (self.config.noCalendar)\n            self.calendarContainer.classList.add(\"noCalendar\");\n        var defaults = (0,_utils_dates__WEBPACK_IMPORTED_MODULE_4__.getDefaultHours)(self.config);\n        self.timeContainer = (0,_utils_dom__WEBPACK_IMPORTED_MODULE_3__.createElement)(\"div\", \"flatpickr-time\");\n        self.timeContainer.tabIndex = -1;\n        var separator = (0,_utils_dom__WEBPACK_IMPORTED_MODULE_3__.createElement)(\"span\", \"flatpickr-time-separator\", \":\");\n        var hourInput = (0,_utils_dom__WEBPACK_IMPORTED_MODULE_3__.createNumberInput)(\"flatpickr-hour\", {\n            \"aria-label\": self.l10n.hourAriaLabel,\n        });\n        self.hourElement = hourInput.getElementsByTagName(\"input\")[0];\n        var minuteInput = (0,_utils_dom__WEBPACK_IMPORTED_MODULE_3__.createNumberInput)(\"flatpickr-minute\", {\n            \"aria-label\": self.l10n.minuteAriaLabel,\n        });\n        self.minuteElement = minuteInput.getElementsByTagName(\"input\")[0];\n        self.hourElement.tabIndex = self.minuteElement.tabIndex = -1;\n        self.hourElement.value = (0,_utils__WEBPACK_IMPORTED_MODULE_2__.pad)(self.latestSelectedDateObj\n            ? self.latestSelectedDateObj.getHours()\n            : self.config.time_24hr\n                ? defaults.hours\n                : military2ampm(defaults.hours));\n        self.minuteElement.value = (0,_utils__WEBPACK_IMPORTED_MODULE_2__.pad)(self.latestSelectedDateObj\n            ? self.latestSelectedDateObj.getMinutes()\n            : defaults.minutes);\n        self.hourElement.setAttribute(\"step\", self.config.hourIncrement.toString());\n        self.minuteElement.setAttribute(\"step\", self.config.minuteIncrement.toString());\n        self.hourElement.setAttribute(\"min\", self.config.time_24hr ? \"0\" : \"1\");\n        self.hourElement.setAttribute(\"max\", self.config.time_24hr ? \"23\" : \"12\");\n        self.hourElement.setAttribute(\"maxlength\", \"2\");\n        self.minuteElement.setAttribute(\"min\", \"0\");\n        self.minuteElement.setAttribute(\"max\", \"59\");\n        self.minuteElement.setAttribute(\"maxlength\", \"2\");\n        self.timeContainer.appendChild(hourInput);\n        self.timeContainer.appendChild(separator);\n        self.timeContainer.appendChild(minuteInput);\n        if (self.config.time_24hr)\n            self.timeContainer.classList.add(\"time24hr\");\n        if (self.config.enableSeconds) {\n            self.timeContainer.classList.add(\"hasSeconds\");\n            var secondInput = (0,_utils_dom__WEBPACK_IMPORTED_MODULE_3__.createNumberInput)(\"flatpickr-second\");\n            self.secondElement = secondInput.getElementsByTagName(\"input\")[0];\n            self.secondElement.value = (0,_utils__WEBPACK_IMPORTED_MODULE_2__.pad)(self.latestSelectedDateObj\n                ? self.latestSelectedDateObj.getSeconds()\n                : defaults.seconds);\n            self.secondElement.setAttribute(\"step\", self.minuteElement.getAttribute(\"step\"));\n            self.secondElement.setAttribute(\"min\", \"0\");\n            self.secondElement.setAttribute(\"max\", \"59\");\n            self.secondElement.setAttribute(\"maxlength\", \"2\");\n            self.timeContainer.appendChild((0,_utils_dom__WEBPACK_IMPORTED_MODULE_3__.createElement)(\"span\", \"flatpickr-time-separator\", \":\"));\n            self.timeContainer.appendChild(secondInput);\n        }\n        if (!self.config.time_24hr) {\n            self.amPM = (0,_utils_dom__WEBPACK_IMPORTED_MODULE_3__.createElement)(\"span\", \"flatpickr-am-pm\", self.l10n.amPM[(0,_utils__WEBPACK_IMPORTED_MODULE_2__.int)((self.latestSelectedDateObj\n                ? self.hourElement.value\n                : self.config.defaultHour) > 11)]);\n            self.amPM.title = self.l10n.toggleTitle;\n            self.amPM.tabIndex = -1;\n            self.timeContainer.appendChild(self.amPM);\n        }\n        return self.timeContainer;\n    }\n    function buildWeekdays() {\n        if (!self.weekdayContainer)\n            self.weekdayContainer = (0,_utils_dom__WEBPACK_IMPORTED_MODULE_3__.createElement)(\"div\", \"flatpickr-weekdays\");\n        else\n            (0,_utils_dom__WEBPACK_IMPORTED_MODULE_3__.clearNode)(self.weekdayContainer);\n        for (var i = self.config.showMonths; i--;) {\n            var container = (0,_utils_dom__WEBPACK_IMPORTED_MODULE_3__.createElement)(\"div\", \"flatpickr-weekdaycontainer\");\n            self.weekdayContainer.appendChild(container);\n        }\n        updateWeekdays();\n        return self.weekdayContainer;\n    }\n    function updateWeekdays() {\n        if (!self.weekdayContainer) {\n            return;\n        }\n        var firstDayOfWeek = self.l10n.firstDayOfWeek;\n        var weekdays = __spreadArrays(self.l10n.weekdays.shorthand);\n        if (firstDayOfWeek > 0 && firstDayOfWeek < weekdays.length) {\n            weekdays = __spreadArrays(weekdays.splice(firstDayOfWeek, weekdays.length), weekdays.splice(0, firstDayOfWeek));\n        }\n        for (var i = self.config.showMonths; i--;) {\n            self.weekdayContainer.children[i].innerHTML = \"\\n      <span class='flatpickr-weekday'>\\n        \" + weekdays.join(\"</span><span class='flatpickr-weekday'>\") + \"\\n      </span>\\n      \";\n        }\n    }\n    function buildWeeks() {\n        self.calendarContainer.classList.add(\"hasWeeks\");\n        var weekWrapper = (0,_utils_dom__WEBPACK_IMPORTED_MODULE_3__.createElement)(\"div\", \"flatpickr-weekwrapper\");\n        weekWrapper.appendChild((0,_utils_dom__WEBPACK_IMPORTED_MODULE_3__.createElement)(\"span\", \"flatpickr-weekday\", self.l10n.weekAbbreviation));\n        var weekNumbers = (0,_utils_dom__WEBPACK_IMPORTED_MODULE_3__.createElement)(\"div\", \"flatpickr-weeks\");\n        weekWrapper.appendChild(weekNumbers);\n        return {\n            weekWrapper: weekWrapper,\n            weekNumbers: weekNumbers,\n        };\n    }\n    function changeMonth(value, isOffset) {\n        if (isOffset === void 0) { isOffset = true; }\n        var delta = isOffset ? value : value - self.currentMonth;\n        if ((delta < 0 && self._hidePrevMonthArrow === true) ||\n            (delta > 0 && self._hideNextMonthArrow === true))\n            return;\n        self.currentMonth += delta;\n        if (self.currentMonth < 0 || self.currentMonth > 11) {\n            self.currentYear += self.currentMonth > 11 ? 1 : -1;\n            self.currentMonth = (self.currentMonth + 12) % 12;\n            triggerEvent(\"onYearChange\");\n            buildMonthSwitch();\n        }\n        buildDays();\n        triggerEvent(\"onMonthChange\");\n        updateNavigationCurrentMonth();\n    }\n    function clear(triggerChangeEvent, toInitial) {\n        if (triggerChangeEvent === void 0) { triggerChangeEvent = true; }\n        if (toInitial === void 0) { toInitial = true; }\n        self.input.value = \"\";\n        if (self.altInput !== undefined)\n            self.altInput.value = \"\";\n        if (self.mobileInput !== undefined)\n            self.mobileInput.value = \"\";\n        self.selectedDates = [];\n        self.latestSelectedDateObj = undefined;\n        if (toInitial === true) {\n            self.currentYear = self._initialDate.getFullYear();\n            self.currentMonth = self._initialDate.getMonth();\n        }\n        if (self.config.enableTime === true) {\n            var _a = (0,_utils_dates__WEBPACK_IMPORTED_MODULE_4__.getDefaultHours)(self.config), hours = _a.hours, minutes = _a.minutes, seconds = _a.seconds;\n            setHours(hours, minutes, seconds);\n        }\n        self.redraw();\n        if (triggerChangeEvent)\n            triggerEvent(\"onChange\");\n    }\n    function close() {\n        self.isOpen = false;\n        if (!self.isMobile) {\n            if (self.calendarContainer !== undefined) {\n                self.calendarContainer.classList.remove(\"open\");\n            }\n            if (self._input !== undefined) {\n                self._input.classList.remove(\"active\");\n            }\n        }\n        triggerEvent(\"onClose\");\n    }\n    function destroy() {\n        if (self.config !== undefined)\n            triggerEvent(\"onDestroy\");\n        for (var i = self._handlers.length; i--;) {\n            self._handlers[i].remove();\n        }\n        self._handlers = [];\n        if (self.mobileInput) {\n            if (self.mobileInput.parentNode)\n                self.mobileInput.parentNode.removeChild(self.mobileInput);\n            self.mobileInput = undefined;\n        }\n        else if (self.calendarContainer && self.calendarContainer.parentNode) {\n            if (self.config.static && self.calendarContainer.parentNode) {\n                var wrapper = self.calendarContainer.parentNode;\n                wrapper.lastChild && wrapper.removeChild(wrapper.lastChild);\n                if (wrapper.parentNode) {\n                    while (wrapper.firstChild)\n                        wrapper.parentNode.insertBefore(wrapper.firstChild, wrapper);\n                    wrapper.parentNode.removeChild(wrapper);\n                }\n            }\n            else\n                self.calendarContainer.parentNode.removeChild(self.calendarContainer);\n        }\n        if (self.altInput) {\n            self.input.type = \"text\";\n            if (self.altInput.parentNode)\n                self.altInput.parentNode.removeChild(self.altInput);\n            delete self.altInput;\n        }\n        if (self.input) {\n            self.input.type = self.input._type;\n            self.input.classList.remove(\"flatpickr-input\");\n            self.input.removeAttribute(\"readonly\");\n        }\n        [\n            \"_showTimeInput\",\n            \"latestSelectedDateObj\",\n            \"_hideNextMonthArrow\",\n            \"_hidePrevMonthArrow\",\n            \"__hideNextMonthArrow\",\n            \"__hidePrevMonthArrow\",\n            \"isMobile\",\n            \"isOpen\",\n            \"selectedDateElem\",\n            \"minDateHasTime\",\n            \"maxDateHasTime\",\n            \"days\",\n            \"daysContainer\",\n            \"_input\",\n            \"_positionElement\",\n            \"innerContainer\",\n            \"rContainer\",\n            \"monthNav\",\n            \"todayDateElem\",\n            \"calendarContainer\",\n            \"weekdayContainer\",\n            \"prevMonthNav\",\n            \"nextMonthNav\",\n            \"monthsDropdownContainer\",\n            \"currentMonthElement\",\n            \"currentYearElement\",\n            \"navigationCurrentMonth\",\n            \"selectedDateElem\",\n            \"config\",\n        ].forEach(function (k) {\n            try {\n                delete self[k];\n            }\n            catch (_) { }\n        });\n    }\n    function isCalendarElem(elem) {\n        return self.calendarContainer.contains(elem);\n    }\n    function documentClick(e) {\n        if (self.isOpen && !self.config.inline) {\n            var eventTarget_1 = (0,_utils_dom__WEBPACK_IMPORTED_MODULE_3__.getEventTarget)(e);\n            var isCalendarElement = isCalendarElem(eventTarget_1);\n            var isInput = eventTarget_1 === self.input ||\n                eventTarget_1 === self.altInput ||\n                self.element.contains(eventTarget_1) ||\n                (e.path &&\n                    e.path.indexOf &&\n                    (~e.path.indexOf(self.input) ||\n                        ~e.path.indexOf(self.altInput)));\n            var lostFocus = !isInput &&\n                !isCalendarElement &&\n                !isCalendarElem(e.relatedTarget);\n            var isIgnored = !self.config.ignoredFocusElements.some(function (elem) {\n                return elem.contains(eventTarget_1);\n            });\n            if (lostFocus && isIgnored) {\n                if (self.config.allowInput) {\n                    self.setDate(self._input.value, false, self.config.altInput\n                        ? self.config.altFormat\n                        : self.config.dateFormat);\n                }\n                if (self.timeContainer !== undefined &&\n                    self.minuteElement !== undefined &&\n                    self.hourElement !== undefined &&\n                    self.input.value !== \"\" &&\n                    self.input.value !== undefined) {\n                    updateTime();\n                }\n                self.close();\n                if (self.config &&\n                    self.config.mode === \"range\" &&\n                    self.selectedDates.length === 1)\n                    self.clear(false);\n            }\n        }\n    }\n    function changeYear(newYear) {\n        if (!newYear ||\n            (self.config.minDate && newYear < self.config.minDate.getFullYear()) ||\n            (self.config.maxDate && newYear > self.config.maxDate.getFullYear()))\n            return;\n        var newYearNum = newYear, isNewYear = self.currentYear !== newYearNum;\n        self.currentYear = newYearNum || self.currentYear;\n        if (self.config.maxDate &&\n            self.currentYear === self.config.maxDate.getFullYear()) {\n            self.currentMonth = Math.min(self.config.maxDate.getMonth(), self.currentMonth);\n        }\n        else if (self.config.minDate &&\n            self.currentYear === self.config.minDate.getFullYear()) {\n            self.currentMonth = Math.max(self.config.minDate.getMonth(), self.currentMonth);\n        }\n        if (isNewYear) {\n            self.redraw();\n            triggerEvent(\"onYearChange\");\n            buildMonthSwitch();\n        }\n    }\n    function isEnabled(date, timeless) {\n        var _a;\n        if (timeless === void 0) { timeless = true; }\n        var dateToCheck = self.parseDate(date, undefined, timeless);\n        if ((self.config.minDate &&\n            dateToCheck &&\n            (0,_utils_dates__WEBPACK_IMPORTED_MODULE_4__.compareDates)(dateToCheck, self.config.minDate, timeless !== undefined ? timeless : !self.minDateHasTime) < 0) ||\n            (self.config.maxDate &&\n                dateToCheck &&\n                (0,_utils_dates__WEBPACK_IMPORTED_MODULE_4__.compareDates)(dateToCheck, self.config.maxDate, timeless !== undefined ? timeless : !self.maxDateHasTime) > 0))\n            return false;\n        if (!self.config.enable && self.config.disable.length === 0)\n            return true;\n        if (dateToCheck === undefined)\n            return false;\n        var bool = !!self.config.enable, array = (_a = self.config.enable) !== null && _a !== void 0 ? _a : self.config.disable;\n        for (var i = 0, d = void 0; i < array.length; i++) {\n            d = array[i];\n            if (typeof d === \"function\" &&\n                d(dateToCheck))\n                return bool;\n            else if (d instanceof Date &&\n                dateToCheck !== undefined &&\n                d.getTime() === dateToCheck.getTime())\n                return bool;\n            else if (typeof d === \"string\") {\n                var parsed = self.parseDate(d, undefined, true);\n                return parsed && parsed.getTime() === dateToCheck.getTime()\n                    ? bool\n                    : !bool;\n            }\n            else if (typeof d === \"object\" &&\n                dateToCheck !== undefined &&\n                d.from &&\n                d.to &&\n                dateToCheck.getTime() >= d.from.getTime() &&\n                dateToCheck.getTime() <= d.to.getTime())\n                return bool;\n        }\n        return !bool;\n    }\n    function isInView(elem) {\n        if (self.daysContainer !== undefined)\n            return (elem.className.indexOf(\"hidden\") === -1 &&\n                elem.className.indexOf(\"flatpickr-disabled\") === -1 &&\n                self.daysContainer.contains(elem));\n        return false;\n    }\n    function onBlur(e) {\n        var isInput = e.target === self._input;\n        var valueChanged = self._input.value.trimEnd() !== getDateStr();\n        if (isInput &&\n            valueChanged &&\n            !(e.relatedTarget && isCalendarElem(e.relatedTarget))) {\n            self.setDate(self._input.value, true, e.target === self.altInput\n                ? self.config.altFormat\n                : self.config.dateFormat);\n        }\n    }\n    function onKeyDown(e) {\n        var eventTarget = (0,_utils_dom__WEBPACK_IMPORTED_MODULE_3__.getEventTarget)(e);\n        var isInput = self.config.wrap\n            ? element.contains(eventTarget)\n            : eventTarget === self._input;\n        var allowInput = self.config.allowInput;\n        var allowKeydown = self.isOpen && (!allowInput || !isInput);\n        var allowInlineKeydown = self.config.inline && isInput && !allowInput;\n        if (e.keyCode === 13 && isInput) {\n            if (allowInput) {\n                self.setDate(self._input.value, true, eventTarget === self.altInput\n                    ? self.config.altFormat\n                    : self.config.dateFormat);\n                self.close();\n                return eventTarget.blur();\n            }\n            else {\n                self.open();\n            }\n        }\n        else if (isCalendarElem(eventTarget) ||\n            allowKeydown ||\n            allowInlineKeydown) {\n            var isTimeObj = !!self.timeContainer &&\n                self.timeContainer.contains(eventTarget);\n            switch (e.keyCode) {\n                case 13:\n                    if (isTimeObj) {\n                        e.preventDefault();\n                        updateTime();\n                        focusAndClose();\n                    }\n                    else\n                        selectDate(e);\n                    break;\n                case 27:\n                    e.preventDefault();\n                    focusAndClose();\n                    break;\n                case 8:\n                case 46:\n                    if (isInput && !self.config.allowInput) {\n                        e.preventDefault();\n                        self.clear();\n                    }\n                    break;\n                case 37:\n                case 39:\n                    if (!isTimeObj && !isInput) {\n                        e.preventDefault();\n                        var activeElement = getClosestActiveElement();\n                        if (self.daysContainer !== undefined &&\n                            (allowInput === false ||\n                                (activeElement && isInView(activeElement)))) {\n                            var delta_1 = e.keyCode === 39 ? 1 : -1;\n                            if (!e.ctrlKey)\n                                focusOnDay(undefined, delta_1);\n                            else {\n                                e.stopPropagation();\n                                changeMonth(delta_1);\n                                focusOnDay(getFirstAvailableDay(1), 0);\n                            }\n                        }\n                    }\n                    else if (self.hourElement)\n                        self.hourElement.focus();\n                    break;\n                case 38:\n                case 40:\n                    e.preventDefault();\n                    var delta = e.keyCode === 40 ? 1 : -1;\n                    if ((self.daysContainer &&\n                        eventTarget.$i !== undefined) ||\n                        eventTarget === self.input ||\n                        eventTarget === self.altInput) {\n                        if (e.ctrlKey) {\n                            e.stopPropagation();\n                            changeYear(self.currentYear - delta);\n                            focusOnDay(getFirstAvailableDay(1), 0);\n                        }\n                        else if (!isTimeObj)\n                            focusOnDay(undefined, delta * 7);\n                    }\n                    else if (eventTarget === self.currentYearElement) {\n                        changeYear(self.currentYear - delta);\n                    }\n                    else if (self.config.enableTime) {\n                        if (!isTimeObj && self.hourElement)\n                            self.hourElement.focus();\n                        updateTime(e);\n                        self._debouncedChange();\n                    }\n                    break;\n                case 9:\n                    if (isTimeObj) {\n                        var elems = [\n                            self.hourElement,\n                            self.minuteElement,\n                            self.secondElement,\n                            self.amPM,\n                        ]\n                            .concat(self.pluginElements)\n                            .filter(function (x) { return x; });\n                        var i = elems.indexOf(eventTarget);\n                        if (i !== -1) {\n                            var target = elems[i + (e.shiftKey ? -1 : 1)];\n                            e.preventDefault();\n                            (target || self._input).focus();\n                        }\n                    }\n                    else if (!self.config.noCalendar &&\n                        self.daysContainer &&\n                        self.daysContainer.contains(eventTarget) &&\n                        e.shiftKey) {\n                        e.preventDefault();\n                        self._input.focus();\n                    }\n                    break;\n                default:\n                    break;\n            }\n        }\n        if (self.amPM !== undefined && eventTarget === self.amPM) {\n            switch (e.key) {\n                case self.l10n.amPM[0].charAt(0):\n                case self.l10n.amPM[0].charAt(0).toLowerCase():\n                    self.amPM.textContent = self.l10n.amPM[0];\n                    setHoursFromInputs();\n                    updateValue();\n                    break;\n                case self.l10n.amPM[1].charAt(0):\n                case self.l10n.amPM[1].charAt(0).toLowerCase():\n                    self.amPM.textContent = self.l10n.amPM[1];\n                    setHoursFromInputs();\n                    updateValue();\n                    break;\n            }\n        }\n        if (isInput || isCalendarElem(eventTarget)) {\n            triggerEvent(\"onKeyDown\", e);\n        }\n    }\n    function onMouseOver(elem, cellClass) {\n        if (cellClass === void 0) { cellClass = \"flatpickr-day\"; }\n        if (self.selectedDates.length !== 1 ||\n            (elem &&\n                (!elem.classList.contains(cellClass) ||\n                    elem.classList.contains(\"flatpickr-disabled\"))))\n            return;\n        var hoverDate = elem\n            ? elem.dateObj.getTime()\n            : self.days.firstElementChild.dateObj.getTime(), initialDate = self.parseDate(self.selectedDates[0], undefined, true).getTime(), rangeStartDate = Math.min(hoverDate, self.selectedDates[0].getTime()), rangeEndDate = Math.max(hoverDate, self.selectedDates[0].getTime());\n        var containsDisabled = false;\n        var minRange = 0, maxRange = 0;\n        for (var t = rangeStartDate; t < rangeEndDate; t += _utils_dates__WEBPACK_IMPORTED_MODULE_4__.duration.DAY) {\n            if (!isEnabled(new Date(t), true)) {\n                containsDisabled =\n                    containsDisabled || (t > rangeStartDate && t < rangeEndDate);\n                if (t < initialDate && (!minRange || t > minRange))\n                    minRange = t;\n                else if (t > initialDate && (!maxRange || t < maxRange))\n                    maxRange = t;\n            }\n        }\n        var hoverableCells = Array.from(self.rContainer.querySelectorAll(\"*:nth-child(-n+\" + self.config.showMonths + \") > .\" + cellClass));\n        hoverableCells.forEach(function (dayElem) {\n            var date = dayElem.dateObj;\n            var timestamp = date.getTime();\n            var outOfRange = (minRange > 0 && timestamp < minRange) ||\n                (maxRange > 0 && timestamp > maxRange);\n            if (outOfRange) {\n                dayElem.classList.add(\"notAllowed\");\n                [\"inRange\", \"startRange\", \"endRange\"].forEach(function (c) {\n                    dayElem.classList.remove(c);\n                });\n                return;\n            }\n            else if (containsDisabled && !outOfRange)\n                return;\n            [\"startRange\", \"inRange\", \"endRange\", \"notAllowed\"].forEach(function (c) {\n                dayElem.classList.remove(c);\n            });\n            if (elem !== undefined) {\n                elem.classList.add(hoverDate <= self.selectedDates[0].getTime()\n                    ? \"startRange\"\n                    : \"endRange\");\n                if (initialDate < hoverDate && timestamp === initialDate)\n                    dayElem.classList.add(\"startRange\");\n                else if (initialDate > hoverDate && timestamp === initialDate)\n                    dayElem.classList.add(\"endRange\");\n                if (timestamp >= minRange &&\n                    (maxRange === 0 || timestamp <= maxRange) &&\n                    (0,_utils_dates__WEBPACK_IMPORTED_MODULE_4__.isBetween)(timestamp, initialDate, hoverDate))\n                    dayElem.classList.add(\"inRange\");\n            }\n        });\n    }\n    function onResize() {\n        if (self.isOpen && !self.config.static && !self.config.inline)\n            positionCalendar();\n    }\n    function open(e, positionElement) {\n        if (positionElement === void 0) { positionElement = self._positionElement; }\n        if (self.isMobile === true) {\n            if (e) {\n                e.preventDefault();\n                var eventTarget = (0,_utils_dom__WEBPACK_IMPORTED_MODULE_3__.getEventTarget)(e);\n                if (eventTarget) {\n                    eventTarget.blur();\n                }\n            }\n            if (self.mobileInput !== undefined) {\n                self.mobileInput.focus();\n                self.mobileInput.click();\n            }\n            triggerEvent(\"onOpen\");\n            return;\n        }\n        else if (self._input.disabled || self.config.inline) {\n            return;\n        }\n        var wasOpen = self.isOpen;\n        self.isOpen = true;\n        if (!wasOpen) {\n            self.calendarContainer.classList.add(\"open\");\n            self._input.classList.add(\"active\");\n            triggerEvent(\"onOpen\");\n            positionCalendar(positionElement);\n        }\n        if (self.config.enableTime === true && self.config.noCalendar === true) {\n            if (self.config.allowInput === false &&\n                (e === undefined ||\n                    !self.timeContainer.contains(e.relatedTarget))) {\n                setTimeout(function () { return self.hourElement.select(); }, 50);\n            }\n        }\n    }\n    function minMaxDateSetter(type) {\n        return function (date) {\n            var dateObj = (self.config[\"_\" + type + \"Date\"] = self.parseDate(date, self.config.dateFormat));\n            var inverseDateObj = self.config[\"_\" + (type === \"min\" ? \"max\" : \"min\") + \"Date\"];\n            if (dateObj !== undefined) {\n                self[type === \"min\" ? \"minDateHasTime\" : \"maxDateHasTime\"] =\n                    dateObj.getHours() > 0 ||\n                        dateObj.getMinutes() > 0 ||\n                        dateObj.getSeconds() > 0;\n            }\n            if (self.selectedDates) {\n                self.selectedDates = self.selectedDates.filter(function (d) { return isEnabled(d); });\n                if (!self.selectedDates.length && type === \"min\")\n                    setHoursFromDate(dateObj);\n                updateValue();\n            }\n            if (self.daysContainer) {\n                redraw();\n                if (dateObj !== undefined)\n                    self.currentYearElement[type] = dateObj.getFullYear().toString();\n                else\n                    self.currentYearElement.removeAttribute(type);\n                self.currentYearElement.disabled =\n                    !!inverseDateObj &&\n                        dateObj !== undefined &&\n                        inverseDateObj.getFullYear() === dateObj.getFullYear();\n            }\n        };\n    }\n    function parseConfig() {\n        var boolOpts = [\n            \"wrap\",\n            \"weekNumbers\",\n            \"allowInput\",\n            \"allowInvalidPreload\",\n            \"clickOpens\",\n            \"time_24hr\",\n            \"enableTime\",\n            \"noCalendar\",\n            \"altInput\",\n            \"shorthandCurrentMonth\",\n            \"inline\",\n            \"static\",\n            \"enableSeconds\",\n            \"disableMobile\",\n        ];\n        var userConfig = __assign(__assign({}, JSON.parse(JSON.stringify(element.dataset || {}))), instanceConfig);\n        var formats = {};\n        self.config.parseDate = userConfig.parseDate;\n        self.config.formatDate = userConfig.formatDate;\n        Object.defineProperty(self.config, \"enable\", {\n            get: function () { return self.config._enable; },\n            set: function (dates) {\n                self.config._enable = parseDateRules(dates);\n            },\n        });\n        Object.defineProperty(self.config, \"disable\", {\n            get: function () { return self.config._disable; },\n            set: function (dates) {\n                self.config._disable = parseDateRules(dates);\n            },\n        });\n        var timeMode = userConfig.mode === \"time\";\n        if (!userConfig.dateFormat && (userConfig.enableTime || timeMode)) {\n            var defaultDateFormat = flatpickr.defaultConfig.dateFormat || _types_options__WEBPACK_IMPORTED_MODULE_0__.defaults.dateFormat;\n            formats.dateFormat =\n                userConfig.noCalendar || timeMode\n                    ? \"H:i\" + (userConfig.enableSeconds ? \":S\" : \"\")\n                    : defaultDateFormat + \" H:i\" + (userConfig.enableSeconds ? \":S\" : \"\");\n        }\n        if (userConfig.altInput &&\n            (userConfig.enableTime || timeMode) &&\n            !userConfig.altFormat) {\n            var defaultAltFormat = flatpickr.defaultConfig.altFormat || _types_options__WEBPACK_IMPORTED_MODULE_0__.defaults.altFormat;\n            formats.altFormat =\n                userConfig.noCalendar || timeMode\n                    ? \"h:i\" + (userConfig.enableSeconds ? \":S K\" : \" K\")\n                    : defaultAltFormat + (\" h:i\" + (userConfig.enableSeconds ? \":S\" : \"\") + \" K\");\n        }\n        Object.defineProperty(self.config, \"minDate\", {\n            get: function () { return self.config._minDate; },\n            set: minMaxDateSetter(\"min\"),\n        });\n        Object.defineProperty(self.config, \"maxDate\", {\n            get: function () { return self.config._maxDate; },\n            set: minMaxDateSetter(\"max\"),\n        });\n        var minMaxTimeSetter = function (type) { return function (val) {\n            self.config[type === \"min\" ? \"_minTime\" : \"_maxTime\"] = self.parseDate(val, \"H:i:S\");\n        }; };\n        Object.defineProperty(self.config, \"minTime\", {\n            get: function () { return self.config._minTime; },\n            set: minMaxTimeSetter(\"min\"),\n        });\n        Object.defineProperty(self.config, \"maxTime\", {\n            get: function () { return self.config._maxTime; },\n            set: minMaxTimeSetter(\"max\"),\n        });\n        if (userConfig.mode === \"time\") {\n            self.config.noCalendar = true;\n            self.config.enableTime = true;\n        }\n        Object.assign(self.config, formats, userConfig);\n        for (var i = 0; i < boolOpts.length; i++)\n            self.config[boolOpts[i]] =\n                self.config[boolOpts[i]] === true ||\n                    self.config[boolOpts[i]] === \"true\";\n        _types_options__WEBPACK_IMPORTED_MODULE_0__.HOOKS.filter(function (hook) { return self.config[hook] !== undefined; }).forEach(function (hook) {\n            self.config[hook] = (0,_utils__WEBPACK_IMPORTED_MODULE_2__.arrayify)(self.config[hook] || []).map(bindToInstance);\n        });\n        self.isMobile =\n            !self.config.disableMobile &&\n                !self.config.inline &&\n                self.config.mode === \"single\" &&\n                !self.config.disable.length &&\n                !self.config.enable &&\n                !self.config.weekNumbers &&\n                /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);\n        for (var i = 0; i < self.config.plugins.length; i++) {\n            var pluginConf = self.config.plugins[i](self) || {};\n            for (var key in pluginConf) {\n                if (_types_options__WEBPACK_IMPORTED_MODULE_0__.HOOKS.indexOf(key) > -1) {\n                    self.config[key] = (0,_utils__WEBPACK_IMPORTED_MODULE_2__.arrayify)(pluginConf[key])\n                        .map(bindToInstance)\n                        .concat(self.config[key]);\n                }\n                else if (typeof userConfig[key] === \"undefined\")\n                    self.config[key] = pluginConf[key];\n            }\n        }\n        if (!userConfig.altInputClass) {\n            self.config.altInputClass =\n                getInputElem().className + \" \" + self.config.altInputClass;\n        }\n        triggerEvent(\"onParseConfig\");\n    }\n    function getInputElem() {\n        return self.config.wrap\n            ? element.querySelector(\"[data-input]\")\n            : element;\n    }\n    function setupLocale() {\n        if (typeof self.config.locale !== \"object\" &&\n            typeof flatpickr.l10ns[self.config.locale] === \"undefined\")\n            self.config.errorHandler(new Error(\"flatpickr: invalid locale \" + self.config.locale));\n        self.l10n = __assign(__assign({}, flatpickr.l10ns.default), (typeof self.config.locale === \"object\"\n            ? self.config.locale\n            : self.config.locale !== \"default\"\n                ? flatpickr.l10ns[self.config.locale]\n                : undefined));\n        _utils_formatting__WEBPACK_IMPORTED_MODULE_5__.tokenRegex.D = \"(\" + self.l10n.weekdays.shorthand.join(\"|\") + \")\";\n        _utils_formatting__WEBPACK_IMPORTED_MODULE_5__.tokenRegex.l = \"(\" + self.l10n.weekdays.longhand.join(\"|\") + \")\";\n        _utils_formatting__WEBPACK_IMPORTED_MODULE_5__.tokenRegex.M = \"(\" + self.l10n.months.shorthand.join(\"|\") + \")\";\n        _utils_formatting__WEBPACK_IMPORTED_MODULE_5__.tokenRegex.F = \"(\" + self.l10n.months.longhand.join(\"|\") + \")\";\n        _utils_formatting__WEBPACK_IMPORTED_MODULE_5__.tokenRegex.K = \"(\" + self.l10n.amPM[0] + \"|\" + self.l10n.amPM[1] + \"|\" + self.l10n.amPM[0].toLowerCase() + \"|\" + self.l10n.amPM[1].toLowerCase() + \")\";\n        var userConfig = __assign(__assign({}, instanceConfig), JSON.parse(JSON.stringify(element.dataset || {})));\n        if (userConfig.time_24hr === undefined &&\n            flatpickr.defaultConfig.time_24hr === undefined) {\n            self.config.time_24hr = self.l10n.time_24hr;\n        }\n        self.formatDate = (0,_utils_dates__WEBPACK_IMPORTED_MODULE_4__.createDateFormatter)(self);\n        self.parseDate = (0,_utils_dates__WEBPACK_IMPORTED_MODULE_4__.createDateParser)({ config: self.config, l10n: self.l10n });\n    }\n    function positionCalendar(customPositionElement) {\n        if (typeof self.config.position === \"function\") {\n            return void self.config.position(self, customPositionElement);\n        }\n        if (self.calendarContainer === undefined)\n            return;\n        triggerEvent(\"onPreCalendarPosition\");\n        var positionElement = customPositionElement || self._positionElement;\n        var calendarHeight = Array.prototype.reduce.call(self.calendarContainer.children, (function (acc, child) { return acc + child.offsetHeight; }), 0), calendarWidth = self.calendarContainer.offsetWidth, configPos = self.config.position.split(\" \"), configPosVertical = configPos[0], configPosHorizontal = configPos.length > 1 ? configPos[1] : null, inputBounds = positionElement.getBoundingClientRect(), distanceFromBottom = window.innerHeight - inputBounds.bottom, showOnTop = configPosVertical === \"above\" ||\n            (configPosVertical !== \"below\" &&\n                distanceFromBottom < calendarHeight &&\n                inputBounds.top > calendarHeight);\n        var top = window.pageYOffset +\n            inputBounds.top +\n            (!showOnTop ? positionElement.offsetHeight + 2 : -calendarHeight - 2);\n        (0,_utils_dom__WEBPACK_IMPORTED_MODULE_3__.toggleClass)(self.calendarContainer, \"arrowTop\", !showOnTop);\n        (0,_utils_dom__WEBPACK_IMPORTED_MODULE_3__.toggleClass)(self.calendarContainer, \"arrowBottom\", showOnTop);\n        if (self.config.inline)\n            return;\n        var left = window.pageXOffset + inputBounds.left;\n        var isCenter = false;\n        var isRight = false;\n        if (configPosHorizontal === \"center\") {\n            left -= (calendarWidth - inputBounds.width) / 2;\n            isCenter = true;\n        }\n        else if (configPosHorizontal === \"right\") {\n            left -= calendarWidth - inputBounds.width;\n            isRight = true;\n        }\n        (0,_utils_dom__WEBPACK_IMPORTED_MODULE_3__.toggleClass)(self.calendarContainer, \"arrowLeft\", !isCenter && !isRight);\n        (0,_utils_dom__WEBPACK_IMPORTED_MODULE_3__.toggleClass)(self.calendarContainer, \"arrowCenter\", isCenter);\n        (0,_utils_dom__WEBPACK_IMPORTED_MODULE_3__.toggleClass)(self.calendarContainer, \"arrowRight\", isRight);\n        var right = window.document.body.offsetWidth -\n            (window.pageXOffset + inputBounds.right);\n        var rightMost = left + calendarWidth > window.document.body.offsetWidth;\n        var centerMost = right + calendarWidth > window.document.body.offsetWidth;\n        (0,_utils_dom__WEBPACK_IMPORTED_MODULE_3__.toggleClass)(self.calendarContainer, \"rightMost\", rightMost);\n        if (self.config.static)\n            return;\n        self.calendarContainer.style.top = top + \"px\";\n        if (!rightMost) {\n            self.calendarContainer.style.left = left + \"px\";\n            self.calendarContainer.style.right = \"auto\";\n        }\n        else if (!centerMost) {\n            self.calendarContainer.style.left = \"auto\";\n            self.calendarContainer.style.right = right + \"px\";\n        }\n        else {\n            var doc = getDocumentStyleSheet();\n            if (doc === undefined)\n                return;\n            var bodyWidth = window.document.body.offsetWidth;\n            var centerLeft = Math.max(0, bodyWidth / 2 - calendarWidth / 2);\n            var centerBefore = \".flatpickr-calendar.centerMost:before\";\n            var centerAfter = \".flatpickr-calendar.centerMost:after\";\n            var centerIndex = doc.cssRules.length;\n            var centerStyle = \"{left:\" + inputBounds.left + \"px;right:auto;}\";\n            (0,_utils_dom__WEBPACK_IMPORTED_MODULE_3__.toggleClass)(self.calendarContainer, \"rightMost\", false);\n            (0,_utils_dom__WEBPACK_IMPORTED_MODULE_3__.toggleClass)(self.calendarContainer, \"centerMost\", true);\n            doc.insertRule(centerBefore + \",\" + centerAfter + centerStyle, centerIndex);\n            self.calendarContainer.style.left = centerLeft + \"px\";\n            self.calendarContainer.style.right = \"auto\";\n        }\n    }\n    function getDocumentStyleSheet() {\n        var editableSheet = null;\n        for (var i = 0; i < document.styleSheets.length; i++) {\n            var sheet = document.styleSheets[i];\n            if (!sheet.cssRules)\n                continue;\n            try {\n                sheet.cssRules;\n            }\n            catch (err) {\n                continue;\n            }\n            editableSheet = sheet;\n            break;\n        }\n        return editableSheet != null ? editableSheet : createStyleSheet();\n    }\n    function createStyleSheet() {\n        var style = document.createElement(\"style\");\n        document.head.appendChild(style);\n        return style.sheet;\n    }\n    function redraw() {\n        if (self.config.noCalendar || self.isMobile)\n            return;\n        buildMonthSwitch();\n        updateNavigationCurrentMonth();\n        buildDays();\n    }\n    function focusAndClose() {\n        self._input.focus();\n        if (window.navigator.userAgent.indexOf(\"MSIE\") !== -1 ||\n            navigator.msMaxTouchPoints !== undefined) {\n            setTimeout(self.close, 0);\n        }\n        else {\n            self.close();\n        }\n    }\n    function selectDate(e) {\n        e.preventDefault();\n        e.stopPropagation();\n        var isSelectable = function (day) {\n            return day.classList &&\n                day.classList.contains(\"flatpickr-day\") &&\n                !day.classList.contains(\"flatpickr-disabled\") &&\n                !day.classList.contains(\"notAllowed\");\n        };\n        var t = (0,_utils_dom__WEBPACK_IMPORTED_MODULE_3__.findParent)((0,_utils_dom__WEBPACK_IMPORTED_MODULE_3__.getEventTarget)(e), isSelectable);\n        if (t === undefined)\n            return;\n        var target = t;\n        var selectedDate = (self.latestSelectedDateObj = new Date(target.dateObj.getTime()));\n        var shouldChangeMonth = (selectedDate.getMonth() < self.currentMonth ||\n            selectedDate.getMonth() >\n                self.currentMonth + self.config.showMonths - 1) &&\n            self.config.mode !== \"range\";\n        self.selectedDateElem = target;\n        if (self.config.mode === \"single\")\n            self.selectedDates = [selectedDate];\n        else if (self.config.mode === \"multiple\") {\n            var selectedIndex = isDateSelected(selectedDate);\n            if (selectedIndex)\n                self.selectedDates.splice(parseInt(selectedIndex), 1);\n            else\n                self.selectedDates.push(selectedDate);\n        }\n        else if (self.config.mode === \"range\") {\n            if (self.selectedDates.length === 2) {\n                self.clear(false, false);\n            }\n            self.latestSelectedDateObj = selectedDate;\n            self.selectedDates.push(selectedDate);\n            if ((0,_utils_dates__WEBPACK_IMPORTED_MODULE_4__.compareDates)(selectedDate, self.selectedDates[0], true) !== 0)\n                self.selectedDates.sort(function (a, b) { return a.getTime() - b.getTime(); });\n        }\n        setHoursFromInputs();\n        if (shouldChangeMonth) {\n            var isNewYear = self.currentYear !== selectedDate.getFullYear();\n            self.currentYear = selectedDate.getFullYear();\n            self.currentMonth = selectedDate.getMonth();\n            if (isNewYear) {\n                triggerEvent(\"onYearChange\");\n                buildMonthSwitch();\n            }\n            triggerEvent(\"onMonthChange\");\n        }\n        updateNavigationCurrentMonth();\n        buildDays();\n        updateValue();\n        if (!shouldChangeMonth &&\n            self.config.mode !== \"range\" &&\n            self.config.showMonths === 1)\n            focusOnDayElem(target);\n        else if (self.selectedDateElem !== undefined &&\n            self.hourElement === undefined) {\n            self.selectedDateElem && self.selectedDateElem.focus();\n        }\n        if (self.hourElement !== undefined)\n            self.hourElement !== undefined && self.hourElement.focus();\n        if (self.config.closeOnSelect) {\n            var single = self.config.mode === \"single\" && !self.config.enableTime;\n            var range = self.config.mode === \"range\" &&\n                self.selectedDates.length === 2 &&\n                !self.config.enableTime;\n            if (single || range) {\n                focusAndClose();\n            }\n        }\n        triggerChange();\n    }\n    var CALLBACKS = {\n        locale: [setupLocale, updateWeekdays],\n        showMonths: [buildMonths, setCalendarWidth, buildWeekdays],\n        minDate: [jumpToDate],\n        maxDate: [jumpToDate],\n        positionElement: [updatePositionElement],\n        clickOpens: [\n            function () {\n                if (self.config.clickOpens === true) {\n                    bind(self._input, \"focus\", self.open);\n                    bind(self._input, \"click\", self.open);\n                }\n                else {\n                    self._input.removeEventListener(\"focus\", self.open);\n                    self._input.removeEventListener(\"click\", self.open);\n                }\n            },\n        ],\n    };\n    function set(option, value) {\n        if (option !== null && typeof option === \"object\") {\n            Object.assign(self.config, option);\n            for (var key in option) {\n                if (CALLBACKS[key] !== undefined)\n                    CALLBACKS[key].forEach(function (x) { return x(); });\n            }\n        }\n        else {\n            self.config[option] = value;\n            if (CALLBACKS[option] !== undefined)\n                CALLBACKS[option].forEach(function (x) { return x(); });\n            else if (_types_options__WEBPACK_IMPORTED_MODULE_0__.HOOKS.indexOf(option) > -1)\n                self.config[option] = (0,_utils__WEBPACK_IMPORTED_MODULE_2__.arrayify)(value);\n        }\n        self.redraw();\n        updateValue(true);\n    }\n    function setSelectedDate(inputDate, format) {\n        var dates = [];\n        if (inputDate instanceof Array)\n            dates = inputDate.map(function (d) { return self.parseDate(d, format); });\n        else if (inputDate instanceof Date || typeof inputDate === \"number\")\n            dates = [self.parseDate(inputDate, format)];\n        else if (typeof inputDate === \"string\") {\n            switch (self.config.mode) {\n                case \"single\":\n                case \"time\":\n                    dates = [self.parseDate(inputDate, format)];\n                    break;\n                case \"multiple\":\n                    dates = inputDate\n                        .split(self.config.conjunction)\n                        .map(function (date) { return self.parseDate(date, format); });\n                    break;\n                case \"range\":\n                    dates = inputDate\n                        .split(self.l10n.rangeSeparator)\n                        .map(function (date) { return self.parseDate(date, format); });\n                    break;\n                default:\n                    break;\n            }\n        }\n        else\n            self.config.errorHandler(new Error(\"Invalid date supplied: \" + JSON.stringify(inputDate)));\n        self.selectedDates = (self.config.allowInvalidPreload\n            ? dates\n            : dates.filter(function (d) { return d instanceof Date && isEnabled(d, false); }));\n        if (self.config.mode === \"range\")\n            self.selectedDates.sort(function (a, b) { return a.getTime() - b.getTime(); });\n    }\n    function setDate(date, triggerChange, format) {\n        if (triggerChange === void 0) { triggerChange = false; }\n        if (format === void 0) { format = self.config.dateFormat; }\n        if ((date !== 0 && !date) || (date instanceof Array && date.length === 0))\n            return self.clear(triggerChange);\n        setSelectedDate(date, format);\n        self.latestSelectedDateObj =\n            self.selectedDates[self.selectedDates.length - 1];\n        self.redraw();\n        jumpToDate(undefined, triggerChange);\n        setHoursFromDate();\n        if (self.selectedDates.length === 0) {\n            self.clear(false);\n        }\n        updateValue(triggerChange);\n        if (triggerChange)\n            triggerEvent(\"onChange\");\n    }\n    function parseDateRules(arr) {\n        return arr\n            .slice()\n            .map(function (rule) {\n            if (typeof rule === \"string\" ||\n                typeof rule === \"number\" ||\n                rule instanceof Date) {\n                return self.parseDate(rule, undefined, true);\n            }\n            else if (rule &&\n                typeof rule === \"object\" &&\n                rule.from &&\n                rule.to)\n                return {\n                    from: self.parseDate(rule.from, undefined),\n                    to: self.parseDate(rule.to, undefined),\n                };\n            return rule;\n        })\n            .filter(function (x) { return x; });\n    }\n    function setupDates() {\n        self.selectedDates = [];\n        self.now = self.parseDate(self.config.now) || new Date();\n        var preloadedDate = self.config.defaultDate ||\n            ((self.input.nodeName === \"INPUT\" ||\n                self.input.nodeName === \"TEXTAREA\") &&\n                self.input.placeholder &&\n                self.input.value === self.input.placeholder\n                ? null\n                : self.input.value);\n        if (preloadedDate)\n            setSelectedDate(preloadedDate, self.config.dateFormat);\n        self._initialDate =\n            self.selectedDates.length > 0\n                ? self.selectedDates[0]\n                : self.config.minDate &&\n                    self.config.minDate.getTime() > self.now.getTime()\n                    ? self.config.minDate\n                    : self.config.maxDate &&\n                        self.config.maxDate.getTime() < self.now.getTime()\n                        ? self.config.maxDate\n                        : self.now;\n        self.currentYear = self._initialDate.getFullYear();\n        self.currentMonth = self._initialDate.getMonth();\n        if (self.selectedDates.length > 0)\n            self.latestSelectedDateObj = self.selectedDates[0];\n        if (self.config.minTime !== undefined)\n            self.config.minTime = self.parseDate(self.config.minTime, \"H:i\");\n        if (self.config.maxTime !== undefined)\n            self.config.maxTime = self.parseDate(self.config.maxTime, \"H:i\");\n        self.minDateHasTime =\n            !!self.config.minDate &&\n                (self.config.minDate.getHours() > 0 ||\n                    self.config.minDate.getMinutes() > 0 ||\n                    self.config.minDate.getSeconds() > 0);\n        self.maxDateHasTime =\n            !!self.config.maxDate &&\n                (self.config.maxDate.getHours() > 0 ||\n                    self.config.maxDate.getMinutes() > 0 ||\n                    self.config.maxDate.getSeconds() > 0);\n    }\n    function setupInputs() {\n        self.input = getInputElem();\n        if (!self.input) {\n            self.config.errorHandler(new Error(\"Invalid input element specified\"));\n            return;\n        }\n        self.input._type = self.input.type;\n        self.input.type = \"text\";\n        self.input.classList.add(\"flatpickr-input\");\n        self._input = self.input;\n        if (self.config.altInput) {\n            self.altInput = (0,_utils_dom__WEBPACK_IMPORTED_MODULE_3__.createElement)(self.input.nodeName, self.config.altInputClass);\n            self._input = self.altInput;\n            self.altInput.placeholder = self.input.placeholder;\n            self.altInput.disabled = self.input.disabled;\n            self.altInput.required = self.input.required;\n            self.altInput.tabIndex = self.input.tabIndex;\n            self.altInput.type = \"text\";\n            self.input.setAttribute(\"type\", \"hidden\");\n            if (!self.config.static && self.input.parentNode)\n                self.input.parentNode.insertBefore(self.altInput, self.input.nextSibling);\n        }\n        if (!self.config.allowInput)\n            self._input.setAttribute(\"readonly\", \"readonly\");\n        updatePositionElement();\n    }\n    function updatePositionElement() {\n        self._positionElement = self.config.positionElement || self._input;\n    }\n    function setupMobile() {\n        var inputType = self.config.enableTime\n            ? self.config.noCalendar\n                ? \"time\"\n                : \"datetime-local\"\n            : \"date\";\n        self.mobileInput = (0,_utils_dom__WEBPACK_IMPORTED_MODULE_3__.createElement)(\"input\", self.input.className + \" flatpickr-mobile\");\n        self.mobileInput.tabIndex = 1;\n        self.mobileInput.type = inputType;\n        self.mobileInput.disabled = self.input.disabled;\n        self.mobileInput.required = self.input.required;\n        self.mobileInput.placeholder = self.input.placeholder;\n        self.mobileFormatStr =\n            inputType === \"datetime-local\"\n                ? \"Y-m-d\\\\TH:i:S\"\n                : inputType === \"date\"\n                    ? \"Y-m-d\"\n                    : \"H:i:S\";\n        if (self.selectedDates.length > 0) {\n            self.mobileInput.defaultValue = self.mobileInput.value = self.formatDate(self.selectedDates[0], self.mobileFormatStr);\n        }\n        if (self.config.minDate)\n            self.mobileInput.min = self.formatDate(self.config.minDate, \"Y-m-d\");\n        if (self.config.maxDate)\n            self.mobileInput.max = self.formatDate(self.config.maxDate, \"Y-m-d\");\n        if (self.input.getAttribute(\"step\"))\n            self.mobileInput.step = String(self.input.getAttribute(\"step\"));\n        self.input.type = \"hidden\";\n        if (self.altInput !== undefined)\n            self.altInput.type = \"hidden\";\n        try {\n            if (self.input.parentNode)\n                self.input.parentNode.insertBefore(self.mobileInput, self.input.nextSibling);\n        }\n        catch (_a) { }\n        bind(self.mobileInput, \"change\", function (e) {\n            self.setDate((0,_utils_dom__WEBPACK_IMPORTED_MODULE_3__.getEventTarget)(e).value, false, self.mobileFormatStr);\n            triggerEvent(\"onChange\");\n            triggerEvent(\"onClose\");\n        });\n    }\n    function toggle(e) {\n        if (self.isOpen === true)\n            return self.close();\n        self.open(e);\n    }\n    function triggerEvent(event, data) {\n        if (self.config === undefined)\n            return;\n        var hooks = self.config[event];\n        if (hooks !== undefined && hooks.length > 0) {\n            for (var i = 0; hooks[i] && i < hooks.length; i++)\n                hooks[i](self.selectedDates, self.input.value, self, data);\n        }\n        if (event === \"onChange\") {\n            self.input.dispatchEvent(createEvent(\"change\"));\n            self.input.dispatchEvent(createEvent(\"input\"));\n        }\n    }\n    function createEvent(name) {\n        var e = document.createEvent(\"Event\");\n        e.initEvent(name, true, true);\n        return e;\n    }\n    function isDateSelected(date) {\n        for (var i = 0; i < self.selectedDates.length; i++) {\n            var selectedDate = self.selectedDates[i];\n            if (selectedDate instanceof Date &&\n                (0,_utils_dates__WEBPACK_IMPORTED_MODULE_4__.compareDates)(selectedDate, date) === 0)\n                return \"\" + i;\n        }\n        return false;\n    }\n    function isDateInRange(date) {\n        if (self.config.mode !== \"range\" || self.selectedDates.length < 2)\n            return false;\n        return ((0,_utils_dates__WEBPACK_IMPORTED_MODULE_4__.compareDates)(date, self.selectedDates[0]) >= 0 &&\n            (0,_utils_dates__WEBPACK_IMPORTED_MODULE_4__.compareDates)(date, self.selectedDates[1]) <= 0);\n    }\n    function updateNavigationCurrentMonth() {\n        if (self.config.noCalendar || self.isMobile || !self.monthNav)\n            return;\n        self.yearElements.forEach(function (yearElement, i) {\n            var d = new Date(self.currentYear, self.currentMonth, 1);\n            d.setMonth(self.currentMonth + i);\n            if (self.config.showMonths > 1 ||\n                self.config.monthSelectorType === \"static\") {\n                self.monthElements[i].textContent =\n                    (0,_utils_formatting__WEBPACK_IMPORTED_MODULE_5__.monthToStr)(d.getMonth(), self.config.shorthandCurrentMonth, self.l10n) + \" \";\n            }\n            else {\n                self.monthsDropdownContainer.value = d.getMonth().toString();\n            }\n            yearElement.value = d.getFullYear().toString();\n        });\n        self._hidePrevMonthArrow =\n            self.config.minDate !== undefined &&\n                (self.currentYear === self.config.minDate.getFullYear()\n                    ? self.currentMonth <= self.config.minDate.getMonth()\n                    : self.currentYear < self.config.minDate.getFullYear());\n        self._hideNextMonthArrow =\n            self.config.maxDate !== undefined &&\n                (self.currentYear === self.config.maxDate.getFullYear()\n                    ? self.currentMonth + 1 > self.config.maxDate.getMonth()\n                    : self.currentYear > self.config.maxDate.getFullYear());\n    }\n    function getDateStr(specificFormat) {\n        var format = specificFormat ||\n            (self.config.altInput ? self.config.altFormat : self.config.dateFormat);\n        return self.selectedDates\n            .map(function (dObj) { return self.formatDate(dObj, format); })\n            .filter(function (d, i, arr) {\n            return self.config.mode !== \"range\" ||\n                self.config.enableTime ||\n                arr.indexOf(d) === i;\n        })\n            .join(self.config.mode !== \"range\"\n            ? self.config.conjunction\n            : self.l10n.rangeSeparator);\n    }\n    function updateValue(triggerChange) {\n        if (triggerChange === void 0) { triggerChange = true; }\n        if (self.mobileInput !== undefined && self.mobileFormatStr) {\n            self.mobileInput.value =\n                self.latestSelectedDateObj !== undefined\n                    ? self.formatDate(self.latestSelectedDateObj, self.mobileFormatStr)\n                    : \"\";\n        }\n        self.input.value = getDateStr(self.config.dateFormat);\n        if (self.altInput !== undefined) {\n            self.altInput.value = getDateStr(self.config.altFormat);\n        }\n        if (triggerChange !== false)\n            triggerEvent(\"onValueUpdate\");\n    }\n    function onMonthNavClick(e) {\n        var eventTarget = (0,_utils_dom__WEBPACK_IMPORTED_MODULE_3__.getEventTarget)(e);\n        var isPrevMonth = self.prevMonthNav.contains(eventTarget);\n        var isNextMonth = self.nextMonthNav.contains(eventTarget);\n        if (isPrevMonth || isNextMonth) {\n            changeMonth(isPrevMonth ? -1 : 1);\n        }\n        else if (self.yearElements.indexOf(eventTarget) >= 0) {\n            eventTarget.select();\n        }\n        else if (eventTarget.classList.contains(\"arrowUp\")) {\n            self.changeYear(self.currentYear + 1);\n        }\n        else if (eventTarget.classList.contains(\"arrowDown\")) {\n            self.changeYear(self.currentYear - 1);\n        }\n    }\n    function timeWrapper(e) {\n        e.preventDefault();\n        var isKeyDown = e.type === \"keydown\", eventTarget = (0,_utils_dom__WEBPACK_IMPORTED_MODULE_3__.getEventTarget)(e), input = eventTarget;\n        if (self.amPM !== undefined && eventTarget === self.amPM) {\n            self.amPM.textContent =\n                self.l10n.amPM[(0,_utils__WEBPACK_IMPORTED_MODULE_2__.int)(self.amPM.textContent === self.l10n.amPM[0])];\n        }\n        var min = parseFloat(input.getAttribute(\"min\")), max = parseFloat(input.getAttribute(\"max\")), step = parseFloat(input.getAttribute(\"step\")), curValue = parseInt(input.value, 10), delta = e.delta ||\n            (isKeyDown ? (e.which === 38 ? 1 : -1) : 0);\n        var newValue = curValue + step * delta;\n        if (typeof input.value !== \"undefined\" && input.value.length === 2) {\n            var isHourElem = input === self.hourElement, isMinuteElem = input === self.minuteElement;\n            if (newValue < min) {\n                newValue =\n                    max +\n                        newValue +\n                        (0,_utils__WEBPACK_IMPORTED_MODULE_2__.int)(!isHourElem) +\n                        ((0,_utils__WEBPACK_IMPORTED_MODULE_2__.int)(isHourElem) && (0,_utils__WEBPACK_IMPORTED_MODULE_2__.int)(!self.amPM));\n                if (isMinuteElem)\n                    incrementNumInput(undefined, -1, self.hourElement);\n            }\n            else if (newValue > max) {\n                newValue =\n                    input === self.hourElement ? newValue - max - (0,_utils__WEBPACK_IMPORTED_MODULE_2__.int)(!self.amPM) : min;\n                if (isMinuteElem)\n                    incrementNumInput(undefined, 1, self.hourElement);\n            }\n            if (self.amPM &&\n                isHourElem &&\n                (step === 1\n                    ? newValue + curValue === 23\n                    : Math.abs(newValue - curValue) > step)) {\n                self.amPM.textContent =\n                    self.l10n.amPM[(0,_utils__WEBPACK_IMPORTED_MODULE_2__.int)(self.amPM.textContent === self.l10n.amPM[0])];\n            }\n            input.value = (0,_utils__WEBPACK_IMPORTED_MODULE_2__.pad)(newValue);\n        }\n    }\n    init();\n    return self;\n}\nfunction _flatpickr(nodeList, config) {\n    var nodes = Array.prototype.slice\n        .call(nodeList)\n        .filter(function (x) { return x instanceof HTMLElement; });\n    var instances = [];\n    for (var i = 0; i < nodes.length; i++) {\n        var node = nodes[i];\n        try {\n            if (node.getAttribute(\"data-fp-omit\") !== null)\n                continue;\n            if (node._flatpickr !== undefined) {\n                node._flatpickr.destroy();\n                node._flatpickr = undefined;\n            }\n            node._flatpickr = FlatpickrInstance(node, config || {});\n            instances.push(node._flatpickr);\n        }\n        catch (e) {\n            console.error(e);\n        }\n    }\n    return instances.length === 1 ? instances[0] : instances;\n}\nif (typeof HTMLElement !== \"undefined\" &&\n    typeof HTMLCollection !== \"undefined\" &&\n    typeof NodeList !== \"undefined\") {\n    HTMLCollection.prototype.flatpickr = NodeList.prototype.flatpickr = function (config) {\n        return _flatpickr(this, config);\n    };\n    HTMLElement.prototype.flatpickr = function (config) {\n        return _flatpickr([this], config);\n    };\n}\nvar flatpickr = function (selector, config) {\n    if (typeof selector === \"string\") {\n        return _flatpickr(window.document.querySelectorAll(selector), config);\n    }\n    else if (selector instanceof Node) {\n        return _flatpickr([selector], config);\n    }\n    else {\n        return _flatpickr(selector, config);\n    }\n};\nflatpickr.defaultConfig = {};\nflatpickr.l10ns = {\n    en: __assign({}, _l10n_default__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n    default: __assign({}, _l10n_default__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n};\nflatpickr.localize = function (l10n) {\n    flatpickr.l10ns.default = __assign(__assign({}, flatpickr.l10ns.default), l10n);\n};\nflatpickr.setDefaults = function (config) {\n    flatpickr.defaultConfig = __assign(__assign({}, flatpickr.defaultConfig), config);\n};\nflatpickr.parseDate = (0,_utils_dates__WEBPACK_IMPORTED_MODULE_4__.createDateParser)({});\nflatpickr.formatDate = (0,_utils_dates__WEBPACK_IMPORTED_MODULE_4__.createDateFormatter)({});\nflatpickr.compareDates = _utils_dates__WEBPACK_IMPORTED_MODULE_4__.compareDates;\nif (typeof jQuery !== \"undefined\" && typeof jQuery.fn !== \"undefined\") {\n    jQuery.fn.flatpickr = function (config) {\n        return _flatpickr(this, config);\n    };\n}\nDate.prototype.fp_incr = function (days) {\n    return new Date(this.getFullYear(), this.getMonth(), this.getDate() + (typeof days === \"string\" ? parseInt(days, 10) : days));\n};\nif (typeof window !== \"undefined\") {\n    window.flatpickr = flatpickr;\n}\n/* harmony default export */ __webpack_exports__[\"default\"] = (flatpickr);\n\n\n//# sourceURL=webpack://falcon/./node_modules/flatpickr/dist/esm/index.js?");

/***/ }),

/***/ "./node_modules/flatpickr/dist/esm/l10n/default.js":
/*!*********************************************************!*\
  !*** ./node_modules/flatpickr/dist/esm/l10n/default.js ***!
  \*********************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"english\": function() { return /* binding */ english; }\n/* harmony export */ });\nvar english = {\n    weekdays: {\n        shorthand: [\"Sun\", \"Mon\", \"Tue\", \"Wed\", \"Thu\", \"Fri\", \"Sat\"],\n        longhand: [\n            \"Sunday\",\n            \"Monday\",\n            \"Tuesday\",\n            \"Wednesday\",\n            \"Thursday\",\n            \"Friday\",\n            \"Saturday\",\n        ],\n    },\n    months: {\n        shorthand: [\n            \"Jan\",\n            \"Feb\",\n            \"Mar\",\n            \"Apr\",\n            \"May\",\n            \"Jun\",\n            \"Jul\",\n            \"Aug\",\n            \"Sep\",\n            \"Oct\",\n            \"Nov\",\n            \"Dec\",\n        ],\n        longhand: [\n            \"January\",\n            \"February\",\n            \"March\",\n            \"April\",\n            \"May\",\n            \"June\",\n            \"July\",\n            \"August\",\n            \"September\",\n            \"October\",\n            \"November\",\n            \"December\",\n        ],\n    },\n    daysInMonth: [31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31],\n    firstDayOfWeek: 0,\n    ordinal: function (nth) {\n        var s = nth % 100;\n        if (s > 3 && s < 21)\n            return \"th\";\n        switch (s % 10) {\n            case 1:\n                return \"st\";\n            case 2:\n                return \"nd\";\n            case 3:\n                return \"rd\";\n            default:\n                return \"th\";\n        }\n    },\n    rangeSeparator: \" to \",\n    weekAbbreviation: \"Wk\",\n    scrollTitle: \"Scroll to increment\",\n    toggleTitle: \"Click to toggle\",\n    amPM: [\"AM\", \"PM\"],\n    yearAriaLabel: \"Year\",\n    monthAriaLabel: \"Month\",\n    hourAriaLabel: \"Hour\",\n    minuteAriaLabel: \"Minute\",\n    time_24hr: false,\n};\n/* harmony default export */ __webpack_exports__[\"default\"] = (english);\n\n\n//# sourceURL=webpack://falcon/./node_modules/flatpickr/dist/esm/l10n/default.js?");

/***/ }),

/***/ "./node_modules/flatpickr/dist/esm/types/options.js":
/*!**********************************************************!*\
  !*** ./node_modules/flatpickr/dist/esm/types/options.js ***!
  \**********************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"HOOKS\": function() { return /* binding */ HOOKS; },\n/* harmony export */   \"defaults\": function() { return /* binding */ defaults; }\n/* harmony export */ });\nvar HOOKS = [\n    \"onChange\",\n    \"onClose\",\n    \"onDayCreate\",\n    \"onDestroy\",\n    \"onKeyDown\",\n    \"onMonthChange\",\n    \"onOpen\",\n    \"onParseConfig\",\n    \"onReady\",\n    \"onValueUpdate\",\n    \"onYearChange\",\n    \"onPreCalendarPosition\",\n];\nvar defaults = {\n    _disable: [],\n    allowInput: false,\n    allowInvalidPreload: false,\n    altFormat: \"F j, Y\",\n    altInput: false,\n    altInputClass: \"form-control input\",\n    animate: typeof window === \"object\" &&\n        window.navigator.userAgent.indexOf(\"MSIE\") === -1,\n    ariaDateFormat: \"F j, Y\",\n    autoFillDefaultTime: true,\n    clickOpens: true,\n    closeOnSelect: true,\n    conjunction: \", \",\n    dateFormat: \"Y-m-d\",\n    defaultHour: 12,\n    defaultMinute: 0,\n    defaultSeconds: 0,\n    disable: [],\n    disableMobile: false,\n    enableSeconds: false,\n    enableTime: false,\n    errorHandler: function (err) {\n        return typeof console !== \"undefined\" && console.warn(err);\n    },\n    getWeek: function (givenDate) {\n        var date = new Date(givenDate.getTime());\n        date.setHours(0, 0, 0, 0);\n        date.setDate(date.getDate() + 3 - ((date.getDay() + 6) % 7));\n        var week1 = new Date(date.getFullYear(), 0, 4);\n        return (1 +\n            Math.round(((date.getTime() - week1.getTime()) / 86400000 -\n                3 +\n                ((week1.getDay() + 6) % 7)) /\n                7));\n    },\n    hourIncrement: 1,\n    ignoredFocusElements: [],\n    inline: false,\n    locale: \"default\",\n    minuteIncrement: 5,\n    mode: \"single\",\n    monthSelectorType: \"dropdown\",\n    nextArrow: \"<svg version='1.1' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink' viewBox='0 0 17 17'><g></g><path d='M13.207 8.472l-7.854 7.854-0.707-0.707 7.146-7.146-7.146-7.148 0.707-0.707 7.854 7.854z' /></svg>\",\n    noCalendar: false,\n    now: new Date(),\n    onChange: [],\n    onClose: [],\n    onDayCreate: [],\n    onDestroy: [],\n    onKeyDown: [],\n    onMonthChange: [],\n    onOpen: [],\n    onParseConfig: [],\n    onReady: [],\n    onValueUpdate: [],\n    onYearChange: [],\n    onPreCalendarPosition: [],\n    plugins: [],\n    position: \"auto\",\n    positionElement: undefined,\n    prevArrow: \"<svg version='1.1' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink' viewBox='0 0 17 17'><g></g><path d='M5.207 8.471l7.146 7.147-0.707 0.707-7.853-7.854 7.854-7.853 0.707 0.707-7.147 7.146z' /></svg>\",\n    shorthandCurrentMonth: false,\n    showMonths: 1,\n    static: false,\n    time_24hr: false,\n    weekNumbers: false,\n    wrap: false,\n};\n\n\n//# sourceURL=webpack://falcon/./node_modules/flatpickr/dist/esm/types/options.js?");

/***/ }),

/***/ "./node_modules/flatpickr/dist/esm/utils/dates.js":
/*!********************************************************!*\
  !*** ./node_modules/flatpickr/dist/esm/utils/dates.js ***!
  \********************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"calculateSecondsSinceMidnight\": function() { return /* binding */ calculateSecondsSinceMidnight; },\n/* harmony export */   \"compareDates\": function() { return /* binding */ compareDates; },\n/* harmony export */   \"compareTimes\": function() { return /* binding */ compareTimes; },\n/* harmony export */   \"createDateFormatter\": function() { return /* binding */ createDateFormatter; },\n/* harmony export */   \"createDateParser\": function() { return /* binding */ createDateParser; },\n/* harmony export */   \"duration\": function() { return /* binding */ duration; },\n/* harmony export */   \"getDefaultHours\": function() { return /* binding */ getDefaultHours; },\n/* harmony export */   \"isBetween\": function() { return /* binding */ isBetween; },\n/* harmony export */   \"parseSeconds\": function() { return /* binding */ parseSeconds; }\n/* harmony export */ });\n/* harmony import */ var _formatting__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./formatting */ \"./node_modules/flatpickr/dist/esm/utils/formatting.js\");\n/* harmony import */ var _types_options__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../types/options */ \"./node_modules/flatpickr/dist/esm/types/options.js\");\n/* harmony import */ var _l10n_default__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../l10n/default */ \"./node_modules/flatpickr/dist/esm/l10n/default.js\");\n\n\n\nvar createDateFormatter = function (_a) {\n    var _b = _a.config, config = _b === void 0 ? _types_options__WEBPACK_IMPORTED_MODULE_1__.defaults : _b, _c = _a.l10n, l10n = _c === void 0 ? _l10n_default__WEBPACK_IMPORTED_MODULE_2__.english : _c, _d = _a.isMobile, isMobile = _d === void 0 ? false : _d;\n    return function (dateObj, frmt, overrideLocale) {\n        var locale = overrideLocale || l10n;\n        if (config.formatDate !== undefined && !isMobile) {\n            return config.formatDate(dateObj, frmt, locale);\n        }\n        return frmt\n            .split(\"\")\n            .map(function (c, i, arr) {\n            return _formatting__WEBPACK_IMPORTED_MODULE_0__.formats[c] && arr[i - 1] !== \"\\\\\"\n                ? _formatting__WEBPACK_IMPORTED_MODULE_0__.formats[c](dateObj, locale, config)\n                : c !== \"\\\\\"\n                    ? c\n                    : \"\";\n        })\n            .join(\"\");\n    };\n};\nvar createDateParser = function (_a) {\n    var _b = _a.config, config = _b === void 0 ? _types_options__WEBPACK_IMPORTED_MODULE_1__.defaults : _b, _c = _a.l10n, l10n = _c === void 0 ? _l10n_default__WEBPACK_IMPORTED_MODULE_2__.english : _c;\n    return function (date, givenFormat, timeless, customLocale) {\n        if (date !== 0 && !date)\n            return undefined;\n        var locale = customLocale || l10n;\n        var parsedDate;\n        var dateOrig = date;\n        if (date instanceof Date)\n            parsedDate = new Date(date.getTime());\n        else if (typeof date !== \"string\" &&\n            date.toFixed !== undefined)\n            parsedDate = new Date(date);\n        else if (typeof date === \"string\") {\n            var format = givenFormat || (config || _types_options__WEBPACK_IMPORTED_MODULE_1__.defaults).dateFormat;\n            var datestr = String(date).trim();\n            if (datestr === \"today\") {\n                parsedDate = new Date();\n                timeless = true;\n            }\n            else if (config && config.parseDate) {\n                parsedDate = config.parseDate(date, format);\n            }\n            else if (/Z$/.test(datestr) ||\n                /GMT$/.test(datestr)) {\n                parsedDate = new Date(date);\n            }\n            else {\n                var matched = void 0, ops = [];\n                for (var i = 0, matchIndex = 0, regexStr = \"\"; i < format.length; i++) {\n                    var token = format[i];\n                    var isBackSlash = token === \"\\\\\";\n                    var escaped = format[i - 1] === \"\\\\\" || isBackSlash;\n                    if (_formatting__WEBPACK_IMPORTED_MODULE_0__.tokenRegex[token] && !escaped) {\n                        regexStr += _formatting__WEBPACK_IMPORTED_MODULE_0__.tokenRegex[token];\n                        var match = new RegExp(regexStr).exec(date);\n                        if (match && (matched = true)) {\n                            ops[token !== \"Y\" ? \"push\" : \"unshift\"]({\n                                fn: _formatting__WEBPACK_IMPORTED_MODULE_0__.revFormat[token],\n                                val: match[++matchIndex],\n                            });\n                        }\n                    }\n                    else if (!isBackSlash)\n                        regexStr += \".\";\n                }\n                parsedDate =\n                    !config || !config.noCalendar\n                        ? new Date(new Date().getFullYear(), 0, 1, 0, 0, 0, 0)\n                        : new Date(new Date().setHours(0, 0, 0, 0));\n                ops.forEach(function (_a) {\n                    var fn = _a.fn, val = _a.val;\n                    return (parsedDate = fn(parsedDate, val, locale) || parsedDate);\n                });\n                parsedDate = matched ? parsedDate : undefined;\n            }\n        }\n        if (!(parsedDate instanceof Date && !isNaN(parsedDate.getTime()))) {\n            config.errorHandler(new Error(\"Invalid date provided: \" + dateOrig));\n            return undefined;\n        }\n        if (timeless === true)\n            parsedDate.setHours(0, 0, 0, 0);\n        return parsedDate;\n    };\n};\nfunction compareDates(date1, date2, timeless) {\n    if (timeless === void 0) { timeless = true; }\n    if (timeless !== false) {\n        return (new Date(date1.getTime()).setHours(0, 0, 0, 0) -\n            new Date(date2.getTime()).setHours(0, 0, 0, 0));\n    }\n    return date1.getTime() - date2.getTime();\n}\nfunction compareTimes(date1, date2) {\n    return (3600 * (date1.getHours() - date2.getHours()) +\n        60 * (date1.getMinutes() - date2.getMinutes()) +\n        date1.getSeconds() -\n        date2.getSeconds());\n}\nvar isBetween = function (ts, ts1, ts2) {\n    return ts > Math.min(ts1, ts2) && ts < Math.max(ts1, ts2);\n};\nvar calculateSecondsSinceMidnight = function (hours, minutes, seconds) {\n    return hours * 3600 + minutes * 60 + seconds;\n};\nvar parseSeconds = function (secondsSinceMidnight) {\n    var hours = Math.floor(secondsSinceMidnight / 3600), minutes = (secondsSinceMidnight - hours * 3600) / 60;\n    return [hours, minutes, secondsSinceMidnight - hours * 3600 - minutes * 60];\n};\nvar duration = {\n    DAY: 86400000,\n};\nfunction getDefaultHours(config) {\n    var hours = config.defaultHour;\n    var minutes = config.defaultMinute;\n    var seconds = config.defaultSeconds;\n    if (config.minDate !== undefined) {\n        var minHour = config.minDate.getHours();\n        var minMinutes = config.minDate.getMinutes();\n        var minSeconds = config.minDate.getSeconds();\n        if (hours < minHour) {\n            hours = minHour;\n        }\n        if (hours === minHour && minutes < minMinutes) {\n            minutes = minMinutes;\n        }\n        if (hours === minHour && minutes === minMinutes && seconds < minSeconds)\n            seconds = config.minDate.getSeconds();\n    }\n    if (config.maxDate !== undefined) {\n        var maxHr = config.maxDate.getHours();\n        var maxMinutes = config.maxDate.getMinutes();\n        hours = Math.min(hours, maxHr);\n        if (hours === maxHr)\n            minutes = Math.min(maxMinutes, minutes);\n        if (hours === maxHr && minutes === maxMinutes)\n            seconds = config.maxDate.getSeconds();\n    }\n    return { hours: hours, minutes: minutes, seconds: seconds };\n}\n\n\n//# sourceURL=webpack://falcon/./node_modules/flatpickr/dist/esm/utils/dates.js?");

/***/ }),

/***/ "./node_modules/flatpickr/dist/esm/utils/dom.js":
/*!******************************************************!*\
  !*** ./node_modules/flatpickr/dist/esm/utils/dom.js ***!
  \******************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"clearNode\": function() { return /* binding */ clearNode; },\n/* harmony export */   \"createElement\": function() { return /* binding */ createElement; },\n/* harmony export */   \"createNumberInput\": function() { return /* binding */ createNumberInput; },\n/* harmony export */   \"findParent\": function() { return /* binding */ findParent; },\n/* harmony export */   \"getEventTarget\": function() { return /* binding */ getEventTarget; },\n/* harmony export */   \"toggleClass\": function() { return /* binding */ toggleClass; }\n/* harmony export */ });\nfunction toggleClass(elem, className, bool) {\n    if (bool === true)\n        return elem.classList.add(className);\n    elem.classList.remove(className);\n}\nfunction createElement(tag, className, content) {\n    var e = window.document.createElement(tag);\n    className = className || \"\";\n    content = content || \"\";\n    e.className = className;\n    if (content !== undefined)\n        e.textContent = content;\n    return e;\n}\nfunction clearNode(node) {\n    while (node.firstChild)\n        node.removeChild(node.firstChild);\n}\nfunction findParent(node, condition) {\n    if (condition(node))\n        return node;\n    else if (node.parentNode)\n        return findParent(node.parentNode, condition);\n    return undefined;\n}\nfunction createNumberInput(inputClassName, opts) {\n    var wrapper = createElement(\"div\", \"numInputWrapper\"), numInput = createElement(\"input\", \"numInput \" + inputClassName), arrowUp = createElement(\"span\", \"arrowUp\"), arrowDown = createElement(\"span\", \"arrowDown\");\n    if (navigator.userAgent.indexOf(\"MSIE 9.0\") === -1) {\n        numInput.type = \"number\";\n    }\n    else {\n        numInput.type = \"text\";\n        numInput.pattern = \"\\\\d*\";\n    }\n    if (opts !== undefined)\n        for (var key in opts)\n            numInput.setAttribute(key, opts[key]);\n    wrapper.appendChild(numInput);\n    wrapper.appendChild(arrowUp);\n    wrapper.appendChild(arrowDown);\n    return wrapper;\n}\nfunction getEventTarget(event) {\n    try {\n        if (typeof event.composedPath === \"function\") {\n            var path = event.composedPath();\n            return path[0];\n        }\n        return event.target;\n    }\n    catch (error) {\n        return event.target;\n    }\n}\n\n\n//# sourceURL=webpack://falcon/./node_modules/flatpickr/dist/esm/utils/dom.js?");

/***/ }),

/***/ "./node_modules/flatpickr/dist/esm/utils/formatting.js":
/*!*************************************************************!*\
  !*** ./node_modules/flatpickr/dist/esm/utils/formatting.js ***!
  \*************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"formats\": function() { return /* binding */ formats; },\n/* harmony export */   \"monthToStr\": function() { return /* binding */ monthToStr; },\n/* harmony export */   \"revFormat\": function() { return /* binding */ revFormat; },\n/* harmony export */   \"tokenRegex\": function() { return /* binding */ tokenRegex; }\n/* harmony export */ });\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../utils */ \"./node_modules/flatpickr/dist/esm/utils/index.js\");\n\nvar doNothing = function () { return undefined; };\nvar monthToStr = function (monthNumber, shorthand, locale) { return locale.months[shorthand ? \"shorthand\" : \"longhand\"][monthNumber]; };\nvar revFormat = {\n    D: doNothing,\n    F: function (dateObj, monthName, locale) {\n        dateObj.setMonth(locale.months.longhand.indexOf(monthName));\n    },\n    G: function (dateObj, hour) {\n        dateObj.setHours((dateObj.getHours() >= 12 ? 12 : 0) + parseFloat(hour));\n    },\n    H: function (dateObj, hour) {\n        dateObj.setHours(parseFloat(hour));\n    },\n    J: function (dateObj, day) {\n        dateObj.setDate(parseFloat(day));\n    },\n    K: function (dateObj, amPM, locale) {\n        dateObj.setHours((dateObj.getHours() % 12) +\n            12 * (0,_utils__WEBPACK_IMPORTED_MODULE_0__.int)(new RegExp(locale.amPM[1], \"i\").test(amPM)));\n    },\n    M: function (dateObj, shortMonth, locale) {\n        dateObj.setMonth(locale.months.shorthand.indexOf(shortMonth));\n    },\n    S: function (dateObj, seconds) {\n        dateObj.setSeconds(parseFloat(seconds));\n    },\n    U: function (_, unixSeconds) { return new Date(parseFloat(unixSeconds) * 1000); },\n    W: function (dateObj, weekNum, locale) {\n        var weekNumber = parseInt(weekNum);\n        var date = new Date(dateObj.getFullYear(), 0, 2 + (weekNumber - 1) * 7, 0, 0, 0, 0);\n        date.setDate(date.getDate() - date.getDay() + locale.firstDayOfWeek);\n        return date;\n    },\n    Y: function (dateObj, year) {\n        dateObj.setFullYear(parseFloat(year));\n    },\n    Z: function (_, ISODate) { return new Date(ISODate); },\n    d: function (dateObj, day) {\n        dateObj.setDate(parseFloat(day));\n    },\n    h: function (dateObj, hour) {\n        dateObj.setHours((dateObj.getHours() >= 12 ? 12 : 0) + parseFloat(hour));\n    },\n    i: function (dateObj, minutes) {\n        dateObj.setMinutes(parseFloat(minutes));\n    },\n    j: function (dateObj, day) {\n        dateObj.setDate(parseFloat(day));\n    },\n    l: doNothing,\n    m: function (dateObj, month) {\n        dateObj.setMonth(parseFloat(month) - 1);\n    },\n    n: function (dateObj, month) {\n        dateObj.setMonth(parseFloat(month) - 1);\n    },\n    s: function (dateObj, seconds) {\n        dateObj.setSeconds(parseFloat(seconds));\n    },\n    u: function (_, unixMillSeconds) {\n        return new Date(parseFloat(unixMillSeconds));\n    },\n    w: doNothing,\n    y: function (dateObj, year) {\n        dateObj.setFullYear(2000 + parseFloat(year));\n    },\n};\nvar tokenRegex = {\n    D: \"\",\n    F: \"\",\n    G: \"(\\\\d\\\\d|\\\\d)\",\n    H: \"(\\\\d\\\\d|\\\\d)\",\n    J: \"(\\\\d\\\\d|\\\\d)\\\\w+\",\n    K: \"\",\n    M: \"\",\n    S: \"(\\\\d\\\\d|\\\\d)\",\n    U: \"(.+)\",\n    W: \"(\\\\d\\\\d|\\\\d)\",\n    Y: \"(\\\\d{4})\",\n    Z: \"(.+)\",\n    d: \"(\\\\d\\\\d|\\\\d)\",\n    h: \"(\\\\d\\\\d|\\\\d)\",\n    i: \"(\\\\d\\\\d|\\\\d)\",\n    j: \"(\\\\d\\\\d|\\\\d)\",\n    l: \"\",\n    m: \"(\\\\d\\\\d|\\\\d)\",\n    n: \"(\\\\d\\\\d|\\\\d)\",\n    s: \"(\\\\d\\\\d|\\\\d)\",\n    u: \"(.+)\",\n    w: \"(\\\\d\\\\d|\\\\d)\",\n    y: \"(\\\\d{2})\",\n};\nvar formats = {\n    Z: function (date) { return date.toISOString(); },\n    D: function (date, locale, options) {\n        return locale.weekdays.shorthand[formats.w(date, locale, options)];\n    },\n    F: function (date, locale, options) {\n        return monthToStr(formats.n(date, locale, options) - 1, false, locale);\n    },\n    G: function (date, locale, options) {\n        return (0,_utils__WEBPACK_IMPORTED_MODULE_0__.pad)(formats.h(date, locale, options));\n    },\n    H: function (date) { return (0,_utils__WEBPACK_IMPORTED_MODULE_0__.pad)(date.getHours()); },\n    J: function (date, locale) {\n        return locale.ordinal !== undefined\n            ? date.getDate() + locale.ordinal(date.getDate())\n            : date.getDate();\n    },\n    K: function (date, locale) { return locale.amPM[(0,_utils__WEBPACK_IMPORTED_MODULE_0__.int)(date.getHours() > 11)]; },\n    M: function (date, locale) {\n        return monthToStr(date.getMonth(), true, locale);\n    },\n    S: function (date) { return (0,_utils__WEBPACK_IMPORTED_MODULE_0__.pad)(date.getSeconds()); },\n    U: function (date) { return date.getTime() / 1000; },\n    W: function (date, _, options) {\n        return options.getWeek(date);\n    },\n    Y: function (date) { return (0,_utils__WEBPACK_IMPORTED_MODULE_0__.pad)(date.getFullYear(), 4); },\n    d: function (date) { return (0,_utils__WEBPACK_IMPORTED_MODULE_0__.pad)(date.getDate()); },\n    h: function (date) { return (date.getHours() % 12 ? date.getHours() % 12 : 12); },\n    i: function (date) { return (0,_utils__WEBPACK_IMPORTED_MODULE_0__.pad)(date.getMinutes()); },\n    j: function (date) { return date.getDate(); },\n    l: function (date, locale) {\n        return locale.weekdays.longhand[date.getDay()];\n    },\n    m: function (date) { return (0,_utils__WEBPACK_IMPORTED_MODULE_0__.pad)(date.getMonth() + 1); },\n    n: function (date) { return date.getMonth() + 1; },\n    s: function (date) { return date.getSeconds(); },\n    u: function (date) { return date.getTime(); },\n    w: function (date) { return date.getDay(); },\n    y: function (date) { return String(date.getFullYear()).substring(2); },\n};\n\n\n//# sourceURL=webpack://falcon/./node_modules/flatpickr/dist/esm/utils/formatting.js?");

/***/ }),

/***/ "./node_modules/flatpickr/dist/esm/utils/index.js":
/*!********************************************************!*\
  !*** ./node_modules/flatpickr/dist/esm/utils/index.js ***!
  \********************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"arrayify\": function() { return /* binding */ arrayify; },\n/* harmony export */   \"debounce\": function() { return /* binding */ debounce; },\n/* harmony export */   \"int\": function() { return /* binding */ int; },\n/* harmony export */   \"pad\": function() { return /* binding */ pad; }\n/* harmony export */ });\nvar pad = function (number, length) {\n    if (length === void 0) { length = 2; }\n    return (\"000\" + number).slice(length * -1);\n};\nvar int = function (bool) { return (bool === true ? 1 : 0); };\nfunction debounce(fn, wait) {\n    var t;\n    return function () {\n        var _this = this;\n        var args = arguments;\n        clearTimeout(t);\n        t = setTimeout(function () { return fn.apply(_this, args); }, wait);\n    };\n}\nvar arrayify = function (obj) {\n    return obj instanceof Array ? obj : [obj];\n};\n\n\n//# sourceURL=webpack://falcon/./node_modules/flatpickr/dist/esm/utils/index.js?");

/***/ }),

/***/ "./node_modules/flatpickr/dist/esm/utils/polyfills.js":
/*!************************************************************!*\
  !*** ./node_modules/flatpickr/dist/esm/utils/polyfills.js ***!
  \************************************************************/
/***/ (function() {

"use strict";
eval("\nif (typeof Object.assign !== \"function\") {\n    Object.assign = function (target) {\n        var args = [];\n        for (var _i = 1; _i < arguments.length; _i++) {\n            args[_i - 1] = arguments[_i];\n        }\n        if (!target) {\n            throw TypeError(\"Cannot convert undefined or null to object\");\n        }\n        var _loop_1 = function (source) {\n            if (source) {\n                Object.keys(source).forEach(function (key) { return (target[key] = source[key]); });\n            }\n        };\n        for (var _a = 0, args_1 = args; _a < args_1.length; _a++) {\n            var source = args_1[_a];\n            _loop_1(source);\n        }\n        return target;\n    };\n}\n\n\n//# sourceURL=webpack://falcon/./node_modules/flatpickr/dist/esm/utils/polyfills.js?");

/***/ }),

/***/ "./node_modules/flatpickr/dist/l10n/bn.js":
/*!************************************************!*\
  !*** ./node_modules/flatpickr/dist/l10n/bn.js ***!
  \************************************************/
/***/ (function(__unused_webpack_module, exports) {

eval("(function (global, factory) {\n   true ? factory(exports) :\n  0;\n}(this, (function (exports) { 'use strict';\n\n  var fp = typeof window !== \"undefined\" && window.flatpickr !== undefined\n      ? window.flatpickr\n      : {\n          l10ns: {},\n      };\n  var Bangla = {\n      weekdays: {\n          shorthand: [\"রবি\", \"সোম\", \"মঙ্গল\", \"বুধ\", \"বৃহস্পতি\", \"শুক্র\", \"শনি\"],\n          longhand: [\n              \"রবিবার\",\n              \"সোমবার\",\n              \"মঙ্গলবার\",\n              \"বুধবার\",\n              \"বৃহস্পতিবার\",\n              \"শুক্রবার\",\n              \"শনিবার\",\n          ],\n      },\n      months: {\n          shorthand: [\n              \"জানু\",\n              \"ফেব্রু\",\n              \"মার্চ\",\n              \"এপ্রিল\",\n              \"মে\",\n              \"জুন\",\n              \"জুলাই\",\n              \"আগ\",\n              \"সেপ্টে\",\n              \"অক্টো\",\n              \"নভে\",\n              \"ডিসে\",\n          ],\n          longhand: [\n              \"জানুয়ারী\",\n              \"ফেব্রুয়ারী\",\n              \"মার্চ\",\n              \"এপ্রিল\",\n              \"মে\",\n              \"জুন\",\n              \"জুলাই\",\n              \"আগস্ট\",\n              \"সেপ্টেম্বর\",\n              \"অক্টোবর\",\n              \"নভেম্বর\",\n              \"ডিসেম্বর\",\n          ],\n      },\n  };\n  fp.l10ns.bn = Bangla;\n  var bn = fp.l10ns;\n\n  exports.Bangla = Bangla;\n  exports.default = bn;\n\n  Object.defineProperty(exports, '__esModule', { value: true });\n\n})));\n\n\n//# sourceURL=webpack://falcon/./node_modules/flatpickr/dist/l10n/bn.js?");

/***/ }),

/***/ "./src/js/flatpickr.js":
/*!*****************************!*\
  !*** ./src/js/flatpickr.js ***!
  \*****************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var flatpickr__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! flatpickr */ \"./node_modules/flatpickr/dist/esm/index.js\");\n/* harmony import */ var flatpickr_dist_l10n_bn_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! flatpickr/dist/l10n/bn.js */ \"./node_modules/flatpickr/dist/l10n/bn.js\");\n/* harmony import */ var flatpickr_dist_l10n_bn_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(flatpickr_dist_l10n_bn_js__WEBPACK_IMPORTED_MODULE_1__);\n\r\n\r\n\r\n/* -------------------------------------------------------------------------- */\r\n/*                                    Utils                                   */\r\n/* -------------------------------------------------------------------------- */\r\n\r\nconst camelize = (str) => {\r\n  const text = str.replace(/[-_\\s.]+(.)?/g, (_, c) =>\r\n    c ? c.toUpperCase() : ''\r\n  );\r\n  return `${text.substr(0, 1).toLowerCase()}${text.substr(1)}`;\r\n};\r\n\r\nconst getData = (el, data) => {\r\n  try {\r\n    return JSON.parse(el.dataset[camelize(data)]);\r\n  } catch (e) {\r\n    return el.dataset[camelize(data)];\r\n  }\r\n};\r\n\r\n/* -------------------------------------------------------------------------- */\r\n/*                                  Flatpickr                                 */\r\n/* -------------------------------------------------------------------------- */\r\n\r\ndocument.querySelectorAll('.datetimepicker').forEach((item) => {\r\n  (0,flatpickr__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(item, getData(item, 'options'));\r\n});\r\n\n\n//# sourceURL=webpack://falcon/./src/js/flatpickr.js?");

/***/ })

/******/ 	});
/************************************************************************/
/******/ 	// The module cache
/******/ 	var __webpack_module_cache__ = {};
/******/ 	
/******/ 	// The require function
/******/ 	function __webpack_require__(moduleId) {
/******/ 		// Check if module is in cache
/******/ 		var cachedModule = __webpack_module_cache__[moduleId];
/******/ 		if (cachedModule !== undefined) {
/******/ 			return cachedModule.exports;
/******/ 		}
/******/ 		// Create a new module (and put it into the cache)
/******/ 		var module = __webpack_module_cache__[moduleId] = {
/******/ 			// no module.id needed
/******/ 			// no module.loaded needed
/******/ 			exports: {}
/******/ 		};
/******/ 	
/******/ 		// Execute the module function
/******/ 		__webpack_modules__[moduleId].call(module.exports, module, module.exports, __webpack_require__);
/******/ 	
/******/ 		// Return the exports of the module
/******/ 		return module.exports;
/******/ 	}
/******/ 	
/************************************************************************/
/******/ 	/* webpack/runtime/compat get default export */
/******/ 	!function() {
/******/ 		// getDefaultExport function for compatibility with non-harmony modules
/******/ 		__webpack_require__.n = function(module) {
/******/ 			var getter = module && module.__esModule ?
/******/ 				function() { return module['default']; } :
/******/ 				function() { return module; };
/******/ 			__webpack_require__.d(getter, { a: getter });
/******/ 			return getter;
/******/ 		};
/******/ 	}();
/******/ 	
/******/ 	/* webpack/runtime/define property getters */
/******/ 	!function() {
/******/ 		// define getter functions for harmony exports
/******/ 		__webpack_require__.d = function(exports, definition) {
/******/ 			for(var key in definition) {
/******/ 				if(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {
/******/ 					Object.defineProperty(exports, key, { enumerable: true, get: definition[key] });
/******/ 				}
/******/ 			}
/******/ 		};
/******/ 	}();
/******/ 	
/******/ 	/* webpack/runtime/hasOwnProperty shorthand */
/******/ 	!function() {
/******/ 		__webpack_require__.o = function(obj, prop) { return Object.prototype.hasOwnProperty.call(obj, prop); }
/******/ 	}();
/******/ 	
/******/ 	/* webpack/runtime/make namespace object */
/******/ 	!function() {
/******/ 		// define __esModule on exports
/******/ 		__webpack_require__.r = function(exports) {
/******/ 			if(typeof Symbol !== 'undefined' && Symbol.toStringTag) {
/******/ 				Object.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });
/******/ 			}
/******/ 			Object.defineProperty(exports, '__esModule', { value: true });
/******/ 		};
/******/ 	}();
/******/ 	
/************************************************************************/
/******/ 	
/******/ 	// startup
/******/ 	// Load entry module and return exports
/******/ 	// This entry module can't be inlined because the eval devtool is used.
/******/ 	var __webpack_exports__ = __webpack_require__("./src/js/flatpickr.js");
/******/ 	
/******/ })()
;