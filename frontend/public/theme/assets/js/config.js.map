{"version": 3, "sources": ["config.js"], "names": ["CONFIG", "isNavbarVerticalCollapsed", "theme", "isRTL", "isFluid", "navbarStyle", "navbarPosition", "Object", "keys", "for<PERSON>ach", "key", "localStorage", "getItem", "setItem", "JSON", "parse", "document", "documentElement", "classList", "add", "setAttribute", "window", "matchMedia", "matches"], "mappings": ";;AAAA;AACA;AACA;AACA,IAAAA,MAAA,GAAA;EACAC,yBAAA,EAAA,KAAA;EACAC,KAAA,EAAA,OAAA;EACAC,KAAA,EAAA,KAAA;EACAC,OAAA,EAAA,KAAA;EACAC,WAAA,EAAA,aAAA;EACAC,cAAA,EAAA;AACA,CAAA;AAEAC,MAAA,CAAAC,IAAA,CAAAR,MAAA,CAAA,CAAAS,OAAA,CAAA,UAAAC,GAAA,EAAA;EACA,IAAAC,YAAA,CAAAC,OAAA,CAAAF,GAAA,CAAA,KAAA,IAAA,EAAA;IACAC,YAAA,CAAAE,OAAA,CAAAH,GAAA,EAAAV,MAAA,CAAAU,GAAA,CAAA,CAAA;EACA;AACA,CAAA,CAAA;AAEA,IAAAI,IAAA,CAAAC,KAAA,CAAAJ,YAAA,CAAAC,OAAA,CAAA,2BAAA,CAAA,CAAA,EAAA;EACAI,QAAA,CAAAC,eAAA,CAAAC,SAAA,CAAAC,GAAA,CAAA,2BAAA,CAAA;AACA;AAEA,IAAAR,YAAA,CAAAC,OAAA,CAAA,OAAA,CAAA,KAAA,MAAA,EAAA;EACAI,QAAA,CAAAC,eAAA,CAAAG,YAAA,CAAA,eAAA,EAAA,MAAA,CAAA;AACA,CAAA,MAAA,IAAAT,YAAA,CAAAC,OAAA,CAAA,OAAA,CAAA,KAAA,MAAA,EAAA;EACAI,QAAA,CAAAC,eAAA,CAAAG,YAAA,CACA,eAAA,EACAC,MAAA,CAAAC,UAAA,CAAA,8BAAA,CAAA,CAAAC,OAAA,GAAA,MAAA,GAAA,OACA,CAAA;AACA", "file": "config.js", "sourcesContent": ["/* -------------------------------------------------------------------------- */\r\n/*                              Config                                        */\r\n/* -------------------------------------------------------------------------- */\r\nconst CONFIG = {\r\n  isNavbarVerticalCollapsed: false,\r\n  theme: 'light',\r\n  isRTL: false,\r\n  isFluid: false,\r\n  navbarStyle: 'transparent',\r\n  navbarPosition: 'vertical'\r\n};\r\n\r\nObject.keys(CONFIG).forEach(key => {\r\n  if (localStorage.getItem(key) === null) {\r\n    localStorage.setItem(key, CONFIG[key]);\r\n  }\r\n});\r\n\r\nif (!!JSON.parse(localStorage.getItem('isNavbarVerticalCollapsed'))) {\r\n  document.documentElement.classList.add('navbar-vertical-collapsed');\r\n}\r\n\r\nif (localStorage.getItem('theme') === 'dark') {\r\n  document.documentElement.setAttribute('data-bs-theme', 'dark');\r\n} else if (localStorage.getItem('theme') === 'auto') {\r\n  document.documentElement.setAttribute(\r\n    'data-bs-theme',\r\n    window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light'\r\n  );\r\n}\r\n\r\nexport default CONFIG;\r\n"]}