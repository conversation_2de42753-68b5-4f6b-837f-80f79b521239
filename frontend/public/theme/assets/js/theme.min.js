"use strict";var _excluded=["endValue"];function _objectWithoutProperties(e,t){if(null==e)return{};var a,o=_objectWithoutPropertiesLoose(e,t);if(Object.getOwnPropertySymbols)for(var n=Object.getOwnPropertySymbols(e),r=0;r<n.length;r++)a=n[r],0<=t.indexOf(a)||Object.prototype.propertyIsEnumerable.call(e,a)&&(o[a]=e[a]);return o}function _objectWithoutPropertiesLoose(e,t){if(null==e)return{};for(var a,o={},n=Object.keys(e),r=0;r<n.length;r++)a=n[r],0<=t.indexOf(a)||(o[a]=e[a]);return o}function _toConsumableArray(e){return _arrayWithoutHoles(e)||_iterableToArray(e)||_unsupportedIterableToArray(e)||_nonIterableSpread()}function _nonIterableSpread(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function _unsupportedIterableToArray(e,t){var a;if(e)return"string"==typeof e?_arrayLikeToArray(e,t):"Map"===(a="Object"===(a=Object.prototype.toString.call(e).slice(8,-1))&&e.constructor?e.constructor.name:a)||"Set"===a?Array.from(e):"Arguments"===a||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(a)?_arrayLikeToArray(e,t):void 0}function _iterableToArray(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}function _arrayWithoutHoles(e){if(Array.isArray(e))return _arrayLikeToArray(e)}function _arrayLikeToArray(e,t){(null==t||t>e.length)&&(t=e.length);for(var a=0,o=new Array(t);a<t;a++)o[a]=e[a];return o}function ownKeys(t,e){var a,o=Object.keys(t);return Object.getOwnPropertySymbols&&(a=Object.getOwnPropertySymbols(t),e&&(a=a.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),o.push.apply(o,a)),o}function _objectSpread(t){for(var e=1;e<arguments.length;e++){var a=null!=arguments[e]?arguments[e]:{};e%2?ownKeys(Object(a),!0).forEach(function(e){_defineProperty(t,e,a[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(a)):ownKeys(Object(a)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(a,e))})}return t}function _defineProperty(e,t,a){return(t=_toPropertyKey(t))in e?Object.defineProperty(e,t,{value:a,enumerable:!0,configurable:!0,writable:!0}):e[t]=a,e}function _typeof(e){return(_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function _classCallCheck(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function _defineProperties(e,t){for(var a=0;a<t.length;a++){var o=t[a];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,_toPropertyKey(o.key),o)}}function _createClass(e,t,a){return t&&_defineProperties(e.prototype,t),a&&_defineProperties(e,a),Object.defineProperty(e,"prototype",{writable:!1}),e}function _toPropertyKey(e){e=_toPrimitive(e,"string");return"symbol"==_typeof(e)?e:String(e)}function _toPrimitive(e,t){if("object"!=_typeof(e)||!e)return e;var a=e[Symbol.toPrimitive];if(void 0===a)return("string"===t?String:Number)(e);a=a.call(e,t||"default");if("object"!=_typeof(a))return a;throw new TypeError("@@toPrimitive must return a primitive value.")}var docReady=function(e){"loading"===document.readyState?document.addEventListener("DOMContentLoaded",e):setTimeout(e,1)},resize=function(e){return window.addEventListener("resize",e)},isIterableArray=function(e){return Array.isArray(e)&&!!e.length},camelize=function(e){e=e.replace(/[-_\s.]+(.)?/g,function(e,t){return t?t.toUpperCase():""});return"".concat(e.substr(0,1).toLowerCase()).concat(e.substr(1))},getData=function(t,a){try{return JSON.parse(t.dataset[camelize(a)])}catch(e){return t.dataset[camelize(a)]}},hexToRgb=function(e){e=0===e.indexOf("#")?e.substring(1):e,e=/^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(e.replace(/^#?([a-f\d])([a-f\d])([a-f\d])$/i,function(e,t,a,o){return t+t+a+a+o+o}));return e?[parseInt(e[1],16),parseInt(e[2],16),parseInt(e[3],16)]:null},rgbaColor=function(){var e=0<arguments.length&&void 0!==arguments[0]?arguments[0]:"#fff",t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:.5;return"rgba(".concat(hexToRgb(e),", ").concat(t,")")},getColor=function(e){var t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:document.documentElement;return getComputedStyle(t).getPropertyValue("--falcon-".concat(e)).trim()},getColors=function(e){return{primary:getColor("primary",e),secondary:getColor("secondary",e),success:getColor("success",e),info:getColor("info",e),warning:getColor("warning",e),danger:getColor("danger",e),light:getColor("light",e),dark:getColor("dark",e),white:getColor("white",e),black:getColor("black",e),emphasis:getColor("emphasis-color",e)}},getSubtleColors=function(e){return{primary:getColor("primary-bg-subtle",e),secondary:getColor("secondary-bg-subtle",e),success:getColor("success-bg-subtle",e),info:getColor("info-bg-subtle",e),warning:getColor("warning-bg-subtle",e),danger:getColor("danger-bg-subtle",e),light:getColor("light-bg-subtle",e),dark:getColor("dark-bg-subtle",e)}},getGrays=function(e){return{100:getColor("gray-100",e),200:getColor("gray-200",e),300:getColor("gray-300",e),400:getColor("gray-400",e),500:getColor("gray-500",e),600:getColor("gray-600",e),700:getColor("gray-700",e),800:getColor("gray-800",e),900:getColor("gray-900",e),1e3:getColor("gray-1000",e),1100:getColor("gray-1100",e)}},hasClass=function(e,t){return e.classList.value.includes(t)},addClass=function(e,t){e.classList.add(t)},removeClass=function(e,t){e.classList.remove(t)},getOffset=function(e){var e=e.getBoundingClientRect(),t=window.pageXOffset||document.documentElement.scrollLeft,a=window.pageYOffset||document.documentElement.scrollTop;return{top:e.top+a,left:e.left+t}};function isScrolledIntoView(e){var e=e.getBoundingClientRect(),t=window.innerHeight||document.documentElement.clientHeight,a=window.innerWidth||document.documentElement.clientWidth,t=e.top<=t&&0<=e.top+e.height,a=e.left<=a&&0<=e.left+e.width;return t&&a}var breakpoints={xs:0,sm:576,md:768,lg:992,xl:1200,xxl:1540},getBreakpoint=function(e){var t,e=e&&e.classList.value;return t=e?breakpoints[e.split(" ").filter(function(e){return e.includes("navbar-expand-")}).pop().split("-").pop()]:t},getSystemTheme=function(){return window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light"},isDark=function(){return"auto"===localStorage.getItem("theme")?getSystemTheme():localStorage.getItem("theme")},setCookie=function(e,t,a){var o=new Date;o.setTime(o.getTime()+a),document.cookie="".concat(e,"=").concat(t,";expires=").concat(o.toUTCString())},getCookie=function(e){e=document.cookie.match("(^|;) ?".concat(e,"=([^;]*)(;|$)"));return e&&e[2]},settings={tinymce:{theme:"oxide"},chart:{borderColor:"rgba(255, 255, 255, 0.8)"}},newChart=function(e,t){e=e.getContext("2d");return new window.Chart(e,t)},getItemFromStore=function(t,a){var o=2<arguments.length&&void 0!==arguments[2]?arguments[2]:localStorage;try{return JSON.parse(o.getItem(t))||a}catch(e){return o.getItem(t)||a}},setItemToStore=function(e,t){return(2<arguments.length&&void 0!==arguments[2]?arguments[2]:localStorage).setItem(e,t)},getStoreSpace=function(){var e=0<arguments.length&&void 0!==arguments[0]?arguments[0]:localStorage;return parseFloat((escape(encodeURIComponent(JSON.stringify(e))).length/1048576).toFixed(2))},getDates=function(a,e){var o=2<arguments.length&&void 0!==arguments[2]?arguments[2]:864e5;return Array.from({length:1+(e-a)/o},function(e,t){return new Date(a.valueOf()+o*t)})},getPastDates=function(e){var t;switch(e){case"week":t=7;break;case"month":t=30;break;case"year":t=365;break;default:t=e}var a=new Date,o=a,a=new Date((new Date).setDate(a.getDate()-(t-1)));return getDates(a,o)},getRandomNumber=function(e,t){return Math.floor(Math.random()*(t-e)+e)},utils={docReady:docReady,breakpoints:breakpoints,resize:resize,isIterableArray:isIterableArray,camelize:camelize,getData:getData,hasClass:hasClass,addClass:addClass,hexToRgb:hexToRgb,rgbaColor:rgbaColor,getColor:getColor,getColors:getColors,getSubtleColors:getSubtleColors,getGrays:getGrays,getOffset:getOffset,isScrolledIntoView:isScrolledIntoView,getBreakpoint:getBreakpoint,setCookie:setCookie,getCookie:getCookie,newChart:newChart,settings:settings,getItemFromStore:getItemFromStore,setItemToStore:setItemToStore,getStoreSpace:getStoreSpace,getDates:getDates,getPastDates:getPastDates,getRandomNumber:getRandomNumber,removeClass:removeClass,getSystemTheme:getSystemTheme,isDark:isDark},detectorInit=function(){var e=window.is,t=document.querySelector("html");e.opera()&&addClass(t,"opera"),e.mobile()&&addClass(t,"mobile"),e.firefox()&&addClass(t,"firefox"),e.safari()&&addClass(t,"safari"),e.ios()&&addClass(t,"ios"),e.iphone()&&addClass(t,"iphone"),e.ipad()&&addClass(t,"ipad"),e.ie()&&addClass(t,"ie"),e.edge()&&addClass(t,"edge"),e.chrome()&&addClass(t,"chrome"),e.mac()&&addClass(t,"osx"),e.windows()&&addClass(t,"windows"),navigator.userAgent.match("CriOS")&&addClass(t,"chrome")},DomNode=function(){function t(e){_classCallCheck(this,t),this.node=e}return _createClass(t,[{key:"addClass",value:function(e){this.isValidNode()&&this.node.classList.add(e)}},{key:"removeClass",value:function(e){this.isValidNode()&&this.node.classList.remove(e)}},{key:"toggleClass",value:function(e){this.isValidNode()&&this.node.classList.toggle(e)}},{key:"hasClass",value:function(e){this.isValidNode()&&this.node.classList.contains(e)}},{key:"data",value:function(t){if(this.isValidNode())try{return JSON.parse(this.node.dataset[this.camelize(t)])}catch(e){return this.node.dataset[this.camelize(t)]}return null}},{key:"attr",value:function(e){return this.isValidNode()&&this.node[e]}},{key:"setAttribute",value:function(e,t){this.isValidNode()&&this.node.setAttribute(e,t)}},{key:"removeAttribute",value:function(e){this.isValidNode()&&this.node.removeAttribute(e)}},{key:"setProp",value:function(e,t){this.isValidNode()&&(this.node[e]=t)}},{key:"on",value:function(e,t){this.isValidNode()&&this.node.addEventListener(e,t)}},{key:"isValidNode",value:function(){return!!this.node}},{key:"camelize",value:function(e){e=e.replace(/[-_\s.]+(.)?/g,function(e,t){return t?t.toUpperCase():""});return"".concat(e.substr(0,1).toLowerCase()).concat(e.substr(1))}}]),t}(),orders=[{id:1,dropdownId:"order-dropdown-1",orderId:"#181",mailLink:"mailto:<EMAIL>",name:"Ricky Antony",email:"<EMAIL>",date:"20/04/2019",address:"Ricky Antony, 2392 Main Avenue, Penasauka, New Jersey 02149",shippingType:"Via Flat Rate",status:"Completed",badge:{type:"success",icon:"fas fa-check"},amount:"$99"},{id:2,dropdownId:"order-dropdown-2",orderId:"#182",mailLink:"mailto:<EMAIL>",name:"Kin Rossow",email:"<EMAIL>",date:"20/04/2019",address:"Kin Rossow, 1 Hollywood Blvd,Beverly Hills, California 90210",shippingType:"Via Free Shipping",status:"Processing",badge:{type:"primary",icon:"fas fa-redo"},amount:"$120"},{id:3,dropdownId:"order-dropdown-3",orderId:"#183",mailLink:"mailto:<EMAIL>",name:"Merry Diana",email:"<EMAIL>",date:"30/04/2019",address:"Merry Diana, 1 Infinite Loop, Cupertino, California 90210",shippingType:"Via Link Road",status:"On Hold",badge:{type:"secondary",icon:"fas fa-ban"},amount:"$70"},{id:4,dropdownId:"order-dropdown-4",orderId:"#184",mailLink:"mailto:<EMAIL>",name:"Bucky Robert",email:"<EMAIL>",date:"30/04/2019",address:"Bucky Robert, 1 Infinite Loop, Cupertino, California 90210",shippingType:"Via Free Shipping",status:"Pending",badge:{type:"warning",icon:"fas fa-stream"},amount:"$92"},{id:5,dropdownId:"order-dropdown-5",orderId:"#185",mailLink:"mailto:<EMAIL>",name:"Rocky Zampa",email:"<EMAIL>",date:"30/04/2019",address:"Rocky Zampa, 1 Infinite Loop, Cupertino, California 90210",shippingType:"Via Free Road",status:"On Hold",badge:{type:"secondary",icon:"fas fa-ban"},amount:"$120"},{id:6,dropdownId:"order-dropdown-6",orderId:"#186",mailLink:"mailto:<EMAIL>",name:"Ricky John",email:"<EMAIL>",date:"30/04/2019",address:"Ricky John, 1 Infinite Loop, Cupertino, California 90210",shippingType:"Via Free Shipping",status:"Processing",badge:{type:"primary",icon:"fas fa-redo"},amount:"$145"},{id:7,dropdownId:"order-dropdown-7",orderId:"#187",mailLink:"mailto:<EMAIL>",name:"Cristofer Henric",email:"<EMAIL>",date:"30/04/2019",address:"Cristofer Henric, 1 Infinite Loop, Cupertino, California 90210",shippingType:"Via Flat Rate",status:"Completed",badge:{type:"success",icon:"fas fa-check"},amount:"$55"},{id:8,dropdownId:"order-dropdown-8",orderId:"#188",mailLink:"mailto:<EMAIL>",name:"Brate Lee",email:"<EMAIL>",date:"29/04/2019",address:"Brate Lee, 1 Infinite Loop, Cupertino, California 90210",shippingType:"Via Link Road",status:"On Hold",badge:{type:"secondary",icon:"fas fa-ban"},amount:"$90"},{id:9,dropdownId:"order-dropdown-9",orderId:"#189",mailLink:"mailto:<EMAIL>",name:"Thomas Stephenson",email:"<EMAIL>",date:"29/04/2019",address:"Thomas Stephenson, 116 Ballifeary Road, Bamff",shippingType:"Via Flat Rate",status:"Processing",badge:{type:"primary",icon:"fas fa-redo"},amount:"$52"},{id:10,dropdownId:"order-dropdown-10",orderId:"#190",mailLink:"mailto:<EMAIL>",name:"Evie Singh",email:"<EMAIL>",date:"29/04/2019",address:"Evie Singh, 54 Castledore Road, Tunstead",shippingType:"Via Flat Rate",status:"Completed",badge:{type:"success",icon:"fas fa-check"},amount:"$90"},{id:11,dropdownId:"order-dropdown-11",orderId:"#191",mailLink:"mailto:<EMAIL>",name:"David Peters",email:"<EMAIL>",date:"29/04/2019",address:"David Peters, Rhyd Y Groes, Rhosgoch, LL66 0AT",shippingType:"Via Link Road",status:"Completed",badge:{type:"success",icon:"fas fa-check"},amount:"$69"},{id:12,dropdownId:"order-dropdown-12",orderId:"#192",mailLink:"mailto:<EMAIL>",name:"Jennifer Johnson",email:"<EMAIL>",date:"28/04/2019",address:"Jennifer Johnson, Rhyd Y Groes, Rhosgoch, LL66 0AT",shippingType:"Via Flat Rate",status:"Processing",badge:{type:"primary",icon:"fas fa-redo"},amount:"$112"},{id:13,dropdownId:"order-dropdown-13",orderId:"#193",mailLink:"mailto:<EMAIL>",name:" Demarcus Okuneva",email:"<EMAIL>",date:"28/04/2019",address:" Demarcus Okuneva, 90555 Upton Drive Jeffreyview, UT 08771",shippingType:"Via Flat Rate",status:"Completed",badge:{type:"success",icon:"fas fa-check"},amount:"$99"},{id:14,dropdownId:"order-dropdown-14",orderId:"#194",mailLink:"mailto:<EMAIL>",name:"Simeon Harber",email:"<EMAIL>",date:"27/04/2019",address:"Simeon Harber, 702 Kunde Plain Apt. 634 East Bridgetview, HI 13134-1862",shippingType:"Via Free Shipping",status:"On Hold",badge:{type:"secondary",icon:"fas fa-ban"},amount:"$129"},{id:15,dropdownId:"order-dropdown-15",orderId:"#195",mailLink:"mailto:<EMAIL>",name:"Lavon Haley",email:"<EMAIL>",date:"27/04/2019",address:"Lavon Haley, 30998 Adonis Locks McGlynnside, ID 27241",shippingType:"Via Free Shipping",status:"Pending",badge:{type:"warning",icon:"fas fa-stream"},amount:"$70"},{id:16,dropdownId:"order-dropdown-16",orderId:"#196",mailLink:"mailto:<EMAIL>",name:"Ashley Kirlin",email:"<EMAIL>",date:"26/04/2019",address:"Ashley Kirlin, 43304 Prosacco Shore South Dejuanfurt, MO 18623-0505",shippingType:"Via Link Road",status:"Processing",badge:{type:"primary",icon:"fas fa-redo"},amount:"$39"},{id:17,dropdownId:"order-dropdown-17",orderId:"#197",mailLink:"mailto:<EMAIL>",name:"Johnnie Considine",email:"<EMAIL>",date:"26/04/2019",address:"Johnnie Considine, 6008 Hermann Points Suite 294 Hansenville, TN 14210",shippingType:"Via Flat Rate",status:"Pending",badge:{type:"warning",icon:"fas fa-stream"},amount:"$70"},{id:18,dropdownId:"order-dropdown-18",orderId:"#198",mailLink:"mailto:<EMAIL>",name:"Trace Farrell",email:"<EMAIL>",date:"26/04/2019",address:"Trace Farrell, 431 Steuber Mews Apt. 252 Germanland, AK 25882",shippingType:"Via Free Shipping",status:"Completed",badge:{type:"success",icon:"fas fa-check"},amount:"$70"},{id:19,dropdownId:"order-dropdown-19",orderId:"#199",mailLink:"mailto:<EMAIL>",name:"Estell Nienow",email:"<EMAIL>",date:"26/04/2019",address:"Estell Nienow, 4167 Laverna Manor Marysemouth, NV 74590",shippingType:"Via Free Shipping",status:"Completed",badge:{type:"success",icon:"fas fa-check"},amount:"$59"},{id:20,dropdownId:"order-dropdown-20",orderId:"#200",mailLink:"mailto:<EMAIL>",name:"Daisha Howe",email:"<EMAIL>",date:"25/04/2019",address:"Daisha Howe, 829 Lavonne Valley Apt. 074 Stehrfort, RI 77914-0379",shippingType:"Via Free Shipping",status:"Completed",badge:{type:"success",icon:"fas fa-check"},amount:"$39"},{id:21,dropdownId:"order-dropdown-21",orderId:"#201",mailLink:"mailto:<EMAIL>",name:"Miles Haley",email:"<EMAIL>",date:"24/04/2019",address:"Miles Haley, 53150 Thad Squares Apt. 263 Archibaldfort, MO 00837",shippingType:"Via Flat Rate",status:"Completed",badge:{type:"success",icon:"fas fa-check"},amount:"$55"},{id:22,dropdownId:"order-dropdown-22",orderId:"#202",mailLink:"mailto:<EMAIL>",name:"Brenda Watsica",email:"<EMAIL>",date:"24/04/2019",address:"Brenda Watsica, 9198 O'Kon Harbors Morarborough, IA 75409-7383",shippingType:"Via Free Shipping",status:"Completed",badge:{type:"success",icon:"fas fa-check"},amount:"$89"},{id:23,dropdownId:"order-dropdown-23",orderId:"#203",mailLink:"mailto:<EMAIL>",name:"Ellie O'Reilly",email:"<EMAIL>",date:"24/04/2019",address:"Ellie O'Reilly, 1478 Kaitlin Haven Apt. 061 Lake Muhammadmouth, SC 35848",shippingType:"Via Free Shipping",status:"Completed",badge:{type:"success",icon:"fas fa-check"},amount:"$47"},{id:24,dropdownId:"order-dropdown-24",orderId:"#204",mailLink:"mailto:<EMAIL>",name:"Garry Brainstrow",email:"<EMAIL>",date:"23/04/2019",address:"Garry Brainstrow, 13572 Kurt Mews South Merritt, IA 52491",shippingType:"Via Free Shipping",status:"Completed",badge:{type:"success",icon:"fas fa-check"},amount:"$139"},{id:25,dropdownId:"order-dropdown-25",orderId:"#205",mailLink:"mailto:<EMAIL>",name:"Estell Pollich",email:"<EMAIL>",date:"23/04/2019",address:"Estell Pollich, 13572 Kurt Mews South Merritt, IA 52491",shippingType:"Via Free Shipping",status:"On Hold",badge:{type:"secondary",icon:"fas fa-ban"},amount:"$49"},{id:26,dropdownId:"order-dropdown-26",orderId:"#206",mailLink:"mailto:<EMAIL>",name:"Ara Mueller",email:"<EMAIL>",date:"23/04/2019",address:"Ara Mueller, 91979 Kohler Place Waelchiborough, CT 41291",shippingType:"Via Flat Rate",status:"On Hold",badge:{type:"secondary",icon:"fas fa-ban"},amount:"$19"},{id:27,dropdownId:"order-dropdown-27",orderId:"#207",mailLink:"mailto:<EMAIL>",name:"Lucienne Blick",email:"<EMAIL>",date:"23/04/2019",address:"Lucienne Blick, 6757 Giuseppe Meadows Geraldinemouth, MO 48819-4970",shippingType:"Via Flat Rate",status:"On Hold",badge:{type:"secondary",icon:"fas fa-ban"},amount:"$59"},{id:28,dropdownId:"order-dropdown-28",orderId:"#208",mailLink:"mailto:<EMAIL>",name:"Laverne Haag",email:"<EMAIL>",date:"22/04/2019",address:"Laverne Haag, 2327 Kaylee Mill East Citlalli, AZ 89582-3143",shippingType:"Via Flat Rate",status:"On Hold",badge:{type:"secondary",icon:"fas fa-ban"},amount:"$49"},{id:29,dropdownId:"order-dropdown-29",orderId:"#209",mailLink:"mailto:<EMAIL>",name:"Brandon Bednar",email:"<EMAIL>",date:"22/04/2019",address:"Brandon Bednar, 25156 Isaac Crossing Apt. 810 Lonborough, CO 83774-5999",shippingType:"Via Flat Rate",status:"On Hold",badge:{type:"secondary",icon:"fas fa-ban"},amount:"$39"},{id:30,dropdownId:"order-dropdown-30",orderId:"#210",mailLink:"mailto:<EMAIL>",name:"Dimitri Boehm",email:"<EMAIL>",date:"23/04/2019",address:"Dimitri Boehm, 71603 Wolff Plains Apt. 885 Johnstonton, MI 01581",shippingType:"Via Flat Rate",status:"On Hold",badge:{type:"secondary",icon:"fas fa-ban"},amount:"$111"}],advanceAjaxTableInit=function(){function e(e,t){e.disabled=t,e.classList[t?"add":"remove"]("disabled")}var a,t,o,n,r,i,l,s,c,d,u,m,g,y,p,h,v,f=document.getElementById("advanceAjaxTable");f&&(a={page:10,pagination:{item:"<li><button class='page' type='button'></button></li>"},item:function(e){var t=e.orderId,a=e.id,o=e.name,n=e.email,r=e.date,i=e.address,l=e.shippingType,s=e.status,c=e.badge,e=e.amount;return'\n          <tr class="btn-reveal-trigger">\n            <td class="order py-2 align-middle white-space-nowrap">\n              <a href="https://prium.github.io/falcon/v3.16.0/app/e-commerce/orders/order-details.html">\n                <strong>'.concat(t,"</strong>\n              </a>\n              by\n              <strong>").concat(o,'</strong>\n              <br />\n              <a href="mailto:').concat(n,'">').concat(n,'</a>\n            </td>\n            <td class="py-2 align-middle">\n              ').concat(r,'\n            </td>\n            <td class="py-2 align-middle white-space-nowrap">\n              ').concat(i,'\n              <p class="mb-0 text-500">').concat(l,'</p>\n            </td>\n            <td class="py-2 align-middle text-center fs-9 white-space-nowrap">\n              <span class="badge rounded-pill d-block badge-subtle-').concat(c.type,'">\n                ').concat(s,'\n                <span class="ms-1 ').concat(c.icon,'" data-fa-transform="shrink-2"></span>\n              </span>\n            </td>\n            <td class="py-2 align-middle text-end fs-9 fw-medium">\n              ').concat(e,'\n            </td>\n            <td class="py-2 align-middle white-space-nowrap text-end">\n              <div class="dropstart font-sans-serif position-static d-inline-block">\n                <button class="btn btn-link text-600 btn-sm dropdown-toggle btn-reveal" type=\'button\' id="order-dropdown-').concat(a,'" data-bs-toggle="dropdown" data-boundary="window" aria-haspopup="true" aria-expanded="false" data-bs-reference="parent">\n                  <span class="fas fa-ellipsis-h fs-10"></span>\n                </button>\n                <div class="dropdown-menu dropdown-menu-end border py-2" aria-labelledby="order-dropdown-').concat(a,'">\n                  <a href="#!" class="dropdown-item">View</a>\n                  <a href="#!" class="dropdown-item">Edit</a>\n                  <a href="#!" class="dropdown-item">Refund</a>\n                  <div class"dropdown-divider"></div>\n                  <a href="#!" class="dropdown-item text-warning">Archive</a>\n                  <a href="#!" class="dropdown-item text-warning">Archive</a>\n                </div>\n              </div>\n            </td>\n          </tr>\n        ')}},t=f.querySelector('[data-list-pagination="next"]'),o=f.querySelector('[data-list-pagination="prev"]'),n=f.querySelector('[data-list-view="*"]'),r=f.querySelector('[data-list-view="less"]'),i=f.querySelector("[data-list-info]"),l=document.querySelector("[data-list-filter]"),(s=new window.List(f,a,orders)).on("updated",function(e){var t=f.querySelector(".fallback")||document.getElementById(a.fallback);t&&(0===e.matchingItems.length?t.classList.remove("d-none"):t.classList.add("d-none"))}),c=s.items.length,d=s.page,u=s.listContainer.querySelector(".btn-close"),m=Math.ceil(c/d),g=s.visibleItems.length,y=1,u&&u.addEventListener("search.close",function(){return s.fuzzySearch("")}),(p=function(){i&&(i.innerHTML="".concat(s.i," to ").concat(g," of ").concat(c)),o&&e(o,1===y),t&&e(t,y===m),1<y&&y<m&&(e(t,!1),e(o,!1))})(),t&&t.addEventListener("click",function(e){e.preventDefault(),y+=1;e=s.i+d;e<=s.size()&&s.show(e,d),g+=s.visibleItems.length,p()}),o&&o.addEventListener("click",function(e){e.preventDefault(),--y,g-=s.visibleItems.length;e=s.i-d;0<e&&s.show(e,d),p()}),h=function(){r.classList.toggle("d-none"),n.classList.toggle("d-none")},n&&n.addEventListener("click",function(){s.show(1,c),y=m=1,g=c,p(),h()}),r&&r.addEventListener("click",function(){s.show(1,d),m=Math.ceil(c/d),y=1,g=s.visibleItems.length,p(),h()}),a.pagination&&f.querySelector(".pagination").addEventListener("click",function(e){"page"===e.target.classList[0]&&(y=Number(e.target.innerText),p())}),a.filter)&&(v=a.filter.key,l.addEventListener("change",function(t){s.filter(function(e){return""===t.target.value||e.values()[v].toLowerCase().includes(t.target.value.toLowerCase())})}))},anchors=new window.AnchorJS,bottomBarInit=(anchors.options={icon:"#"},anchors.add("[data-anchor]"),function(){function i(e){var t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:0,e=e.getBoundingClientRect();return 0<e.bottom&&e.top>t&&0<e.right&&e.left<(window.innerWidth||document.documentElement.clientWidth)&&e.top<(window.innerHeight||document.documentElement.clientHeight)}var e=document.querySelectorAll("[data-bottom-bar]"),l=[document.querySelector('[data-bs-target="#navbarVerticalCollapse"]'),document.querySelector('[data-bs-target="#navbarStandard"]')];e.length&&e.forEach(function(t){var e=utils.getData(t,"bottom-bar"),e=window._.merge({target:"#bottom-bar-target",offsetTop:0,breakPoint:"lg"},e),a=e.target,o=e.offsetTop,n=e.breakPoint,r=document.getElementById(a);window.addEventListener("scroll",function(){window.matchMedia("(max-width: ".concat(utils.breakpoints[n],"px)")).matches&&(i(r,o)?utils.addClass(t,"hide"):utils.removeClass(t,"hide"))});l.forEach(function(e){return e&&e.addEventListener("click",function(){utils.hasClass(e,"collapsed")?i(r,o)||utils.removeClass(t,"hide"):utils.addClass(t,"hide")})})})}),elementMap=new Map,BulkSelect=function(){function a(e,t){_classCallCheck(this,a),this.element=e,this.option=_objectSpread({displayNoneClassName:"d-none"},t),elementMap.set(this.element,this)}return _createClass(a,[{key:"init",value:function(){this.attachNodes(),this.clickBulkCheckbox(),this.clickRowCheckbox()}},{key:"getSelectedRows",value:function(){return Array.from(this.bulkSelectRows).filter(function(e){return e.checked}).map(function(e){return utils.getData(e,"bulk-select-row")})}},{key:"attachNodes",value:function(){var e=utils.getData(this.element,"bulk-select"),t=e.body,a=e.actions,e=e.replacedElement;this.actions=new DomNode(document.getElementById(a)),this.replacedElement=new DomNode(document.getElementById(e)),this.bulkSelectRows=document.getElementById(t).querySelectorAll("[data-bulk-select-row]")}},{key:"attachRowNodes",value:function(e){this.bulkSelectRows=e}},{key:"clickBulkCheckbox",value:function(){var t=this;this.element.addEventListener("click",function(){"indeterminate"===t.element.indeterminate?(t.actions.addClass(t.option.displayNoneClassName),t.replacedElement.removeClass(t.option.displayNoneClassName),t.removeBulkCheck(),t.bulkSelectRows.forEach(function(e){e=new DomNode(e);e.setProp("checked",!1),e.setAttribute("checked",!1)})):(t.toggleDisplay(),t.bulkSelectRows.forEach(function(e){e.checked=t.element.checked}))})}},{key:"clickRowCheckbox",value:function(){var t=this;this.bulkSelectRows.forEach(function(e){new DomNode(e).on("click",function(){"indeterminate"!==t.element.indeterminate&&(t.element.indeterminate=!0,t.element.setAttribute("indeterminate","indeterminate"),t.element.checked=!0,t.element.setAttribute("checked",!0),t.actions.removeClass(t.option.displayNoneClassName),t.replacedElement.addClass(t.option.displayNoneClassName)),_toConsumableArray(t.bulkSelectRows).every(function(e){return e.checked})&&(t.element.indeterminate=!1,t.element.setAttribute("indeterminate",!1)),_toConsumableArray(t.bulkSelectRows).every(function(e){return!e.checked})&&(t.removeBulkCheck(),t.toggleDisplay())})})}},{key:"removeBulkCheck",value:function(){this.element.indeterminate=!1,this.element.removeAttribute("indeterminate"),this.element.checked=!1,this.element.setAttribute("checked",!1)}},{key:"toggleDisplay",value:function(){this.actions.toggleClass(this.option.displayNoneClassName),this.replacedElement.toggleClass(this.option.displayNoneClassName)}}],[{key:"getInstance",value:function(e){return elementMap.has(e)?elementMap.get(e):null}}]),a}();function bulkSelectInit(){var e=document.querySelectorAll("[data-bulk-select");e.length&&e.forEach(function(e){new BulkSelect(e).init()})}window.BulkSelect=BulkSelect;var chatInit=function(){function t(e){e&&(e.scrollTop=e.scrollHeight)}var a="click",o="shown.bs.tab",e="keyup",n=".chat-sidebar",r=".chat-contact",i=".chat-content-scroll-area",l=".card-chat-pane.active .chat-content-scroll-area",s=".chat-editor-area .emojiarea-editor",c=".btn-send",d=".btn-chat-info",u=".conversation-info",m=".contacts-list-show",g="unread-message",y="text-primary",p="show",h="index",v=document.querySelector(n),n=document.querySelectorAll(r),f=document.querySelector(s),b=document.querySelector(c),w=document.querySelector(i);setTimeout(function(){t(w)},700),document.querySelectorAll(r).forEach(function(e){e.addEventListener(a,function(e){var t=e.currentTarget;t.classList.add("active"),window.innerWidth<768&&!e.target.classList.contains("hover-actions")&&(v.style.left="-100%"),t.classList.contains(g)&&t.classList.remove(g)})}),n.forEach(function(e){e.addEventListener(o,function(){f.innerHTML="",b.classList.remove(y);var e=document.querySelector(l);t(e)})}),f&&(f.setAttribute("placeholder","Type your message"),f.addEventListener(e,function(e){e.target.textContent.length<=0?(b.classList.remove(y),"<br>"===e.target.innerHTML&&(e.target.innerHTML="")):b.classList.add(y);e=document.querySelector(l);t(e)})),f&&document.querySelectorAll(d).forEach(function(e){e.addEventListener(a,function(e){e=e.currentTarget,e=utils.getData(e,h);document.querySelector("".concat(u,"[data-").concat(h,"='").concat(e,"']")).classList.toggle(p)})}),document.querySelectorAll(m).forEach(function(e){e.addEventListener(a,function(){v.style.left=0})}),utils.resize(function(){var e=document.querySelector(l);t(e)})},choicesInit=function(){window.Choices&&document.querySelectorAll(".js-choice").forEach(function(a){var e=utils.getData(a,"options"),e=new window.Choices(a,_objectSpread({itemSelectText:""},e));return document.querySelectorAll(".needs-validation").forEach(function(e){function t(){e.querySelectorAll(".choices").forEach(function(e){var t=e.querySelector(".choices__list--single"),a=e.querySelector(".choices__list--multiple");e.querySelector("[required]")&&(t&&(""!==(null==(t=t.querySelector(".choices__item--selectable"))?void 0:t.getAttribute("data-value"))?(e.classList.remove("invalid"),e.classList.add("valid")):(e.classList.remove("valid"),e.classList.add("invalid"))),a)&&(e.getElementsByTagName("option").length?(e.classList.remove("invalid"),e.classList.add("valid")):(e.classList.remove("valid"),e.classList.add("invalid")))})}e.addEventListener("submit",function(){t()}),a.addEventListener("change",function(){t()})}),e})},cookieNoticeInit=function(){var e=".notice",t='[data-bs-toggle="notice"]',a="click",r="hidden.bs.toast",i="options",o="hide",e=document.querySelectorAll(e),l=!0,e=(e.forEach(function(e){var t=new window.bootstrap.Toast(e),a=_objectSpread({autoShow:!1,autoShowDelay:0,showOnce:!1,cookieExpireTime:36e5},utils.getData(e,i)),o=a.showOnce,n=a.autoShow,a=a.autoShowDelay;o&&(o=utils.getCookie("notice"),l=null===o),n&&l&&setTimeout(function(){t.show()},a),e.addEventListener(r,function(e){e=e.currentTarget,e=_objectSpread({cookieExpireTime:36e5,showOnce:!1},utils.getData(e,i));e.showOnce&&utils.setCookie("notice",!1,e.cookieExpireTime)})}),document.querySelector(t));e&&e.addEventListener(a,function(e){var e=e.currentTarget.getAttribute("href"),e=new window.bootstrap.Toast(document.querySelector(e)),t=e._element;utils.hasClass(t,o)?e.show():e.hide()})},copyLink=function(){var e=document.getElementById("copyLinkModal"),e=(e&&e.addEventListener("shown.bs.modal",function(){document.querySelector(".invitation-link").select()}),document.querySelectorAll("[data-copy]"));e&&e.forEach(function(e){var t=new window.bootstrap.Tooltip(e);e.addEventListener("mouseover",function(){return t.show()}),e.addEventListener("mouseleave",function(){return t.hide()}),e.addEventListener("click",function(e){e.stopPropagation();e=e.target,e.setAttribute("data-original-title","Copied"),t.show(),e.setAttribute("data-original-title","Copy to clipboard"),t.update(),e=utils.getData(e,"copy");document.querySelector(e).select(),document.execCommand("copy")})})},countupInit=function(){window.countUp&&document.querySelectorAll("[data-countup]").forEach(function(e){var t=utils.getData(e,"countup"),a=t.endValue,t=_objectWithoutProperties(t,_excluded),e=new window.countUp.CountUp(e,a,_objectSpread({duration:5},t));e.error?console.error(e.error):e.start()})},dataTablesInit=function(){var n,e,r;window.jQuery&&(e=(n=window.jQuery)("[data-datatables]"),r=function(e){e.find(".pagination").addClass("pagination-sm")},e.length)&&e.each(function(e,t){var t=n(t),a=n.extend({dom:"<'row mx-0'<'col-md-6'l><'col-md-6'f>><'table-responsive scrollbar'tr><'row g-0 align-items-center justify-content-center justify-content-sm-between'<'col-auto mb-2 mb-sm-0 px-3'i><'col-auto px-3'p>>"},t.data("datatables")),o=(t.DataTable(a),t.closest(".dataTables_wrapper"));r(o),t.on("draw.dt",function(){return r(o)})})},dropdownMenuInit=function(){var t,a;window.is.ios()&&(t="shown.bs.dropdown",a="hidden.bs.dropdown",document.querySelectorAll(".table-responsive").forEach(function(e){e.addEventListener(t,function(e){var t=e.currentTarget;t.scrollWidth>t.clientWidth&&(t.style.paddingBottom="".concat(e.target.nextElementSibling.clientHeight,"px"))}),e.addEventListener(a,function(e){e.currentTarget.style.paddingBottom=""})}))},dropdownOnHover=function(){var e=document.querySelector("[data-top-nav-dropdowns]");e&&e.addEventListener("mouseover",function(e){var t;e.target.className.includes("dropdown-toggle")&&992<window.innerWidth&&((t=new window.bootstrap.Dropdown(e.target))._element.classList.add("show"),t._menu.classList.add("show"),t._menu.setAttribute("data-bs-popper","none"),e.target.parentNode.addEventListener("mouseleave",function(){t.hide()}))})},dropzoneInit=(window.Dropzone&&(window.Dropzone.autoDiscover=!1),function(){var n=window._.merge,e="[data-dropzone]",r=".dz-preview",i=".dz-preview .dz-preview-cover",l="dz-file-processing",s="dz-file-complete",c="dz-processing",d="options",u="addedfile",m="removedfile",g="complete",e=document.querySelectorAll(e);e.length&&e.forEach(function(e){var t,o=(t=utils.getData(e,d)||{}).data||{},a=n({url:"/assets/php/",addRemoveLinks:!1,previewsContainer:e.querySelector(r),previewTemplate:e.querySelector(r).innerHTML,thumbnailWidth:null,thumbnailHeight:null,maxFilesize:20,autoProcessQueue:!1,filesizeBase:1e3,init:function(){var a=this;o.length&&o.forEach(function(e){var t={name:e.name,size:e.size};a.options.addedfile.call(a,t),a.options.thumbnail.call(a,t,"".concat(e.url,"/").concat(e.name))}),a.on(u,function(){"maxFiles"in t&&(1===t.maxFiles&&1<e.querySelectorAll(i).length&&e.querySelector(i).remove(),1===t.maxFiles)&&1<this.files.length&&this.removeFile(this.files[0])})},error:function(e,t){e.previewElement&&(e.previewElement.classList.add("dz-error"),"string"!=typeof t&&t.error&&(t=t.error),Array.from(e.previewElement.querySelectorAll("[data-dz-errormessage]")).forEach(function(e){e.textContent=t}))}},t),a=(e.querySelector(r).innerHTML="",new window.Dropzone(e,a));a.on(u,function(){e.querySelector(i)&&e.querySelector(i).classList.remove(s),e.classList.add(l)}),a.on(m,function(){e.querySelector(i)&&e.querySelector(i).classList.remove(c),e.classList.add(s)}),a.on(g,function(){e.querySelector(i)&&e.querySelector(i).classList.remove(c),e.classList.add(s)})})}),emojiMartInit=function(){var e,n=(window.EmojiMart||{}).Picker;n&&(e=document.querySelectorAll("[data-emoji-mart]"))&&Array.from(e).forEach(function(t){var e=utils.getData(t,"emoji-mart-input-target"),a=document.querySelector(e),o=new n(window._.merge(utils.getData(t,"emoji-mart"),{previewPosition:"none",skinTonePosition:"none",onEmojiSelect:function(e){a&&(a.innerHTML+=e.native)},onClickOutside:function(e){o.contains(e.target)||t.contains(e.target)||o.classList.add("d-none")}}));o.classList.add("d-none"),t.parentElement.appendChild(o),t.addEventListener("click",function(){return o.classList.toggle("d-none")})})},defaultPredefinedRanges=[{id:"today",label:"Today",range:[new Date((new Date).setHours(0,0,0,0)),new Date]},{id:"this_month",label:"This Month",range:[new Date((new Date).getFullYear(),(new Date).getMonth(),1),new Date((new Date).getFullYear(),(new Date).getMonth()+1,0)]},{id:"last_month",label:"Last Month",range:[new Date((new Date).getFullYear(),(new Date).getMonth()-1,1),new Date((new Date).getFullYear(),(new Date).getMonth(),0)]},{id:"last_7_days",label:"Last 7 Days",range:[new Date((new Date).getTime()-6048e5),new Date]},{id:"last_30_days",label:"Last 30 Days",range:[new Date((new Date).getTime()-2592e6),new Date]}],formValidationInit=(document.querySelectorAll(".datetimepicker").forEach(function(e){function l(n,e){e=1<arguments.length&&void 0!==e?e:[],e=(Array.isArray(e)?e:n).map(function(e){var t,a,o;return"object"===_typeof(e)?(a=e,(t=(t=n).find(function(e){return e.id===Object.keys(a)[0]}))?_objectSpread(_objectSpread({},t),{},{label:a[Object.keys(a)[0]]}):a):(o=e,n.find(function(e){return e.id===o})||null)}).filter(Boolean);return'\n      <ul class="flatpickr-predefined-ranges list-group list-group-flush">\n        '.concat(e.map(function(e){var t=e.range,e=e.label;return'\n            <button type="button" \n              data-range="'.concat(t.map(function(e){return e instanceof Date?e.toISOString():e}).join(","),'" \n              class="nav-link list-group-item list-group-item-action">\n              ').concat(e,"\n            </button>\n          ")}).join(""),"\n      </ul>\n    ")}var s,t=utils.getData(e,"options");s=t,window.flatpickr(e,_objectSpread(_objectSpread({},s),{},{onOpen:function(e,t,a){var o,n,r,i=a.calendarContainer;s.predefinedRanges&&(i.classList.add("predefinedRange"),r=r=l(defaultPredefinedRanges,s.predefinedRanges),(n=i).querySelector(".flatpickr-predefined-ranges")||n.insertAdjacentHTML("afterbegin",r),o=a,_toConsumableArray(i.querySelectorAll("[data-range]")).map(function(a){return a.addEventListener("click",function(){var e=new Date(utils.getData(a,"range").split(",")[0]),t=new Date(utils.getData(a,"range").split(",")[1]);o.setDate([e,t],!0),o.redraw()})}))},onClose:function(e,t,a){s.predefinedRanges&&a.calendarContainer.classList.remove("predefinedRange")}}))}),function(){var e=document.querySelectorAll(".needs-validation");Array.prototype.slice.call(e).forEach(function(t){t.addEventListener("submit",function(e){t.checkValidity()||(e.preventDefault(),e.stopPropagation()),t.classList.add("was-validated")},!1)})}),merge=window._.merge,renderCalendar=function(e,t){var t=merge({initialView:"dayGridMonth",editable:!0,direction:document.querySelector("html").getAttribute("dir"),headerToolbar:{left:"prev,next today",center:"title",right:"dayGridMonth,timeGridWeek,timeGridDay"},buttonText:{month:"Month",week:"Week",day:"Day"}},t),a=new window.FullCalendar.Calendar(e,t);return a.render(),null!=(e=document.querySelector(".navbar-vertical-toggle"))&&e.addEventListener("navbar.vertical.toggle",function(){return a.updateSize()}),a},fullCalendarInit=function(){document.querySelectorAll("[data-calendar]").forEach(function(e){var t=utils.getData(e,"calendar");renderCalendar(e,t)})},fullCalendar={renderCalendar:renderCalendar,fullCalendarInit:fullCalendarInit},glightboxInit=function(){window.GLightbox&&window.GLightbox({selector:"[data-gallery]"})};function initMap(){var u,m=document.body,e=document.querySelectorAll(".googlemap");e.length&&window.google&&(u={Default:[{featureType:"water",elementType:"geometry",stylers:[{color:"#e9e9e9"},{lightness:17}]},{featureType:"landscape",elementType:"geometry",stylers:[{color:"#f5f5f5"},{lightness:20}]},{featureType:"road.highway",elementType:"geometry.fill",stylers:[{color:"#ffffff"},{lightness:17}]},{featureType:"road.highway",elementType:"geometry.stroke",stylers:[{color:"#ffffff"},{lightness:29},{weight:.2}]},{featureType:"road.arterial",elementType:"geometry",stylers:[{color:"#ffffff"},{lightness:18}]},{featureType:"road.local",elementType:"geometry",stylers:[{color:"#ffffff"},{lightness:16}]},{featureType:"poi",elementType:"geometry",stylers:[{color:"#f5f5f5"},{lightness:21}]},{featureType:"poi.park",elementType:"geometry",stylers:[{color:"#dedede"},{lightness:21}]},{elementType:"labels.text.stroke",stylers:[{visibility:"on"},{color:"#ffffff"},{lightness:16}]},{elementType:"labels.text.fill",stylers:[{saturation:36},{color:"#333333"},{lightness:40}]},{elementType:"labels.icon",stylers:[{visibility:"off"}]},{featureType:"transit",elementType:"geometry",stylers:[{color:"#f2f2f2"},{lightness:19}]},{featureType:"administrative",elementType:"geometry.fill",stylers:[{color:"#fefefe"},{lightness:20}]},{featureType:"administrative",elementType:"geometry.stroke",stylers:[{color:"#fefefe"},{lightness:17},{weight:1.2}]}],Gray:[{featureType:"all",elementType:"labels.text.fill",stylers:[{saturation:36},{color:"#000000"},{lightness:40}]},{featureType:"all",elementType:"labels.text.stroke",stylers:[{visibility:"on"},{color:"#000000"},{lightness:16}]},{featureType:"all",elementType:"labels.icon",stylers:[{visibility:"off"}]},{featureType:"administrative",elementType:"geometry.fill",stylers:[{color:"#000000"},{lightness:20}]},{featureType:"administrative",elementType:"geometry.stroke",stylers:[{color:"#000000"},{lightness:17},{weight:1.2}]},{featureType:"landscape",elementType:"geometry",stylers:[{color:"#000000"},{lightness:20}]},{featureType:"poi",elementType:"geometry",stylers:[{color:"#000000"},{lightness:21}]},{featureType:"road.highway",elementType:"geometry.fill",stylers:[{color:"#000000"},{lightness:17}]},{featureType:"road.highway",elementType:"geometry.stroke",stylers:[{color:"#000000"},{lightness:29},{weight:.2}]},{featureType:"road.arterial",elementType:"geometry",stylers:[{color:"#000000"},{lightness:18}]},{featureType:"road.local",elementType:"geometry",stylers:[{color:"#000000"},{lightness:16}]},{featureType:"transit",elementType:"geometry",stylers:[{color:"#000000"},{lightness:19}]},{featureType:"water",elementType:"geometry",stylers:[{color:"#000000"},{lightness:17}]}],Midnight:[{featureType:"all",elementType:"labels.text.fill",stylers:[{color:"#ffffff"}]},{featureType:"all",elementType:"labels.text.stroke",stylers:[{color:"#000000"},{lightness:13}]},{featureType:"administrative",elementType:"geometry.fill",stylers:[{color:"#000000"}]},{featureType:"administrative",elementType:"geometry.stroke",stylers:[{color:"#144b53"},{lightness:14},{weight:1.4}]},{featureType:"landscape",elementType:"all",stylers:[{color:"#08304b"}]},{featureType:"poi",elementType:"geometry",stylers:[{color:"#0c4152"},{lightness:5}]},{featureType:"road.highway",elementType:"geometry.fill",stylers:[{color:"#000000"}]},{featureType:"road.highway",elementType:"geometry.stroke",stylers:[{color:"#0b434f"},{lightness:25}]},{featureType:"road.arterial",elementType:"geometry.fill",stylers:[{color:"#000000"}]},{featureType:"road.arterial",elementType:"geometry.stroke",stylers:[{color:"#0b3d51"},{lightness:16}]},{featureType:"road.local",elementType:"geometry",stylers:[{color:"#000000"}]},{featureType:"transit",elementType:"all",stylers:[{color:"#146474"}]},{featureType:"water",elementType:"all",stylers:[{color:"#021019"}]}],Hopper:[{featureType:"water",elementType:"geometry",stylers:[{hue:"#165c64"},{saturation:34},{lightness:-69},{visibility:"on"}]},{featureType:"landscape",elementType:"geometry",stylers:[{hue:"#b7caaa"},{saturation:-14},{lightness:-18},{visibility:"on"}]},{featureType:"landscape.man_made",elementType:"all",stylers:[{hue:"#cbdac1"},{saturation:-6},{lightness:-9},{visibility:"on"}]},{featureType:"road",elementType:"geometry",stylers:[{hue:"#8d9b83"},{saturation:-89},{lightness:-12},{visibility:"on"}]},{featureType:"road.highway",elementType:"geometry",stylers:[{hue:"#d4dad0"},{saturation:-88},{lightness:54},{visibility:"simplified"}]},{featureType:"road.arterial",elementType:"geometry",stylers:[{hue:"#bdc5b6"},{saturation:-89},{lightness:-3},{visibility:"simplified"}]},{featureType:"road.local",elementType:"geometry",stylers:[{hue:"#bdc5b6"},{saturation:-89},{lightness:-26},{visibility:"on"}]},{featureType:"poi",elementType:"geometry",stylers:[{hue:"#c17118"},{saturation:61},{lightness:-45},{visibility:"on"}]},{featureType:"poi.park",elementType:"all",stylers:[{hue:"#8ba975"},{saturation:-46},{lightness:-28},{visibility:"on"}]},{featureType:"transit",elementType:"geometry",stylers:[{hue:"#a43218"},{saturation:74},{lightness:-51},{visibility:"simplified"}]},{featureType:"administrative.province",elementType:"all",stylers:[{hue:"#ffffff"},{saturation:0},{lightness:100},{visibility:"simplified"}]},{featureType:"administrative.neighborhood",elementType:"all",stylers:[{hue:"#ffffff"},{saturation:0},{lightness:100},{visibility:"off"}]},{featureType:"administrative.locality",elementType:"labels",stylers:[{hue:"#ffffff"},{saturation:0},{lightness:100},{visibility:"off"}]},{featureType:"administrative.land_parcel",elementType:"all",stylers:[{hue:"#ffffff"},{saturation:0},{lightness:100},{visibility:"off"}]},{featureType:"administrative",elementType:"all",stylers:[{hue:"#3a3935"},{saturation:5},{lightness:-57},{visibility:"off"}]},{featureType:"poi.medical",elementType:"geometry",stylers:[{hue:"#cba923"},{saturation:50},{lightness:-46},{visibility:"on"}]}],Beard:[{featureType:"poi.business",elementType:"labels.text",stylers:[{visibility:"on"},{color:"#333333"}]}],AssassianCreed:[{featureType:"all",elementType:"all",stylers:[{visibility:"on"}]},{featureType:"all",elementType:"labels",stylers:[{visibility:"off"},{saturation:"-100"}]},{featureType:"all",elementType:"labels.text.fill",stylers:[{saturation:36},{color:"#000000"},{lightness:40},{visibility:"off"}]},{featureType:"all",elementType:"labels.text.stroke",stylers:[{visibility:"off"},{color:"#000000"},{lightness:16}]},{featureType:"all",elementType:"labels.icon",stylers:[{visibility:"off"}]},{featureType:"administrative",elementType:"geometry.fill",stylers:[{color:"#000000"},{lightness:20}]},{featureType:"administrative",elementType:"geometry.stroke",stylers:[{color:"#000000"},{lightness:17},{weight:1.2}]},{featureType:"landscape",elementType:"geometry",stylers:[{color:"#000000"},{lightness:20}]},{featureType:"landscape",elementType:"geometry.fill",stylers:[{color:"#4d6059"}]},{featureType:"landscape",elementType:"geometry.stroke",stylers:[{color:"#4d6059"}]},{featureType:"landscape.natural",elementType:"geometry.fill",stylers:[{color:"#4d6059"}]},{featureType:"poi",elementType:"geometry",stylers:[{lightness:21}]},{featureType:"poi",elementType:"geometry.fill",stylers:[{color:"#4d6059"}]},{featureType:"poi",elementType:"geometry.stroke",stylers:[{color:"#4d6059"}]},{featureType:"road",elementType:"geometry",stylers:[{visibility:"on"},{color:"#7f8d89"}]},{featureType:"road",elementType:"geometry.fill",stylers:[{color:"#7f8d89"}]},{featureType:"road.highway",elementType:"geometry.fill",stylers:[{color:"#7f8d89"},{lightness:17}]},{featureType:"road.highway",elementType:"geometry.stroke",stylers:[{color:"#7f8d89"},{lightness:29},{weight:.2}]},{featureType:"road.arterial",elementType:"geometry",stylers:[{color:"#000000"},{lightness:18}]},{featureType:"road.arterial",elementType:"geometry.fill",stylers:[{color:"#7f8d89"}]},{featureType:"road.arterial",elementType:"geometry.stroke",stylers:[{color:"#7f8d89"}]},{featureType:"road.local",elementType:"geometry",stylers:[{color:"#000000"},{lightness:16}]},{featureType:"road.local",elementType:"geometry.fill",stylers:[{color:"#7f8d89"}]},{featureType:"road.local",elementType:"geometry.stroke",stylers:[{color:"#7f8d89"}]},{featureType:"transit",elementType:"geometry",stylers:[{color:"#000000"},{lightness:19}]},{featureType:"water",elementType:"all",stylers:[{color:"#2b3638"},{visibility:"on"}]},{featureType:"water",elementType:"geometry",stylers:[{color:"#2b3638"},{lightness:17}]},{featureType:"water",elementType:"geometry.fill",stylers:[{color:"#24282b"}]},{featureType:"water",elementType:"geometry.stroke",stylers:[{color:"#24282b"}]},{featureType:"water",elementType:"labels",stylers:[{visibility:"off"}]},{featureType:"water",elementType:"labels.text",stylers:[{visibility:"off "}]},{featureType:"water",elementType:"labels.text.fill",stylers:[{visibility:"off"}]},{featureType:"water",elementType:"labels.text.stroke",stylers:[{visibility:"off"}]},{featureType:"water",elementType:"labels.icon",stylers:[{visibility:"off"}]}],SubtleGray:[{featureType:"administrative",elementType:"all",stylers:[{saturation:"-100"}]},{featureType:"administrative.province",elementType:"all",stylers:[{visibility:"off"}]},{featureType:"landscape",elementType:"all",stylers:[{saturation:-100},{lightness:65},{visibility:"on"}]},{featureType:"poi",elementType:"all",stylers:[{saturation:-100},{lightness:"50"},{visibility:"simplified"}]},{featureType:"road",elementType:"all",stylers:[{saturation:-100}]},{featureType:"road.highway",elementType:"all",stylers:[{visibility:"simplified"}]},{featureType:"road.arterial",elementType:"all",stylers:[{lightness:"30"}]},{featureType:"road.local",elementType:"all",stylers:[{lightness:"40"}]},{featureType:"transit",elementType:"all",stylers:[{saturation:-100},{visibility:"simplified"}]},{featureType:"water",elementType:"geometry",stylers:[{hue:"#ffff00"},{lightness:-25},{saturation:-97}]},{featureType:"water",elementType:"labels",stylers:[{lightness:-25},{saturation:-100}]}],Tripitty:[{featureType:"all",elementType:"labels",stylers:[{visibility:"off"}]},{featureType:"administrative",elementType:"all",stylers:[{visibility:"off"}]},{featureType:"landscape",elementType:"all",stylers:[{color:"#2c5ca5"}]},{featureType:"poi",elementType:"all",stylers:[{color:"#2c5ca5"}]},{featureType:"road",elementType:"all",stylers:[{visibility:"off"}]},{featureType:"transit",elementType:"all",stylers:[{visibility:"off"}]},{featureType:"water",elementType:"all",stylers:[{color:"#193a70"},{visibility:"on"}]}],Cobalt:[{featureType:"all",elementType:"all",stylers:[{invert_lightness:!0},{saturation:10},{lightness:30},{gamma:.5},{hue:"#435158"}]}]},e.forEach(function(e){var t,a,o,n,r=utils.getData(e,"latlng").split(","),i=e.innerHTML,l=utils.getData(e,"icon")?utils.getData(e,"icon"):"https://maps.gstatic.com/mapfiles/api-3/images/spotlight-poi.png",s=utils.getData(e,"zoom"),c=e,d=utils.getData(e,"theme");return"streetview"===utils.getData(e,"theme")?(t=utils.getData(e,"pov"),t={position:{lat:Number(r[0]),lng:Number(r[1])},pov:t,zoom:s,gestureHandling:"none",scrollwheel:!1},new window.google.maps.StreetViewPanorama(c,t)):(t={zoom:s,scrollwheel:utils.getData(e,"scrollwheel"),center:new window.google.maps.LatLng(r[0],r[1]),styles:"dark"===utils.isDark()?u.Cobalt:u[d]},a=new window.google.maps.Map(c,t),o=new window.google.maps.InfoWindow({content:i}),(n=new window.google.maps.Marker({position:new window.google.maps.LatLng(r[0],r[1]),icon:l,map:a})).addListener("click",function(){o.open(a,n)}),m&&m.addEventListener("clickControl",function(e){var e=e.detail,t=e.control,e=e.value;"theme"===t&&a.set("styles","dark"===e?u.Cobalt:u[d])}),null)}))}var hideOnCollapseInit=function(){var e=document.querySelector("#previewMailForm"),t=document.querySelector("#preview-footer");e&&e.addEventListener("show.bs.collapse",function(){t.classList.add("d-none")})},iconCopiedInit=function(){var e=document.getElementById("icon-list"),t=document.getElementById("icon-copied-toast"),a=new window.bootstrap.Toast(t);e&&e.addEventListener("click",function(e){e=e.target;"INPUT"===e.tagName&&(e.select(),e.setSelectionRange(0,99999),document.execCommand("copy"),t.querySelector(".toast-body").innerHTML='<span class="fw-black">Copied:</span> <code>'.concat(e.value,"</code>"),a.show())})},inputmaskInit=function(){window.Inputmask&&document.querySelectorAll("[data-input-mask]").forEach(function(e){var t=utils.getData(e,"input-mask"),t=window._.merge({showMaskOnFocus:!1,showMaskOnHover:!1,jitMasking:!0},t);return new window.Inputmask(_objectSpread({},t)).mask(e)})},kanbanInit=function(){var o=".kanban-column",n=".kanban-items-container",e=".btn-add-card",a=".collapse",t="#addListForm",r='[data-dismiss="collapse"]',i='[data-btn-form="hide"]',l='[data-input="add-card"]',s='[data-input="add-list"]',c="form-added",d="d-none",u="click",m="show.bs.collapse",g="shown.bs.collapse",e=document.querySelectorAll(e),i=document.querySelectorAll(i),t=document.querySelector(t),r=document.querySelectorAll(r);e&&e.forEach(function(e){e.addEventListener(u,function(e){var e=e.currentTarget.closest(o),t=e.querySelector(n),a=t.scrollHeight;e.classList.add(c),t.querySelector(l).focus(),t.scrollTo({top:a})})}),i.forEach(function(e){e.addEventListener(u,function(e){e.currentTarget.closest(o).classList.remove(c)})}),t&&(t.addEventListener(m,function(e){e=e.currentTarget.nextElementSibling;e&&e.classList.add(d)}),t.addEventListener(g,function(e){e.currentTarget.querySelector(s).focus()})),r.forEach(function(e){e.addEventListener(u,function(e){var e=e.currentTarget.closest(a),t=window.bootstrap.Collapse.getInstance(e);utils.hasClass(e.nextElementSibling,d)&&e.nextElementSibling.classList.remove(d),t.hide()})})},leafletActiveUserInit=function(){var a,r,i=window.L,e=document.getElementById("map");i&&e&&(a=i.tileLayer.colorFilter("https://{s}.basemaps.cartocdn.com/light_all/{z}/{x}/{y}{r}.png",{attribution:null,transparent:!0,filter:"dark"===utils.isDark()?["invert:98%","grayscale:69%","bright:89%","contrast:111%","hue:205deg","saturate:1000%"]:["bright:101%","contrast:101%","hue:23deg","saturate:225%"]}),e=i.map("map",{center:i.latLng(10.737,0),zoom:0,layers:[a],minZoom:1.3,zoomSnap:.5,dragging:!i.Browser.mobile,tap:!i.Browser.mobile}),r=i.markerClusterGroup({chunkedLoading:!1,spiderfyOnMaxZoom:!1}),[{lat:53.958332,long:-1.080278,name:"Diana Meyer",street:"Slude Strand 27",location:"1130 Kobenhavn"},{lat:52.958332,long:-1.080278,name:"Diana Meyer",street:"Slude Strand 27",location:"1130 Kobenhavn"},{lat:51.958332,long:-1.080278,name:"Diana Meyer",street:"Slude Strand 27",location:"1130 Kobenhavn"},{lat:53.958332,long:-1.080278,name:"Diana Meyer",street:"Slude Strand 27",location:"1130 Kobenhavn"},{lat:54.958332,long:-1.080278,name:"Diana Meyer",street:"Slude Strand 27",location:"1130 Kobenhavn"},{lat:55.958332,long:-1.080278,name:"Diana Meyer",street:"Slude Strand 27",location:"1130 Kobenhavn"},{lat:53.908332,long:-1.080278,name:"Diana Meyer",street:"Slude Strand 27",location:"1130 Kobenhavn"},{lat:53.008332,long:-1.080278,name:"Diana Meyer",street:"Slude Strand 27",location:"1130 Kobenhavn"},{lat:53.158332,long:-1.080278,name:"Diana Meyer",street:"Slude Strand 27",location:"1130 Kobenhavn"},{lat:53.000032,long:-1.080278,name:"Diana Meyer",street:"Slude Strand 27",location:"1130 Kobenhavn"},{lat:52.292001,long:-2.22,name:"Anke Schroder",street:"Industrivej 54",location:"4140 Borup"},{lat:52.392001,long:-2.22,name:"Anke Schroder",street:"Industrivej 54",location:"4140 Borup"},{lat:51.492001,long:-2.22,name:"Anke Schroder",street:"Industrivej 54",location:"4140 Borup"},{lat:51.192001,long:-2.22,name:"Anke Schroder",street:"Industrivej 54",location:"4140 Borup"},{lat:52.292001,long:-2.22,name:"Anke Schroder",street:"Industrivej 54",location:"4140 Borup"},{lat:54.392001,long:-2.22,name:"Anke Schroder",street:"Industrivej 54",location:"4140 Borup"},{lat:51.292001,long:-2.22,name:"Anke Schroder",street:"Industrivej 54",location:"4140 Borup"},{lat:52.102001,long:-2.22,name:"Anke Schroder",street:"Industrivej 54",location:"4140 Borup"},{lat:52.202001,long:-2.22,name:"Anke Schroder",street:"Industrivej 54",location:"4140 Borup"},{lat:51.063202,long:-1.308,name:"Tobias Vogel",street:"Mollebakken 33",location:"3650 Olstykke"},{lat:51.363202,long:-1.308,name:"Tobias Vogel",street:"Mollebakken 33",location:"3650 Olstykke"},{lat:51.463202,long:-1.308,name:"Tobias Vogel",street:"Mollebakken 33",location:"3650 Olstykke"},{lat:51.563202,long:-1.308,name:"Tobias Vogel",street:"Mollebakken 33",location:"3650 Olstykke"},{lat:51.763202,long:-1.308,name:"Tobias Vogel",street:"Mollebakken 33",location:"3650 Olstykke"},{lat:51.863202,long:-1.308,name:"Tobias Vogel",street:"Mollebakken 33",location:"3650 Olstykke"},{lat:51.963202,long:-1.308,name:"Tobias Vogel",street:"Mollebakken 33",location:"3650 Olstykke"},{lat:51.000202,long:-1.308,name:"Tobias Vogel",street:"Mollebakken 33",location:"3650 Olstykke"},{lat:51.000202,long:-1.308,name:"Tobias Vogel",street:"Mollebakken 33",location:"3650 Olstykke"},{lat:51.163202,long:-1.308,name:"Tobias Vogel",street:"Mollebakken 33",location:"3650 Olstykke"},{lat:52.263202,long:-1.308,name:"Tobias Vogel",street:"Mollebakken 33",location:"3650 Olstykke"},{lat:53.463202,long:-1.308,name:"Tobias Vogel",street:"Mollebakken 33",location:"3650 Olstykke"},{lat:55.163202,long:-1.308,name:"Tobias Vogel",street:"Mollebakken 33",location:"3650 Olstykke"},{lat:56.263202,long:-1.308,name:"Tobias Vogel",street:"Mollebakken 33",location:"3650 Olstykke"},{lat:56.463202,long:-1.308,name:"Tobias Vogel",street:"Mollebakken 33",location:"3650 Olstykke"},{lat:56.563202,long:-1.308,name:"Tobias Vogel",street:"Mollebakken 33",location:"3650 Olstykke"},{lat:56.663202,long:-1.308,name:"Tobias Vogel",street:"Mollebakken 33",location:"3650 Olstykke"},{lat:56.763202,long:-1.308,name:"Tobias Vogel",street:"Mollebakken 33",location:"3650 Olstykke"},{lat:56.863202,long:-1.308,name:"Tobias Vogel",street:"Mollebakken 33",location:"3650 Olstykke"},{lat:56.963202,long:-1.308,name:"Tobias Vogel",street:"Mollebakken 33",location:"3650 Olstykke"},{lat:57.973202,long:-1.308,name:"Tobias Vogel",street:"Mollebakken 33",location:"3650 Olstykke"},{lat:57.163202,long:-1.308,name:"Tobias Vogel",street:"Mollebakken 33",location:"3650 Olstykke"},{lat:51.163202,long:-1.308,name:"Tobias Vogel",street:"Mollebakken 33",location:"3650 Olstykke"},{lat:51.263202,long:-1.308,name:"Tobias Vogel",street:"Mollebakken 33",location:"3650 Olstykke"},{lat:51.363202,long:-1.308,name:"Tobias Vogel",street:"Mollebakken 33",location:"3650 Olstykke"},{lat:51.409,long:-2.647,name:"Richard Hendricks",street:"37 Seafield Place",location:"London"},{lat:53.68,long:-1.49,name:"Richard Hendricks",street:"37 Seafield Place",location:"London"},{lat:50.259998,long:-5.051,name:"Richard Hendricks",street:"37 Seafield Place",location:"London"},{lat:54.906101,long:-1.38113,name:"Richard Hendricks",street:"37 Seafield Place",location:"London"},{lat:53.383331,long:-1.466667,name:"Richard Hendricks",street:"37 Seafield Place",location:"London"},{lat:53.483002,long:-2.2931,name:"Richard Hendricks",street:"37 Seafield Place",location:"London"},{lat:51.509865,long:-.118092,name:"Richard Hendricks",street:"37 Seafield Place",location:"London"},{lat:51.109865,long:-.118092,name:"Richard Hendricks",street:"37 Seafield Place",location:"London"},{lat:51.209865,long:-.118092,name:"Richard Hendricks",street:"37 Seafield Place",location:"London"},{lat:51.309865,long:-.118092,name:"Richard Hendricks",street:"37 Seafield Place",location:"London"},{lat:51.409865,long:-.118092,name:"Richard Hendricks",street:"37 Seafield Place",location:"London"},{lat:51.609865,long:-.118092,name:"Richard Hendricks",street:"37 Seafield Place",location:"London"},{lat:51.709865,long:-.118092,name:"Richard Hendricks",street:"37 Seafield Place",location:"London"},{lat:51.809865,long:-.118092,name:"Richard Hendricks",street:"37 Seafield Place",location:"London"},{lat:51.909865,long:-.118092,name:"Richard Hendricks",street:"37 Seafield Place",location:"London"},{lat:52.109865,long:-.118092,name:"Richard Hendricks",street:"37 Seafield Place",location:"London"},{lat:52.209865,long:-.118092,name:"Richard Hendricks",street:"37 Seafield Place",location:"London"},{lat:52.309865,long:-.118092,name:"Richard Hendricks",street:"37 Seafield Place",location:"London"},{lat:52.409865,long:-.118092,name:"Richard Hendricks",street:"37 Seafield Place",location:"London"},{lat:52.509865,long:-.118092,name:"Richard Hendricks",street:"37 Seafield Place",location:"London"},{lat:52.609865,long:-.118092,name:"Richard Hendricks",street:"37 Seafield Place",location:"London"},{lat:52.709865,long:-.118092,name:"Richard Hendricks",street:"37 Seafield Place",location:"London"},{lat:52.809865,long:-.118092,name:"Richard Hendricks",street:"37 Seafield Place",location:"London"},{lat:52.909865,long:-.118092,name:"Richard Hendricks",street:"37 Seafield Place",location:"London"},{lat:52.519865,long:-.118092,name:"Richard Hendricks",street:"37 Seafield Place",location:"London"},{lat:52.529865,long:-.118092,name:"Richard Hendricks",street:"37 Seafield Place",location:"London"},{lat:52.539865,long:-.118092,name:"Richard Hendricks",street:"37 Seafield Place",location:"London"},{lat:53.549865,long:-.118092,name:"Richard Hendricks",street:"37 Seafield Place",location:"London"},{lat:52.549865,long:-.118092,name:"Richard Hendricks",street:"37 Seafield Place",location:"London"},{lat:53.109865,long:-.118092,name:"Richard Hendricks",street:"37 Seafield Place",location:"London"},{lat:53.209865,long:-.118092,name:"Richard Hendricks",street:"37 Seafield Place",location:"London"},{lat:53.319865,long:-.118092,name:"Richard Hendricks",street:"37 Seafield Place",location:"London"},{lat:53.329865,long:-.118092,name:"Richard Hendricks",street:"37 Seafield Place",location:"London"},{lat:53.409865,long:-.118092,name:"Richard Hendricks",street:"37 Seafield Place",location:"London"},{lat:53.559865,long:-.118092,name:"Richard Hendricks",street:"37 Seafield Place",location:"London"},{lat:53.619865,long:-.118092,name:"Richard Hendricks",street:"37 Seafield Place",location:"London"},{lat:53.629865,long:-.118092,name:"Richard Hendricks",street:"37 Seafield Place",location:"London"},{lat:53.639865,long:-.118092,name:"Richard Hendricks",street:"37 Seafield Place",location:"London"},{lat:53.649865,long:-.118092,name:"Richard Hendricks",street:"37 Seafield Place",location:"London"},{lat:53.669865,long:-.118092,name:"Richard Hendricks",street:"37 Seafield Place",location:"London"},{lat:53.669865,long:-.118092,name:"Richard Hendricks",street:"37 Seafield Place",location:"London"},{lat:53.719865,long:-.118092,name:"Richard Hendricks",street:"37 Seafield Place",location:"London"},{lat:53.739865,long:-.118092,name:"Richard Hendricks",street:"37 Seafield Place",location:"London"},{lat:53.749865,long:-.118092,name:"Richard Hendricks",street:"37 Seafield Place",location:"London"},{lat:53.759865,long:-.118092,name:"Richard Hendricks",street:"37 Seafield Place",location:"London"},{lat:53.769865,long:-.118092,name:"Richard Hendricks",street:"37 Seafield Place",location:"London"},{lat:53.769865,long:-.118092,name:"Richard Hendricks",street:"37 Seafield Place",location:"London"},{lat:53.819865,long:-.118092,name:"Richard Hendricks",street:"37 Seafield Place",location:"London"},{lat:53.829865,long:-.118092,name:"Richard Hendricks",street:"37 Seafield Place",location:"London"},{lat:53.483959,long:-2.244644,name:"Ethel B. Brooks",street:"2576 Sun Valley Road"},{lat:40.737,long:-73.923,name:"Marshall D. Lewis",street:"1489 Michigan Avenue",location:"Michigan"},{lat:39.737,long:-73.923,name:"Marshall D. Lewis",street:"1489 Michigan Avenue",location:"Michigan"},{lat:38.737,long:-73.923,name:"Marshall D. Lewis",street:"1489 Michigan Avenue",location:"Michigan"},{lat:37.737,long:-73.923,name:"Marshall D. Lewis",street:"1489 Michigan Avenue",location:"Michigan"},{lat:40.737,long:-73.923,name:"Marshall D. Lewis",street:"1489 Michigan Avenue",location:"Michigan"},{lat:41.737,long:-73.923,name:"Marshall D. Lewis",street:"1489 Michigan Avenue",location:"Michigan"},{lat:42.737,long:-73.923,name:"Marshall D. Lewis",street:"1489 Michigan Avenue",location:"Michigan"},{lat:43.737,long:-73.923,name:"Marshall D. Lewis",street:"1489 Michigan Avenue",location:"Michigan"},{lat:44.737,long:-73.923,name:"Marshall D. Lewis",street:"1489 Michigan Avenue",location:"Michigan"},{lat:45.737,long:-73.923,name:"Marshall D. Lewis",street:"1489 Michigan Avenue",location:"Michigan"},{lat:46.7128,long:74.006,name:"Elizabeth C. Lyons",street:"4553 Kenwood Place",location:"Fort Lauderdale"},{lat:40.7128,long:74.1181,name:"Elizabeth C. Lyons",street:"4553 Kenwood Place",location:"Fort Lauderdale"},{lat:14.235,long:51.9253,name:"Ralph D. Wylie",street:"3186 Levy Court",location:"North Reading"},{lat:15.235,long:51.9253,name:"Ralph D. Wylie",street:"3186 Levy Court",location:"North Reading"},{lat:16.235,long:51.9253,name:"Ralph D. Wylie",street:"3186 Levy Court",location:"North Reading"},{lat:14.235,long:51.9253,name:"Ralph D. Wylie",street:"3186 Levy Court",location:"North Reading"},{lat:15.8267,long:47.9218,name:"Hope A. Atkins",street:"3715 Hillcrest Drive",location:"Seattle"},{lat:15.9267,long:47.9218,name:"Hope A. Atkins",street:"3715 Hillcrest Drive",location:"Seattle"},{lat:23.4425,long:58.4438,name:"Samuel R. Bailey",street:"2883 Raoul Wallenberg Place",location:"Cheshire"},{lat:23.5425,long:58.3438,name:"Samuel R. Bailey",street:"2883 Raoul Wallenberg Place",location:"Cheshire"},{lat:-37.8927369333,long:175.4087452333,name:"Samuel R. Bailey",street:"3228 Glory Road",location:"Nashville"},{lat:-38.9064188833,long:175.4441556833,name:"Samuel R. Bailey",street:"3228 Glory Road",location:"Nashville"},{lat:-12.409874,long:-65.596832,name:"Ann J. Perdue",street:"921 Ella Street",location:"Dublin"},{lat:-22.090887,long:-57.411827,name:"Jorge C. Woods",street:"4800 North Bend River Road",location:"Allen"},{lat:-19.019585,long:-65.261963,name:"Russ E. Panek",street:"4068 Hartland Avenue",location:"Appleton"},{lat:-16.500093,long:-68.214684,name:"Russ E. Panek",street:"4068 Hartland Avenue",location:"Appleton"},{lat:-17.413977,long:-66.165321,name:"Russ E. Panek",street:"4068 Hartland Avenue",location:"Appleton"},{lat:-16.489689,long:-68.119293,name:"Russ E. Panek",street:"4068 Hartland Avenue",location:"Appleton"},{lat:54.766323,long:3.08603729,name:"Russ E. Panek",street:"4068 Hartland Avenue",location:"Appleton"},{lat:54.866323,long:3.08603729,name:"Russ E. Panek",street:"4068 Hartland Avenue",location:"Appleton"},{lat:49.537685,long:3.08603729,name:"Russ E. Panek",street:"4068 Hartland Avenue",location:"Appleton"},{lat:54.715424,long:.509207,name:"Russ E. Panek",street:"4068 Hartland Avenue",location:"Appleton"},{lat:44.891666,long:10.136665,name:"Russ E. Panek",street:"4068 Hartland Avenue",location:"Appleton"},{lat:48.078335,long:14.535004,name:"Russ E. Panek",street:"4068 Hartland Avenue",location:"Appleton"},{lat:-26.358055,long:27.398056,name:"Russ E. Panek",street:"4068 Hartland Avenue",location:"Appleton"},{lat:-29.1,long:26.2167,name:"Wilbur J. Dry",street:"2043 Jadewood Drive",location:"Northbrook"},{lat:-29.883333,long:31.049999,name:"Wilbur J. Dry",street:"2043 Jadewood Drive",location:"Northbrook"},{lat:-26.266111,long:27.865833,name:"Wilbur J. Dry",street:"2043 Jadewood Drive",location:"Northbrook"},{lat:-29.087217,long:26.154898,name:"Wilbur J. Dry",street:"2043 Jadewood Drive",location:"Northbrook"},{lat:-33.958252,long:25.619022,name:"Wilbur J. Dry",street:"2043 Jadewood Drive",location:"Northbrook"},{lat:-33.977074,long:22.457581,name:"Wilbur J. Dry",street:"2043 Jadewood Drive",location:"Northbrook"},{lat:-26.563404,long:27.844164,name:"Wilbur J. Dry",street:"2043 Jadewood Drive",location:"Northbrook"},{lat:51.21389,long:-102.462776,name:"Joseph B. Poole",street:"3364 Lunetta Street",location:"Wichita Falls"},{lat:52.321945,long:-106.584167,name:"Joseph B. Poole",street:"3364 Lunetta Street",location:"Wichita Falls"},{lat:50.288055,long:-107.793892,name:"Joseph B. Poole",street:"3364 Lunetta Street",location:"Wichita Falls"},{lat:52.7575,long:-108.28611,name:"Joseph B. Poole",street:"3364 Lunetta Street",location:"Wichita Falls"},{lat:50.393333,long:-105.551941,name:"Joseph B. Poole",street:"3364 Lunetta Street",location:"Wichita Falls"},{lat:50.930557,long:-102.807777,name:"Joseph B. Poole",street:"3364 Lunetta Street",location:"Wichita Falls"},{lat:52.856388,long:-104.610001,name:"Joseph B. Poole",street:"3364 Lunetta Street",location:"Wichita Falls"},{lat:52.289722,long:-106.666664,name:"Joseph B. Poole",street:"3364 Lunetta Street",location:"Wichita Falls"},{lat:52.201942,long:-105.123055,name:"Joseph B. Poole",street:"3364 Lunetta Street",location:"Wichita Falls"},{lat:53.278046,long:-110.00547,name:"Joseph B. Poole",street:"3364 Lunetta Street",location:"Wichita Falls"},{lat:49.13673,long:-102.990959,name:"Joseph B. Poole",street:"3364 Lunetta Street",location:"Wichita Falls"},{lat:45.484531,long:-73.597023,name:"Claudette D. Nowakowski",street:"3742 Farland Avenue",location:"San Antonio"},{lat:45.266666,long:-71.900002,name:"Claudette D. Nowakowski",street:"3742 Farland Avenue",location:"San Antonio"},{lat:45.349998,long:-72.51667,name:"Claudette D. Nowakowski",street:"3742 Farland Avenue",location:"San Antonio"},{lat:47.333332,long:-79.433334,name:"Claudette D. Nowakowski",street:"3742 Farland Avenue",location:"San Antonio"},{lat:45.400002,long:-74.033333,name:"Claudette D. Nowakowski",street:"3742 Farland Avenue",location:"San Antonio"},{lat:45.683334,long:-73.433334,name:"Claudette D. Nowakowski",street:"3742 Farland Avenue",location:"San Antonio"},{lat:48.099998,long:-77.783333,name:"Claudette D. Nowakowski",street:"3742 Farland Avenue",location:"San Antonio"},{lat:45.5,long:-72.316666,name:"Claudette D. Nowakowski",street:"3742 Farland Avenue",location:"San Antonio"},{lat:46.349998,long:-72.550003,name:"Claudette D. Nowakowski",street:"3742 Farland Avenue",location:"San Antonio"},{lat:48.119999,long:-69.18,name:"Claudette D. Nowakowski",street:"3742 Farland Avenue",location:"San Antonio"},{lat:45.599998,long:-75.25,name:"Claudette D. Nowakowski",street:"3742 Farland Avenue",location:"San Antonio"},{lat:46.099998,long:-71.300003,name:"Claudette D. Nowakowski",street:"3742 Farland Avenue",location:"San Antonio"},{lat:45.700001,long:-73.633331,name:"Claudette D. Nowakowski",street:"3742 Farland Avenue",location:"San Antonio"},{lat:47.68,long:-68.879997,name:"Claudette D. Nowakowski",street:"3742 Farland Avenue",location:"San Antonio"},{lat:46.716667,long:-79.099998,name:"299"},{lat:45.016666,long:-72.099998,name:"299"}].map(function(e){var t=e.name,a=e.location,o=e.street,n=i.icon({iconUrl:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABkAAAApCAYAAADAk4LOAAAACXBIWXMAAAFgAAABYAEg2RPaAAADpElEQVRYCZ1XS1LbQBBtybIdiMEJKSpUqihgEW/xDdARyAnirOIl3MBH8NK7mBvkBpFv4Gy9IRSpFIQiRPyNfqkeZkY9HwmFt7Lm06+7p/vN2MmyDIrQ6QebALAHAD4AbFuWfQeAAACGs5H/w5jlsJJw4wMA+GhMFuMA99jIDJJOP+ihZwDQFmNuowWO1wS3viDXpdEdZPEc0odruj0EgN5s5H8tJOEEX8R3rbkMtcU34NTqhe5nSQTJ7Tkk80s6/Gk28scGiULguFBffgdufdEwWoQ0uoXo8hdAlooVH0REjISfwZSlyHGh0V5n6aHAtKTxXI5g6nQnMH0P4bEgwtR18Yw8Pj8QZ4ARUAI0Hl+fQZZGisGEBVwHr7XKzox57DXZ/ij8Cdwe2u057z9/wygOxRl4S2vSUHx1oucaMQGAHTrgtdag9mK5aN+Wx/uAAQ9Zenp/SRce4TpaNbQK4+sTcGqeTB/aIXv3XN5oj2VKqii++U0JunpZ8urxee4hvjqVc2hHpBDXuKKT9XMgVYJ1/1fPGSeaikzgmWWkMIi9bVf8UhotXxzORn5gWFchI8QyttlzjS0qpsaIGY2MMsujV/AUSdcY0dDpB6/EiOPYzclR1CI5mOez3ekHvrFLxa7cR5pTscfrXjk0Vhm5V2PqLUWnH3R5GbPGpMVD7E1ckXesKBQ7AS/vmQ1c0+kHuxpBj98lTCm8pbc5QRJRdZ6qHb/wGryXq3Lxszv+5gySuwvxueXySwYvHEjuQ9ofTGKYlrmK1EsCHMd5SoD7mZ1HHFCBHLNbMEshvrugqWLn01hpVVJhFgVGkDvK7hR6n2B+d9C7xsqWsbkqHv4cCsWezEb+o2SR+SFweUBxfA5wH7kShjKt2vWL57Px3GhIFEezkb8pxvUWHYhotAfCk2AtkEcxoOttrxUWDR5svb1emSQKj0WXK1HYIgFREbiBqmoZcB2RkbE+byMZiosorVgAZF1ID7yQhEs38wa7nUqNDezdlavC2HbBGSQkGgZ8uJVBmzeiKCRRpEa9ilWghORVeGB7BxeSKF5xqbFBkxBrFKUk/JHA7ppENQaCnCjthK+3opCEYyANztXmZN858cDYWSUSHk3A311GAZDvo6deNKUk1EsqnJoQlkYBNlmxQZeaMgmxoUokICoHDce351RCCiuKoirJWEgNOYvQplM2VCLhUqF7jf94rW9kHVUjQeheV4riv0i4ZOzzz/2y/+0KAOAfr4EE4HpCFhwAAAAASUVORK5CYII=\n        "}),e=i.marker(new i.LatLng(e.lat,e.long),{icon:n},{name:t,location:a}),n='\n        <h6 class="mb-1">'.concat(t,'</h6>\n        <p class="m-0 text-500">').concat(o,", ").concat(a,"</p>\n      "),t=i.popup({minWidth:180}).setContent(n);return e.bindPopup(t),r.addLayer(e),!0}),e.addLayer(r),document.body.addEventListener("clickControl",function(e){var e=e.detail,t=e.control,e=e.value;"theme"===t&&a.updateFilter("dark"===e?["invert:98%","grayscale:69%","bright:89%","contrast:111%","hue:205deg","saturate:1000%"]:["bright:101%","contrast:101%","hue:23deg","saturate:225%"])}))},togglePaginationButtonDisable=function(e,t){e.disabled=t,e.classList[t?"add":"remove"]("disabled")},listInit=function(){var e;window.List&&(e=document.querySelectorAll("[data-list]")).length&&e.forEach(function(a){function t(){d&&(d.innerHTML="".concat(m.i," to ").concat(v," of ").concat(g)),l&&togglePaginationButtonDisable(l,1===f),i&&togglePaginationButtonDisable(i,f===h),1<f&&f<h&&(togglePaginationButtonDisable(i,!1),togglePaginationButtonDisable(l,!1))}function e(){c.classList.toggle("d-none"),s.classList.toggle("d-none")}var o,n=a.querySelector("[data-bulk-select]"),r=utils.getData(a,"list"),i=(r.pagination&&(r=_objectSpread(_objectSpread({},r),{},{pagination:_objectSpread({item:"<li><button class='page' type='button'></button></li>"},r.pagination)})),a.querySelector('[data-list-pagination="next"]')),l=a.querySelector('[data-list-pagination="prev"]'),s=a.querySelector('[data-list-view="*"]'),c=a.querySelector('[data-list-view="less"]'),d=a.querySelector("[data-list-info]"),u=document.querySelector("[data-list-filter]"),m=new window.List(a,r),g=(m.on("updated",function(e){var t=a.querySelector(".fallback")||document.getElementById(r.fallback);t&&(0===e.matchingItems.length?t.classList.remove("d-none"):t.classList.add("d-none"))}),m.items.length),y=m.page,p=m.listContainer.querySelector(".btn-close"),h=Math.ceil(g/y),v=m.visibleItems.length,f=1;p&&p.addEventListener("search.close",function(){m.fuzzySearch("")}),t(),i&&i.addEventListener("click",function(e){e.preventDefault(),f+=1;e=m.i+y;e<=m.size()&&m.show(e,y),v+=m.visibleItems.length,t()}),l&&l.addEventListener("click",function(e){e.preventDefault(),--f,v-=m.visibleItems.length;e=m.i-y;0<e&&m.show(e,y),t()});s&&s.addEventListener("click",function(){m.show(1,g),f=h=1,v=g,t(),e()}),c&&c.addEventListener("click",function(){m.show(1,y),h=Math.ceil(g/y),f=1,v=m.visibleItems.length,t(),e()}),r.pagination&&a.querySelector(".pagination").addEventListener("click",function(e){"page"===e.target.classList[0]&&(f=Number(e.target.innerText),t())}),r.filter&&(o=r.filter.key,u.addEventListener("change",function(t){m.filter(function(e){return""===t.target.value||e.values()[o].toLowerCase().includes(t.target.value.toLowerCase())})})),n&&(window.BulkSelect.getInstance(n).attachRowNodes(m.items.map(function(e){return e.elm.querySelector("[data-bulk-select-row]")})),n.addEventListener("change",function(){m&&(n.checked?m.items.forEach(function(e){e.elm.querySelector("[data-bulk-select-row]").checked=!0}):m.items.forEach(function(e){e.elm.querySelector("[data-bulk-select-row]").checked=!1}))}))})},lottieInit=function(){var e=document.querySelectorAll(".lottie");e.length&&e.forEach(function(e){var t=utils.getData(e,"options");window.bodymovin.loadAnimation(_objectSpread({container:e,path:"../img/animated-icons/warning-light.json",renderer:"svg",loop:!0,autoplay:!0,name:"Hello World"},t))})},navbarComboInit=function(){function e(e){var t,a,o=utils.getBreakpoint(d),n=utils.getBreakpoint(u);e<n?(a=(e=u.querySelector(r)).innerHTML)&&(t=utils.getData(u,"move-target"),t=document.querySelector(t),e.innerHTML="",t.insertAdjacentHTML("afterend","\n            <div data-move-container>\n              <div class='navbar-vertical-divider'>\n                <hr class='navbar-vertical-hr' />\n              </div>\n              ".concat(a,"\n            </div>\n          ")),o<n)&&(e=document.querySelector(i).querySelector(l),utils.addClass(e,c)):(t=document.querySelector(i))&&(a=t.querySelector(l),utils.hasClass(a,c)&&a.classList.remove(c),t.querySelector(s).remove(),u.querySelector(r).innerHTML=t.innerHTML,t.remove())}var t=".navbar-vertical",a='[data-navbar-top="combo"]',r=".collapse",i="[data-move-container]",l=".navbar-nav",s=".navbar-vertical-divider",c="flex-column",d=document.querySelector(t),u=document.querySelector(a);e(window.innerWidth),utils.resize(function(){return e(window.innerWidth)})},navbarDarkenOnScroll=function(){var e="[data-navbar-darken-on-scroll]",t=".navbar-collapse",a=".navbar-toggler",o="collapsed",n="scroll",r="show.bs.collapse",i="hide.bs.collapse",l="hidden.bs.collapse",s=document.querySelector(e);function c(){s.classList.remove("bg-dark"),s.classList.remove("bg-100")}function d(e){"dark"===e?(s.classList.remove("navbar-dark"),s.classList.add("navbar-light")):(s.classList.remove("navbar-light"),s.classList.add("navbar-dark"))}var u,m,g,y,p,h,v,f;function b(e,t){var a=document.documentElement,a=_objectSpread(_objectSpread({},utils.getColors(a)),utils.getGrays(a)),e=Object.keys(a).includes(e)?e:t;return{color:a[e],bgClassName:"bg-".concat(e)}}s&&(e=utils.isDark(),u="dark"===e?"100":"dark",m=utils.getData(s,"navbar-darken-on-scroll"),d(e),document.body.addEventListener("clickControl",function(e){var e=e.detail,t=e.control,e=e.value;"theme"===t&&(d(e),u="dark"===e?"100":"dark",s.classList.contains("bg-dark")||s.classList.contains("bg-100"))&&(c(),s.classList.add(b(m,u).bgClassName))}),g=window.innerHeight,y=document.documentElement,p=s.querySelector(t),h=utils.hexToRgb(b(m,u).color),v=window.getComputedStyle(s).backgroundImage,f="background-color 0.35s ease",s.style.backgroundImage="none",window.addEventListener(n,function(){var e=y.scrollTop/g*2;1<=e&&(e=1),s.style.backgroundColor="rgba(".concat(h[0],", ").concat(h[1],", ").concat(h[2],", ").concat(e,")"),s.style.backgroundImage=0<e||utils.hasClass(p,"show")?v:"none"}),utils.resize(function(){var e=utils.getBreakpoint(s);window.innerWidth>e?(c(),s.style.backgroundImage=y.scrollTop?v:"none",s.style.transition="none"):utils.hasClass(s.querySelector(a),o)&&(c(),s.style.backgroundImage=v),window.innerWidth<=e&&(s.style.transition=utils.hasClass(p,"show")?f:"none")}),p.addEventListener(r,function(){s.classList.add(b(m,u).bgClassName),s.style.backgroundImage=v,s.style.transition=f}),p.addEventListener(i,function(){c(),y.scrollTop||(s.style.backgroundImage="none")}),p.addEventListener(l,function(){s.style.transition="none"}))},navbarTopDropShadow=function(){function e(e){0<e.scrollTop&&d?u&&u.classList.add(r):u&&u.classList.remove(r)}var t=".navbar:not(.navbar-vertical)",a=".navbar-vertical",o=".navbar-vertical-content",n="navbarVerticalCollapse",r="navbar-glass-shadow",i="show",l="scroll",s="show.bs.collapse",c="hidden.bs.collapse",d=!0,u=document.querySelector(t),t=document.querySelector(a),m=document.querySelector(o),g=document.getElementById(n),y=document.documentElement,p=utils.getBreakpoint(t);e(y),window.addEventListener(l,function(){e(y)}),m&&m.addEventListener(l,function(){window.outerWidth<p&&(d=!0,e(m))}),g&&g.addEventListener(s,function(){window.outerWidth<p&&(d=!1,e(y))}),g&&g.addEventListener(c,function(){d=!(utils.hasClass(g,i)&&window.outerWidth<p),e(y)})},handleNavbarVerticalCollapsed=function(){var e="html",t=".navbar-vertical-toggle",a=".navbar-vertical .navbar-collapse",o="click",n="mouseover",r="mouseleave",i="navbar.vertical.toggle",l="navbar-vertical-collapsed",s="navbar-vertical-collapsed-hover",c=document.querySelector(t),d=document.querySelector(e),t=document.querySelector(a);c&&c.addEventListener(o,function(e){c.blur(),d.classList.toggle(l);var t=utils.getItemFromStore("isNavbarVerticalCollapsed"),t=(utils.setItemToStore("isNavbarVerticalCollapsed",!t),new CustomEvent(i));e.currentTarget.dispatchEvent(t)}),t&&(t.addEventListener(n,function(){utils.hasClass(d,l)&&d.classList.add(s)}),t.addEventListener(r,function(){utils.hasClass(d,s)&&d.classList.remove(s)}))},nouisliderInit=function(){window.noUiSlider&&document.querySelectorAll("[data-nouislider]").forEach(function(e){var a=document.querySelector("[data-nouislider-value]"),t=utils.getData(e,"nouislider"),t=window._.merge({start:[10],connect:[!0,!1],step:1,range:{min:[0],max:[100]},tooltips:!0},t);window.noUiSlider.create(e,_objectSpread({},t)),a&&e.noUiSlider.on("update",function(e,t){a.innerHTML=e[t]})})},plyrInit=function(){window.Plyr&&document.querySelectorAll(".player").forEach(function(e){var t=utils.getData(e,"options"),t=window._.merge({captions:{active:!0}},t);return new window.Plyr(e,t)})},popoverInit=function(){[].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]')).map(function(e){return new window.bootstrap.Popover(e)})},progressAnimationToggle=function(){document.querySelectorAll("[data-progress-animation]").forEach(function(e){e.addEventListener("click",function(e){e=utils.getData(e.currentTarget,"progressAnimation");document.getElementById(e).classList.toggle("progress-bar-animated")})})},quantityInit=function(){var e="[data-quantity] [data-type]",n="[data-quantity]",r='[data-quantity] input[type="number"]',t="click",i="min",l="type";document.querySelectorAll(e).forEach(function(e){e.addEventListener(t,function(e){var e=e.currentTarget,t=utils.getData(e,l),e=e.closest(n).querySelector(r),a=e.getAttribute(i),o=parseInt(e.value,10);"plus"===t?o+=1:o=a<o?o-1:o,e.value=o})})},ratingInit=function(){document.querySelectorAll("[data-rater]").forEach(function(e){e=_objectSpread({reverse:utils.getItemFromStore("isRTL"),starSize:32,step:.5,element:e,rateCallback:function(e,t){this.setRating(e),t()}},utils.getData(e,"rater"));return window.raterJs(e)})},scrollInit=function(){var e=Array.from(document.querySelectorAll("[data-hide-on-body-scroll]"));window.innerWidth<1200&&window.addEventListener("scroll",function(){e.forEach(function(e){e=window.bootstrap.Dropdown.getInstance(e);e&&e.hide()})})},scrollbarInit=function(){Array.prototype.forEach.call(document.querySelectorAll(".scrollbar-overlay"),function(e){return new window.SimpleBar(e,{autoHide:!0})})},searchInit=function(){function n(e){var t=e.querySelector(i.SEARCH_TOGGLE),e=e.querySelector(i.DROPDOWN_MENU);t&&e&&(t.setAttribute(s.ARIA_EXPANDED,"false"),t.classList.remove(l.SHOW),e.classList.remove(l.SHOW))}function r(){e.forEach(n)}var i={SEARCH_DISMISS:'[data-bs-dismiss="search"]',DROPDOWN_TOGGLE:'[data-bs-toggle="dropdown"]',DROPDOWN_MENU:".dropdown-menu",SEARCH_BOX:".search-box",SEARCH_INPUT:".search-input",SEARCH_TOGGLE:'[data-bs-toggle="search"]'},l={SHOW:"show"},s={ARIA_EXPANDED:"aria-expanded"},c="click",d="focus",t="show.bs.dropdown",u="search.close",e=document.querySelectorAll(i.SEARCH_BOX);e.forEach(function(a){var o=a.querySelector(i.SEARCH_INPUT),e=a.querySelector(i.SEARCH_DISMISS),t=a.querySelector(i.DROPDOWN_MENU);o&&o.addEventListener(d,function(){r();var e=a.querySelector(i.SEARCH_TOGGLE);e&&t&&(e.setAttribute(s.ARIA_EXPANDED,"true"),e.classList.add(l.SHOW),t.classList.add(l.SHOW))}),document.addEventListener(c,function(e){e=e.target;a.contains(e)||n(a)}),e&&e.addEventListener(c,function(e){n(a),o.value="";var t=new CustomEvent(u);e.currentTarget.dispatchEvent(t)})}),document.querySelectorAll(i.DROPDOWN_TOGGLE).forEach(function(e){e.addEventListener(t,function(){r()})})},select2Init=function(){var o,e;window.jQuery&&(e=(o=window.jQuery)(".selectpicker")).length&&e.each(function(e,t){var t=o(t),a=o.extend({theme:"bootstrap-5"},t.data("options"));t.select2(a)})},sortableInit=function(){var a=utils.getData,e=document.querySelectorAll("[data-sortable]"),o={animation:150,group:{name:"shared"},delay:100,delayOnTouchOnly:!0,forceFallback:!0,onStart:function(){document.body.classList.add("sortable-dragging")},onEnd:function(){document.body.classList.remove("sortable-dragging")}};e.forEach(function(e){var t=a(e,"sortable"),t=window._.merge(o,t);return window.Sortable.create(e,t)})},swiperInit=function(){var e=document.querySelectorAll("[data-swiper]"),i=document.querySelector(".navbar-vertical-toggle");e.forEach(function(e){var t,a,o=utils.getData(e,"swiper"),n=o.thumb,n=(n&&(a=e.querySelectorAll("img"),t="",a.forEach(function(e){t+="\n          <div class='swiper-slide '>\n            <img class='img-fluid rounded mt-1' src=".concat(e.src," alt=''/>\n          </div>\n        ")}),(a=document.createElement("div")).setAttribute("class","swiper thumb"),a.innerHTML="<div class='swiper-wrapper'>".concat(t,"</div>"),(n.parent?document.querySelector(n.parent):e).parentNode.appendChild(a),a=new window.Swiper(a,n)),e.querySelector(".swiper-nav")),r=new window.Swiper(e,_objectSpread(_objectSpread({},o),{},{navigation:{nextEl:null==n?void 0:n.querySelector(".swiper-button-next"),prevEl:null==n?void 0:n.querySelector(".swiper-button-prev")},thumbs:{swiper:a}}));i&&i.addEventListener("navbar.vertical.toggle",function(){r.update()})})},initialDomSetup=function(e){var t,o;e&&(t=e.querySelector('[data-theme-control = "navbarPosition"]'),o=t?getData(t,"page-url"):null,e.querySelectorAll("[data-theme-control]").forEach(function(e){var t=getData(e,"theme-control"),a=getItemFromStore(t);"navbarStyle"!==t||o||"top"!==getItemFromStore("navbarPosition")&&"double-top"!==getItemFromStore("navbarPosition")||e.setAttribute("disabled",!0),"select-one"===e.type&&"navbarPosition"===t&&(e.value=a),"checkbox"===e.type?"theme"===t?("auto"===a?"dark"===getSystemTheme():"dark"===a)&&e.setAttribute("checked",!0):a&&e.setAttribute("checked",!0):"radio"===e.type?a===e.value&&e.setAttribute("checked",!0):a===e.value&&e.classList.add("active")}))},changeTheme=function(e){e.querySelectorAll('[data-theme-control = "theme"]').forEach(function(e){var t=getData(e,"theme-control"),t=getItemFromStore(t);"checkbox"===e.type?"auto"===t?"dark"===getSystemTheme()?e.checked=!0:e.checked=!1:e.checked="dark"===t:"radio"===e.type?t===e.value?e.checked=!0:e.checked=!1:t===e.value?e.classList.add("active"):e.classList.remove("active")})},handleThemeDropdownIcon=function(t){document.querySelectorAll("[data-theme-dropdown-toggle-icon]").forEach(function(e){e.classList.toggle("d-none",t!==getData(e,"theme-dropdown-toggle-icon"))})},themeControl=(handleThemeDropdownIcon(getItemFromStore("theme")),function(){var r=new DomNode(document.body),i=document.querySelector(".navbar-vertical");initialDomSetup(r.node),r.on("click",function(e){var t=new DomNode(e.target);if(t.data("theme-control")){var a=t.data("theme-control"),o=e.target["checkbox"===e.target.type?"checked":"value"];if("theme"===a&&"boolean"==typeof o&&(o=o?"dark":"light"),"navbarPosition"!==a)switch(Object.prototype.hasOwnProperty.call(CONFIG,a)&&setItemToStore(a,o),a){case"theme":document.documentElement.setAttribute("data-bs-theme","auto"===o?getSystemTheme():o);var n=new CustomEvent("clickControl",{detail:{control:a,value:o}});e.currentTarget.dispatchEvent(n),changeTheme(r.node);break;case"navbarStyle":i.classList.remove("navbar-card"),i.classList.remove("navbar-inverted"),i.classList.remove("navbar-vibrant"),"transparent"!==o&&i.classList.add("navbar-".concat(o));break;case"reset":Object.keys(CONFIG).forEach(function(e){localStorage.setItem(e,CONFIG[e])}),window.location.reload();break;default:window.location.reload()}}}),r.on("change",function(e){var t=new DomNode(e.target);"navbarPosition"===t.data("theme-control")&&(Object.prototype.hasOwnProperty.call(CONFIG,"navbarPosition")&&setItemToStore("navbarPosition",e.target.value),(e=getData(t.node.selectedOptions[0],"page-url"))?window.location.replace(e):window.location.replace(window.location.href.split("#")[0]))}),r.on("clickControl",function(e){var e=e.detail,t=e.control,e=e.value;"theme"===t&&handleThemeDropdownIcon(e)})}),tinymceInit=function(){var t,e;window.tinymce&&((t=document.querySelectorAll("[data-tinymce]")).length&&(window.tinymce.execCommand("mceFocus",!1,"course-description"),window.tinymce.init({selector:".tinymce",height:"50vh",menubar:!1,skin:utils.settings.tinymce.theme,content_style:"\n          .mce-content-body {\n            color: ".concat(utils.getColors().emphasis,";\n            background-color: ").concat(utils.getColor("tinymce-bg"),";\n          }\n        "),mobile:{theme:"mobile",toolbar:["undo","bold"]},statusbar:!1,plugins:"link,image,lists,table,media",toolbar:"styles | bold italic link bullist numlist image blockquote table media undo redo",directionality:utils.getItemFromStore("isRTL")?"rtl":"ltr",theme_advanced_toolbar_align:"center",setup:function(e){e.on("change",function(){window.tinymce.triggerSave()})}})),e=document.body)&&e.addEventListener("clickControl",function(e){"theme"===e.detail.control&&t.forEach(function(e){window.tinymce.get(e.id).dom.addStyle("\n                .mce-content-body {\n                  color: ".concat(utils.getColors().emphasis," !important;\n                  background-color: ").concat(utils.getColor("tinymce-bg")," !important;\n                }\n              "))})})},toastInit=function(){[].slice.call(document.querySelectorAll(".toast")).map(function(e){return new window.bootstrap.Toast(e)});var e,t=document.getElementById("liveToastBtn");t&&(e=new window.bootstrap.Toast(document.getElementById("liveToast")),t.addEventListener("click",function(){e&&e.show()}))},tooltipInit=function(){[].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]')).map(function(e){return new window.bootstrap.Tooltip(e,{trigger:"hover"})})},treeviewInit=function(){function i(e){Array.from(e.querySelectorAll(t)).filter(function(e){for(var t=!0;e.parentElement;){if(e.parentElement.classList.contains(f)){t=!1;break}e=e.parentElement}return t}).forEach(function(e,t){t%2==0?(e.classList.add(n),e.classList.remove(o)):(e.classList.add(o),e.classList.remove(n))})}var l="change",s="show.bs.collapse",c="hide.bs.collapse",t=".treeview > li > .treeview-row,.treeview-list.collapse-show > li > .treeview-row",e=".treeview",a=".treeview-list",d="input",u=".treeview-list-item",m=":scope > li > .collapse.collapse-show",g="treeview",y="treeview-list",p="treeview-border",h="treeview-border-transparent",v="collapse-show",f="collapse-hidden",b="treeview-row",o="treeview-row-odd",n="treeview-row-even",e=document.querySelectorAll(e);e.length&&e.forEach(function(o){var e=utils.getData(o,"options"),n=null==e?void 0:e.striped,r=null==e?void 0:e.select,e=(n&&i(o),Array.from(o.querySelectorAll(a)));Array.from(o.querySelectorAll(u)).forEach(function(e){var t=document.createElement("div");t.setAttribute("class",b),e.prepend(t)}),e.forEach(function(e){var a=e.id;if(n||e.classList.add(p),e.addEventListener(s,function(e){e.target.classList.remove(f),e.target.classList.add(v),n?i(o):e.composedPath()[2].classList.add(h)}),e.addEventListener(c,function(e){var t;e.target.classList.add(f),e.target.classList.remove(v),n?i(o):(t=e.composedPath()[2].querySelectorAll(m),e.composedPath()[2].classList.contains(g)||0!==t.length||e.composedPath()[2].classList.remove(h))}),"true"===e.dataset.show){for(var t=[e];e.parentElement;)e.parentElement.classList.contains(y)&&t.unshift(e.parentElement),e=e.parentElement;t.forEach(function(e){new window.bootstrap.Collapse(e,{show:!0})})}r&&o.querySelector("input[data-target='#".concat(a,"']")).addEventListener(l,function(t){Array.from(o.querySelector("#".concat(a)).querySelectorAll(d)).forEach(function(e){e.checked=t.target.checked})})})})},typedTextInit=function(){var e=document.querySelectorAll(".typed-text");e.length&&window.Typed&&e.forEach(function(e){return new window.Typed(e,{strings:utils.getData(e,"typedText"),typeSpeed:100,loop:!0,backDelay:1500})})},unresolvedTicketsTabInit=function(){var e=document.querySelectorAll(".dropdown-toggle-item a"),t=document.querySelector(".table-layout");e.forEach(function(e){e.addEventListener("shown.bs.tab",function(e){t.innerText=e.target.innerText})})},wizardInit=function(){var m=utils.getData,e=".theme-wizard",g="[data-wizard-step]",y="[data-wizard-form]",p="[data-wizard-password]",h="[data-wizard-confirm-password]",v=".next button",f=".previous button",b=".theme-wizard .card-footer",w="submit",S="show.bs.tab",k="shown.bs.tab",C="click";document.querySelectorAll(e).forEach(function(e){var n=e.querySelectorAll(g),r=e.querySelectorAll(y),a=e.querySelector(p),o=e.querySelector(h),t=e.querySelector(v),i=e.querySelector(f),l=e.querySelector(b),s=new Event(w,{bubbles:!0,cancelable:!0}),c=Array.from(n).map(function(e){return window.bootstrap.Tab.getOrCreateInstance(e)}),d=0,u=null;r.forEach(function(t){t.addEventListener(w,function(e){return e.preventDefault(),t.classList.contains("needs-validation")&&(a&&o&&(a.value!==o.value?o.setCustomValidity("Invalid field."):o.setCustomValidity("")),!t.checkValidity())?(u.preventDefault(),!1):(d+=1,null)})}),t.addEventListener(C,function(){d+1<c.length&&c[d+1].show()}),i.addEventListener(C,function(){c[--d].show()}),n.length&&n.forEach(function(a,o){a.addEventListener(S,function(e){var t=m(a,"wizard-step");u=e,d<t&&r[d].dispatchEvent(s)}),a.addEventListener(k,function(){(d=o)===n.length-1&&n.forEach(function(e){e.setAttribute("data-bs-toggle","modal"),e.setAttribute("data-bs-target","#error-modal")});for(var e=0;e<d;e+=1)n[e].classList.add("done"),0<e&&n[e-1].classList.add("complete");for(var t=d;t<n.length;t+=1)n[t].classList.remove("done"),0<t&&n[t-1].classList.remove("complete");d>n.length-2?l.classList.add("d-none"):l.classList.remove("d-none"),0<d&&d!==n.length-1?i.classList.remove("d-none"):i.classList.add("d-none")})})})},_window3=window,dayjs=_window3.dayjs,currentDay=dayjs&&dayjs().format("DD"),currentMonth=dayjs&&dayjs().format("MM"),prevMonth=dayjs&&dayjs().subtract(1,"month").format("MM"),nextMonth=dayjs&&dayjs().add(1,"month").format("MM"),currentYear=dayjs&&dayjs().format("YYYY"),events=[{title:"Boot Camp",start:"".concat(currentYear,"-").concat(currentMonth,"-01 10:00:00"),end:"".concat(currentYear,"-").concat(currentMonth,"-03 16:00:00"),description:"Boston Harbor Now in partnership with the Friends of Christopher Columbus Park, the Wharf District Council and the City of Boston is proud to announce the New Year's Eve Midnight Harbor Fireworks! This beloved nearly 40-year old tradition is made possible by the generous support of local waterfront organizations and businesses and the support of the City of Boston and the Office of Mayor Marty Walsh.",className:"bg-success-subtle",location:"Boston Harborwalk, Christopher Columbus Park, <br /> Boston, MA 02109, United States",organizer:"Boston Harbor Now"},{title:"Crain's New York Business",start:"".concat(currentYear,"-").concat(currentMonth,"-11"),description:"Crain's 2020 Hall of Fame. Sponsored Content By Crain's Content Studio. Crain's Content Studio Presents: New Jersey: Perfect for Business. Crain's Business Forum: Letitia James, New York State Attorney General. Crain's NYC Summit: Examining racial disparities during the pandemic",className:"bg-primary-subtle"},{title:"Conference",start:"".concat(currentYear,"-").concat(currentMonth,"-").concat(currentDay),description:"The Milken Institute Global Conference gathered the best minds in the world to tackle some of its most stubborn challenges. It was a unique experience in which individuals with the power to enact change connected with experts who are reinventing health, technology, philanthropy, industry, and media.",className:"bg-success-subtle",allDay:!0,schedules:[{title:"Reporting",start:"".concat(currentYear,"-").concat(currentMonth,"-").concat(currentDay," 11:00:00"),description:"Time to start the conference and will briefly describe all information about the event.  ",className:"event-bg-success-subtle"},{title:"Lunch",start:"".concat(currentYear,"-").concat(currentMonth,"-").concat(currentDay," 14:00:00"),description:"Lunch facility for all the attendance in the conference.",className:"event-bg-success-subtle"},{title:"Contest",start:"".concat(currentYear,"-").concat(currentMonth,"-").concat(currentDay," 16:00:00"),description:"The starting of the programming contest",className:"event-bg-success-subtle"},{title:"Dinner",start:"".concat(currentYear,"-").concat(currentMonth,"-").concat(currentDay," 22:00:00"),description:"Dinner facility for all the attendance in the conference",className:"event-bg-success-subtle"}]},{title:"ICT Expo ".concat(currentYear," - Product Release"),start:"".concat(currentYear,"-").concat(currentMonth,"-16 10:00:00"),description:"ICT Expo ".concat(currentYear," is the largest private-sector exposition aimed at showcasing IT and ITES products and services in Switzerland."),end:"".concat(currentYear,"-").concat(currentMonth,"-18 16:00:00"),className:"bg-warning-subtle"},{title:"Meeting",start:"".concat(currentYear,"-").concat(currentMonth,"-07 10:00:00"),description:"Discuss about the upcoming projects in current year and assign all tasks to the individuals"},{title:"Contest",start:"".concat(currentYear,"-").concat(currentMonth,"-14 10:00:00"),description:"PeaceX is an international peace and amity organisation that aims at casting a pall at the striking issues surmounting the development of peoples and is committed to impacting the lives of young people all over the world."},{title:"Event With Url",start:"".concat(currentYear,"-").concat(currentMonth,"-23"),description:"Sample example of a event with url. Click the event, will redirect to the given link.",className:"bg-success-subtle",url:"http://google.com"},{title:"Competition",start:"".concat(currentYear,"-").concat(currentMonth,"-26"),description:"The Future of Zambia – Top 30 Under 30 is an annual award, ranking scheme, and recognition platform for young Zambian achievers under the age of 30, who are building brands, creating jobs, changing the game, and transforming the country.",className:"bg-danger-subtle"},{title:"Birthday Party",start:"".concat(currentYear,"-").concat(nextMonth,"-05"),description:"Will celebrate birthday party with my friends and family",className:"bg-primary-subtle"},{title:"Click for Google",url:"http://google.com/",start:"".concat(currentYear,"-").concat(prevMonth,"-10"),description:"Applications are open for the New Media Writing Prize 2020. The New Media Writing Prize (NMWP) showcases exciting and inventive stories and poetry that integrate a variety of formats, platforms, and digital media.",className:"bg-primary-subtle"}],appCalendarInit=function(){function a(e){document.querySelector(r).textContent=e}var l,o=".active",e="#addEventForm",t="#addEventModal",n="#appCalendar",r=".calendar-title",i="[data-fc-view]",s="[data-event]",c="[data-view-title]",d="#eventDetailsModal",u="#eventDetailsModal .modal-content",m="#addEventModal [name='startDate']",g="[name='title']",y="click",p="shown.bs.modal",h="submit",v="event",f="fc-view",b="active",w=events.reduce(function(e,t){return t.schedules?e.concat(t.schedules.concat(t)):e.concat(t)},[]),n=document.querySelector(n),e=document.querySelector(e),S=document.querySelector(t),k=document.querySelector(d);n&&(l=renderCalendar(n,{headerToolbar:!1,dayMaxEvents:2,height:800,stickyHeaderDates:!1,views:{week:{eventLimit:3}},eventTimeFormat:{hour:"numeric",minute:"2-digit",omitZeroMinute:!0,meridiem:!0},events:w,eventClick:function(e){e.event.url?(window.open(e.event.url,"_blank"),e.jsEvent.preventDefault()):(e=getTemplate(e.event),document.querySelector(u).innerHTML=e,new window.bootstrap.Modal(k).show())},dateClick:function(e){new window.bootstrap.Modal(S).show(),document.querySelector(m)._flatpickr.setDate([e.dateStr])}}),a(l.currentData.viewTitle),document.querySelectorAll(s).forEach(function(e){e.addEventListener(y,function(e){e=e.currentTarget;switch(utils.getData(e,v)){case"prev":l.prev(),a(l.currentData.viewTitle);break;case"next":l.next(),a(l.currentData.viewTitle);break;default:l.today(),a(l.currentData.viewTitle)}})}),document.querySelectorAll(i).forEach(function(e){e.addEventListener(y,function(e){e.preventDefault();var e=e.currentTarget,t=e.textContent;e.parentElement.querySelector(o).classList.remove(b),e.classList.add(b),document.querySelector(c).textContent=t,l.changeView(utils.getData(e,f)),a(l.currentData.viewTitle)})}),e)&&e.addEventListener(h,function(e){e.preventDefault();var t=e.target,a=t.title,o=t.startDate,n=t.endDate,r=t.label,i=t.description,t=t.allDay;l.addEvent({title:a.value,start:o.value,end:n.value||null,allDay:t.checked,className:t.checked&&r.value?"bg-".concat(r.value,"-subtle"):"",description:i.value}),e.target.reset(),window.bootstrap.Modal.getInstance(S).hide()}),S&&S.addEventListener(p,function(e){e.currentTarget.querySelector(g).focus()})},managementCalendarInit=function(){var a,t,o,n,r,i,e="#addEventForm",l="#addEventModal",s="#managementAppCalendar",c="#eventDetailsModal",d="#eventDetailsModal .modal-content",u="[data-event]",m='#addEventModal [name="startDate"]',g="[data-calendar-events]",y="click",p="submit",h=[],v="event",s=document.querySelector(s);s&&(a=utils.getData(s,"calendar-option"),t=document.getElementById(null==a?void 0:a.events),e=document.querySelector(e),o=document.querySelector(l),n=document.querySelector(c),r=function(e){var t=document.getElementById(null==a?void 0:a.title);t&&(t.textContent=e)},managementEvents&&managementEvents.forEach(function(e){h.push({start:e.start,end:e.end,display:"background",classNames:"border border-2 border-".concat(e.classNames," bg-100")})}),t&&managementEvents.forEach(function(e){t.innerHTML+="\n          <li class= 'border-top pt-3 mb-3 pb-1 cursor-pointer' data-calendar-events>\n            <div class= 'border-start border-3 border-".concat(e.classNames,' ps-3 mt-1\'>\n              <h6 class="mb-1 fw-semi-bold text-700">').concat(e.title,"</h6>\n              <p class= 'fs-11 text-600 mb-0'>").concat(e.startTime||""," ").concat(e.endTime?"-":""," ").concat(e.endTime||"","</p>\n            </div>\n          </li> ")}),(l=document.querySelectorAll(g))&&l.forEach(function(e,t){e.addEventListener(y,function(){var e=managementEvents[t],e=getTemplate(e);document.querySelector(d).innerHTML=e,new window.bootstrap.Modal(n).show()})}),s)&&(i=renderCalendar(s,{headerToolbar:!1,dayMaxEvents:2,height:360,stickyHeaderDates:!1,dateClick:function(e){new window.bootstrap.Modal(o).show(),document.querySelector(m)._flatpickr.setDate([e.dateStr])},events:h}),r(i.currentData.viewTitle),c=i.currentData.currentDate.getDay(),(g=document.getElementById(null==a?void 0:a.day))&&(g.textContent=["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"][c]),document.querySelectorAll(u).forEach(function(e){e.addEventListener(y,function(e){e=e.currentTarget;switch(utils.getData(e,v)){case"prev":i.prev(),r(i.currentData.viewTitle);break;case"next":i.next(),r(i.currentData.viewTitle);break;default:i.today(),r(i.currentData.viewTitle)}})}),e)&&e.addEventListener(p,function(e){e.preventDefault(),e.target.reset(),window.bootstrap.Modal.getInstance(o).hide()})},thisDay=window.dayjs&&window.dayjs().format("DD"),plus2Day=window.dayjs&&window.dayjs().add(2,"day").format("DD"),thisMonthNumber=window.dayjs&&window.dayjs().format("MM"),thisMonthName=window.dayjs&&window.dayjs().format("MMM"),upcomingMonthNumber=window.dayjs&&window.dayjs().add(1,"month").format("MM"),upcomingMonthName=window.dayjs&&window.dayjs().format("MMM"),thisYear=window.dayjs&&window.dayjs().format("YYYY"),managementEvents=[{title:"Monthly team meeting for Falcon React Project",start:"".concat(thisYear,"-").concat(thisMonthNumber,"-07"),end:"".concat(thisYear,"-").concat(thisMonthNumber,"-09"),startTime:"07 ".concat(thisMonthName,", ").concat(thisYear),endTime:"10 ".concat(thisMonthName,", ").concat(thisYear),classNames:"primary",extendedProps:{description:"Boston Harbor Now in partnership with the Friends of Christopher Columbus Park, the Wharf District Council.",location:"Boston Harborwalk, Christopher Columbus Park, <br /> Boston, MA 02109, United States",organizer:"Boston Harbor Now"}},{title:"Newmarket Nights",start:"".concat(thisYear,"-").concat(thisMonthNumber,"-16"),end:"".concat(thisYear,"-").concat(thisMonthNumber,"-18"),startTime:"16 ".concat(thisMonthName,", ").concat(thisYear),classNames:"success",extendedProps:{description:"Boston Harbor Now in partnership with the Friends of Christopher Columbus Park, the Wharf District Council.",location:"Boston Harborwalk, Christopher Columbus Park, <br /> Boston, MA 02109, United States",organizer:"Boston Harbor Now"}},{title:"Folk Festival",start:"".concat(thisYear,"-").concat(thisMonthNumber,"-25"),end:"".concat(thisYear,"-").concat(thisMonthNumber,"-28"),startTime:"07 ".concat(thisMonthName,", ").concat(thisYear),endTime:"10 ".concat(thisMonthName,", ").concat(thisYear),classNames:"warning",extendedProps:{description:"Boston Harbor Now in partnership with the Friends of Christopher Columbus Park, the Wharf District Council.",location:"Boston Harborwalk, Christopher Columbus Park, <br /> Boston, MA 02109, United States",organizer:"Boston Harbor Now"}},{title:"Film Festival",start:"".concat(thisYear,"-").concat(upcomingMonthNumber,"-").concat(thisDay),end:"".concat(thisYear,"-").concat(upcomingMonthNumber,"-").concat(plus2Day),startTime:"07 ".concat(upcomingMonthName,", ").concat(thisYear),endTime:"10 ".concat(upcomingMonthName,", ").concat(thisYear),classNames:"danger",extendedProps:{description:"Boston Harbor Now in partnership with the Friends of Christopher Columbus Park, the Wharf District Council.",location:"Boston Harborwalk, Christopher Columbus Park, <br /> Boston, MA 02109, United States",organizer:"Boston Harbor Now"}},{title:"Meeting",start:"".concat(thisYear,"-").concat(upcomingMonthNumber,"-28"),startTime:"07 ".concat(upcomingMonthName,", ").concat(thisYear),classNames:"warning",extendedProps:{description:"Boston Harbor Now in partnership with the Friends of Christopher Columbus Park, the Wharf District Council.",location:"Boston Harborwalk, Christopher Columbus Park, <br /> Boston, MA 02109, United States",organizer:"Boston Harbor Now"}}],getStackIcon=function(e,t){return'\n  <span class="fa-stack ms-n1 me-3">\n    <i class="fas fa-circle fa-stack-2x text-200"></i>\n    <i class="'.concat(e,' fa-stack-1x text-primary" data-fa-transform=').concat(t,"></i>\n  </span>\n")},getTemplate=function(e){return'\n<div class="modal-header bg-body-tertiary ps-card pe-5 border-bottom-0">\n  <div>\n    <h5 class="modal-title mb-0">'.concat(e.title,"</h5>\n    ").concat(e.extendedProps.organizer?'<p class="mb-0 fs-10 mt-1">\n        by <a href="#!">'.concat(e.extendedProps.organizer,"</a>\n      </p>"):"",'\n  </div>\n  <button type="button" class="btn-close position-absolute end-0 top-0 mt-3 me-3" data-bs-dismiss="modal" aria-label="Close"></button>\n</div>\n<div class="modal-body px-card pb-card pt-1 fs-10">\n  ').concat(e.extendedProps.description?'\n      <div class="d-flex mt-3">\n        '.concat(getStackIcon("fas fa-align-left"),'\n        <div class="flex-1">\n          <h6>Description</h6>\n          <p class="mb-0">\n            \n          ').concat(e.extendedProps.description.split(" ").slice(0,30).join(" "),"\n          </p>\n        </div>\n      </div>\n    "):"",' \n  <div class="d-flex mt-3">\n    ').concat(getStackIcon("fas fa-calendar-check"),'\n    <div class="flex-1">\n        <h6>Date and Time</h6>\n        <p class="mb-1">\n          ').concat(window.dayjs&&window.dayjs(e.start).format("dddd, MMMM D, YYYY, h:mm A")," \n          ").concat(e.end?"– <br/>".concat(window.dayjs&&window.dayjs(e.end).subtract(1,"day").format("dddd, MMMM D, YYYY, h:mm A")):"","\n        </p>\n    </div>\n  </div>\n  ").concat(e.extendedProps.location?'\n        <div class="d-flex mt-3">\n          '.concat(getStackIcon("fas fa-map-marker-alt"),'\n          <div class="flex-1">\n              <h6>Location</h6>\n              <p class="mb-0">').concat(e.extendedProps.location,"</p>\n          </div>\n        </div>\n      "):"","\n  ").concat(e.schedules?'\n        <div class="d-flex mt-3">\n        '.concat(getStackIcon("fas fa-clock"),'\n        <div class="flex-1">\n            <h6>Schedule</h6>\n            \n            <ul class="list-unstyled timeline mb-0">\n              ').concat(e.schedules.map(function(e){return"<li>".concat(e.title,"</li>")}).join(""),"\n            </ul>\n        </div>\n      "):"",'\n  </div>\n</div>\n<div class="modal-footer d-flex justify-content-end bg-body-tertiary px-card border-top-0">\n  <a href="').concat(document.location.href.split("/").slice(0,5).join("/"),'/app/events/create-an-event.html" class="btn btn-falcon-default btn-sm">\n    <span class="fas fa-pencil-alt fs-11 mr-2"></span> Edit\n  </a>\n  <a href=\'').concat(document.location.href.split("/").slice(0,5).join("/"),'/app/events/event-detail.html\' class="btn btn-falcon-primary btn-sm">\n    See more details\n    <span class="fas fa-angle-right fs-11 ml-1"></span>\n  </a>\n</div>\n')},D3PackedBubbleInit=function(){var a,o,n,r,i,e,t,l,s;document.querySelector(".d3-packed-bubble-chart")&&(l=d3.select(".d3-packed-bubble-svg"),a=d3.select(".d3-packed-bubble-tooltip"),o=a.select(".d3-tooltip-dot"),n=a.select(".d3-tooltip-name"),r=a.select(".d3-tooltip-value"),i={backgroundColor:utils.getColor("gray-100"),tooltipNameColor:utils.getColor("gray-700"),tooltipValueColor:utils.getColor("gray-700")},s="#ffffff",e="1.8rem",t=[{name:"Blockchain",value:160,color:"#2A7BE4"},{name:"NFT",value:20,color:"#1956A6"},{name:"HTML",value:90,color:"#195099"},{name:"Crypto",value:57,color:"#2A7BE4"},{name:"Photoshop",value:117,color:"#2A7BE4"},{name:"UX",value:20,color:"#1956A6"},{name:"AWS",value:90,color:"#195099"},{name:"3D",value:33,color:"#9DBFEB"},{name:"Writing",value:117,color:"#2A7BE4"},{name:"sql",value:20,color:"#1956A6"},{name:"Blender",value:90,color:"#195099"},{name:"UI/UX",value:33,color:"#9DBFEB"},{name:"Blockchain",value:117,color:"#2A7BE4"},{name:"css",value:20,color:"#1956A6"},{name:"Marketing",value:90,color:"#195099"},{name:"Meta",value:33,color:"#9DBFEB"},{name:"js",value:12,color:"#0F67D9"},{name:"FOREX",value:66,color:"#7FA5D5"},{name:"UI",value:33,color:"#8ABBFB"},{name:"Vector",value:56,color:"#85B6F5"},{name:"CAD",value:28,color:"#6486B4"},{name:"Python",value:66,color:"#2A7BE4"},{name:"Adobe",value:66,color:"#68A0E9"},{name:"C#",value:20,color:"#385780"},{name:"Branding",value:88,color:"#74A2DE"},{name:"Bitcoin",value:80,color:"#4E7AB4"},{name:"AI",value:34,color:"#71AFFF"}],a.style("visibility","hidden"),l.attr("width","100%").attr("height","100%").attr("viewBox","-20 10 ".concat(960," ").concat(960)),t=t,t=d3.pack().size([960,960]).padding(30)(d3.hierarchy({children:t}).sum(function(e){return e.value})),t=l.selectAll().data(t.children).enter().append("g").style("cursor","pointer").style("pointer-events","all").attr("text-anchor","middle").on("mousemove",function(e){return a.style("top","".concat(e.clientY-40,"px")).style("left","".concat(e.clientX-40,"px"))}).attr("transform",function(e){return"translate(".concat(e.x,", ").concat(e.y,")")}),l=t.append("circle").style("fill",function(e){return e.data.color}).on("mouseover",function(e,t){d3.select(e.target).transition().ease(d3.easeExpInOut).duration(200).attr("r",function(e){return 1.1*e.r}),a.style("visibility","visible").style("z-index","100000").style("background-color",i.backgroundColor).style("border","1px solid ".concat(t.data.color)),o.style("background-color",t.data.color),n.text(t.data.name).style("color",i.tooltipNameColor),r.text(t.data.value).style("color",i.tooltipValueColor)}).on("mouseout",function(e){d3.select(e.target).transition().ease(d3.easeExpInOut).duration(200).attr("r",function(e){return e.r}),a.style("visibility","hidden")}),s=t.append("text").style("fill",s).style("font-size",e).style("pointer-events","none").style("opacity",0).attr("dy",".35em").text(function(e){return e.data.name}),t.transition().ease(d3.easeExpInOut).duration(1e3),l.transition().ease(d3.easeExpInOut).duration(1e3).attr("r",function(e){return e.r}),s.transition().delay(400).ease(d3.easeExpInOut).duration(2e3).style("opacity",1))},trendingKeywordsInit=function(){var a,o,n,r,i,e,t,l,s;document.querySelector(".d3-trending-keywords")&&(l=d3.select(".d3-trending-keywords-svg"),a=d3.select(".d3-trending-keywords-tooltip"),o=a.select(".d3-tooltip-dot"),n=a.select(".d3-tooltip-name"),r=a.select(".d3-tooltip-value"),i={backgroundColor:utils.getColor("gray-100"),tooltipNameColor:utils.getColor("gray-700"),tooltipValueColor:utils.getColor("gray-700")},s="#ffffff",e="1.8rem",t=[{name:"Blockchain",value:160,color:"#2A7BE4"},{name:"NFT",value:20,color:"#1956A6"},{name:"HTML",value:90,color:"#195099"},{name:"Crypto",value:57,color:"#2A7BE4"},{name:"Photoshop",value:117,color:"#2A7BE4"},{name:"UX",value:20,color:"#1956A6"},{name:"AWS",value:90,color:"#195099"},{name:"3D",value:33,color:"#9DBFEB"},{name:"Writing",value:117,color:"#2A7BE4"},{name:"sql",value:20,color:"#1956A6"},{name:"Blender",value:90,color:"#195099"},{name:"UI/UX",value:33,color:"#9DBFEB"},{name:"Blockchain",value:117,color:"#2A7BE4"},{name:"css",value:20,color:"#1956A6"},{name:"Marketing",value:90,color:"#195099"},{name:"Meta",value:33,color:"#9DBFEB"},{name:"js",value:12,color:"#0F67D9"},{name:"FOREX",value:66,color:"#7FA5D5"},{name:"UI",value:33,color:"#8ABBFB"},{name:"Vector",value:56,color:"#85B6F5"},{name:"CAD",value:28,color:"#6486B4"},{name:"Python",value:66,color:"#2A7BE4"},{name:"Adobe",value:66,color:"#68A0E9"},{name:"C#",value:20,color:"#385780"},{name:"Branding",value:88,color:"#74A2DE"},{name:"Bitcoin",value:80,color:"#4E7AB4"},{name:"AI",value:34,color:"#71AFFF"}],a.style("visibility","hidden"),l.attr("width","100%").attr("height","100%").attr("viewBox","-20 10 ".concat(960," ").concat(960)),t=t,t=d3.pack().size([960,960]).padding(30)(d3.hierarchy({children:t}).sum(function(e){return e.value})),t=l.selectAll().data(t.children).enter().append("g").style("cursor","pointer").style("pointer-events","all").attr("text-anchor","middle").on("mousemove",function(e){return a.style("top","".concat(e.clientY-40,"px")).style("left","".concat(e.clientX-40,"px"))}).attr("transform",function(e){return"translate(".concat(e.x,", ").concat(e.y,")")}),l=t.append("circle").style("fill",function(e){return e.data.color}).on("mouseover",function(e,t){d3.select(e.target).transition().ease(d3.easeExpInOut).duration(200).attr("r",function(e){return 1.1*e.r}),a.style("visibility","visible").style("z-index","100000").style("background-color",i.backgroundColor).style("border","1px solid ".concat(t.data.color)),o.style("background-color",t.data.color),n.text(t.data.name).style("color",i.tooltipNameColor),r.text(t.data.value).style("color",i.tooltipValueColor)}).on("mouseout",function(e){d3.select(e.target).transition().ease(d3.easeExpInOut).duration(200).attr("r",function(e){return e.r}),a.style("visibility","hidden")}),s=t.append("text").style("fill",s).style("font-size",e).style("pointer-events","none").style("opacity",0).attr("dy",".35em").text(function(e){return e.data.name}),t.transition().ease(d3.easeExpInOut).duration(1e3),l.transition().ease(d3.easeExpInOut).duration(1e3).attr("r",function(e){return e.r}),s.transition().delay(400).ease(d3.easeExpInOut).duration(2e3).style("opacity",1))},barChartInit=function(){var e=document.getElementById("chartjs-bar-chart");chartJsInit(e,function(){return{type:"bar",data:{labels:["Jan","Feb","Mar","Apr","May","Jun"],datasets:[{label:"# of Votes",data:[12,19,3,5,6,3],backgroundColor:[utils.getSubtleColors().secondary,utils.getSubtleColors().warning,utils.getSubtleColors().info,utils.getSubtleColors().success,utils.getSubtleColors().info,utils.getSubtleColors().primary],borderWidth:0}]},options:{plugins:{tooltip:chartJsDefaultTooltip(),legend:{labels:{color:utils.getGrays()[500]}}},scales:{x:{ticks:{color:utils.getGrays()[500]},grid:{color:utils.getGrays()[300],drawBorder:!1}},y:{ticks:{color:utils.getGrays()[500]},grid:{color:utils.getGrays()[300],drawBorder:!1}}}}}})},chartBubble=function(){var e=document.getElementById("chartjs-bubble-chart");chartJsInit(e,function(){return{type:"bubble",data:{datasets:[{label:"Dataset 1",data:getBubbleDataset(5,5,15,0,100),backgroundColor:utils.getColor("info"),hoverBackgroundColor:utils.getColor("info")},{label:"Dataset 2",data:getBubbleDataset(5,5,15,0,100),backgroundColor:utils.getColor("success"),hoverBackgroundColor:utils.getColor("success")},{label:"Dataset 3",data:getBubbleDataset(5,5,15,0,100),backgroundColor:utils.getColor("warning"),hoverBackgroundColor:utils.getColor("warning")}]},options:{plugins:{legend:{position:"top",labels:{color:utils.getGrays()[500]}},tooltip:chartJsDefaultTooltip()},scales:{x:{ticks:{color:utils.getGrays()[500]},grid:{color:utils.getGrays()[300],drawBorder:!1}},y:{ticks:{color:utils.getGrays()[500]},grid:{color:utils.getGrays()[300],drawBorder:!1}}}}}})},chartCombo=function(){var e=document.getElementById("chartjs-combo-chart");chartJsInit(e,function(){return{type:"bar",data:{labels:["January","February","March","April","May","June","July"],datasets:[{type:"line",label:"Dataset 1",borderColor:utils.getColor("primary"),borderWidth:2,fill:!1,data:[55,80,-60,-22,-50,40,90]},{type:"bar",label:"Dataset 2",backgroundColor:utils.getSubtleColors().danger,data:[4,-80,90,-22,70,35,-50],borderWidth:1},{type:"bar",label:"Dataset 3",backgroundColor:utils.getSubtleColors().primary,data:[-30,30,-18,100,-45,-25,-50],borderWidth:1}]},options:{maintainAspectRatio:!1,plugins:{tooltip:chartJsDefaultTooltip(),legend:{position:"top",labels:{color:utils.getGrays()[500]}}},scales:{x:{ticks:{color:utils.getGrays()[500]},grid:{color:utils.getGrays()[300],drawBorder:!1}},y:{ticks:{color:utils.getGrays()[500]},grid:{color:utils.getGrays()[300],drawBorder:!1}}}}}})},chartDoughnut=function(){var e=document.getElementById("chartjs-doughnut-chart");chartJsInit(e,function(){return{type:"doughnut",data:{datasets:[{data:[5,3,2,1,1],backgroundColor:[utils.rgbaColor(utils.getColor("facebook"),.75),utils.rgbaColor(utils.getColor("youtube"),.75),utils.rgbaColor(utils.getColor("twitter"),.75),utils.rgbaColor(utils.getColor("linkedin"),.75),utils.rgbaColor(utils.getColor("github"),.75)],borderWidth:1,borderColor:utils.getGrays()[100]}],labels:["Facebook","Youtube","Twitter","Linkedin","GitHub"]},options:{plugins:{tooltip:chartJsDefaultTooltip(),legend:{labels:{color:utils.getGrays()[500]}}},maintainAspectRatio:!1}}})},chartHalfDoughnutInit=function(){document.querySelectorAll("[data-half-doughnut]").forEach(function(a){a&&chartJsInit(a,function(){var e=utils.getData(a,"half-doughnut"),e=window._.merge({type:"doughnut",data:{labels:["Reached","Target"],datasets:[{data:[50,50],backgroundColor:["primary","gray-300"],borderWidth:[0,0,0,0]}]},options:{rotation:-90,circumference:"180",cutout:"80%",hover:{mode:null},plugins:{legend:{display:!1},tooltip:{enabled:!1}}}},e),t=e.data.datasets[0];return t.backgroundColor=[utils.getColor(t.backgroundColor[0]),utils.getColor(t.backgroundColor[1])],e})})},chartLine=function(){var e=document.getElementById("chartjs-line-chart");chartJsInit(e,function(){return{type:"bar",data:{labels:["January","February","March","April","May","June","July"],datasets:[{type:"line",label:"Dataset 1",borderColor:utils.getColor("primary"),borderWidth:2,fill:!1,data:[55,80,60,22,50,40,90],tension:.3}]},options:{plugins:{tooltip:chartJsDefaultTooltip(),legend:{labels:{color:utils.getGrays()[500]}}},scales:{x:{ticks:{color:utils.getGrays()[500]},grid:{color:utils.getGrays()[300],drawBorder:!1}},y:{ticks:{color:utils.getGrays()[500]},grid:{color:utils.getGrays()[300],drawBorder:!1}}}}}})},chartPie=function(){var e=document.getElementById("chartjs-pie-chart");chartJsInit(e,function(){return{type:"pie",data:{datasets:[{data:[5,3,2,1,1],backgroundColor:[utils.rgbaColor(utils.getColor("facebook"),.75),utils.rgbaColor(utils.getColor("youtube"),.75),utils.rgbaColor(utils.getColor("twitter"),.75),utils.rgbaColor(utils.getColor("linkedin"),.75),utils.rgbaColor(utils.getColor("github"),.75)],borderWidth:1,borderColor:utils.getGrays()[100]}],labels:["Facebook","Youtube","Twitter","Linkedin","GitHub"]},options:{plugins:{tooltip:chartJsDefaultTooltip(),legend:{labels:{color:utils.getGrays()[500]}}},maintainAspectRatio:!1}}})},chartPolar=function(){var e=document.getElementById("chartjs-polar-chart");chartJsInit(e,function(){return{type:"polarArea",data:{datasets:[{data:[10,20,50,40,30],backgroundColor:[utils.rgbaColor(utils.getColor("facebook"),.5),utils.rgbaColor(utils.getColor("youtube"),.5),utils.rgbaColor(utils.getColor("twitter"),.5),utils.rgbaColor(utils.getColor("linkedin"),.5),utils.rgbaColor(utils.getColor("success"),.5)],borderWidth:1,borderColor:utils.getGrays()[400]}],labels:["Facebook","Youtube","Twitter","Linkedin","Medium"]},options:{plugins:{tooltip:chartJsDefaultTooltip(),legend:{labels:{color:utils.getGrays()[500]}}},maintainAspectRatio:!1,scales:{r:{grid:{color:utils.getGrays()[300]}}}}}})},chartRadar=function(){var e=document.getElementById("chartjs-radar-chart");chartJsInit(e,function(){return{type:"radar",data:{labels:["English","Maths","Physics","Chemistry","Biology","History"],datasets:[{label:"Student A",backgroundColor:utils.rgbaColor(utils.getColor("success"),.5),data:[65,75,70,80,60,80],borderWidth:1},{label:"Student B",backgroundColor:utils.rgbaColor(utils.getColor("primary"),.5),data:[54,65,60,70,70,75],borderWidth:1}]},options:{plugins:{tooltip:chartJsDefaultTooltip(),legend:{labels:{color:utils.getGrays()[500]}}},maintainAspectRatio:!1,scales:{r:{grid:{color:utils.getGrays()[300]}}}}}})},chartScatter=function(){var e=document.getElementById("chartjs-scatter-chart");chartJsInit(e,function(){return{type:"scatter",data:{datasets:[{label:"Dataset one",data:[{x:-98,y:42},{x:-85,y:-29},{x:-87,y:-70},{x:-53,y:28},{x:-29,y:4},{x:-2,y:-42},{x:5,y:3},{x:39,y:19},{x:49,y:79},{x:83,y:-9},{x:93,y:12}],pointBackgroundColor:utils.getColor("primary"),borderColor:utils.getColor("primary"),borderWidth:1},{label:"Dataset Two",data:[{x:53,y:12},{x:-78,y:42},{x:-65,y:-39},{x:-57,y:-20},{x:57,y:28},{x:-35,y:75},{x:-29,y:-43},{x:15,y:31},{x:97,y:19},{x:49,y:69},{x:33,y:-57}],pointBackgroundColor:utils.getColor("warning"),borderColor:utils.getColor("warning"),borderWidth:1,borderRadius:"50%"}]},options:{plugins:{tooltip:chartJsDefaultTooltip(),legend:{labels:{color:utils.getGrays()[500]}}},scales:{x:{ticks:{color:utils.getGrays()[500]},grid:{color:utils.getGrays()[300],drawBorder:!1}},y:{ticks:{color:utils.getGrays()[500]},grid:{color:utils.getGrays()[300],drawBorder:!1}}},animation:{duration:2e3}}}})},chartJsInit=function(e,t){var a,o;e&&(a=e.getContext("2d"),o=new window.Chart(a,t()),document.body.addEventListener("clickControl",function(e){return"theme"===e.detail.control&&(o.destroy(),o=new window.Chart(a,t())),null}))},chartJsDefaultTooltip=function(){return{backgroundColor:utils.getGrays()[100],borderColor:utils.getGrays()[300],borderWidth:1,titleColor:utils.getColors().emphasis,callbacks:{labelTextColor:function(){return utils.getColors().emphasis}}}},getBubbleDataset=function(e,t,a,o,n){return Array.from(Array(e).keys()).map(function(){return{x:utils.getRandomNumber(o,n),y:utils.getRandomNumber(o,n),r:utils.getRandomNumber(t,a)}})},productShareDoughnutInit=function(){var e=document.getElementById("marketShareDoughnut");chartJsInit(e,function(){return{type:"doughnut",data:{labels:["Flacon","Sparrow"],datasets:[{data:[50,88],backgroundColor:[utils.getColor("primary"),utils.getColor("gray-300")],borderColor:[utils.getColor("primary"),utils.getColor("gray-300")]}]},options:{tooltips:chartJsDefaultTooltip(),rotation:-90,circumference:"180",cutout:"80%",plugins:{legend:{display:!1}}}}})},activeUsersChartReportInit=function(){var e,t,a=document.querySelector(".echart-active-users-report");a&&(e=utils.getData(a,"options"),a=window.echarts.init(a),t=function(e){return"\n      <div>\n        <p class='mb-2 text-600'>".concat(window.dayjs(e[0].axisValue).format("MMM DD, YYYY"),'</p>\n        <div class=\'ms-1\'>\n          <h6 class="fs-10 text-700"><span class="fas fa-circle text-primary me-2"></span>').concat(e[0].value,'</h6>\n          <h6 class="fs-10 text-700"><span class="fas fa-circle text-success me-2"></span>').concat(e[1].value,'</h6>\n          <h6 class="fs-10 text-700"><span class="fas fa-circle text-info me-2"></span>').concat(e[2].value,"</h6>\n        </div>\n      </div>\n      ")},echartSetOption(a,e,function(){return{color:[utils.getColor("primary"),utils.getColor("success"),utils.getColor("info")],tooltip:{trigger:"axis",padding:[7,10],backgroundColor:utils.getGrays()[100],borderColor:utils.getGrays()[300],textStyle:{color:utils.getGrays()[1100]},borderWidth:1,transitionDuration:0,position:function(e,t,a,o,n){return getPosition(e,t,a,o,n)},formatter:t},xAxis:{type:"category",data:utils.getPastDates(30).map(function(e){return window.dayjs(e).format("DD MMM, YYYY")}),boundaryGap:!1,silent:!0,axisPointer:{lineStyle:{color:utils.getGrays()[300]}},splitLine:{show:!1},axisLine:{lineStyle:{color:utils.getGrays()[300]}},axisTick:{show:!0,length:20,lineStyle:{color:utils.getGrays()[200]},interval:5},axisLabel:{color:utils.getGrays()[600],formatter:function(e){return window.dayjs(e).format("MMM DD")},align:"left",fontSize:11,padding:[0,0,0,5],interval:5}},yAxis:{type:"value",position:"right",axisPointer:{show:!1},splitLine:{lineStyle:{color:utils.getGrays()[200]}},axisLabel:{show:!0,color:utils.getGrays()[600],formatter:function(e){return"".concat(Math.round(e/1e3*10)/10,"k")}},axisTick:{show:!1},axisLine:{show:!1}},series:[{type:"line",data:[4164,4652,4817,4841,4920,5439,5486,5498,5512,5538,5841,5877,6086,6146,6199,6431,6704,7939,8127,8296,8322,8389,8411,8502,8868,8977,9273,9325,9345,9430],showSymbol:!1,symbol:"circle",itemStyle:{borderColor:utils.getColors().primary,borderWidth:2},lineStyle:{color:utils.getColor("primary")},symbolSize:2},{type:"line",data:[2164,2292,2386,2430,2528,3045,3255,3295,3481,3604,3688,3840,3932,3949,4003,4298,4424,4869,4922,4973,5155,5267,5566,5689,5692,5758,5773,5799,5960,6e3],showSymbol:!1,symbol:"circle",itemStyle:{borderColor:utils.getColors().success,borderWidth:2},lineStyle:{color:utils.getColor("success")},symbolSize:2},{type:"line",data:[1069,1089,1125,1141,1162,1179,1185,1216,1274,1322,1346,1395,1439,1564,1581,1590,1656,1815,1868,2010,2133,2179,2264,2265,2278,2343,2354,2456,2472,2480],showSymbol:!1,symbol:"circle",itemStyle:{borderColor:utils.getColors().info,borderWidth:2},lineStyle:{color:utils.getColor("info")},symbolSize:2}],grid:{right:"30px",left:"5px",bottom:"20px",top:"20px"}}}))},assignmentScoresInit=function(){var e,t,a=document.querySelector(".echart-assignment-scores");a&&(e=utils.getData(a,"options"),a=window.echarts.init(a),t=[{value:12,name:"90-100%"},{value:16,name:"70-90%"},{value:12,name:"40-70%"},{value:2,name:"0-40%"}],echartSetOption(a,e,function(){return{color:[utils.getColors().success,utils.getColors().primary,utils.getColors().info,utils.getColors().warning],tooltip:{trigger:"item",padding:[7,10],backgroundColor:utils.getGrays()[100],borderColor:utils.getGrays()[300],textStyle:{color:utils.getGrays()[1100]},borderWidth:1,transitionDuration:0,formatter:function(e){return"<strong>".concat(e.data.name,":</strong> ").concat(e.data.value," courses")}},position:function(e,t,a,o,n){return getPosition(e,t,a,o,n)},legend:{show:!1},series:[{type:"pie",radius:["85%","60%"],avoidLabelOverlap:!1,hoverAnimation:!1,itemStyle:{borderWidth:2,borderColor:utils.getColor("gray-100")},label:{normal:{show:!1,position:"center",textStyle:{fontSize:"20",fontWeight:"500",color:utils.getGrays()[700]}},emphasis:{show:!1}},labelLine:{normal:{show:!1}},data:t}]}}))},audienceChartInit=function(){function a(e){var t=(e[0].value-e[1].value)/e[1].value*100,t='\n      <div class="d-flex align-items-center ms-2">\n        <span class="fas fa-caret-'.concat(t<0?"down":"up"," text-").concat(t<0?"danger":"success",'"></span>\n        <h6 class="fs-11 mb-0 ms-1 fw-semi-bold">').concat(Math.abs(t).toFixed(2)," %</h6>\n      </div>\n    "),a=new Date(e[0].axisValue),a=new Date((new Date).setDate(a.getDate()-7));return"<div>\n          <p class='mb-0 fs-11 text-600'>".concat(window.dayjs(e[0].axisValue).format("MMM DD")," vs ").concat(window.dayjs(a).format("MMM DD"),"</p>\n          <div class=\"d-flex align-items-center\">\n            <p class='mb-0 text-600 fs-10'>\n              Users: <span class='text-800 fw-semi-bold fs-10'>").concat(e[0].data,"</span>\n            </p>\n            ").concat(t,"\n          </div>\n        </div>")}function o(e,t){return function(){return{color:utils.getGrays()[100],tooltip:{trigger:"axis",padding:[7,10],backgroundColor:utils.getGrays()[100],borderColor:utils.getGrays()[300],textStyle:{color:utils.getGrays()[1100]},borderWidth:1,transitionDuration:0,position:function(e,t,a,o,n){return getPosition(e,t,a,o,n)},axisPointer:{type:"none"},formatter:a},xAxis:{type:"category",data:r.dates,axisLabel:{color:utils.getGrays()[600],formatter:function(e){return window.dayjs(e).format("MMM DD")},align:"left",fontSize:11,padding:[0,0,0,5],showMaxLabel:!1},axisLine:{lineStyle:{color:utils.getGrays()[200]}},axisTick:{show:!0,length:20,lineStyle:{color:utils.getGrays()[200]}},boundaryGap:!1},yAxis:{position:"right",axisPointer:{type:"none"},axisTick:"none",splitLine:{lineStyle:{color:utils.getGrays()[200]}},axisLine:{show:!1},axisLabel:{color:utils.getGrays()[600]}},series:[{type:"line",data:e,showSymbol:!1,symbol:"circle",itemStyle:{borderColor:utils.getColors().primary,borderWidth:2},lineStyle:{color:utils.getColor("primary")},areaStyle:{color:{type:"linear",x:0,y:0,x2:0,y2:1,colorStops:[{offset:0,color:utils.rgbaColor(utils.getColors().primary,.2)},{offset:1,color:utils.rgbaColor(utils.getColors().primary,0)}]}}},{type:"line",data:t,symbol:"none",lineStyle:{type:"dashed",width:1,color:utils.getColor("info")}}],grid:{right:"40px",left:"5px",bottom:"10%",top:"3%"}}}}function n(e,t){var a=utils.getData(e,"options"),e=window.echarts.init(e);echartSetOption(e,a,t)}var r={dates:utils.getPastDates(7),dataset:{users:[[504,333,400,606,451,685,404],[237,229,707,575,420,536,258]],sessions:[[322,694,235,537,791,292,806],[584,661,214,286,526,707,627]],rate:[[789,749,412,697,633,254,472],[276,739,525,394,643,653,719]],duration:[[625,269,479,654,549,305,671],[499,670,550,222,696,695,469]]}},e=document.querySelector("#audience-chart-tab");e&&(n(document.querySelector(".echart-audience"),o(r.dataset.users[0],r.dataset.users[1])),Array.from(e.querySelectorAll('[data-bs-toggle="tab"]')).forEach(function(a){a.addEventListener("shown.bs.tab",function(){var e=a.href.split("#").pop(),t=document.getElementById(e).querySelector(".echart-audience");n(t,o(r.dataset[e][0],r.dataset[e][1]))})}))},avgEnrollmentRateInit=function(){var e,t,a,o,n=document.querySelector(".echart-avg-enrollment-rate");n&&(e=utils.getData(n,"options"),t=document.querySelector("#".concat(e.optionOne)),a=document.querySelector("#".concat(e.optionTwo)),o=window.echarts.init(n),echartSetOption(o,e,function(){return{color:utils.getGrays()[100],tooltip:{trigger:"axis",padding:[7,10],backgroundColor:utils.getGrays()[100],borderColor:utils.getGrays()[300],textStyle:{color:utils.getGrays()[1100]},borderWidth:1,formatter:function(e){return e.map(function(e){var t=e.seriesName,a=e.value,e=e.borderColor;return'<span class= "fas fa-circle fs-11" style="color: '.concat(e,"\"></span>\n            <span class='text-600'>\n              ").concat(t," : <strong>").concat(a,"</strong>\n            </span>")}).join("<br/>")},transitionDuration:0},legend:{show:!1},xAxis:[{type:"category",position:"bottom",data:["launch","week 1","week 2","week 3","week 4","week 5","week 6","week 7","week 8","week 9","week 10","week 11","week 12"],boundaryGap:!1,axisPointer:{lineStyle:{color:utils.getGrays()[200],type:"line"}},splitLine:{show:!1},axisLine:{lineStyle:{color:utils.getGrays()[200],type:"line"}},axisTick:{show:!1},axisLabel:{color:utils.getColor("gray-500"),formatter:function(e){return e},interval:3,margin:15,showMinLabel:!0,showMaxLabel:!1,align:"center"}},{type:"category",position:"bottom",data:["launch","week 1","week 2","week 3","week 4","week 5","week 6","week 7","week 8","week 9","week 10","week 11","week 12"],boundaryGap:!1,axisPointer:{lineStyle:{color:utils.getGrays()[200],type:"line"}},splitLine:{show:!1},axisLine:{lineStyle:{color:utils.getGrays()[200],type:"line"}},axisTick:{show:!1},axisLabel:{color:utils.getColor("gray-500"),formatter:function(e){return e},interval:200,margin:15,showMaxLabel:!0,showMinLabel:!1,align:"right"}}],yAxis:{type:"value",splitNumber:3,axisPointer:{show:!1},splitLine:{lineStyle:{color:utils.getColor("gray-200"),type:"line"}},boundaryGap:!1,axisLabel:{showMinLabel:!1,show:!0,color:utils.getColor("gray-400"),formatter:function(e){return"".concat(Math.round(e/1e3*10)/10,"k")}},axisTick:{show:!1},axisLine:{show:!1}},series:[{name:"On Sale Course",type:"line",data:[2e3,2800,2200,3001,600,600,2e3,2e3,700,1e3,200,900,1200],lineStyle:{color:utils.getColor("primary")},itemStyle:{borderColor:utils.getColor("primary"),borderWidth:2},symbol:"circle",symbolSize:10,hoverAnimation:!0},{name:"Regular Paid Course",type:"line",data:[1700,1200,500,700,1500,1100,700,1100,2600,2050,1050,600,700],lineStyle:{color:utils.getColor("warning"),type:"dashed"},itemStyle:{borderColor:utils.getColor("warning"),borderWidth:2},symbol:"circle",symbolSize:10,hoverAnimation:!0,areaStyle:{color:{type:"linear",x:0,y:0,x2:0,y2:1,colorStops:[{offset:0,color:utils.rgbaColor(utils.getColor("warning"),.4)},{offset:1,color:utils.rgbaColor(utils.getColor("warning"),0)}]}}}],grid:{right:"10px",left:"30px",bottom:"15%",top:"5%"}}}),t.addEventListener("click",function(){t.classList.toggle("opacity-50"),o.dispatchAction({type:"legendToggleSelect",name:"On Sale Course"})}),a.addEventListener("click",function(){a.classList.toggle("opacity-50"),o.dispatchAction({type:"legendToggleSelect",name:"Regular Paid Course"})}))},bandwidthSavedInit=function(){var t,a,o,n=document.querySelector(".echart-bandwidth-saved");n&&(t=utils.getData(n,"options"),a=window.echarts.init(n),o=function(){return{series:[{type:"gauge",startAngle:90,endAngle:-270,radius:"90%",pointer:{show:!1},progress:{show:!0,overlap:!1,roundCap:!0,clip:!1,itemStyle:{color:{type:"linear",x:0,y:0,x2:1,y2:0,colorStops:[{offset:0,color:"#1970e2"},{offset:1,color:"#4695ff"}]}}},axisLine:{lineStyle:{width:8,color:[[1,utils.getColor("gray-200")]]}},splitLine:{show:!1},axisTick:{show:!1},axisLabel:{show:!1},data:[{value:93,detail:{offsetCenter:["7%","4%"]}}],detail:{width:50,height:14,fontSize:28,fontWeight:500,fontFamily:"poppins",color:utils.getColor("gray-500"),formatter:"{value}%",valueAnimation:!0},animationDuration:3e3}]}},window.addEventListener("scroll",function e(){utils.isScrolledIntoView(n)&&(echartSetOption(a,t,o),window.removeEventListener("scroll",e))}))},basicEchartsInit=function(){document.querySelectorAll("[data-echarts]").forEach(function(e){var t=utils.getData(e,"echarts"),e=window.echarts.init(e);echartSetOption(e,t,function(){return{color:utils.getColors().primary,tooltip:{trigger:"item",axisPointer:{type:"none"},padding:[7,10],backgroundColor:utils.getGrays()[100],borderColor:utils.getGrays()[300],textStyle:{color:utils.getGrays()[1100]},borderWidth:1,transitionDuration:0,position:function(e,t,a,o,n){return getPosition(e,t,a,o,n)}},xAxis:{type:"category",show:!1,boundaryGap:!1},yAxis:{show:!1,type:"value",boundaryGap:!1},series:[{type:"bar",symbol:"none",areaStyle:{color:{type:"linear",x:0,y:0,x2:0,y2:1,colorStops:[{offset:0,color:utils.rgbaColor(utils.getColor("primary"),.25)},{offset:1,color:utils.rgbaColor(utils.getColor("primary"),0)}]}}}],grid:{right:"0",left:"0",bottom:"0",top:"0"}}})})},bounceRateChartInit=function(){function e(e){return"\n    <div>\n      <p class='mb-0 text-600'>".concat(window.dayjs(e[0].axisValue).format("DD, MMMM"),'</p>\n      <div class="d-flex align-items-center">\n        <p class="mb-0 text-600">\n          Rate : <span class=\'text-800\'>').concat(e[0].value,"%</span>\n        </p>\n      </div>\n    </div>\n  ")}var t,a,o=document.querySelector(".echart-bounce-rate"),n={week:[41,45,37,44,35,39,43],month:[40,37,42,44,36,39,37,43,38,35,43,39,42,36,37,36,42,44,34,41,37,41,40,40,43,34,41,35,44,41,40]};o&&(t=utils.getData(o,"options"),a=window.echarts.init(o),echartSetOption(a,t,function(){return{color:utils.getGrays()[100],title:{text:"Bounce Rate",padding:[5,0,0,0],textStyle:{color:utils.getGrays()[900],fontSize:13,fontWeight:600}},tooltip:{trigger:"axis",axisPointer:{type:"none"},padding:[7,10],backgroundColor:utils.getGrays()[100],borderColor:utils.getGrays()[300],textStyle:{color:utils.getGrays()[1100]},borderWidth:1,transitionDuration:0,position:function(e,t,a,o,n){return getPosition(e,t,a,o,n)},formatter:e},xAxis:{type:"category",data:utils.getPastDates(30).map(function(e){return window.dayjs(e).format("DD MMM, YYYY")}),axisPointer:{lineStyle:{color:utils.getGrays()[300]}},splitLine:{show:!1},axisLine:{lineStyle:{color:utils.getGrays()[400]}},axisTick:{show:!1},axisLabel:{color:utils.getGrays()[600],formatter:function(e){return window.dayjs(e).format("MMM DD")},fontSize:11}},yAxis:{type:"value",axisPointer:{show:!1},splitLine:{lineStyle:{color:utils.getGrays()[200]}},axisLabel:{show:!0,color:utils.getGrays()[600],formatter:function(e){return"".concat(e,"%")},margin:15},axisTick:{show:!1},axisLine:{show:!1}},series:[{type:"line",data:[40,37,42,44,36,39,37,43,38,35,43,39,42,36,37,36,42,44,34,41,37,41,40,40,43,34,41,35,44,41,40],showSymbol:!1,symbol:"circle",itemStyle:{borderColor:utils.getColors().primary,borderWidth:2},lineStyle:{color:utils.getColor("primary")},symbolSize:2}],grid:{right:"10px",left:"40px",bottom:"10%",top:"13%"}}}),o=document.querySelector("[data-target='.echart-bounce-rate']"))&&o.addEventListener("change",function(e){e=e.currentTarget.value;a.setOption({xAxis:{data:utils.getPastDates(e).map(function(e){return window.dayjs(e).format("DD MMM, YYYY")})},series:[{data:n[e]}]})})},browsedCoursesInit=function(){var t,e,a,o,n,r=document.querySelector(".echart-browsed-courses"),i=["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],l=function(e){return"\n    <div>\n      <p class='mb-2 text-600'>\n      ".concat(window.dayjs(e[0].axisValue).isValid()?window.dayjs(e[0].axisValue).format("MMMM YYYY"):e[0].axisValue,"\n      </p>\n      ").concat(e.map(function(e){var t=e.seriesName,a=e.value,e=e.borderColor;return'\n          <span class= "fas fa-circle fs-11" style="color: '.concat(e,"\"></span>\n          <span class='text-600'>\n            ").concat(t," : <strong>").concat(a,"</strong>\n          </span>\n        ")}).join("<br />"),"\n    </div>")};r&&(t=utils.getData(r,"options"),e=document.querySelector("#".concat(t.optionOne)),a=document.querySelector("#".concat(t.optionTwo)),o=window.echarts.init(r),n=function(){return{color:utils.getGrays()[100],legend:{data:["newCourseBrowsed","paidCourseBrowsed"],show:!1},xAxis:{type:"category",data:["2020-01-01","2020-02-01","2020-03-01","2020-04-01","2020-05-01","2020-06-01","2020-07-01","2020-08-01","2020-09-01","2020-10-01","2020-11-01","2020-12-01","2021-01-01","2021-02-01","2021-03-01","2021-04-01","2021-05-01","2021-06-01","2021-07-01","2021-08-01","2021-09-01","2021-10-01","2021-11-01","2021-12-01"],axisLine:{show:!1},splitLine:{show:!1},axisTick:{show:!1},axisLabel:{color:utils.getGrays()[600],formatter:function(e){e=new Date(e);return"".concat(i[e.getMonth()])},interval:2}},yAxis:{type:"value",show:!1},tooltip:{trigger:"axis",padding:[7,10],axisPointer:{type:"none"},backgroundColor:utils.getGrays()[100],borderColor:utils.getGrays()[300],textStyle:{color:utils.getGrays()[1100]},borderWidth:1,transitionDuration:0,formatter:function(e){return l(e)}},series:[{name:"Total",type:"bar",barWidth:"50%",z:-1,data:[600,832,901,934,1290,1330,1320,1250,1190,1345,1009,1320,600,832,901,934,1290,1330,1320,1250,1190,1345,1009,1320],itemStyle:{emphasis:{color:utils.getSubtleColors().info,barBorderRadius:[5,5,0,0],borderWidth:1,borderColor:utils.getGrays()[300]},normal:{color:utils.getSubtleColors().primary,barBorderRadius:[5,5,0,0],borderWidth:1,borderColor:utils.getGrays()[300]}}},{name:"Paid",type:"bar",barWidth:"50%",barGap:"-100%",data:[320,420,800,100,1e3,930,720,1020,800,320,450,150,320,420,800,100,1e3,930,720,1020,800,320,450,150],itemStyle:{normal:{barBorderRadius:[5,5,0,0],color:utils.getColors().primary,borderWidth:1,borderColor:utils.getColors().primary}}}],grid:{right:"0px",left:"0px",bottom:"10%",top:"15%"}}},window.addEventListener("scroll",function e(){utils.isScrolledIntoView(r)&&(echartSetOption(o,t,n),window.removeEventListener("scroll",e))}),e.addEventListener("click",function(){e.classList.toggle("opacity-50"),o.dispatchAction({type:"legendToggleSelect",name:"Total"})}),a.addEventListener("click",function(){a.classList.toggle("opacity-50"),o.dispatchAction({type:"legendToggleSelect",name:"Paid"})}))},candleChartInit=function(){var e,t,a,o,n,r,i,l,s,c,d,u,m=document.querySelector(".echart-candle-chart");m&&(e=utils.getData(m,"options"),t=window.echarts.init(m),a=document.getElementById(m.dataset.actionTarget).querySelector("[data-zoom='in']"),o=document.getElementById(m.dataset.actionTarget).querySelector("[data-zoom='out']"),n=utils.getColors().warning,r=utils.getColors().primary,d=[],u=[],[["2013/1/24",2320.26,2320.26,2287.3,2362.94],["2013/1/25",2300,2291.3,2288.26,2308.38],["2013/1/28",2295.35,2346.5,2295.35,2346.92],["2013/1/29",2347.22,2358.98,2337.35,2363.8],["2013/1/30",2360.75,2382.48,2347.89,2383.76],["2013/1/31",2383.43,2385.42,2371.23,2391.82],["2013/2/1",2377.41,2419.02,2369.57,2421.15],["2013/2/4",2425.92,2428.15,2417.58,2440.38],["2013/2/5",2411,2433.13,2403.3,2437.42],["2013/2/6",2432.68,2434.48,2427.7,2441.73],["2013/2/7",2430.69,2418.53,2394.22,2433.89],["2013/2/8",2416.62,2432.4,2414.4,2443.03],["2013/2/18",2441.91,2421.56,2415.43,2444.8],["2013/2/19",2420.26,2382.91,2373.53,2427.07],["2013/2/20",2383.49,2397.18,2370.61,2397.94],["2013/2/21",2378.82,2325.95,2309.17,2378.82],["2013/2/22",2322.94,2314.16,2308.76,2330.88],["2013/2/25",2320.62,2325.82,2315.01,2338.78],["2013/2/26",2313.74,2293.34,2289.89,2340.71],["2013/2/27",2297.77,2313.22,2292.03,2324.63],["2013/2/28",2322.32,2365.59,2308.92,2366.16],["2013/3/1",2364.54,2359.51,2330.86,2369.65],["2013/3/4",2332.08,2273.4,2259.25,2333.54],["2013/3/5",2274.81,2326.31,2270.1,2328.14],["2013/3/6",2333.61,2347.18,2321.6,2351.44],["2013/3/7",2340.44,2324.29,2304.27,2352.02],["2013/3/8",2326.42,2318.61,2314.59,2333.67],["2013/3/11",2314.68,2310.59,2296.58,2320.96],["2013/3/12",2309.16,2286.6,2264.83,2333.29],["2013/3/13",2282.17,2263.97,2253.25,2286.33],["2013/3/14",2255.77,2270.28,2253.31,2276.22],["2013/3/15",2269.31,2278.4,2250,2312.08],["2013/3/18",2267.29,2240.02,2239.21,2276.05],["2013/3/19",2244.26,2257.43,2232.02,2261.31],["2013/3/20",2257.74,2317.37,2257.42,2317.86],["2013/3/21",2318.21,2324.24,2311.6,2330.81],["2013/3/22",2321.4,2328.28,2314.97,2332],["2013/3/25",2334.74,2326.72,2319.91,2344.89],["2013/3/26",2318.58,2297.67,2281.12,2319.99],["2013/3/27",2299.38,2301.26,2289,2323.48],["2013/3/28",2273.55,2236.3,2232.91,2273.55],["2013/3/29",2238.49,2236.62,2228.81,2246.87],["2013/4/1",2229.46,2234.4,2227.31,2243.95],["2013/4/2",2234.9,2227.74,2220.44,2253.42],["2013/4/3",2232.69,2225.29,2217.25,2241.34],["2013/4/8",2196.24,2211.59,2180.67,2212.59],["2013/4/9",2215.47,2225.77,2215.47,2234.73],["2013/4/10",2224.93,2226.13,2212.56,2233.04],["2013/4/11",2236.98,2219.55,2217.26,2242.48],["2013/4/12",2218.09,2206.78,2204.44,2226.26]].forEach(function(e){d.push(e.splice(0,1)[0]),u.push(e)}),i={categoryData:d,values:u},l=0,s=70,echartSetOption(t,e,function(){return{tooltip:{trigger:"axis",padding:[7,10],backgroundColor:utils.getGrays()[100],borderColor:utils.getGrays()[300],textStyle:{color:utils.getGrays()[1100]},borderWidth:1,position:function(e,t,a,o,n){return getPosition(e,t,a,o,n)}},xAxis:{type:"category",data:i.categoryData,scale:!0,splitLine:{show:!1},splitNumber:10,min:"dataMin",max:"dataMax",boundaryGap:!0,axisPointer:{lineStyle:{color:utils.getGrays()[300],type:"dashed"}},axisLine:{lineStyle:{color:utils.getGrays()[300],type:"solid"}},axisTick:{show:!1},axisLabel:{color:utils.getGrays()[600],formatter:function(e){return new Date(e).toLocaleString("en-US",{month:"short",day:"numeric"})},margin:15,fontWeight:500}},yAxis:{scale:!0,position:"right",axisPointer:{show:!1},splitLine:{lineStyle:{color:utils.getGrays()[200],type:"dashed"}},boundaryGap:!1,axisLabel:{show:!0,color:utils.getGrays()[600],margin:15,fontWeight:500},axisTick:{show:!1},axisLine:{show:!1}},dataZoom:[{type:"inside",start:l,end:s}],series:[{name:"candlestick",type:"candlestick",data:i.values,itemStyle:{color:n,color0:r,borderColor:n,borderColor0:r}}],grid:{right:"70px",left:"20px",bottom:"15%",top:"20px"}}}),c=function(){t.dispatchAction({type:"dataZoom",start:l,end:s})},a.addEventListener("click",function(){10<s&&(s-=10),s<=10&&(a.disabled=!0),0<s&&(o.disabled=!1,c())}),o.addEventListener("click",function(){s<100&&(s+=10),100<=s&&(o.disabled=!0),0<s&&(a.disabled=!1,c())}),t.on("dataZoom",function(e){e.batch&&(l=e.batch[0].start,s=e.batch[0].end)}))},closedVsGoalInit=function(){var e,t=document.querySelector(".echart-closed-vs-goal"),a=["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"];t&&(e=utils.getData(t,"options"),t=window.echarts.init(t),echartSetOption(t,e,function(){return{color:[utils.getColors().primary,utils.getColors().warning],tooltip:{trigger:"axis",padding:[7,10],backgroundColor:utils.getGrays()[100],borderColor:utils.getGrays()[300],textStyle:{color:utils.getGrays()[1100]},borderWidth:1,formatter:tooltipFormatter,transitionDuration:0,position:function(e,t,a,o,n){return getPosition(e,t,a,o,n)}},legend:{left:"left",data:["Closed Amount","Revenue Date"],itemWidth:10,itemHeight:10,borderRadius:0,icon:"circle",inactiveColor:utils.getGrays()[400],textStyle:{color:utils.getGrays()[700]},itemGap:20},xAxis:{type:"category",name:"Closed Date",nameGap:50,nameLocation:"center",offset:0,nameTextStyle:{color:utils.getGrays()[700]},data:["2019-06-15","2019-06-22","2019-06-29","2019-07-06","2019-07-13","2019-07-20","2019-07-27","2019-07-12","2019-07-03"],boundaryGap:!1,axisPointer:{lineStyle:{color:utils.getGrays()[300],type:"dashed"}},splitLine:{show:!1},axisLine:{lineStyle:{color:utils.rgbaColor("#000",.01),type:"dashed"}},axisTick:{show:!1},axisLabel:{color:utils.getGrays()[400],formatter:function(e){e=new Date(e);return"".concat(e.getDate()," ").concat(a[e.getMonth()]," , 21")},margin:20}},yAxis:{type:"value",name:"Closed Amount",nameGap:85,nameLocation:"middle",nameTextStyle:{color:utils.getGrays()[700]},splitNumber:3,axisPointer:{show:!1},splitLine:{lineStyle:{color:utils.getGrays()[200]}},boundaryGap:!1,axisLabel:{show:!0,color:utils.getGrays()[400],formatter:function(e){return"$".concat(e)},margin:15},axisTick:{show:!1},axisLine:{show:!1}},series:[{type:"line",name:"Closed Amount",data:[0,5e3,18e3,4e4,58e3,65e3,9e4,11e4,14e4],symbolSize:5,symbol:"circle",smooth:!1,hoverAnimation:!0,lineStyle:{color:utils.rgbaColor(utils.getColor("primary"))},itemStyle:{borderColor:utils.rgbaColor(utils.getColor("primary"),.6),borderWidth:2}},{type:"line",name:"Revenue Date",data:[0,1e4,24e3,35e3,45e3,53e3,57e3,68e3,79e3],symbolSize:5,symbol:"circle",smooth:!1,hoverAnimation:!0,lineStyle:{color:utils.rgbaColor(utils.getColor("warning"))},itemStyle:{borderColor:utils.rgbaColor(utils.getColor("warning"),.6),borderWidth:2}}],grid:{right:"25px",left:"100px",bottom:"60px",top:"35px"}}}))},courseEnrollmentsInit=function(){var t,a,o,n=document.querySelector(".echart-bar-course-enrollments"),e=[["course","Free Course","Paid Course","On sale Course"],["Sun",4300,8500,5e3],["Mon",8300,7300,4500],["Tue",8600,6200,3600],["Wed",7200,5300,4500],["Thu",8e3,5e3,2600],["Fri",5e3,7e3,8800],["Sat",8e3,9e3,6e3]];n&&(t=utils.getData(n,"options"),a=window.echarts.init(n),o=function(){return{color:[utils.rgbaColor(utils.getColors().info,.6),utils.getColors().primary,utils.rgbaColor(utils.getColors().warning,.4)],dataset:{source:e},tooltip:{trigger:"item",padding:[7,10],backgroundColor:utils.getGrays()[100],borderColor:utils.getGrays().primary,textStyle:{color:utils.getGrays()[1100]},borderWidth:1,transitionDuration:0,formatter:function(e){return'<div class="fw-semibold">'.concat(e.seriesName,'</div><div class="fs-10 text-600"><strong>').concat(e.name,":</strong> ").concat(e.value[e.componentIndex+1],"</div>")}},legend:{data:["Free Course","Paid Course","On sale Course"],left:"left",itemWidth:10,itemHeight:10,borderRadius:0,icon:"circle",inactiveColor:utils.getGrays()[400],textStyle:{color:utils.getGrays()[700]}},xAxis:{type:"category",axisLabel:{color:utils.getGrays()[400]},axisLine:{lineStyle:{color:utils.getGrays()[300],type:"line"}},splitLine:{show:!0,lineStyle:{color:utils.getGrays()[200],type:"line",width:.5}},axisTick:{show:!1},boundaryGap:!0},yAxis:{axisPointer:{type:"none"},axisTick:"none",splitLine:{lineStyle:{color:utils.getGrays()[200],type:"dashed"}},axisLine:{show:!1},axisLabel:{color:utils.getGrays()[400],formatter:function(e){return"".concat(Math.round(e/1e3*10)/10,"k")}}},series:[{type:"bar",barWidth:"15%",barGap:"30%",label:{normal:{show:!1}},z:10,emphasis:{focus:"series"},itemStyle:{normal:{barBorderRadius:[2,2,0,0]}}},{type:"bar",barWidth:"15%",barGap:"30%",label:{normal:{show:!1}},z:10,emphasis:{focus:"series"},itemStyle:{normal:{barBorderRadius:[2,2,0,0]}}},{type:"bar",barWidth:"15%",barGap:"30%",label:{normal:{show:!1}},z:10,emphasis:{focus:"series"},itemStyle:{normal:{barBorderRadius:[2,2,0,0]}}}],grid:{right:"1px",left:"30px",bottom:"10%",top:"20%"}}},window.addEventListener("scroll",function e(){utils.isScrolledIntoView(n)&&(echartSetOption(a,t,o),window.removeEventListener("scroll",e))}))},courseStatusInit=function(){var t,a,o,n=document.querySelector(".echart-course-status"),e=[{value:13,name:"Completed",itemStyle:{color:utils.getColor("primary")}},{value:20,name:"On going",itemStyle:{color:utils.getColor("info")}},{value:10,name:"Droped",itemStyle:{color:utils.getColor("warning")}},{value:7,name:"Refunded",itemStyle:{color:utils.getColor("success")}}];n&&(t=utils.getData(n,"options"),a=window.echarts.init(n),o=function(){return{legend:{show:!1},series:[{type:"pie",radius:"70%",itemStyle:{borderWidth:2,borderColor:utils.getColor("gray-100")},label:{show:!1},center:["50%","50%"],data:e}],tooltip:{trigger:"item",padding:[7,10],backgroundColor:utils.getGrays()[100],borderColor:utils.getGrays()[300],textStyle:{color:utils.getGrays()[1100]},borderWidth:1,transitionDuration:0,axisPointer:{type:"none"}}}},window.addEventListener("scroll",function e(){utils.isScrolledIntoView(n)&&(echartSetOption(a,t,o),window.removeEventListener("scroll",e))}))},revenueChartInit=function(){function r(e){return'\n    <div class="card">\n      <div class="card-header bg-body-tertiary py-2">\n        <h6 class="text-600 mb-0">'.concat(e[0].axisValue,'</h6>\n      </div>\n      <div class="card-body py-2">\n        <h6 class="text-600 fw-normal">\n          <span class="fas fa-circle text-primary me-2"></span>Revenue: \n          <span class="fw-medium">$').concat(e[0].data,'</span></h6>\n        <h6 class="text-600 mb-0 fw-normal"> \n          <span class="fas fa-circle text-warning me-2"></span>Revenue Goal: \n          <span class="fw-medium">$').concat(e[1].data,"</span></h6>\n      </div>\n    </div>\n  ")}var i={dates:utils.getDates(new Date("5-6-2019"),new Date("5-6-2021"),2592e6),dataset:{revenue:[[645,500,550,550,473,405,286,601,743,450,604,815,855,722,700,896,866,952,719,558,737,885,972,650,600],[440,250,270,400,175,180,200,400,600,380,340,550,650,450,400,688,650,721,500,300,445,680,568,400,371]],users:[[545,500,650,727,773,705,686,501,643,580,604,615,755,722,727,816,836,952,719,758,937,785,872,850,800],[340,360,230,250,410,430,450,200,220,540,500,250,355,320,500,630,680,500,520,550,750,720,700,780,750]],deals:[[545,400,450,627,473,450,460,780,770,800,504,550,500,530,727,716,736,820,719,758,737,885,872,850,800],[245,300,450,427,273,250,260,580,570,500,402,450,400,330,527,516,536,620,519,558,537,483,472,250,300]],profit:[[545,400,450,627,673,605,686,501,843,518,504,715,955,622,627,716,736,952,619,558,937,785,872,550,400],[340,360,330,300,410,380,450,400,420,240,200,250,355,320,500,630,680,400,420,450,650,620,700,450,340]]}};["revenue","users","deals","profit"].forEach(function(e){var t,a,o,n=document.querySelector(".echart-crm-".concat(e));n&&(n=n,a=i.dataset[e][0],o=i.dataset[e][1],e=function(){return{color:utils.getColors().white,tooltip:{trigger:"axis",padding:0,backgroundColor:"transparent",borderWidth:0,transitionDuration:0,position:function(e,t,a,o,n){return getPosition(e,t,a,o,n)},axisPointer:{type:"none"},formatter:r},xAxis:{type:"category",data:utils.getPastDates(25).map(function(e){return window.dayjs(e).format("DD MMM, YYYY")}),axisLabel:{color:utils.getGrays()[600],formatter:function(e){return window.dayjs(e).format("MMM DD")},align:"left",fontSize:11,padding:[0,0,0,5],showMaxLabel:!1},axisLine:{show:!1},axisTick:{show:!1},boundaryGap:!0},yAxis:{position:"right",axisPointer:{type:"none"},axisTick:"none",splitLine:{show:!1},axisLine:{show:!1},axisLabel:{show:!1}},series:[{type:"bar",name:"Revenue",data:a,lineStyle:{color:utils.getColor("primary")},itemStyle:{barBorderRadius:[4,4,0,0],color:utils.getGrays()[100],borderColor:utils.getGrays()[300],borderWidth:1},emphasis:{itemStyle:{color:utils.getColor("primary")}}},{type:"line",name:"Revenue Goal",data:o,symbol:"circle",symbolSize:6,animation:!1,itemStyle:{color:utils.getColor("warning")},lineStyle:{type:"dashed",width:2,color:utils.getColor("warning")}}],grid:{right:5,left:5,bottom:"8%",top:"5%"}}},t=utils.getData(n,"options"),n=window.echarts.init(n),echartSetOption(n,t,e))})},echartsCustomerSatisfactionInit=function(){var e,t=document.querySelector(".echart-customer-setisfaction");t&&(e=utils.getData(t,"options"),t=window.echarts.init(t),echartSetOption(t,e,function(){return{legend:{left:"center",bottom:22,itemWidth:12,itemHeight:12,borderRadius:0,icon:"circle",inactiveColor:utils.getGrays()[400],inactiveBorderColor:"transparent",textStyle:{color:utils.getGrays()[600],fontSize:12,fontFamily:"Poppins",fontWeight:"500"},itemGap:16},series:[{type:"pie",radius:"70%",label:{show:!1},center:["50%","45%"],itemStyle:{borderWidth:2,borderColor:"dark"===utils.isDark()?"#121E2D":utils.getGrays()[100]},data:[{value:1100,name:"Positive",itemStyle:{color:utils.getColor("primary")}},{value:550,name:"Nagative",itemStyle:{color:utils.rgbaColor(utils.getColor("primary"),.5)}}]}],tooltip:{trigger:"item",padding:[7,10],backgroundColor:utils.getGrays()[100],borderColor:utils.getGrays()[300],textStyle:{color:utils.getGrays()[1100]},borderWidth:1,transitionDuration:0,axisPointer:{type:"none"}}}}))},dealStorageFunnelInit=function(){var e,t,a,o,n=document.querySelector(".echart-deal-storage-funnel");n&&(e=utils.getData(n,"options"),t=e.data,a=e.dataAxis1,o=e.dataAxis2,n=window.echarts.init(n),echartSetOption(n,e,function(){return{yAxis:[{data:a,axisLabel:{inside:!0,textStyle:{color:utils.getGrays()[700],fontWeight:500,fontSize:11,fontFamily:"poppins"}},axisTick:{show:!1},axisLine:{show:!1},z:10},{data:o,axisLabel:{inside:!1,textStyle:{color:utils.getColors().primary,fontWeight:500,fontSize:11,fontFamily:"poppins"},borderRadius:5,backgroundColor:utils.getSubtleColors().primary,padding:[6,16,6,16],width:115},axisTick:{show:!1},axisLine:{show:!1},z:10}],xAxis:{type:"value",min:0,max:35,axisLine:{show:!1},splitLine:{show:!1},inverse:!0,axisTick:{show:!1},axisLabel:{show:!1}},series:[{type:"bar",showBackground:!0,barWidth:25,label:{show:!0,formatter:"{c} ",position:"insideLeft"},backgroundStyle:{color:utils.getGrays()[200],borderRadius:5},itemStyle:{color:utils.getColors().primary,borderRadius:5},data:t}],grid:{right:"65px",left:"0",bottom:"0",top:"0"}}}))},echartsDistributionOfPerformanceInit=function(){var e,t,a,o,n,r=document.querySelector(".echart-distribution-of-performance");r&&(e=utils.getData(r,"options"),r=window.echarts.init(r),t=["Mar 01","Mar 02","Mar 03","Mar 04","Mar 05","Mar 06","Mar 07","Mar 08","Mar 09","Mar 10","Mar 11","Mar 12"],a=[50,25,35,30,45,35,38,30,35,30,35,38],o=[45,50,40,35,50,40,44,35,40,45,40,44],n={itemStyle:{shadowColor:utils.rgbaColor(utils.getColor("dark"),.3)}},echartSetOption(r,e,function(){return{color:[utils.getColor("primary"),"dark"===utils.isDark()?"#236EA1":"#7DD7FE"],legend:{data:["Agent Support","Group Support"],icon:"circle",itemWidth:10,itemHeight:10,padding:[0,0,0,0],textStyle:{color:utils.getGrays()[700],fontWeight:"500",fontSize:"13px"},left:0,itemGap:16},tooltip:{trigger:"item",padding:[7,10],backgroundColor:utils.getGrays()[100],borderColor:utils.getGrays()[300],textStyle:{color:utils.getGrays()[900]},borderWidth:1,transitionDuration:0,axisPointer:{type:"none"}},xAxis:{data:t,splitLine:{show:!1},splitArea:{show:!1},axisLabel:{color:utils.getGrays()[600]},axisLine:{lineStyle:{color:utils.getGrays()[300]}},axisTick:{show:!1}},yAxis:{splitLine:{lineStyle:{color:utils.getGrays()[300],type:"dashed"}},axisLabel:{color:utils.getGrays()[600]}},series:[{name:"Agent Support",type:"bar",stack:"one",emphasis:n,data:a},{name:"Group Support",type:"bar",stack:"one",emphasis:n,data:o,itemStyle:{barBorderRadius:[3,3,0,0]}}],barWidth:"15px",grid:{top:"15%",bottom:0,left:0,right:0,containLabel:!0}}}))},getPosition=function(e,t,a,o,n){return{top:e[1]-n.contentSize[1]-10,left:e[0]-n.contentSize[0]/2}},echartSetOption=function(t,a,o){var e=document.body;t.setOption(window._.merge(o(),a)),e.addEventListener("clickControl",function(e){"theme"===e.detail.control&&t.setOption(window._.merge(o(),a))})},tooltipFormatter=function(e){var t="";return e.forEach(function(e){t+='<div class=\'ms-1\'>\n        <h6 class="text-700">\n          <span class="fas fa-circle me-1 fs-11" style="color:'.concat(e.borderColor||e.color,'"></span>\n          ').concat(e.seriesName," : ").concat("object"===_typeof(e.value)?e.value[1]:e.value,"\n        </h6>\n      </div>")}),"\n    <div>\n      <p class='mb-2 text-600'>\n        ".concat(window.dayjs(e[0].axisValue).isValid()?window.dayjs(e[0].axisValue).format("MMMM DD"):e[0].axisValue,"\n      </p>\n      ").concat(t,"\n    </div>")},resizeEcharts=function(){var e=document.querySelectorAll("[data-echart-responsive]");e.length&&e.forEach(function(e){!utils.getData(e,"echart-responsive")||e.closest(".tab-pane")&&"none"===window.getComputedStyle(e.closest(".tab-pane")).display||window.echarts.init(e).resize()})},navbarVerticalToggle=(utils.resize(function(){return resizeEcharts()}),document.querySelector(".navbar-vertical-toggle")),echartTabs=(navbarVerticalToggle&&navbarVerticalToggle.addEventListener("navbar.vertical.toggle",function(){return resizeEcharts()}),document.querySelectorAll("[data-tab-has-echarts]")),grossRevenueChartInit=(echartTabs&&echartTabs.forEach(function(e){e.addEventListener("shown.bs.tab",function(e){e=e.target,e=e.hash||e.dataset.bsTarget,e=document.getElementById(e.substring(1)),e=null==e?void 0:e.querySelector("[data-echart-tab]");e&&window.echarts.init(e).resize()})}),function(){var e,t,a,o,n,r,i,l,s,c,d,u,m,g=document.querySelector(".echart-gross-revenue-chart"),y=["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"];g&&(e=utils.getData(g,"options"),t=window.echarts.init(g),g="#".concat(e.monthSelect),a=e.target,c="#".concat(e.optionOne),o="#".concat(e.optionTwo),n=document.getElementById(a).querySelector(c),r=document.getElementById(a).querySelector(o),i=function(e){return utils.getDates(window.dayjs().month(e).date(1),window.dayjs().month(Number(e)+1).date(0),2592e5)},l=[[20,40,20,80,50,80,120,80,50,120,110,110],[60,80,60,80,65,130,120,100,30,40,30,70],[100,70,80,50,120,100,130,140,90,100,40,50],[80,50,60,40,60,120,100,130,60,80,50,60],[70,80,100,70,90,60,80,130,40,60,50,80],[90,40,80,80,100,140,100,130,90,60,70,50],[80,60,80,60,40,100,120,100,30,40,30,70],[20,40,20,50,70,60,110,80,90,30,50,50],[60,70,30,40,80,140,80,140,120,130,100,110],[90,90,40,60,40,110,90,110,60,80,60,70],[50,80,50,80,50,80,120,80,50,120,110,110],[60,90,60,70,40,70,100,140,30,40,30,70],[20,40,20,50,30,80,120,100,30,40,30,70]],s=function(e){var t=window.dayjs(e[0].axisValue),a="";return e.forEach(function(e){a+='<h6 class="fs-10 text-700"><span class="fas fa-circle me-2" style="color:'.concat(e.borderColor,'"></span>\n        ').concat(t.format("MMM DD")," : ").concat(e.value,"\n      </h6>")}),"<div class='ms-1'>\n                ".concat(a,"\n              </div>")},echartSetOption(t,e,function(){return{title:{text:"Sales over time",textStyle:{fontWeight:500,fontSize:13,fontFamily:"poppins"}},legend:{show:!1,data:["currentMonth","prevMonth"]},color:utils.getColors().white,tooltip:{trigger:"axis",padding:[7,10],backgroundColor:utils.getGrays()[100],borderColor:utils.getGrays()[300],textStyle:{color:utils.getGrays()[1100]},borderWidth:1,formatter:s,transitionDuration:0,position:function(e,t,a,o,n){return getPosition(e,t,a,o,n)}},xAxis:{type:"category",data:i(0),boundaryGap:!1,axisPointer:{lineStyle:{color:utils.getGrays()[300],type:"dashed"}},axisLine:{lineStyle:{color:utils.getGrays()[300],type:"solid"}},axisTick:{show:!1},axisLabel:{color:utils.getGrays()[400],formatter:function(e){e=new Date(e);return"".concat(y[e.getMonth()].substring(0,3)," ").concat(e.getDate())},margin:15},splitLine:{show:!0,lineStyle:{color:utils.getGrays()[300],type:"dashed"}}},yAxis:{type:"value",axisPointer:{show:!1},splitLine:{lineStyle:{color:utils.getGrays()[300]}},boundaryGap:!1,axisLabel:{show:!0,color:utils.getGrays()[400],margin:15},axisTick:{show:!1},axisLine:{show:!1}},series:[{name:"prevMonth",type:"line",data:l[0],lineStyle:{color:utils.getGrays()[300]},itemStyle:{borderColor:utils.getGrays()[300],borderWidth:2},symbol:"none",smooth:!1,hoverAnimation:!0},{name:"currentMonth",type:"line",data:l[1],lineStyle:{color:utils.getColors().primary},itemStyle:{borderColor:utils.getColors().primary,borderWidth:2},symbol:"none",smooth:!1,hoverAnimation:!0}],grid:{right:"8px",left:"40px",bottom:"15%",top:"20%"}}}),c=document.querySelector(g),d=0,u=l[Number(0)+1],m=l[c.selectedIndex],c.addEventListener("change",function(e){d=e.currentTarget.value,u=l[Number(d)+1],m=l[d],n.querySelector(".text").innerText=y[d],r.querySelector(".text").innerText=y[d-1]||"Dec",t.setOption({xAxis:{data:i(d)},series:[{data:u},{data:m}]})}),n.addEventListener("click",function(){n.classList.toggle("opacity-50"),t.dispatchAction({type:"legendToggleSelect",name:"currentMonth"})}),r.addEventListener("click",function(){r.classList.toggle("opacity-50"),t.dispatchAction({type:"legendToggleSelect",name:"prevMonth"})}))}),leadConversionInit=function(){var e,t=document.querySelector(".echart-lead-conversion");t&&(e=utils.getData(t,"options"),t=window.echarts.init(t),echartSetOption(t,e,function(){return{color:[utils.rgbaColor(utils.getColors().primary,.7),utils.rgbaColor(utils.getColors().info,.6),utils.rgbaColor(utils.getColors().secondary,.2),utils.rgbaColor(utils.getColors().warning,.6)],legend:{data:["Campaigns","Lead","Opportunity","Deal"],left:"0%",icon:"circle",inactiveColor:utils.getGrays()[400],textStyle:{color:utils.getGrays()[700]},itemGap:10},yAxis:{type:"category",data:["kerry Ingram","Bradie Pitter","Harrington","Ashley Shaw","Jenny Horas","Chris Pratt"],axisLine:{show:!1},boundaryGap:!1,splitLine:{lineStyle:{color:utils.getGrays()[200]}},axisTick:{show:!1},axisLabel:{color:utils.getGrays()[600]}},xAxis:{type:"value",splitLine:{lineStyle:{color:utils.getGrays()[200]}},axisLine:{show:!1},axisTick:{show:!1},axisLabel:{show:!1}},tooltip:{trigger:"axis",padding:[7,10],axisPointer:{type:"none"},backgroundColor:utils.getGrays()[100],borderColor:utils.getGrays()[300],textStyle:{color:utils.getGrays()[1100]},borderWidth:1,transitionDuration:0,position:function(e,t,a,o,n){return getPosition(e,t,a,o,n)},formatter:tooltipFormatter},series:[{name:"Campaigns",type:"bar",stack:"total",data:[1405,1300,1620,1430,1500,1520],barWidth:"20%"},{name:"Lead",type:"bar",stack:"total",data:[320,302,301,334,340,390],barWidth:"20%"},{name:"Opportunity",type:"bar",stack:"total",data:[220,182,351,234,290,300],barWidth:"20%"},{name:"Deal",type:"bar",stack:"total",data:[120,182,191,134,190,170],barWidth:"20%"}],grid:{right:5,left:5,bottom:8,top:60,containLabel:!0}}}))},linePaymentChartInit=function(){var e,t,a=document.querySelector(".echart-line-payment"),o={all:[4,1,6,2,7,12,4,6,5,4,5,10],successful:[3,1,4,1,5,9,2,6,5,3,5,8],failed:[1,0,2,1,2,1,1,0,0,1,0,2]},n=["9:00 AM","10:00 AM","11:00 AM","12:00 PM","1:00 PM","2:00 PM","3:00 PM","4:00 PM","5:00 PM","6:00 PM","7:00 PM","8:00 PM"];a&&(e=utils.getData(a,"options"),t=window.echarts.init(a),echartSetOption(t,e,function(){return{tooltip:{trigger:"axis",axisPointer:{type:"none"},padding:[7,10],backgroundColor:utils.getGrays()[100],borderColor:utils.getGrays()[300],borderWidth:1,transitionDuration:0,formatter:function(e){return"".concat(e[0].axisValue," - ").concat(e[0].value," USD")},textStyle:{fontWeight:500,fontSize:12,color:utils.getGrays()[1100]}},xAxis:{type:"category",data:n,splitLine:{show:!0,lineStyle:{color:utils.rgbaColor("#fff",.1)},interval:0},axisLine:{lineStyle:{color:utils.rgbaColor("#fff",.1)}},axisTick:{show:!0,length:10,lineStyle:{color:utils.rgbaColor("#fff",.1)}},axisLabel:{color:utils.getGrays()[400],fontWeight:600,formatter:function(e){return e.substring(0,e.length-3)},fontSize:12,interval:window.innerWidth<768?"auto":0,margin:15},boundaryGap:!1},yAxis:{type:"value",axisPointer:{show:!1},splitLine:{show:!1},axisLabel:{show:!1},axisTick:{show:!1},axisLine:{show:!1}},series:[{type:"line",smooth:!0,data:o.successful.map(function(e){return(3.14*e).toFixed(2)}),symbol:"emptyCircle",itemStyle:{color:"light"===utils.isDark()?utils.getColors().white:utils.getColors().primary},lineStyle:{color:"light"===utils.isDark()?utils.rgbaColor(utils.getColors().white,.8):utils.getColors().primary},areaStyle:{color:{type:"linear",x:0,y:0,x2:0,y2:1,colorStops:[{offset:0,color:"light"===utils.isDark()?"rgba(255, 255, 255, 0.5)":utils.rgbaColor(utils.getColors().primary,.5)},{offset:1,color:"light"===utils.isDark()?"rgba(255, 255, 255, 0)":utils.rgbaColor(utils.getColors().primary,0)}]}},emphasis:{lineStyle:{width:2}}}],grid:{right:15,left:15,bottom:"15%",top:0}}}),utils.resize(function(){window.innerWidth<768&&t.setOption({xAxis:{axisLabel:{interval:"auto"}}})}),a=document.querySelector("#dashboard-chart-select"))&&a.addEventListener("change",function(e){e=e.currentTarget.value;t.setOption({series:[{data:o[e].map(function(e){return(3.14*e).toFixed(2)})}]})})},locationBySessionInit=function(){var e,t,a,o=document.querySelector(".echart-location-by-session-map"),n=[{name:"Afghanistan",value:28397.812},{name:"Angola",value:19549.124},{name:"Albania",value:3150.143},{name:"United Arab Emirates",value:8441.537},{name:"Argentina",value:40374.224},{name:"Armenia",value:2963.496},{name:"French Southern and Antarctic Lands",value:268.065},{name:"Australia",value:22404.488},{name:"Austria",value:8401.924},{name:"Azerbaijan",value:9094.718},{name:"Burundi",value:9232.753},{name:"Belgium",value:10941.288},{name:"Benin",value:9509.798},{name:"Burkina Faso",value:15540.284},{name:"Bangladesh",value:151125.475},{name:"Bulgaria",value:7389.175},{name:"The Bahamas",value:66402.316},{name:"Bosnia and Herzegovina",value:3845.929},{name:"Belarus",value:9491.07},{name:"Belize",value:308.595},{name:"Bermuda",value:64.951},{name:"Bolivia",value:716.939},{name:"Brazil",value:195210.154},{name:"Brunei",value:27.223},{name:"Bhutan",value:716.939},{name:"Botswana",value:1969.341},{name:"Central African Rep.",value:4349.921},{name:"Canada",value:34126.24},{name:"Switzerland",value:7830.534},{name:"Chile",value:17150.76},{name:"China",value:1359821.465},{name:"Côte d'Ivoire",value:60508.978},{name:"Cameroon",value:20624.343},{name:"Dem. Rep. Congo",value:62191.161},{name:"Congo",value:3573.024},{name:"Colombia",value:46444.798},{name:"Costa Rica",value:4669.685},{name:"Cuba",value:11281.768},{name:"Northern Cyprus",value:1.468},{name:"Cyprus",value:1103.685},{name:"Czech Republic",value:10553.701},{name:"Germany",value:83017.404},{name:"Djibouti",value:834.036},{name:"Denmark",value:5550.959},{name:"Dominican Republic",value:10016.797},{name:"Algeria",value:37062.82},{name:"Ecuador",value:15001.072},{name:"Egypt",value:78075.705},{name:"Eritrea",value:5741.159},{name:"Spain",value:46182.038},{name:"Estonia",value:1298.533},{name:"Ethiopia",value:87095.281},{name:"Finland",value:5367.693},{name:"Fiji",value:860.559},{name:"Falkland Islands",value:49.581},{name:"France",value:63230.866},{name:"Gabon",value:1556.222},{name:"United Kingdom",value:62066.35},{name:"Georgia",value:4388.674},{name:"Ghana",value:24262.901},{name:"Eq. Guinea",value:10876.033},{name:"Guinea",value:10876.033},{name:"Gambia",value:1680.64},{name:"Guinea Bissau",value:10876.033},{name:"Equatorial Guinea",value:696.167},{name:"Greece",value:11109.999},{name:"Greenland",value:56.546},{name:"Guatemala",value:14341.576},{name:"French Guiana",value:231.169},{name:"Guyana",value:786.126},{name:"Honduras",value:7621.204},{name:"Croatia",value:4338.027},{name:"Haiti",value:9896.4},{name:"Hungary",value:10014.633},{name:"Indonesia",value:240676.485},{name:"India",value:1205624.648},{name:"Ireland",value:4467.561},{name:"Iran",value:240676.485},{name:"Iraq",value:30962.38},{name:"Iceland",value:318.042},{name:"Israel",value:7420.368},{name:"Italy",value:60508.978},{name:"Jamaica",value:2741.485},{name:"Jordan",value:6454.554},{name:"Japan",value:127352.833},{name:"Kazakhstan",value:15921.127},{name:"Kenya",value:40909.194},{name:"Kyrgyzstan",value:5334.223},{name:"Cambodia",value:14364.931},{name:"South Korea",value:51452.352},{name:"Kosovo",value:97.743},{name:"Kuwait",value:2991.58},{name:"Laos",value:6395.713},{name:"Lebanon",value:4341.092},{name:"Liberia",value:3957.99},{name:"Libya",value:6040.612},{name:"Sri Lanka",value:20758.779},{name:"Lesotho",value:2008.921},{name:"Lithuania",value:3068.457},{name:"Luxembourg",value:507.885},{name:"Latvia",value:2090.519},{name:"Morocco",value:31642.36},{name:"Moldova",value:103.619},{name:"Madagascar",value:21079.532},{name:"Mexico",value:117886.404},{name:"Macedonia",value:507.885},{name:"Mali",value:13985.961},{name:"Myanmar",value:51931.231},{name:"Montenegro",value:620.078},{name:"Mongolia",value:2712.738},{name:"Mozambique",value:23967.265},{name:"Mauritania",value:3609.42},{name:"Malawi",value:15013.694},{name:"Malaysia",value:28275.835},{name:"Namibia",value:2178.967},{name:"New Caledonia",value:246.379},{name:"Niger",value:15893.746},{name:"Nigeria",value:159707.78},{name:"Nicaragua",value:5822.209},{name:"Netherlands",value:16615.243},{name:"Norway",value:4891.251},{name:"Nepal",value:26846.016},{name:"New Zealand",value:4368.136},{name:"Oman",value:2802.768},{name:"Pakistan",value:173149.306},{name:"Panama",value:3678.128},{name:"Peru",value:29262.83},{name:"Philippines",value:93444.322},{name:"Papua New Guinea",value:6858.945},{name:"Poland",value:38198.754},{name:"Puerto Rico",value:3709.671},{name:"North Korea",value:1.468},{name:"Portugal",value:10589.792},{name:"Paraguay",value:6459.721},{name:"Qatar",value:1749.713},{name:"Romania",value:21861.476},{name:"Russia",value:21861.476},{name:"Rwanda",value:10836.732},{name:"Western Sahara",value:514.648},{name:"Saudi Arabia",value:27258.387},{name:"Sudan",value:35652.002},{name:"S. Sudan",value:9940.929},{name:"Senegal",value:12950.564},{name:"Solomon Islands",value:526.447},{name:"Sierra Leone",value:5751.976},{name:"El Salvador",value:6218.195},{name:"Somaliland",value:9636.173},{name:"Somalia",value:9636.173},{name:"Republic of Serbia",value:3573.024},{name:"Suriname",value:524.96},{name:"Slovakia",value:5433.437},{name:"Slovenia",value:2054.232},{name:"Sweden",value:9382.297},{name:"Swaziland",value:1193.148},{name:"Syria",value:7830.534},{name:"Chad",value:11720.781},{name:"Togo",value:6306.014},{name:"Thailand",value:66402.316},{name:"Tajikistan",value:7627.326},{name:"Turkmenistan",value:5041.995},{name:"East Timor",value:10016.797},{name:"Trinidad and Tobago",value:1328.095},{name:"Tunisia",value:10631.83},{name:"Turkey",value:72137.546},{name:"Tanzania",value:44973.33},{name:"Uganda",value:33987.213},{name:"Ukraine",value:46050.22},{name:"Uruguay",value:3371.982},{name:"United States",value:312247.116},{name:"Uzbekistan",value:27769.27},{name:"Venezuela",value:236.299},{name:"Vietnam",value:89047.397},{name:"Vanuatu",value:236.299},{name:"West Bank",value:13.565},{name:"Yemen",value:22763.008},{name:"South Africa",value:51452.352},{name:"Zambia",value:13216.985},{name:"Zimbabwe",value:13076.978}];o&&(e=utils.getData(o,"options"),t=window.echarts.init(o),echartSetOption(t,e,function(){return{tooltip:{trigger:"item",padding:[7,10],backgroundColor:utils.getGrays()[100],borderColor:utils.getGrays()[300],textStyle:{color:utils.getGrays()[1100]},borderWidth:1,transitionDuration:0,formatter:function(e){var t;return"<strong>".concat(null==(t=e.data)?void 0:t.name," :</strong> ").concat((100*(e.data&&e.data.value/6961500)).toFixed(2),"%")}},visualMap:{show:!1,min:800,max:5e4,inRange:{color:[utils.getColors().primary,utils.rgbaColor(utils.getColors().primary,.8),utils.rgbaColor(utils.getColors().primary,.6),utils.rgbaColor(utils.getColors().primary,.4),utils.rgbaColor(utils.getColors().primary,.2)].reverse()}},series:[{type:"map",map:"world",data:n,roam:"move",scaleLimit:{min:1,max:5},left:0,right:0,label:{show:!1},itemStyle:{borderColor:utils.getGrays()[300]},emphasis:{label:{show:!1},itemStyle:{areaColor:utils.getColor("warning")}}}]}}),a=1,null!=(o=document.querySelector(".location-by-session-map-reset"))&&o.addEventListener("click",function(){a=1,t.dispatchAction({type:"restore"}),t.setOption({series:{zoom:1}})}),null!=(e=document.querySelector(".location-by-session-map-zoom"))&&e.addEventListener("click",function(){a<5&&(a+=1),t.setOption({series:{zoom:a}})}),null!=(o=document.querySelector(".location-by-session-map-zoomOut")))&&o.addEventListener("click",function(){1<a&&--a,t.setOption({series:{zoom:a}})})},marketShareEcommerceInit=function(){var e,t=document.querySelector(".echart-product-share");t&&(e=utils.getData(t,"options"),t=window.echarts.init(t),echartSetOption(t,e,function(){return{color:[utils.getColors().primary,utils.getColors().info,utils.getColors().warning],tooltip:{trigger:"item",padding:[7,10],backgroundColor:utils.getGrays()[100],borderColor:utils.getGrays()[300],textStyle:{color:utils.getGrays()[1100]},borderWidth:1,transitionDuration:0,formatter:function(e){return"<strong>".concat(e.data.name,":</strong> ").concat(e.percent,"%")}},position:function(e,t,a,o,n){return getPosition(e,t,a,o,n)},legend:{show:!1},series:[{type:"pie",radius:["100%","80%"],avoidLabelOverlap:!1,hoverAnimation:!1,itemStyle:{borderWidth:2,borderColor:utils.getColor("gray-100")},label:{normal:{show:!1,position:"center",textStyle:{fontSize:"20",fontWeight:"500",color:utils.getGrays()[700]}},emphasis:{show:!1}},labelLine:{normal:{show:!1}},data:[{value:53e5,name:"Falcon"},{value:19e5,name:"Sparrow"},{value:2e6,name:"Phoenix"}]}]}}))},marketShareInit=function(){var e,t=document.querySelector(".echart-market-share");t&&(e=utils.getData(t,"options"),t=window.echarts.init(t),echartSetOption(t,e,function(){return{color:[utils.getColors().primary,utils.getColors().info,utils.getGrays()[300]],tooltip:{trigger:"item",padding:[7,10],backgroundColor:utils.getGrays()[100],borderColor:utils.getGrays()[300],textStyle:{color:utils.getGrays()[1100]},borderWidth:1,transitionDuration:0,formatter:function(e){return"<strong>".concat(e.data.name,":</strong> ").concat(e.percent,"%")}},position:function(e,t,a,o,n){return getPosition(e,t,a,o,n)},legend:{show:!1},series:[{type:"pie",radius:["100%","87%"],avoidLabelOverlap:!1,hoverAnimation:!1,itemStyle:{borderWidth:2,borderColor:utils.getColor("gray-100")},label:{normal:{show:!1,position:"center",textStyle:{fontSize:"20",fontWeight:"500",color:utils.getGrays()[100]}},emphasis:{show:!1}},labelLine:{normal:{show:!1}},data:[{value:53e5,name:"Samsung"},{value:19e5,name:"Huawei"},{value:2e6,name:"Apple"}]}]}}))},marketingExpensesInit=function(){var t,a,e,o,n,r=document.querySelector(".echart-marketing-expenses");r&&(t=utils.getData(r,"options"),a=window.echarts.init(r),e=[{value:412600,name:"Offline Marketing",itemStyle:{color:utils.getColor("primary")},label:{rich:{per:{color:"#1C4F93"}}}},{value:641500,name:"Digital Marketing",itemStyle:{color:utils.rgbaColor(utils.getColor("info"),.35)},label:{rich:{per:{color:"#1978A2"}}}}],o=[{value:91600,name:"Event Sponsorship",itemStyle:{color:utils.rgbaColor(utils.getColor("primary"),.4)}},{value:183e3,name:"Outrich Event",itemStyle:{color:utils.rgbaColor(utils.getColor("primary"),.6)}},{value:138e3,name:"Ad Campaign",itemStyle:{color:utils.rgbaColor(utils.getColor("primary"),.8)}},{value:183e3,name:"Social Media",itemStyle:{color:utils.rgbaColor(utils.getColor("info"),.2)}},{value:45900,name:"Google Ads",itemStyle:{color:utils.rgbaColor(utils.getColor("info"),.35)}},{value:138e3,name:"Influencer Marketing",itemStyle:{color:utils.rgbaColor(utils.getColor("info"),.5)}},{value:183e3,name:"Email Marketing",itemStyle:{color:utils.rgbaColor(utils.getColor("info"),.7)}},{value:91600,name:"Generate Backlinks",itemStyle:{color:utils.rgbaColor(utils.getColor("info"),.8)}}],n=function(){return{tooltip:{trigger:"item",backgroundColor:utils.getGrays()[100],textStyle:{color:utils.getGrays()[1100]},formatter:"{b}<br/> {c} ({d}%)"},series:[{name:"Marketing Expenses",type:"pie",selectedMode:"single",radius:["45%","60%"],label:{show:!1},labelLine:{show:!1},itemStyle:{borderColor:utils.getColor("gray-100"),borderWidth:2},data:o},{name:"Marketing Expenses",type:"pie",radius:["70%","75%"],barWidth:10,labelLine:{length:0,show:!1},label:{formatter:"{per|{d}%}",rich:{per:{fontSize:14,fontWeight:"bold",lineHeight:33}}},data:e}]}},window.addEventListener("scroll",function e(){utils.isScrolledIntoView(r)&&(echartSetOption(a,t,n),window.removeEventListener("scroll",e))}))},mostLeadsInit=function(){var e,t=document.querySelector(".echart-most-leads");t&&(e=utils.getData(t,"options"),t=window.echarts.init(t),echartSetOption(t,e,function(){return{color:[utils.getColors().primary,utils.rgbaColor(utils.getColors().primary,.5),utils.getColors().warning,utils.getColors().info],tooltip:{trigger:"item",padding:[7,10],backgroundColor:utils.getGrays()[100],borderColor:utils.getGrays()[300],textStyle:{color:utils.getGrays()[1100]},borderWidth:1,transitionDuration:0,formatter:function(e){return"<strong>".concat(e.data.name,":</strong> ").concat(e.percent,"%")}},position:function(e,t,a,o,n){return getPosition(e,t,a,o,n)},legend:{show:!1},series:[{type:"pie",radius:["100%","67%"],avoidLabelOverlap:!1,hoverAnimation:!1,itemStyle:{borderWidth:2,borderColor:utils.getColor("gray-100")},label:{normal:{show:!1,position:"center",textStyle:{fontSize:"20",fontWeight:"500",color:utils.getGrays()[700]}},emphasis:{show:!1}},labelLine:{normal:{show:!1}},data:[{value:60,name:"Social"},{value:30,name:"Other"},{value:10,name:"Call"},{value:120,name:"Email"}]}]}}))},echartsNumberOfTicketsInit=function(){var e,t,a,o,n,r,i,l,s=document.querySelector(".echart-number-of-tickets");s&&(e=utils.getData(s,"options"),t=window.echarts.init(s),s=document.querySelectorAll("[data-number-of-tickets]"),a=["Mar 01","Mar 02","Mar 03","Mar 04","Mar 05","Mar 06"],o=[45,35,55,55,55,45],n=[58,42,65,65,65,30],r=[38,25,42,42,42,45],i=[62,45,75,75,75,55],l={itemStyle:{shadowColor:utils.rgbaColor(utils.getColor("dark"),.3),borderRadius:[5,5,5,5]}},echartSetOption(t,e,function(){return{color:[utils.getColor("primary"),"dark"===utils.isDark()?"#1E4C88":"#94BCF1","dark"===utils.isDark()?"#1A3A64":"#C0D8F7","dark"===utils.isDark()?"#225FAE":"#6AA3ED"],tooltip:{trigger:"item",padding:[7,10],backgroundColor:utils.getGrays()[100],borderColor:utils.getGrays()[300],textStyle:{color:utils.getGrays()[900]},borderWidth:1,transitionDuration:0,axisPointer:{type:"none"}},legend:{data:["On Hold Tickets","Open Tickets","Due Tickets","Unassigned Tickets"],show:!1},xAxis:{data:a,splitLine:{show:!1},splitArea:{show:!1},axisLabel:{color:utils.getGrays()[600]},axisLine:{lineStyle:{color:utils.getGrays()[300],type:"dashed"}},axisTick:{show:!1}},yAxis:{splitLine:{lineStyle:{color:utils.getGrays()[300],type:"dashed"}},axisLabel:{color:utils.getGrays()[600]}},series:[{name:"On Hold Tickets",type:"bar",stack:"one",emphasis:l,data:o},{name:"Open Tickets",type:"bar",stack:"two",emphasis:l,data:n},{name:"Due Tickets",type:"bar",stack:"three",emphasis:l,data:r},{name:"Unassigned Tickets",type:"bar",stack:"four",emphasis:l,data:i}],itemStyle:{borderRadius:[3,3,0,0]},barWidth:"12px",grid:{top:"10%",bottom:0,left:0,right:0,containLabel:!0}}}),s.forEach(function(e){e.addEventListener("change",function(){t.dispatchAction({type:"legendToggleSelect",name:utils.getData(e,"number-of-tickets")})})}))},realTimeUsersChartInit=function(){var e,t,a,o,n,r,i=document.querySelector(".echart-real-time-users");i&&(e=utils.getData(i,"options"),t=window.echarts.init(i),a=[921,950,916,913,909,962,926,936,977,976,999,981,998,1e3,900,906,973,911,994,982,917,972,952,963,991],o=[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25],n=function(e){return'\n      <div>\n          <h6 class="fs-10 text-700 mb-0"><span class="fas fa-circle me-1 text-info"></span>\n            Users : '.concat(e[0].value,"\n          </h6>\n      </div>\n      ")},echartSetOption(t,e,function(){return{tooltip:{trigger:"axis",padding:[7,10],axisPointer:{type:"none"},backgroundColor:utils.getGrays()[100],borderColor:utils.getGrays()[300],textStyle:{color:utils.getGrays()[1100]},borderWidth:1,transitionDuration:0,position:function(e,t,a,o,n){return getPosition(e,t,a,o,n)},formatter:n},xAxis:{type:"category",axisLabel:{show:!1},axisTick:{show:!1},axisLine:{show:!1},boundaryGap:[.2,.2],data:o},yAxis:{type:"value",scale:!0,boundaryGap:!1,axisLabel:{show:!1},splitLine:{show:!1},min:500,max:1100},series:[{type:"bar",barCategoryGap:"12%",data:a,itemStyle:{color:utils.rgbaColor("#fff",.3)}}],grid:{right:"0px",left:"0px",bottom:0,top:0}}}),r=document.querySelector(".real-time-user"),setInterval(function(){var e=utils.getRandomNumber(900,1e3);a.shift(),a.push(e),o.shift(),o.push(utils.getRandomNumber(100,500)),r.innerHTML=e,t.setOption({xAxis:{data:o},series:[{data:a}]})},2e3))},echartsReceivedTicketsInit=function(){var e,t,a,o,n,r,i=document.querySelector(".echart-received-tickets");i&&(e=utils.getData(i,"options"),i=window.echarts.init(i),t=["Apr 01","Apr 02","Apr 03","Apr 04","Apr 05","Apr 06","Apr 07","Apr 08","Apr 09","Apr 10"],a=[28,35,28,25,21,32,25,30,23,37],o=[20,27,21,15,17,22,18,20,15,27],n=[15,21,23,21,12,14,13,15,10,19],r={itemStyle:{shadowColor:utils.rgbaColor(utils.getColor("dark"),.3)}},echartSetOption(i,e,function(){return{color:[utils.getColor("primary"),utils.getColor("info"),utils.getGrays()[300]],legend:{data:["All Received Tickets","New Received Tickets","Total Received Load Tickets"],icon:"circle",itemWidth:10,itemHeight:10,padding:[0,0,0,0],textStyle:{color:utils.getGrays()[700],fontWeight:"500",fontSize:"13px"},left:0,itemGap:16},tooltip:{trigger:"item",padding:[7,10],backgroundColor:utils.getGrays()[100],borderColor:utils.getGrays()[300],textStyle:{color:utils.getGrays()[900]},borderWidth:1,transitionDuration:0,axisPointer:{type:"none"}},xAxis:{data:t,splitLine:{show:!1},splitArea:{show:!1},axisLabel:{color:utils.getGrays()[600]},axisLine:{lineStyle:{color:utils.getGrays()[300],type:"dashed"}},axisTick:{show:!1}},yAxis:{splitLine:{lineStyle:{color:utils.getGrays()[300],type:"dashed"}},axisLabel:{color:utils.getGrays()[600]}},series:[{name:"All Received Tickets",type:"bar",stack:"one",emphasis:r,data:a},{name:"New Received Tickets",type:"bar",stack:"two",emphasis:r,data:o},{name:"Total Received Load Tickets",type:"bar",stack:"three",emphasis:r,data:n}],itemStyle:{borderRadius:[3,3,0,0]},barWidth:"13.03px",grid:{top:"13%",bottom:0,left:0,right:0,containLabel:!0}}}))},reportForThisWeekInit=function(){var e,t,a,o,n,r=document.querySelector(".echart-bar-report-for-this-week");r&&(o=utils.getData(r,"chart"),e=document.getElementById(null==o?void 0:o.option1),t=document.getElementById(null==o?void 0:o.option2),a=[["product","This Week","Last Week"],["Sun",43,85],["Mon",83,73],["Tue",86,62],["Wed",72,53],["Thu",80,50],["Fri",50,70],["Sat",80,90]],o=utils.getData(r,"options"),n=window.echarts.init(r),t&&t.addEventListener("click",function(){t.classList.toggle("opacity-50"),n.dispatchAction({type:"legendToggleSelect",name:"Last Week"})}),e&&e.addEventListener("click",function(){e.classList.toggle("opacity-50"),n.dispatchAction({type:"legendToggleSelect",name:"This Week"})}),echartSetOption(n,o,function(){return{color:[utils.getColors().primary,utils.getGrays()[300]],dataset:{source:a},tooltip:{trigger:"item",padding:[7,10],backgroundColor:utils.getGrays()[100],borderColor:utils.getGrays()[300],textStyle:{color:utils.getGrays()[1100]},borderWidth:1,transitionDuration:0,position:function(e,t,a,o,n){return getPosition(e,t,a,o,n)},formatter:function(e){return'<div class="font-weight-semi-bold">'.concat(e.seriesName,'</div><div class="fs-10 text-600"><strong>').concat(e.name,":</strong> ").concat(e.value[e.componentIndex+1],"</div>")}},legend:{show:!1},xAxis:{type:"category",axisLabel:{color:utils.getGrays()[400]},axisLine:{lineStyle:{color:utils.getGrays()[300],type:"dashed"}},axisTick:!1,boundaryGap:!0},yAxis:{axisPointer:{type:"none"},axisTick:"none",splitLine:{lineStyle:{color:utils.getGrays()[300],type:"dashed"}},axisLine:{show:!1},axisLabel:{color:utils.getGrays()[400],formatter:function(e){return"".concat(e," hr")}}},series:[{type:"bar",name:"",barWidth:"12%",barGap:"30%",label:{normal:{show:!1}},z:10,itemStyle:{normal:{barBorderRadius:[10,10,0,0],color:utils.getColors().primary}}},{type:"bar",barWidth:"12%",barGap:"30%",label:{normal:{show:!1}},itemStyle:{normal:{barBorderRadius:[4,4,0,0],color:utils.getGrays()[300]}}}],grid:{right:"0",left:"40px",bottom:"10%",top:"15%"}}}))},returningCustomerRateInit=function(){var e,t,a,o,n,r,i,l,s,c,d=document.querySelector(".echart-line-returning-customer-rate"),u=["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"];d&&(t=(e=utils.getData(d,"options")).target,a="#".concat(e.monthSelect),o="#".concat(e.optionOne),n="#".concat(e.optionTwo),r=document.getElementById(t).querySelector(o),i=document.getElementById(t).querySelector(n),l=window.echarts.init(d),s=[[20,40,20,80,50,80,120,80,50,120,110,110],[60,80,60,80,65,130,120,100,30,40,30,70],[100,70,80,50,120,100,130,140,90,100,40,50],[80,50,60,40,60,120,100,130,60,80,50,60],[70,80,100,70,90,60,80,130,40,60,50,80],[90,40,80,80,100,140,100,130,90,60,70,50],[80,60,80,60,40,100,120,100,30,40,30,70],[20,40,20,50,70,60,110,80,90,30,50,50],[60,70,30,40,80,140,80,140,120,130,100,110],[90,90,40,60,40,110,90,110,60,80,60,70],[50,80,50,80,50,80,120,80,50,120,110,110],[60,90,60,70,40,70,100,140,30,40,30,70],[20,40,20,50,30,80,120,100,30,40,30,70]],c=function(e){return utils.getDates(window.dayjs().month(e).date(1),window.dayjs().month(Number(e)+1).date(0),2592e5)},echartSetOption(l,e,function(){return{title:{text:"Customers",textStyle:{fontWeight:500,fontSize:13,fontFamily:"poppins",color:utils.getColor("gray-900")}},legend:{show:!1,data:["New","Returning"]},tooltip:{trigger:"axis",padding:[7,10],backgroundColor:utils.getGrays()[100],borderColor:utils.getGrays()[300],textStyle:{color:utils.getGrays()[1100]},borderWidth:1,transitionDuration:0,position:function(e,t,a,o,n){return getPosition(e,t,a,o,n)},formatter:tooltipFormatter},xAxis:{type:"category",data:c(0),boundaryGap:!1,axisPointer:{lineStyle:{color:utils.getColor("gray-300"),type:"dashed"}},axisLine:{lineStyle:{color:utils.getColor("gray-300"),type:"solid"}},axisTick:{show:!1},axisLabel:{color:utils.getColor("gray-400"),formatter:function(e){e=new Date(e);return(1===e.getDate()?"".concat(u[e.getMonth()].substring(0,3)," "):"").concat(e.getDate())},margin:15},splitLine:{show:!0,lineStyle:{color:utils.getGrays()[300],type:"dashed"}}},yAxis:{type:"value",axisPointer:{show:!1},splitLine:{lineStyle:{color:utils.getGrays()[300]}},boundaryGap:!1,axisLabel:{show:!0,color:utils.getGrays()[400],margin:15},axisTick:{show:!1},axisLine:{show:!1}},series:[{name:"New",type:"line",data:s[1],lineStyle:{color:utils.getColors().primary},itemStyle:{borderColor:utils.getColors().primary,borderWidth:2},areaStyle:{color:{type:"linear",x:0,y:0,x2:0,y2:1,colorStops:[{offset:0,color:utils.rgbaColor(utils.getColor("primary"),.2)},{offset:1,color:utils.rgbaColor(utils.getColor("primary"),.01)}]}},symbol:"none",smooth:!1,hoverAnimation:!0},{name:"Returning",type:"line",data:s[0],lineStyle:{color:utils.getColor("warning")},itemStyle:{borderColor:utils.getColor("warning"),borderWidth:2},areaStyle:{color:{type:"linear",x:0,y:0,x2:0,y2:1,colorStops:[{offset:0,color:utils.rgbaColor(utils.getColor("warning"),.2)},{offset:1,color:utils.rgbaColor(utils.getColor("warning"),.01)}]}},symbol:"none",smooth:!1,hoverAnimation:!0}],grid:{right:"7px",left:"35px",bottom:"8%",top:"15%"}}}),document.querySelector(a).addEventListener("change",function(e){var e=e.currentTarget.value,t=s[Number(e)+1],a=s[e];l.setOption({xAxis:{data:c(e)},series:[{data:t},{data:a}]})}),r.addEventListener("click",function(){r.classList.toggle("opacity-50"),l.dispatchAction({type:"legendToggleSelect",name:"New"})}),i.addEventListener("click",function(){i.classList.toggle("opacity-50"),l.dispatchAction({type:"legendToggleSelect",name:"Returning"})}))},salesByPosLocationInit=function(){var e,t=document.querySelector(".echart-radar-sales-by-pos-location");function a(e){return"<strong > ".concat(e.name,' </strong>\n    <div class="fs-10 text-600">\n      <strong >Marketing</strong>: ').concat(e.value[0],"  <br>\n      <strong>Sales</strong>: ").concat(e.value[1],"  <br>\n      <strong>Dev</strong>: ").concat(e.value[2],"  <br>\n      <strong>Support</strong>: ").concat(e.value[3],"  <br>\n      <strong>Tech</strong>: ").concat(e.value[4],"  <br>\n      <strong>Admin</strong>: ").concat(e.value[5],"  <br>\n    </div>")}t&&(e=utils.getData(t,"options"),t=window.echarts.init(t),echartSetOption(t,e,function(){return{tooltip:{trigger:"item",padding:[7,10],backgroundColor:utils.getColor("gray-100"),borderColor:utils.getColor("gray-300"),textStyle:{color:utils.getGrays()[1100]},borderWidth:1,transitionDuration:0,formatter:a},radar:{splitNumber:7,radius:"75%",axisLine:{show:!0,symbol:"circle",symbolSize:[13,13],lineStyle:{color:{type:"radial",x:.5,y:.5,r:.5,colorStops:[{offset:.7,color:utils.getColor("gray-100")},{offset:1,color:utils.getColor("gray-400")}]}}},splitArea:{show:!1},splitLine:{lineStyle:{color:utils.getColor("gray-300")}},name:{textStyle:{color:utils.getColor("gray-600"),fontWeight:500}},indicator:[{name:"Marketing",max:70},{name:"Admin",max:70},{name:"Tech",max:70},{name:"Support",max:70},{name:"Dev",max:70},{name:"Sales",max:70}]},series:[{name:"Budget vs spending",type:"radar",symbol:"pin",data:[{value:[20,50,60,50,60,60],name:"Budget",itemStyle:{color:utils.rgbaColor(utils.getColors().warning,.5)},areaStyle:{color:utils.rgbaColor(utils.getColors().warning,.24)},symbol:"circle",symbolSize:8},{value:[40,60,30,15,60,35],name:"Spending",areaStyle:{color:utils.rgbaColor(utils.getColors().primary,.24)},symbol:"circle",symbolSize:8,itemStyle:{color:utils.rgbaColor(utils.getColors().primary)}}]}],grid:{top:0,bottom:"100px"}}}))},echartsSatisfactionSurveyInit=function(){var e,t,a,o,n,r,i=document.querySelector(".echart-satisfaction-survey");i&&(e=utils.getData(i,"options"),i=window.echarts.init(i),t=["05 April","06 April","07 April","08 April","09 April","10 April","11 April","12 April","13 April","14 April","15 April"],a=[98,105,65,110,75,55,95,75,90,45,70],o=[80,60,78,58,65,65,75,110,40,60,60],n={itemStyle:{shadowColor:utils.rgbaColor(utils.getColor("dark"),.3),color:utils.rgbaColor(utils.getColor("primary"),.8)}},r={itemStyle:{shadowColor:utils.rgbaColor(utils.getColor("dark"),.3),color:utils.getGrays()[300]}},echartSetOption(i,e,function(){return{color:[utils.getColor("primary"),utils.getGrays()[200]],legend:{data:["Satisfied","Dissatisfied"],icon:"circle",itemWidth:10,itemHeight:10,padding:[0,0,0,0],textStyle:{color:utils.getGrays()[700],fontWeight:"500",fontSize:"13px"},left:0,itemGap:16},tooltip:{trigger:"item",padding:[7,10],backgroundColor:utils.getGrays()[100],borderColor:utils.getGrays()[300],textStyle:{color:utils.getGrays()[900]},borderWidth:1,transitionDuration:0,axisPointer:{type:"none"}},xAxis:{data:t,splitLine:{show:!1},splitArea:{show:!1},axisLabel:{color:utils.getGrays()[600]},axisLine:{lineStyle:{color:utils.getGrays()[300]}},axisTick:{show:!1}},yAxis:{splitLine:{lineStyle:{color:utils.getGrays()[300],type:"dashed"}},axisLabel:{color:utils.getGrays()[600]}},series:[{name:"Satisfied",type:"bar",stack:"one",emphasis:n,data:a},{name:"Dissatisfied",type:"bar",stack:"two",emphasis:r,data:o}],itemStyle:{borderRadius:[3,3,0,0]},barWidth:"13.03px",grid:{top:"13%",bottom:0,left:0,right:0,containLabel:!0}}}))},sessionByBrowserChartInit=function(){var e,t,a,o=document.querySelector(".echart-session-by-browser");o&&(e=utils.getData(o,"options"),t=window.echarts.init(o),a={week:[{value:50.3,name:"Chrome"},{value:20.6,name:"Safari"},{value:30.1,name:"Mozilla"}],month:[{value:35.1,name:"Chrome"},{value:25.6,name:"Safari"},{value:40.3,name:"Mozilla"}],year:[{value:26.1,name:"Chrome"},{value:10.6,name:"Safari"},{value:64.3,name:"Mozilla"}]},echartSetOption(t,e,function(){return{color:[utils.getColors().primary,utils.getColors().success,utils.getColors().info],tooltip:{trigger:"item",padding:[7,10],backgroundColor:utils.getGrays()[100],borderColor:utils.getGrays()[300],textStyle:{color:utils.getGrays()[1100]},borderWidth:1,transitionDuration:0,formatter:function(e){return"<strong>".concat(e.data.name,":</strong> ").concat(e.data.value,"%")},position:function(e,t,a,o,n){return getPosition(e,t,a,o,n)}},legend:{show:!1},series:[{type:"pie",radius:["100%","65%"],avoidLabelOverlap:!1,hoverAnimation:!1,itemStyle:{borderWidth:2,borderColor:utils.getColor("gray-100")},label:{normal:{show:!1},emphasis:{show:!1}},labelLine:{normal:{show:!1}},data:a.week}]}}),o=document.querySelector("[data-target='.echart-session-by-browser']"))&&o.addEventListener("change",function(e){e=e.currentTarget.value;t.setOption({series:[{data:a[e]}]})})},sessionByCountryMapInit=function(){var e,t,a=document.querySelector(".echart-session-by-country-map"),o=[{name:"Afghanistan",value:28397.812},{name:"Angola",value:19549.124},{name:"Albania",value:3150.143},{name:"United Arab Emirates",value:8441.537},{name:"Argentina",value:40374.224},{name:"Armenia",value:2963.496},{name:"French Southern and Antarctic Lands",value:268.065},{name:"Australia",value:22404.488},{name:"Austria",value:8401.924},{name:"Azerbaijan",value:9094.718},{name:"Burundi",value:9232.753},{name:"Belgium",value:10941.288},{name:"Benin",value:9509.798},{name:"Burkina Faso",value:15540.284},{name:"Bangladesh",value:151125.475},{name:"Bulgaria",value:7389.175},{name:"The Bahamas",value:66402.316},{name:"Bosnia and Herzegovina",value:3845.929},{name:"Belarus",value:9491.07},{name:"Belize",value:308.595},{name:"Bermuda",value:64.951},{name:"Bolivia",value:716.939},{name:"Brazil",value:195210.154},{name:"Brunei",value:27.223},{name:"Bhutan",value:716.939},{name:"Botswana",value:1969.341},{name:"Central African Rep.",value:4349.921},{name:"Canada",value:34126.24},{name:"Switzerland",value:7830.534},{name:"Chile",value:17150.76},{name:"China",value:1359821.465},{name:"Côte d'Ivoire",value:60508.978},{name:"Cameroon",value:20624.343},{name:"Dem. Rep. Congo",value:62191.161},{name:"Congo",value:3573.024},{name:"Colombia",value:46444.798},{name:"Costa Rica",value:4669.685},{name:"Cuba",value:11281.768},{name:"Northern Cyprus",value:1.468},{name:"Cyprus",value:1103.685},{name:"Czech Republic",value:10553.701},{name:"Germany",value:83017.404},{name:"Djibouti",value:834.036},{name:"Denmark",value:5550.959},{name:"Dominican Republic",value:10016.797},{name:"Algeria",value:37062.82},{name:"Ecuador",value:15001.072},{name:"Egypt",value:78075.705},{name:"Eritrea",value:5741.159},{name:"Spain",value:46182.038},{name:"Estonia",value:1298.533},{name:"Ethiopia",value:87095.281},{name:"Finland",value:5367.693},{name:"Fiji",value:860.559},{name:"Falkland Islands",value:49.581},{name:"France",value:63230.866},{name:"Gabon",value:1556.222},{name:"United Kingdom",value:62066.35},{name:"Georgia",value:4388.674},{name:"Ghana",value:24262.901},{name:"Eq. Guinea",value:10876.033},{name:"Guinea",value:10876.033},{name:"Gambia",value:1680.64},{name:"Guinea Bissau",value:10876.033},{name:"Equatorial Guinea",value:696.167},{name:"Greece",value:11109.999},{name:"Greenland",value:56.546},{name:"Guatemala",value:14341.576},{name:"French Guiana",value:231.169},{name:"Guyana",value:786.126},{name:"Honduras",value:7621.204},{name:"Croatia",value:4338.027},{name:"Haiti",value:9896.4},{name:"Hungary",value:10014.633},{name:"Indonesia",value:240676.485},{name:"India",value:1205624.648},{name:"Ireland",value:4467.561},{name:"Iran",value:240676.485},{name:"Iraq",value:30962.38},{name:"Iceland",value:318.042},{name:"Israel",value:7420.368},{name:"Italy",value:60508.978},{name:"Jamaica",value:2741.485},{name:"Jordan",value:6454.554},{name:"Japan",value:127352.833},{name:"Kazakhstan",value:15921.127},{name:"Kenya",value:40909.194},{name:"Kyrgyzstan",value:5334.223},{name:"Cambodia",value:14364.931},{name:"South Korea",value:51452.352},{name:"Kosovo",value:97.743},{name:"Kuwait",value:2991.58},{name:"Laos",value:6395.713},{name:"Lebanon",value:4341.092},{name:"Liberia",value:3957.99},{name:"Libya",value:6040.612},{name:"Sri Lanka",value:20758.779},{name:"Lesotho",value:2008.921},{name:"Lithuania",value:3068.457},{name:"Luxembourg",value:507.885},{name:"Latvia",value:2090.519},{name:"Morocco",value:31642.36},{name:"Moldova",value:103.619},{name:"Madagascar",value:21079.532},{name:"Mexico",value:117886.404},{name:"Macedonia",value:507.885},{name:"Mali",value:13985.961},{name:"Myanmar",value:51931.231},{name:"Montenegro",value:620.078},{name:"Mongolia",value:2712.738},{name:"Mozambique",value:23967.265},{name:"Mauritania",value:3609.42},{name:"Malawi",value:15013.694},{name:"Malaysia",value:28275.835},{name:"Namibia",value:2178.967},{name:"New Caledonia",value:246.379},{name:"Niger",value:15893.746},{name:"Nigeria",value:159707.78},{name:"Nicaragua",value:5822.209},{name:"Netherlands",value:16615.243},{name:"Norway",value:4891.251},{name:"Nepal",value:26846.016},{name:"New Zealand",value:4368.136},{name:"Oman",value:2802.768},{name:"Pakistan",value:173149.306},{name:"Panama",value:3678.128},{name:"Peru",value:29262.83},{name:"Philippines",value:93444.322},{name:"Papua New Guinea",value:6858.945},{name:"Poland",value:38198.754},{name:"Puerto Rico",value:3709.671},{name:"North Korea",value:1.468},{name:"Portugal",value:10589.792},{name:"Paraguay",value:6459.721},{name:"Qatar",value:1749.713},{name:"Romania",value:21861.476},{name:"Russia",value:21861.476},{name:"Rwanda",value:10836.732},{name:"Western Sahara",value:514.648},{name:"Saudi Arabia",value:27258.387},{name:"Sudan",value:35652.002},{name:"S. Sudan",value:9940.929},{name:"Senegal",value:12950.564},{name:"Solomon Islands",value:526.447},{name:"Sierra Leone",value:5751.976},{name:"El Salvador",value:6218.195},{name:"Somaliland",value:9636.173},{name:"Somalia",value:9636.173},{name:"Republic of Serbia",value:3573.024},{name:"Suriname",value:524.96},{name:"Slovakia",value:5433.437},{name:"Slovenia",value:2054.232},{name:"Sweden",value:9382.297},{name:"Swaziland",value:1193.148},{name:"Syria",value:7830.534},{name:"Chad",value:11720.781},{name:"Togo",value:6306.014},{name:"Thailand",value:66402.316},{name:"Tajikistan",value:7627.326},{name:"Turkmenistan",value:5041.995},{name:"East Timor",value:10016.797},{name:"Trinidad and Tobago",value:1328.095},{name:"Tunisia",value:10631.83},{name:"Turkey",value:72137.546},{name:"Tanzania",value:44973.33},{name:"Uganda",value:33987.213},{name:"Ukraine",value:46050.22},{name:"Uruguay",value:3371.982},{name:"United States",value:312247.116},{name:"Uzbekistan",value:27769.27},{name:"Venezuela",value:236.299},{name:"Vietnam",value:89047.397},{name:"Vanuatu",value:236.299},{name:"West Bank",value:13.565},{name:"Yemen",value:22763.008},{name:"South Africa",value:51452.352},{name:"Zambia",value:13216.985},{name:"Zimbabwe",value:13076.978}];a&&(e=utils.getData(a,"options"),t=window.echarts.init(a),echartSetOption(t,e,function(){return{tooltip:{trigger:"item",padding:[7,10],backgroundColor:utils.getGrays()[100],borderColor:utils.getGrays()[300],textStyle:{color:utils.getGrays()[1100]},borderWidth:1,transitionDuration:0,formatter:function(e){var t;return"<strong>".concat(null==(t=e.data)?void 0:t.name," :</strong> ").concat((100*(e.data&&e.data.value/6961500)).toFixed(2),"%")}},toolbox:{show:!1,feature:{restore:{}}},visualMap:{show:!1,min:800,max:5e4,inRange:{color:[utils.getColors().primary,utils.rgbaColor(utils.getColors().primary,.8),utils.rgbaColor(utils.getColors().primary,.6),utils.rgbaColor(utils.getColors().primary,.4),utils.rgbaColor(utils.getColors().primary,.2)].reverse()}},series:[{type:"map",map:"world",data:o,roam:!0,scaleLimit:{min:1,max:5},left:0,right:0,label:{show:!1},itemStyle:{borderColor:utils.getGrays()[300]},emphasis:{label:{show:!1},itemStyle:{areaColor:utils.getColor("warning")}}}]}}),null!=(a=document.querySelector(".session-by-country-map-reset")))&&a.addEventListener("click",function(){t.dispatchAction({type:"restore"})})},sessionByCountryChartInit=function(){var e,t=document.querySelector(".echart-session-by-country"),a=[["CHINA","INDIA","USA","IRAN","BRAZIL","PAKISTAN"],[19.53,17.32,4.49,3.46,2.8,1.7]];t&&(e=utils.getData(t,"options"),t=window.echarts.init(t),echartSetOption(t,e,function(){return{tooltip:{trigger:"axis",padding:[7,10],axisPointer:{type:"none"},backgroundColor:utils.getGrays()[100],borderColor:utils.getGrays()[300],textStyle:{color:utils.getGrays()[1100]},borderWidth:1,transitionDuration:0,position:function(e,t,a,o,n){return getPosition(e,t,a,o,n)}},xAxis:{type:"category",data:a[0],axisLabel:{color:utils.getGrays()[600],formatter:function(e){return e.substring(0,3)}},axisLine:{lineStyle:{color:utils.getGrays()[400]}},axisTick:{show:!0,alignWithLabel:!0,lineStyle:{color:utils.getGrays()[200]}}},yAxis:{type:"value",axisTick:{show:!1},splitLine:{lineStyle:{color:utils.getGrays()[300],type:"dashed"}},axisLabel:{color:utils.getGrays()[600],formatter:function(e){return"".concat(e,"%")},fontWeight:500,padding:[3,0,0,0],margin:12},axisLine:{show:!1}},series:[{type:"bar",data:a[1],itemStyle:{barBorderRadius:[3,3,0,0],color:utils.getColors().primary},barWidth:15}],grid:{right:"12px",left:"40px",bottom:"10%",top:"16px"}}}))},echartTicketPriority=function(){var e=document.querySelector('[data-list-pagination-chart="next"]'),t=document.querySelector('[data-list-pagination-chart="prev"]'),a=document.querySelector("[data-list-pagination-chart]");e&&e.addEventListener("click",function(){basicEchartsInit()}),t&&t.addEventListener("click",function(){basicEchartsInit()}),a&&a.addEventListener("click",function(e){"BUTTON"===e.target.tagName&&setTimeout(function(){basicEchartsInit()})})},ticketVolumeChartInit=function(){var e,t,a=document.querySelector(".echart-ticket-volume");a&&(e=utils.getData(a,"options"),t=window.echarts.init(a),a=document.querySelectorAll("[data-ticket-volume]"),echartSetOption(t,e,function(){return{color:[utils.getColors().primary,"dark"===utils.isDark()?"#235FAD":"#6AA2EC","dark"===utils.isDark()?"#1C4477":"#AACAF4","dark"===utils.isDark()?"#152C48":"#DFEBFB"],legend:{data:["On Hold Tickets","Open Tickets","Due Tickets","Unassigned Tickets"],show:!1},xAxis:{type:"category",data:utils.getPastDates(10),axisLine:{show:!1},splitLine:{lineStyle:{color:utils.getGrays()[300]}},axisTick:{show:!1},axisLabel:{color:utils.getGrays()[600],formatter:function(e){return window.dayjs(e).format("MMM DD")}}},yAxis:{type:"value",splitLine:{lineStyle:{color:utils.getGrays()[300]}},axisLine:{show:!1},axisTick:{show:!1},axisLabel:{show:!0,color:utils.getGrays()[600]}},tooltip:{trigger:"axis",padding:[7,10],axisPointer:{type:"none"},backgroundColor:utils.getGrays()[100],borderColor:utils.getGrays()[300],textStyle:{color:utils.getGrays()[1100]},borderWidth:1,transitionDuration:0,position:function(e,t,a,o,n){return getPosition(e,t,a,o,n)},formatter:tooltipFormatter},series:[{name:"On Hold Tickets",type:"bar",stack:"total",data:[8,6,5,12,9,6,9,6,4,7],emphasis:{itemStyle:{color:utils.getColor("primary")}}},{name:"Open Tickets",type:"bar",stack:"total",data:[15,10,7,7,5,6,15,10,7,12],emphasis:{itemStyle:{color:"dark"===utils.isDark()?"#2567BD":"#5595E9"}}},{name:"Due Tickets",type:"bar",stack:"total",data:[5,4,4,6,6,8,7,4,3,5],emphasis:{itemStyle:{color:"dark"===utils.isDark()?"#205396":"#7FB0EF"}}},{name:"Unassigned Tickets",type:"bar",stack:"total",data:[6,3,6,4,12,7,5,3,2,4],itemStyle:{barBorderRadius:[2,2,0,0]},emphasis:{itemStyle:{color:"dark"===utils.isDark()?"#1A3F6F":"#AACAF4"}}}],grid:{right:"0px",left:"23px",bottom:"6%",top:"10%"}}}),a.forEach(function(e){e.addEventListener("change",function(){t.dispatchAction({type:"legendToggleSelect",name:utils.getData(e,"ticket-volume")})})}))},topCustomersChartInit=function(){function o(e){return function(){return{color:utils.getGrays()[100],tooltip:{trigger:"item",padding:[7,10],backgroundColor:utils.getGrays()[100],borderColor:utils.getGrays()[300],textStyle:{color:utils.getGrays()[1100]},borderWidth:1,transitionDuration:0,position:function(e,t,a,o,n){return getPosition(e,t,a,o,n)},axisPointer:{type:"none"}},xAxis:{type:"category",data:r.hours,axisLabel:{color:utils.getGrays()[600],margin:15},axisLine:{lineStyle:{color:utils.getGrays()[300],type:"dashed"}},axisTick:{show:!1},boundaryGap:!1},yAxis:{type:"value",axisPointer:{show:!1},splitLine:{lineStyle:{color:utils.getGrays()[300],type:"dashed"}},boundaryGap:!1,axisLabel:{show:!0,color:utils.getGrays()[600],margin:25},axisTick:{show:!1},axisLine:{show:!1}},series:[{type:"line",data:e,symbol:"circle",symbolSize:10,itemStyle:{borderColor:utils.getColors().primary,borderWidth:2},lineStyle:{color:utils.getColors().primary},areaStyle:{color:{type:"linear",x:0,y:0,x2:0,y2:1,colorStops:[{offset:0,color:utils.rgbaColor(utils.getColors().primary,.1)},{offset:1,color:utils.rgbaColor(utils.getColors().primary,0)}]}}}],grid:{right:"12px",left:"46px",bottom:"12%",top:"3%"}}}}function n(e,t){var a=utils.getData(e,"options"),e=window.echarts.init(e);echartSetOption(e,a,t)}var r={hours:["1H","2H","3H","4H","5H","6H","7H","8H","9H","10H"],dataset:{monday:[[18,50,45,80,45,60,55,82,61,50]],tuesday:[[50,45,32,74,45,55,85,30,25,50]],wednesday:[[88,70,75,54,45,44,25,65,11,20]],thursday:[[20,30,40,50,70,80,85,40,30,20]],friday:[[18,50,45,75,45,80,85,65,61,50]],saturday:[[25,50,45,75,80,44,55,85,61,45]],sunday:[[11,50,45,78,45,54,80,90,50,65]]}},e=document.querySelector("#top-customers-chart-tab");e&&(n(document.querySelector(".echart-top-customers"),o(r.dataset.monday[0])),Array.from(e.querySelectorAll('[data-bs-toggle="tab"]')).forEach(function(a){a.addEventListener("shown.bs.tab",function(){var e=a.href.split("#").pop(),t=document.getElementById(e).querySelector(".echart-top-customers");n(t,o(r.dataset[e][0]))})}))},topProductsInit=function(){var e,t,a=document.querySelector(".echart-bar-top-products");a&&(e=[["product","2019","2018"],["Boots4",43,85],["Reign Pro",83,73],["Slick",86,62],["Falcon",72,53],["Sparrow",80,50],["Hideway",50,70],["Freya",80,90]],t=utils.getData(a,"options"),a=window.echarts.init(a),echartSetOption(a,t,function(){return{color:[utils.getColors().primary,utils.getGrays()[300]],dataset:{source:e},tooltip:{trigger:"item",padding:[7,10],backgroundColor:utils.getGrays()[100],borderColor:utils.getGrays()[300],textStyle:{color:utils.getGrays()[1100]},borderWidth:1,transitionDuration:0,position:function(e,t,a,o,n){return getPosition(e,t,a,o,n)},formatter:function(e){return'<div class="font-weight-semi-bold">'.concat(e.seriesName,'</div><div class="fs-10 text-600"><strong>').concat(e.name,":</strong> ").concat(e.value[e.componentIndex+1],"</div>")}},legend:{data:["2019","2018"],left:"left",itemWidth:10,itemHeight:10,borderRadius:0,icon:"circle",inactiveColor:utils.getGrays()[400],textStyle:{color:utils.getGrays()[700]}},xAxis:{type:"category",axisLabel:{color:utils.getGrays()[400]},axisLine:{lineStyle:{color:utils.getGrays()[300],type:"dashed"}},axisTick:!1,boundaryGap:!0},yAxis:{axisPointer:{type:"none"},axisTick:"none",splitLine:{lineStyle:{color:utils.getGrays()[300],type:"dashed"}},axisLine:{show:!1},axisLabel:{color:utils.getGrays()[400]}},series:[{type:"bar",barWidth:"10px",barGap:"30%",label:{normal:{show:!1}},z:10,itemStyle:{normal:{barBorderRadius:[10,10,0,0],color:utils.getColors().primary}}},{type:"bar",barWidth:"10px",barGap:"30%",label:{normal:{show:!1}},itemStyle:{normal:{barBorderRadius:[4,4,0,0],color:utils.getGrays()[300]}}}],grid:{right:"0",left:"30px",bottom:"10%",top:"20%"}}}))},totalOrderInit=function(){var e,t=document.querySelector(".echart-line-total-order");t&&(e=utils.getData(t,"options"),t=window.echarts.init(t),echartSetOption(t,e,function(){return{tooltip:{triggerOn:"mousemove",trigger:"axis",padding:[7,10],formatter:"{b0}: {c0}",backgroundColor:utils.getGrays()[100],borderColor:utils.getGrays()[300],textStyle:{color:utils.getColors().dark},borderWidth:1,transitionDuration:0,position:function(e,t,a,o,n){return getPosition(e,t,a,o,n)}},xAxis:{type:"category",data:["Week 4","Week 5","Week 6","Week 7"],boundaryGap:!1,splitLine:{show:!1},axisLine:{show:!1,lineStyle:{color:utils.getGrays()[300],type:"dashed"}},axisLabel:{show:!1},axisTick:{show:!1},axisPointer:{type:"none"}},yAxis:{type:"value",splitLine:{show:!1},axisLine:{show:!1},axisLabel:{show:!1},axisTick:{show:!1},axisPointer:{show:!1}},series:[{type:"line",lineStyle:{color:utils.getColors().primary,width:3},itemStyle:{color:utils.getColors().white,borderColor:utils.getColors().primary,borderWidth:2},hoverAnimation:!0,data:[20,40,100,120],smooth:.6,smoothMonotone:"x",showSymbol:!1,symbol:"circle",symbolSize:8,areaStyle:{color:{type:"linear",x:0,y:0,x2:0,y2:1,colorStops:[{offset:0,color:utils.rgbaColor(utils.getColors().primary,.25)},{offset:1,color:utils.rgbaColor(utils.getColors().primary,0)}]}}}],grid:{bottom:"2%",top:"0%",right:"10px",left:"10px"}}}))},totalSalesEcommerce=function(){var e,t,a,o,n=document.querySelector(".echart-line-total-sales-ecommerce"),r=["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"];n&&(e=utils.getData(n,"options"),t="#".concat(e.optionOne),a="#".concat(e.optionTwo),t=document.querySelector(t),a=document.querySelector(a),o=window.echarts.init(n),echartSetOption(o,e,function(){return{color:utils.getGrays()[100],tooltip:{trigger:"axis",padding:[7,10],backgroundColor:utils.getGrays()[100],borderColor:utils.getGrays()[300],textStyle:{color:utils.getGrays()[1100]},borderWidth:1,formatter:function(e){return e.map(function(e){var t=e.value,a=e.borderColor,e=e.seriesName;return'\n          <span class= "fas fa-circle" style="color: '.concat(a,"\"></span>\n          <span class='text-600'>").concat("lastMonth"===e?"Last Month":"Previous Year",": ").concat(t,"</span>\n        ")}).join("<br/>")},transitionDuration:0,position:function(e,t,a,o,n){return getPosition(e,t,a,o,n)}},legend:{data:["lastMonth","previousYear"],show:!1},xAxis:{type:"category",data:["2019-01-05","2019-01-06","2019-01-07","2019-01-08","2019-01-09","2019-01-10","2019-01-11","2019-01-12","2019-01-13","2019-01-14","2019-01-15","2019-01-16"],boundaryGap:!1,axisPointer:{lineStyle:{color:utils.getColor("gray-300"),type:"dashed"}},splitLine:{show:!1},axisLine:{lineStyle:{color:utils.rgbaColor("#000",.01),type:"dashed"}},axisTick:{show:!1},axisLabel:{color:utils.getColor("gray-400"),formatter:function(e){e=new Date(e);return"".concat(r[e.getMonth()]," ").concat(e.getDate())},margin:15}},yAxis:{type:"value",axisPointer:{show:!1},splitLine:{lineStyle:{color:utils.getColor("gray-300"),type:"dashed"}},boundaryGap:!1,axisLabel:{show:!0,color:utils.getColor("gray-400"),margin:15},axisTick:{show:!1},axisLine:{show:!1}},series:[{name:"lastMonth",type:"line",data:[50,80,60,80,65,90,130,90,30,40,30,70],lineStyle:{color:utils.getColor("primary")},itemStyle:{borderColor:utils.getColor("primary"),borderWidth:2},symbol:"circle",symbolSize:10,hoverAnimation:!0,areaStyle:{color:{type:"linear",x:0,y:0,x2:0,y2:1,colorStops:[{offset:0,color:utils.rgbaColor(utils.getColor("primary"),.2)},{offset:1,color:utils.rgbaColor(utils.getColor("primary"),0)}]}}},{name:"previousYear",type:"line",data:[110,30,40,50,80,70,50,40,110,90,60,60],lineStyle:{color:utils.rgbaColor(utils.getColor("warning"),.3)},itemStyle:{borderColor:utils.rgbaColor(utils.getColor("warning"),.6),borderWidth:2},symbol:"circle",symbolSize:10,hoverAnimation:!0}],grid:{right:"18px",left:"40px",bottom:"15%",top:"5%"}}}),t.addEventListener("click",function(){o.dispatchAction({type:"legendToggleSelect",name:"lastMonth"})}),a.addEventListener("click",function(){o.dispatchAction({type:"legendToggleSelect",name:"previousYear"})}))},totalSalesInit=function(){var e,t,o,a=document.querySelector(".echart-line-total-sales"),n=["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"];a&&(e=utils.getData(a,"options"),t=window.echarts.init(a),o=[[60,80,60,80,65,130,120,100,30,40,30,70],[100,70,80,50,120,100,130,140,90,100,40,50],[80,50,60,40,60,120,100,130,60,80,50,60],[70,80,100,70,90,60,80,130,40,60,50,80],[90,40,80,80,100,140,100,130,90,60,70,50],[80,60,80,60,40,100,120,100,30,40,30,70],[20,40,20,50,70,60,110,80,90,30,50,50],[60,70,30,40,80,140,80,140,120,130,100,110],[90,90,40,60,40,110,90,110,60,80,60,70],[50,80,50,80,50,80,120,80,50,120,110,110],[60,90,60,70,40,70,100,140,30,40,30,70],[20,40,20,50,30,80,120,100,30,40,30,70]],echartSetOption(t,e,function(){return{color:utils.getGrays()[100],tooltip:{trigger:"axis",padding:[7,10],backgroundColor:utils.getGrays()[100],borderColor:utils.getGrays()[300],textStyle:{color:utils.getGrays()[1100]},borderWidth:1,formatter:function(e){return t=(e=(e=e)[0]).name,e=e.value,t=new Date(t),"".concat(n[0]," ").concat(t.getDate(),", ").concat(e);var t},transitionDuration:0,position:function(e,t,a,o,n){return getPosition(e,t,a,o,n)}},xAxis:{type:"category",data:["2019-01-05","2019-01-06","2019-01-07","2019-01-08","2019-01-09","2019-01-10","2019-01-11","2019-01-12","2019-01-13","2019-01-14","2019-01-15","2019-01-16"],boundaryGap:!1,axisPointer:{lineStyle:{color:utils.getGrays()[300],type:"dashed"}},splitLine:{show:!1},axisLine:{lineStyle:{color:utils.rgbaColor("#000",.01),type:"dashed"}},axisTick:{show:!1},axisLabel:{color:utils.getGrays()[400],formatter:function(e){e=new Date(e);return"".concat(n[e.getMonth()]," ").concat(e.getDate())},margin:15}},yAxis:{type:"value",axisPointer:{show:!1},splitLine:{lineStyle:{color:utils.getGrays()[300],type:"dashed"}},boundaryGap:!1,axisLabel:{show:!0,color:utils.getGrays()[400],margin:15},axisTick:{show:!1},axisLine:{show:!1}},series:[{type:"line",data:o[0],lineStyle:{color:utils.getColors().primary},itemStyle:{borderColor:utils.getColors().primary,borderWidth:2},symbol:"circle",symbolSize:10,smooth:!1,hoverAnimation:!0,areaStyle:{color:{type:"linear",x:0,y:0,x2:0,y2:1,colorStops:[{offset:0,color:utils.rgbaColor(utils.getColors().primary,.2)},{offset:1,color:utils.rgbaColor(utils.getColors().primary,0)}]}}}],grid:{right:"28px",left:"40px",bottom:"15%",top:"5%"}}}),a=document.querySelector(".select-month"))&&a.addEventListener("change",function(e){var a=e.currentTarget.value;t.setOption({tooltip:{formatter:function(e){var e=e[0],t=e.name,e=e.value,t=new Date(t);return"".concat(n[a]," ").concat(t.getDate(),", ").concat(e)}},xAxis:{axisLabel:{formatter:function(e){e=new Date(e);return"".concat(n[a]," ").concat(e.getDate())},margin:15}},series:[{data:o[a]}]})})},trafficChannelChartInit=function(){var e,t=document.querySelector(".echart-traffic-channels");t&&(e=utils.getData(t,"options"),t=window.echarts.init(t),echartSetOption(t,e,function(){return{color:[utils.getColors().primary,utils.rgbaColor(utils.getColors().primary,.8),utils.rgbaColor(utils.getColors().primary,.6),utils.rgbaColor(utils.getColors().primary,.4),utils.rgbaColor(utils.getColors().primary,.2)],legend:{data:["Display","Direct","Organic Search","Paid Search","Other"],left:5,itemWidth:10,itemHeight:10,borderRadius:0,icon:"circle",inactiveColor:utils.getGrays()[400],textStyle:{color:utils.getGrays()[700]},itemGap:20},xAxis:{type:"category",data:utils.getPastDates(7).map(function(e){return window.dayjs(e).format("DD MMM, YYYY")}),axisLine:{show:!1},splitLine:{lineStyle:{color:utils.getGrays()[200]}},axisTick:{show:!1},axisLabel:{color:utils.getGrays()[600],formatter:function(e){return window.dayjs(e).format("ddd")}}},yAxis:{type:"value",position:"right",splitLine:{lineStyle:{color:utils.getGrays()[200]}},axisLine:{show:!1},axisTick:{show:!1},axisLabel:{show:!0,color:utils.getGrays()[600],margin:15}},tooltip:{trigger:"axis",padding:[7,10],axisPointer:{type:"none"},backgroundColor:utils.getGrays()[100],borderColor:utils.getGrays()[300],textStyle:{color:utils.getGrays()[1100]},borderWidth:1,transitionDuration:0,position:function(e,t,a,o,n){return getPosition(e,t,a,o,n)},formatter:tooltipFormatter},series:[{name:"Display",type:"bar",stack:"total",data:[320,302,301,334,390,330,320]},{name:"Direct",type:"bar",stack:"total",data:[120,132,101,134,90,230,210]},{name:"Organic Search",type:"bar",stack:"total",data:[220,182,191,234,290,330,310]},{name:"Paid Search",type:"bar",stack:"total",data:[150,212,201,154,190,330,410]},{name:"Other",type:"bar",stack:"total",data:[820,832,901,934,1290,1330,1320],itemStyle:{barBorderRadius:[5,5,0,0]}}],grid:{right:"50px",left:"0px",bottom:"10%",top:"15%"}}}))},echartsUnresolvedTicketsInit=function(){var e,t,a,o,n,r,i,l,s=document.querySelector(".echart-unresolved-tickets");s&&(e=utils.getData(s,"options"),t=window.echarts.init(s),s=document.querySelectorAll("[data-unresolved-tickets]"),a=["Mon","Tue","Wed","Thu","Fri","Sat","Sun"],o=[20,18,15,20,12,15,10],n=[30,20,20,25,20,15,10],r=[35,32,40,50,30,25,15],i=[15,25,20,18,10,15,25],l={itemStyle:{shadowColor:utils.rgbaColor(utils.getColor("dark"),.3)}},echartSetOption(t,e,function(){return{color:[utils.getColor("primary"),utils.getColor("info"),"dark"===utils.isDark()?"#229BD2":"#73D3FE","dark"===utils.isDark()?"#195979":"#A9E4FF"],tooltip:{trigger:"item",padding:[7,10],backgroundColor:utils.getGrays()[100],borderColor:utils.getGrays()[300],textStyle:{color:utils.getGrays()[900]},borderWidth:1,transitionDuration:0,axisPointer:{type:"none"}},legend:{data:["Urgent","High","Medium","Low"],show:!1},xAxis:{data:a,splitLine:{show:!1},splitArea:{show:!1},axisLabel:{color:utils.getGrays()[600],margin:8},axisLine:{lineStyle:{color:utils.getGrays()[300],type:"dashed"}},axisTick:{show:!1}},yAxis:{splitLine:{lineStyle:{color:utils.getGrays()[300],type:"dashed"}},axisLabel:{color:utils.getGrays()[600]},position:"right"},series:[{name:"Urgent",type:"bar",stack:"one",emphasis:l,data:o},{name:"High",type:"bar",stack:"one",emphasis:l,data:n},{name:"Medium",type:"bar",stack:"one",emphasis:l,data:r},{name:"Low",type:"bar",stack:"one",emphasis:l,data:i,itemStyle:{borderRadius:[2,2,0,0]}}],barWidth:"15px",grid:{top:"8%",bottom:10,left:0,right:2,containLabel:!0}}}),s.forEach(function(e){e.addEventListener("change",function(){t.dispatchAction({type:"legendToggleSelect",name:utils.getData(e,"unresolved-tickets")})})}))},userByLocationInit=function(){var e,t,a,o=document.querySelector(".echart-user-by-location-map"),n=[{name:"Afghanistan",value:28397},{name:"Angola",value:19549},{name:"Albania",value:3150},{name:"United Arab Emirates",value:8441},{name:"Argentina",value:40374},{name:"Armenia",value:2963},{name:"French Southern and Antarctic Lands",value:268},{name:"Australia",value:22404},{name:"Austria",value:8401},{name:"Azerbaijan",value:9094},{name:"Burundi",value:9232},{name:"Belgium",value:10941},{name:"Benin",value:9509},{name:"Burkina Faso",value:15540},{name:"Bangladesh",value:151125},{name:"Bulgaria",value:7389},{name:"The Bahamas",value:66402},{name:"Bosnia and Herzegovina",value:3845},{name:"Belarus",value:9491},{name:"Belize",value:308},{name:"Bermuda",value:64},{name:"Bolivia",value:716},{name:"Brazil",value:195210},{name:"Brunei",value:27},{name:"Bhutan",value:716},{name:"Botswana",value:1969},{name:"Central African Rep.",value:4349},{name:"Canada",value:34126},{name:"Switzerland",value:7830},{name:"Chile",value:17150},{name:"China",value:1359821},{name:"Côte d'Ivoire",value:60508},{name:"Cameroon",value:20624},{name:"Dem. Rep. Congo",value:62191},{name:"Congo",value:3573},{name:"Colombia",value:46444},{name:"Costa Rica",value:4669},{name:"Cuba",value:11281},{name:"Northern Cyprus",value:1},{name:"Cyprus",value:1103},{name:"Czech Republic",value:10553},{name:"Germany",value:83017},{name:"Djibouti",value:834},{name:"Denmark",value:5550},{name:"Dominican Republic",value:10016},{name:"Algeria",value:37062},{name:"Ecuador",value:15001},{name:"Egypt",value:78075},{name:"Eritrea",value:5741},{name:"Spain",value:46182},{name:"Estonia",value:1298},{name:"Ethiopia",value:87095},{name:"Finland",value:5367},{name:"Fiji",value:860},{name:"Falkland Islands",value:49},{name:"France",value:63230},{name:"Gabon",value:1556},{name:"United Kingdom",value:62066},{name:"Georgia",value:4388},{name:"Ghana",value:24262},{name:"Eq. Guinea",value:10876},{name:"Guinea",value:10876},{name:"Gambia",value:1680},{name:"Guinea Bissau",value:10876},{name:"Equatorial Guinea",value:696},{name:"Greece",value:11109},{name:"Greenland",value:56},{name:"Guatemala",value:14341},{name:"French Guiana",value:231},{name:"Guyana",value:786},{name:"Honduras",value:7621},{name:"Croatia",value:4338},{name:"Haiti",value:9896},{name:"Hungary",value:10014},{name:"Indonesia",value:240676},{name:"India",value:1205624},{name:"Ireland",value:4467},{name:"Iran",value:240676},{name:"Iraq",value:30962},{name:"Iceland",value:318},{name:"Israel",value:7420},{name:"Italy",value:60508},{name:"Jamaica",value:2741},{name:"Jordan",value:6454},{name:"Japan",value:127352},{name:"Kazakhstan",value:15921},{name:"Kenya",value:40909},{name:"Kyrgyzstan",value:5334},{name:"Cambodia",value:14364},{name:"South Korea",value:51452},{name:"Kosovo",value:97},{name:"Kuwait",value:2991},{name:"Laos",value:6395},{name:"Lebanon",value:4341},{name:"Liberia",value:3957},{name:"Libya",value:6040},{name:"Sri Lanka",value:20758},{name:"Lesotho",value:2008},{name:"Lithuania",value:3068},{name:"Luxembourg",value:507},{name:"Latvia",value:2090},{name:"Morocco",value:31642},{name:"Moldova",value:103},{name:"Madagascar",value:21079},{name:"Mexico",value:117886},{name:"Macedonia",value:507},{name:"Mali",value:13985},{name:"Myanmar",value:51931},{name:"Montenegro",value:620},{name:"Mongolia",value:2712},{name:"Mozambique",value:23967},{name:"Mauritania",value:3609},{name:"Malawi",value:15013},{name:"Malaysia",value:28275},{name:"Namibia",value:2178},{name:"New Caledonia",value:246},{name:"Niger",value:15893},{name:"Nigeria",value:159707},{name:"Nicaragua",value:5822},{name:"Netherlands",value:16615},{name:"Norway",value:4891},{name:"Nepal",value:26846},{name:"New Zealand",value:4368},{name:"Oman",value:2802},{name:"Pakistan",value:173149},{name:"Panama",value:3678},{name:"Peru",value:29262},{name:"Philippines",value:93444},{name:"Papua New Guinea",value:6858},{name:"Poland",value:38198},{name:"Puerto Rico",value:3709},{name:"North Korea",value:1},{name:"Portugal",value:10589},{name:"Paraguay",value:6459},{name:"Qatar",value:1749},{name:"Romania",value:21861},{name:"Russia",value:21861},{name:"Rwanda",value:10836},{name:"Western Sahara",value:514},{name:"Saudi Arabia",value:27258},{name:"Sudan",value:35652},{name:"S. Sudan",value:9940},{name:"Senegal",value:12950},{name:"Solomon Islands",value:526},{name:"Sierra Leone",value:5751},{name:"El Salvador",value:6218},{name:"Somaliland",value:9636},{name:"Somalia",value:9636},{name:"Republic of Serbia",value:3573},{name:"Suriname",value:524},{name:"Slovakia",value:5433},{name:"Slovenia",value:2054},{name:"Sweden",value:9382},{name:"Swaziland",value:1193},{name:"Syria",value:7830},{name:"Chad",value:11720},{name:"Togo",value:6306},{name:"Thailand",value:66402},{name:"Tajikistan",value:7627},{name:"Turkmenistan",value:5041},{name:"East Timor",value:10016},{name:"Trinidad and Tobago",value:1328},{name:"Tunisia",value:10631},{name:"Turkey",value:72137},{name:"Tanzania",value:44973},{name:"Uganda",value:33987},{name:"Ukraine",value:46050},{name:"Uruguay",value:3371},{name:"United States",value:2526},{name:"Uzbekistan",value:27769},{name:"Venezuela",value:236},{name:"Vietnam",value:89047},{name:"Vanuatu",value:236},{name:"West Bank",value:13},{name:"Yemen",value:22763},{name:"South Africa",value:51452},{name:"Zambia",value:13216},{name:"Zimbabwe",value:13076}];o&&(e=utils.getData(o,"options"),t=window.echarts.init(o),echartSetOption(t,e,function(){return{tooltip:{trigger:"item",padding:[7,10],backgroundColor:utils.getGrays()[100],borderColor:utils.getGrays()[300],textStyle:{color:utils.getGrays()[1100]},borderWidth:1,transitionDuration:0,formatter:function(e){var t;return"<strong>".concat(null==(t=e.data)?void 0:t.name," :</strong> ").concat(null==(t=e.data)?void 0:t.value)}},visualMap:{show:!1,min:800,max:5e4,inRange:{color:[utils.getColors().primary,utils.rgbaColor(utils.getColors().primary,.8),utils.rgbaColor(utils.getColors().primary,.6),utils.rgbaColor(utils.getColors().primary,.4),utils.rgbaColor(utils.getColors().primary,.2)].reverse()}},series:[{type:"map",map:"world",data:n,roam:"move",scaleLimit:{min:1,max:5},left:0,right:0,label:{show:!1},itemStyle:{borderColor:utils.getGrays()[300]},emphasis:{label:{show:!1},itemStyle:{areaColor:utils.getColor("warning")}}}]}}),a=1,null!=(o=document.querySelector(".user-by-location-map-zoom"))&&o.addEventListener("click",function(){a<5&&(a+=1),t.setOption({series:{zoom:a}})}),null!=(e=document.querySelector(".user-by-location-map-zoomOut")))&&e.addEventListener("click",function(){1<a&&--a,t.setOption({series:{zoom:a}})})},usersByTimeChartInit=function(){for(var e=document.querySelector(".echart-users-by-time"),t=["12 AM","1 AM","2 AM","3 AM","4 AM","5 AM","6 AM","7 AM","8 AM","9 AM","10 AM","11 AM","12 PM","1 PM","2 PM","3 PM","4 PM","5 PM","6 PM","7 PM","8 PM","9 PM","10 PM","11 PM"],a=[],o=0;o<24;o+=1)for(var n=0;n<7;n+=1)a.push([n,o,utils.getRandomNumber(20,300)]);function r(e){return"\n      <div>\n        <p class='mb-0 text-600'>".concat(window.dayjs(e.name).format("MMM DD, YYYY"),'</p>\n        <div class="d-flex align-items-center">\n          <p class="mb-0 text-600">\n            ').concat(window.dayjs().hour(e.data[1]).format("hA")," : <span class='text-800 fw-semi-bold'>").concat(e.data[2],"</span>\n          </p>\n        </div>\n      </div>\n  ")}var i;e&&(i=utils.getData(e,"options"),e=window.echarts.init(e),echartSetOption(e,i,function(){return{gradientColor:[utils.getColor("info"),utils.getColor("primary")],tooltip:{position:"top",padding:[7,10],backgroundColor:utils.getGrays()[100],borderColor:utils.getGrays()[300],textStyle:{color:utils.getGrays()[1100]},borderWidth:1,formatter:r},xAxis:{type:"category",data:utils.getPastDates(7),splitArea:{show:!0},axisTick:{show:!1},axisLabel:{color:utils.getGrays()[600],formatter:function(e){return window.dayjs(e).format("ddd")}},axisLine:{lineStyle:{color:utils.getGrays()[400]}}},yAxis:{position:"right",type:"category",inverse:!0,data:t,splitArea:{show:!0},axisTick:{show:!1},axisLabel:{color:utils.getGrays()[600],margin:20,padding:[10,0,0,0]},axisLine:{lineStyle:{color:utils.getGrays()[400]}}},visualMap:{type:"piecewise",orient:"horizontal",left:"left",bottom:"3%",itemSymbol:"diamond",itemWidth:"10px",itemHeight:"10px",min:20,max:300,splitNumber:4,textGap:5,textStyle:{color:utils.getGrays()[600],fontWeight:500}},series:[{name:"Users By Time",type:"heatmap",data:a,label:{show:!1},itemStyle:{borderColor:utils.getGrays()[100],borderWidth:3},emphasis:{itemStyle:{shadowBlur:3,shadowColor:utils.rgbaColor(utils.getColors().emphasis,.5)}}}],grid:{right:"60px",left:"0px",bottom:"20%",top:"0%"}}}))},weeklyGoalsInit=function(){var t,a,o,n=document.querySelector(".echart-weekly-goals-lms");n&&(t=utils.getData(n,"options"),a=window.echarts.init(n),o=function(){return{series:[{type:"gauge",startAngle:90,endAngle:-270,radius:"85%",pointer:{show:!1},center:["50%","50%"],progress:{show:!0,overlap:!1,roundCap:!0,clip:!1,itemStyle:{color:utils.getColor("info")}},axisLine:{lineStyle:{width:8,color:[[1,utils.getColor("gray-200")]]}},splitLine:{show:!1},axisTick:{show:!1},axisLabel:{show:!1},data:[79],detail:{show:!1},animationDuration:2e3},{type:"gauge",startAngle:90,endAngle:-270,radius:"70%",pointer:{show:!1},center:["50%","50%"],progress:{show:!0,overlap:!1,roundCap:!0,clip:!1,itemStyle:{color:utils.getColor("primary")}},axisLine:{lineStyle:{width:8,color:[[1,utils.getColor("gray-200")]]}},splitLine:{show:!1},axisTick:{show:!1},axisLabel:{show:!1},data:[85],detail:{show:!1},animationDuration:2e3},{type:"gauge",startAngle:90,endAngle:-270,radius:"55%",pointer:{show:!1},center:["50%","50%"],progress:{show:!0,overlap:!1,roundCap:!0,clip:!1,itemStyle:{color:utils.getColor("success")}},axisLine:{lineStyle:{width:8,color:[[1,utils.getColor("gray-200")]]}},splitLine:{show:!1},axisTick:{show:!1},axisLabel:{show:!1},data:[70],detail:{show:!1},animationDuration:2e3}]}},window.addEventListener("scroll",function e(){utils.isScrolledIntoView(n)&&(echartSetOption(a,t,o),window.removeEventListener("scroll",e))}))},weeklySalesInit=function(){var e,t,a=document.querySelector(".echart-bar-weekly-sales");a&&(e=utils.getData(a,"options"),t=[120,200,150,80,70,110,120],a=window.echarts.init(a),echartSetOption(a,e,function(){return{tooltip:{trigger:"axis",padding:[7,10],formatter:"{b0} : {c0}",transitionDuration:0,backgroundColor:utils.getGrays()[100],borderColor:utils.getGrays()[300],textStyle:{color:utils.getGrays()[1100]},borderWidth:1,position:function(e,t,a,o,n){return getPosition(e,t,a,o,n)}},xAxis:{type:"category",data:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],boundaryGap:!1,axisLine:{show:!1},axisLabel:{show:!1},axisTick:{show:!1},axisPointer:{type:"none"}},yAxis:{type:"value",splitLine:{show:!1},axisLine:{show:!1},axisLabel:{show:!1},axisTick:{show:!1},axisPointer:{type:"none"}},series:[{type:"bar",showBackground:!0,backgroundStyle:{borderRadius:10},barWidth:"5px",itemStyle:{barBorderRadius:10,color:utils.getColors().primary},data:t,z:10,emphasis:{itemStyle:{color:utils.getColors().primary}}}],grid:{right:5,left:10,top:0,bottom:0}}}))};docReady(detectorInit),docReady(handleNavbarVerticalCollapsed),docReady(totalOrderInit),docReady(weeklySalesInit),docReady(marketShareInit),docReady(totalSalesInit),docReady(topProductsInit),docReady(navbarTopDropShadow),docReady(tooltipInit),docReady(popoverInit),docReady(toastInit),docReady(progressAnimationToggle),docReady(glightboxInit),docReady(plyrInit),docReady(initMap),docReady(dropzoneInit),docReady(choicesInit),docReady(formValidationInit),docReady(barChartInit),docReady(leafletActiveUserInit),docReady(countupInit),docReady(copyLink),docReady(navbarDarkenOnScroll),docReady(typedTextInit),docReady(tinymceInit),docReady(chatInit),docReady(quantityInit),docReady(navbarComboInit),docReady(swiperInit),docReady(ratingInit),docReady(kanbanInit),docReady(fullCalendarInit),docReady(appCalendarInit),docReady(managementCalendarInit),docReady(lottieInit),docReady(wizardInit),docReady(searchInit),docReady(cookieNoticeInit),docReady(themeControl),docReady(dropdownOnHover),docReady(marketShareEcommerceInit),docReady(productShareDoughnutInit),docReady(totalSalesEcommerce),docReady(avgEnrollmentRateInit),docReady(bandwidthSavedInit),docReady(salesByPosLocationInit),docReady(returningCustomerRateInit),docReady(candleChartInit),docReady(grossRevenueChartInit),docReady(scrollbarInit),docReady(iconCopiedInit),docReady(reportForThisWeekInit),docReady(basicEchartsInit),docReady(chartScatter),docReady(chartDoughnut),docReady(chartPie),docReady(chartPolar),docReady(chartRadar),docReady(chartCombo),docReady(dropdownMenuInit),docReady(audienceChartInit),docReady(sessionByBrowserChartInit),docReady(sessionByCountryChartInit),docReady(activeUsersChartReportInit),docReady(trafficChannelChartInit),docReady(bounceRateChartInit),docReady(usersByTimeChartInit),docReady(sessionByCountryMapInit),docReady(mostLeadsInit),docReady(closedVsGoalInit),docReady(leadConversionInit),docReady(dealStorageFunnelInit),docReady(revenueChartInit),docReady(locationBySessionInit),docReady(realTimeUsersChartInit),docReady(linePaymentChartInit),docReady(chartBubble),docReady(chartLine),docReady(treeviewInit),docReady(scrollInit),docReady(echartsUnresolvedTicketsInit),docReady(echartsNumberOfTicketsInit),docReady(echartsCustomerSatisfactionInit),docReady(echartsDistributionOfPerformanceInit),docReady(echartsSatisfactionSurveyInit),docReady(echartsReceivedTicketsInit),docReady(topCustomersChartInit),docReady(ticketVolumeChartInit),docReady(echartTicketPriority),docReady(userByLocationInit),docReady(courseEnrollmentsInit),docReady(weeklyGoalsInit),docReady(assignmentScoresInit),docReady(browsedCoursesInit),docReady(courseStatusInit),docReady(bottomBarInit),docReady(marketingExpensesInit),docReady(chartHalfDoughnutInit),docReady(trendingKeywordsInit),docReady(D3PackedBubbleInit),docReady(dataTablesInit),docReady(select2Init),docReady(hideOnCollapseInit),docReady(unresolvedTicketsTabInit),docReady(inputmaskInit),docReady(emojiMartInit),docReady(nouisliderInit),docReady(bulkSelectInit),docReady(advanceAjaxTableInit),docReady(listInit),docReady(sortableInit);
//# sourceMappingURL=theme.min.js.map
