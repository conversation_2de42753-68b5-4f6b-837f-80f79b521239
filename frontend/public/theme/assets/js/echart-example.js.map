{"version": 3, "sources": ["utils.js", "echarts-utils.js", "area-pieces-chart.js", "bar-line-mixed-chart.js", "bar-negative-chart.js", "bar-race-chart.js", "bar-series-chart.js", "bar-stacked-chart.js", "bar-timeline-chart.js", "bar-waterfall-chart.js", "basic-bar-chart.js", "basic-candlestick-chart.js", "basic-gauge-chart.js", "basic-line-chart.js", "bubble-chart.js", "candle-stick-mixed-chart.js", "doughnut-chart.js", "doughnut-rounded-chart.js", "dynamic-line-chart.js", "gauge-grade-chart.js", "gauge-multi-ring-chart.js", "gauge-multi-title-chart.js", "gauge-progress-chart.js", "gauge-ring-chart.js", "gradient-bar-chart.js", "heatmap-chart.js", "heatmap-single-series-chart.js", "horizontal-bar-chart.js", "line-area-chart.js", "line-gradient-chart.js", "line-log-chart.js", "line-marker-chart.js", "line-race-chart.js", "line-share-dataset-chart.js", "map-usa.js", "nested-pies-chart.js", "pie-chart.js", "pie-edge-align-chart.js", "pie-label-align-chart.js", "pie-multiple-chart.js", "radar-chart.js", "radar-customized-chart.js", "radar-multiple-chart.js", "scatter-basic-chart.js", "scatter-quartet.js", "scatter-single-axis-chart.js", "stacked-area-chart.js", "stacked-horizontal-bar-chart.js", "stacked-line-chart.js", "stacked-vertical-chart.js", "step-line-chart.js", "echarts-example.js"], "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "fn", "document", "readyState", "addEventListener", "setTimeout", "resize", "window", "isIterableArray", "array", "Array", "isArray", "length", "camelize", "str", "text", "replace", "match", "capture", "toUpperCase", "concat", "substr", "toLowerCase", "getData", "el", "data", "JSON", "parse", "dataset", "e", "hexToRgb", "hexValue", "hex", "indexOf", "substring", "result", "exec", "m", "r", "g", "b", "parseInt", "rgbaColor", "color", "arguments", "undefined", "alpha", "getColor", "name", "dom", "documentElement", "getComputedStyle", "getPropertyValue", "trim", "getColors", "primary", "secondary", "success", "info", "warning", "danger", "light", "dark", "white", "black", "emphasis", "getSubtleColors", "<PERSON><PERSON><PERSON><PERSON>", "100", "200", "300", "400", "500", "600", "700", "800", "900", "1000", "1100", "hasClass", "className", "classList", "value", "includes", "addClass", "add", "removeClass", "remove", "getOffset", "rect", "getBoundingClientRect", "scrollLeft", "pageXOffset", "scrollTop", "pageYOffset", "top", "left", "isScrolledIntoView", "windowHeight", "innerHeight", "clientHeight", "windowWidth", "innerWidth", "clientWidth", "vertInView", "height", "horInView", "width", "breakpoints", "xs", "sm", "md", "lg", "xl", "xxl", "getBreakpoint", "breakpoint", "classes", "split", "filter", "cls", "pop", "getSystemTheme", "matchMedia", "matches", "isDark", "localStorage", "getItem", "<PERSON><PERSON><PERSON><PERSON>", "expire", "expires", "Date", "setTime", "getTime", "cookie", "toUTCString", "<PERSON><PERSON><PERSON><PERSON>", "keyValue", "settings", "<PERSON><PERSON><PERSON>", "theme", "chart", "borderColor", "new<PERSON>hart", "config", "ctx", "getContext", "Chart", "getItemFromStore", "key", "defaultValue", "store", "_unused", "setItemToStore", "payload", "setItem", "getStoreSpace", "parseFloat", "escape", "encodeURIComponent", "stringify", "toFixed", "getDates", "startDate", "endDate", "interval", "from", "v", "i", "valueOf", "getPastDates", "duration", "days", "date", "setDate", "getDate", "getRandomNumber", "min", "max", "Math", "floor", "random", "utils", "getPosition", "pos", "params", "size", "contentSize", "echartSetOption", "userOptions", "getDefaultOptions", "themeController", "body", "setOption", "_", "merge", "_ref", "detail", "control", "tooltipFormatter", "tooltipItem", "for<PERSON>ach", "seriesName", "_typeof", "dayjs", "axisValue", "<PERSON><PERSON><PERSON><PERSON>", "format", "echartsAreaPiecesChartInit", "$areaPiecesChartEl", "querySelector", "echarts", "init", "tooltip", "trigger", "padding", "backgroundColor", "textStyle", "borderWidth", "transitionDuration", "position", "axisPointer", "type", "formatter", "xAxis", "boundaryGap", "axisLine", "lineStyle", "axisTick", "show", "axisLabel", "margin", "splitLine", "yAxis", "visualMap", "dimension", "seriesIndex", "pieces", "gt", "lt", "series", "smooth", "symbol", "markLine", "label", "areaStyle", "grid", "right", "bottom", "containLabel", "echartsBarLineChartInit", "months", "$barLineChartEl", "crossStyle", "toolbox", "feature", "dataView", "magicType", "restore", "saveAsImage", "iconStyle", "textFill", "legend", "slice", "itemStyle", "barBorderRadius", "yAxisIndex", "symbolSize", "echartsBarNegativeChartInit", "$barNegativeChartEl", "stack", "echartsBarRaceChartInit", "run", "$barRaceChartEl", "keys", "map", "round", "inverse", "animationDuration", "animationDurationUpdate", "realtimeSort", "fontWeight", "valueAnimation", "animationEasing", "animationEasingUpdate", "item", "setInterval", "echartsBarSeriesChartInit", "$barSeriesChartEl", "echartsBarStackedChartInit", "$barStackedChartEl", "xAxisData", "data1", "data2", "data3", "data4", "push", "emphasisStyle", "<PERSON><PERSON><PERSON><PERSON>", "shadowColor", "splitArea", "echartsBarTimelineChartInit", "dataMap", "dataFormatter", "$barTimelineChartEl", "dataTI", "obj", "Object", "reduce", "acc", "val", "_objectSpread", "_defineProperty", "index", "2005", "2006", "2007", "2008", "2009", "2010", "2011", "dataSI", "dataPI", "baseOption", "timeline", "axisType", "autoPlay", "playInterval", "s", "getFullYear", "checkpointStyle", "shadowOffsetX", "shadowOffsetY", "controlStyle", "title", "calculable", "options", "echartsWaterFallChartInit", "$waterfallChartEl", "tar", "barBorderColor", "echartsBasicBarChartInit", "$barChartEl", "showSymbol", "hoverAnimation", "echartsBasicCandlestickChartInit", "$basicCandleStickChartEl", "dataZoom", "start", "end", "minValueSpan", "scale", "splitNumber", "color0", "borderColor0", "echartsBasicGaugeChartInit", "$basicGaugeChartEl", "radius", "echartsLineChartInit", "$lineChartEl", "echartsBubbleChartInit", "$bubbleChartEl", "sqrt", "focus", "param", "echartsCandlestickMixedChartInit", "colorList", "dates", "dataMA5", "$candleStickMixedChartEl", "dayCount", "sum", "j", "animation", "elRect", "viewSize", "link", "xAxisIndex", "realtime", "handleIcon", "handleSize", "gridIndex", "triggerTooltip", "echartsDoughnutChartInit", "$doughnutChartEl", "center", "avoidLabelOverlap", "labelLine", "echartsDoughnutRoundedChartInit", "$doughnutRoundedChartEl", "orient", "borderRadius", "echartsDynamicLineChartInit", "$dynamicLineChartEl", "now", "randomData", "toString", "getMonth", "join", "shift", "echartsGaugeGradeChartInit", "$gaugeGradeChartEl", "startAngle", "endAngle", "pointer", "icon", "offsetCenter", "distance", "echartsGaugeMultiRingChartInit", "$gaugeMultiRingChartEl", "progress", "overlap", "roundCap", "clip", "echartsGaugeMultiTitleChartInit", "$gaugeMultiTitleChartEl", "anchor", "showAbove", "fontSize", "echartsGaugeProgressChartInit", "$gaugeProgressChartEl", "echartsGaugeRingChartInit", "$gaugeRingChartEl", "echartsGradientBarChartInit", "dataAxis", "$gradientBarChartEl", "inside", "z", "showBackground", "graphic", "LinearGradient", "offset", "on", "dispatchAction", "startValue", "dataIndex", "zoomSize", "endValue", "echartsHeatMapChartInit", "$echartHeatmapChart", "hours", "inRange", "echartsHeatMapSingleSeriesChartInit", "gradientColor", "echartsHorizontalBarChartInit", "$horizontalBarChartEl", "echartsLineAreaChartInit", "$lineAreaChartEl", "x", "y", "x2", "y2", "colorStops", "echartsLineGradientChartInit", "dateList", "valueList", "$lineGradientChartEl", "echartsLineLogChartInit", "$lineLogChartEl", "echartsLineMarkerChartInit", "$lineMarkerChartEl", "markPoint", "echartsLineRaceChartInit", "$lineRaceChartEl", "echartsLineShareDatasetChartInit", "$lineShareChartEl", "showContent", "source", "seriesLayoutBy", "id", "encode", "itemName", "event", "xAxisInfo", "axesInfo", "echartsUsaMapInit", "$usaMapEl", "zoom", "roam", "scaleLimit", "areaColor", "echartsNestedPiesChartInit", "marketingExpenses", "detailedExpenses", "$echartsNestedPies", "rich", "per", "selectedMode", "<PERSON><PERSON><PERSON><PERSON>", "lineHeight", "initChart", "removeEventListener", "echartsPieChartInit", "$pieChartEl", "echartsPieEdgeAlignChartInit", "$echartPieAEdgeAlignChartEl", "subtext", "textAlign", "subtextStyle", "alignTo", "echartsPieLabelAlignChartInit", "$echartPieLabelAlignChartEl", "<PERSON><PERSON><PERSON><PERSON>", "defaultRadius", "smallRadius", "echartsPieMultipleChartInit", "$echartPieMultipleChartEl", "echartsRadarChartInit", "$radarChartEl", "radar", "indicator", "echartsRadarCustomizedChartInit", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "indicators", "num", "shape", "radarIndex", "echartsRadarMultipleChartInit", "getCenter", "month", "echartsScatterBasicChartInit", "$basicScatterChartEl", "echartsScatterQuartetChartInit", "dataAll", "markLineOpt", "gridMdUp", "gridMdDown", "$scatterQuartetChartEl", "align", "coord", "echartsScatterSingleAxisChartInit", "$scatterSingleAxisChartEl", "animationDelay", "idx", "echartsStackedAreaChartInit", "$stackedAreaChartEl", "echartsHorizontalStackedChartInit", "$horizontalStackChartEl", "echartsStackedLineChartInit", "$stackedLineChartEl", "echartsStackedVerticalChartInit", "$stackedVerticalChart", "echartsStepLineChartInit", "$stepLineChartEl", "step"], "mappings": "m2CAGA,IAAAA,SAAA,SAAAC,GAEA,YAAAC,SAAAC,WACAD,SAAAE,iBAAA,mBAAAH,CAAA,EAEAI,WAAAJ,EAAA,CAAA,CAEA,EAEAK,OAAA,SAAAL,GAAA,OAAAM,OAAAH,iBAAA,SAAAH,CAAA,CAAA,EAEAO,gBAAA,SAAAC,GAAA,OAAAC,MAAAC,QAAAF,CAAA,GAAA,CAAA,CAAAA,EAAAG,MAAA,EAEAC,SAAA,SAAAC,GACAC,EAAAD,EAAAE,QAAA,gBAAA,SAAAC,EAAAC,GACA,OAAAA,EACAA,EAAAC,YAAA,EAEA,EACA,CAAA,EACA,MAAA,GAAAC,OAAAL,EAAAM,OAAA,EAAA,CAAA,EAAAC,YAAA,CAAA,EAAAF,OAAAL,EAAAM,OAAA,CAAA,CAAA,CACA,EAEAE,QAAA,SAAAC,EAAAC,GACA,IACA,OAAAC,KAAAC,MAAAH,EAAAI,QAAAf,SAAAY,CAAA,EAAA,CAGA,CAFA,MAAAI,GACA,OAAAL,EAAAI,QAAAf,SAAAY,CAAA,EACA,CACA,EAIAK,SAAA,SAAAC,GAEAC,EAAA,IAAAD,EAAAE,QAAA,GAAA,EAAAF,EAAAG,UAAA,CAAA,EAAAH,EAGAI,EAAA,4CAAAC,KACAJ,EAAAhB,QAFA,mCAEA,SAAAqB,EAAAC,EAAAC,EAAAC,GAAA,OAAAF,EAAAA,EAAAC,EAAAA,EAAAC,EAAAA,CAAA,CAAA,CACA,EACA,OAAAL,EACA,CAAAM,SAAAN,EAAA,GAAA,EAAA,EAAAM,SAAAN,EAAA,GAAA,EAAA,EAAAM,SAAAN,EAAA,GAAA,EAAA,GACA,IACA,EAEAO,UAAA,WAAA,IAAAC,EAAA,EAAAC,UAAAhC,QAAAiC,KAAAA,IAAAD,UAAA,GAAAA,UAAA,GAAA,OAAAE,EAAA,EAAAF,UAAAhC,QAAAiC,KAAAA,IAAAD,UAAA,GAAAA,UAAA,GAAA,GAAA,MAAA,QAAAxB,OAAAU,SAAAa,CAAA,EAAA,IAAA,EAAAvB,OAAA0B,EAAA,GAAA,CAAA,EAIAC,SAAA,SAAAC,GAAA,IAAAC,EAAA,EAAAL,UAAAhC,QAAAiC,KAAAA,IAAAD,UAAA,GAAAA,UAAA,GAAA1C,SAAAgD,gBAAA,OACAC,iBAAAF,CAAA,EAAAG,iBAAA,YAAAhC,OAAA4B,CAAA,CAAA,EAAAK,KAAA,CAAA,EAEAC,UAAA,SAAAL,GAAA,MAAA,CACAM,QAAAR,SAAA,UAAAE,CAAA,EACAO,UAAAT,SAAA,YAAAE,CAAA,EACAQ,QAAAV,SAAA,UAAAE,CAAA,EACAS,KAAAX,SAAA,OAAAE,CAAA,EACAU,QAAAZ,SAAA,UAAAE,CAAA,EACAW,OAAAb,SAAA,SAAAE,CAAA,EACAY,MAAAd,SAAA,QAAAE,CAAA,EACAa,KAAAf,SAAA,OAAAE,CAAA,EACAc,MAAAhB,SAAA,QAAAE,CAAA,EACAe,MAAAjB,SAAA,QAAAE,CAAA,EACAgB,SAAAlB,SAAA,iBAAAE,CAAA,CACA,CAAA,EAEAiB,gBAAA,SAAAjB,GAAA,MAAA,CACAM,QAAAR,SAAA,oBAAAE,CAAA,EACAO,UAAAT,SAAA,sBAAAE,CAAA,EACAQ,QAAAV,SAAA,oBAAAE,CAAA,EACAS,KAAAX,SAAA,iBAAAE,CAAA,EACAU,QAAAZ,SAAA,oBAAAE,CAAA,EACAW,OAAAb,SAAA,mBAAAE,CAAA,EACAY,MAAAd,SAAA,kBAAAE,CAAA,EACAa,KAAAf,SAAA,iBAAAE,CAAA,CACA,CAAA,EAEAkB,SAAA,SAAAlB,GAAA,MAAA,CACAmB,IAAArB,SAAA,WAAAE,CAAA,EACAoB,IAAAtB,SAAA,WAAAE,CAAA,EACAqB,IAAAvB,SAAA,WAAAE,CAAA,EACAsB,IAAAxB,SAAA,WAAAE,CAAA,EACAuB,IAAAzB,SAAA,WAAAE,CAAA,EACAwB,IAAA1B,SAAA,WAAAE,CAAA,EACAyB,IAAA3B,SAAA,WAAAE,CAAA,EACA0B,IAAA5B,SAAA,WAAAE,CAAA,EACA2B,IAAA7B,SAAA,WAAAE,CAAA,EACA4B,IAAA9B,SAAA,YAAAE,CAAA,EACA6B,KAAA/B,SAAA,YAAAE,CAAA,CACA,CAAA,EAEA8B,SAAA,SAAAvD,EAAAwD,GAEA,OAAAxD,EAAAyD,UAAAC,MAAAC,SAAAH,CAAA,CACA,EAEAI,SAAA,SAAA5D,EAAAwD,GACAxD,EAAAyD,UAAAI,IAAAL,CAAA,CACA,EAEAM,YAAA,SAAA9D,EAAAwD,GACAxD,EAAAyD,UAAAM,OAAAP,CAAA,CACA,EAEAQ,UAAA,SAAAhE,GACA,IAAAiE,EAAAjE,EAAAkE,sBAAA,EACAC,EAAApF,OAAAqF,aAAA1F,SAAAgD,gBAAAyC,WACAE,EAAAtF,OAAAuF,aAAA5F,SAAAgD,gBAAA2C,UACA,MAAA,CAAAE,IAAAN,EAAAM,IAAAF,EAAAG,KAAAP,EAAAO,KAAAL,CAAA,CACA,EAEA,SAAAM,mBAAAzE,GACA,IAAAiE,EAAAjE,EAAAkE,sBAAA,EACAQ,EAAA3F,OAAA4F,aAAAjG,SAAAgD,gBAAAkD,aACAC,EAAA9F,OAAA+F,YAAApG,SAAAgD,gBAAAqD,YAEAC,EAAAf,EAAAM,KAAAG,GAAA,GAAAT,EAAAM,IAAAN,EAAAgB,OACAC,EAAAjB,EAAAO,MAAAK,GAAA,GAAAZ,EAAAO,KAAAP,EAAAkB,MAEA,OAAAH,GAAAE,CACA,CAEA,IAAAE,YAAA,CACAC,GAAA,EACAC,GAAA,IACAC,GAAA,IACAC,GAAA,IACAC,GAAA,KACAC,IAAA,IACA,EAEAC,cAAA,SAAA3F,GACA,IACA4F,EADAC,EAAA7F,GAAAA,EAAAyD,UAAAC,MAaA,OAVAkC,EADAC,EAEAT,YACAS,EACAC,MAAA,GAAA,EACAC,OAAA,SAAAC,GAAA,OAAAA,EAAArC,SAAA,gBAAA,CAAA,CAAA,EACAsC,IAAA,EACAH,MAAA,GAAA,EACAG,IAAA,GAGAL,CACA,EAEAM,eAAA,WAAA,OAAAnH,OAAAoH,WAAA,8BAAA,EAAAC,QAAA,OAAA,OAAA,EAEAC,OAAA,WAAA,MAAA,SAAAC,aAAAC,QAAA,OAAA,EAAAL,eAAA,EAAAI,aAAAC,QAAA,OAAA,CAAA,EAGAC,UAAA,SAAAhF,EAAAkC,EAAA+C,GACA,IAAAC,EAAA,IAAAC,KACAD,EAAAE,QAAAF,EAAAG,QAAA,EAAAJ,CAAA,EACA/H,SAAAoI,OAAA,GAAAlH,OAAA4B,EAAA,GAAA,EAAA5B,OAAA8D,EAAA,WAAA,EAAA9D,OAAA8G,EAAAK,YAAA,CAAA,CACA,EAEAC,UAAA,SAAAxF,GACAyF,EAAAvI,SAAAoI,OAAArH,MAAA,UAAAG,OAAA4B,EAAA,eAAA,CAAA,EACA,OAAAyF,GAAAA,EAAA,EACA,EAEAC,SAAA,CACAC,QAAA,CACAC,MAAA,OACA,EACAC,MAAA,CACAC,YAAA,0BACA,CACA,EAIAC,SAAA,SAAAF,EAAAG,GACAC,EAAAJ,EAAAK,WAAA,IAAA,EACA,OAAA,IAAA3I,OAAA4I,MAAAF,EAAAD,CAAA,CACA,EAIAI,iBAAA,SAAAC,EAAAC,GAAA,IAAAC,EAAA,EAAA3G,UAAAhC,QAAAiC,KAAAA,IAAAD,UAAA,GAAAA,UAAA,GAAAkF,aACA,IACA,OAAApG,KAAAC,MAAA4H,EAAAxB,QAAAsB,CAAA,CAAA,GAAAC,CAGA,CAFA,MAAAE,GACA,OAAAD,EAAAxB,QAAAsB,CAAA,GAAAC,CACA,CACA,EAEAG,eAAA,SAAAJ,EAAAK,GAAA,OAAA,EAAA9G,UAAAhC,QAAAiC,KAAAA,IAAAD,UAAA,GAAAA,UAAA,GAAAkF,cAAA6B,QAAAN,EAAAK,CAAA,CAAA,EACAE,cAAA,WAAA,IAAAL,EAAA,EAAA3G,UAAAhC,QAAAiC,KAAAA,IAAAD,UAAA,GAAAA,UAAA,GAAAkF,aAAA,OACA+B,YAAAC,OAAAC,mBAAArI,KAAAsI,UAAAT,CAAA,CAAA,CAAA,EAAA3I,OAAA,SAAAqJ,QAAA,CAAA,CAAA,CAAA,EAIAC,SAAA,SAAAC,EAAAC,GAAA,IAAAC,EAAA,EAAAzH,UAAAhC,QAAAiC,KAAAA,IAAAD,UAAA,GAAAA,UAAA,GAAA,MAGA,OAAAlC,MAAA4J,KAAA,CAAA1J,OAAA,GAFAwJ,EAAAD,GACAE,CACA,EAAA,SAAAE,EAAAC,GAAA,OAAA,IAAArC,KAAAgC,EAAAM,QAAA,EAAAJ,EAAAG,CAAA,CAAA,CAAA,CACA,EAEAE,aAAA,SAAAC,GACA,IAAAC,EAEA,OAAAD,GACA,IAAA,OACAC,EAAA,EACA,MACA,IAAA,QACAA,EAAA,GACA,MACA,IAAA,OACAA,EAAA,IACA,MAEA,QACAA,EAAAD,CACA,CAEA,IAAAE,EAAA,IAAA1C,KACAiC,EAAAS,EACAV,EAAA,IAAAhC,MAAA,IAAAA,MAAA2C,QAAAD,EAAAE,QAAA,GAAAH,EAAA,EAAA,CAAA,EACA,OAAAV,SAAAC,EAAAC,CAAA,CACA,EAGAY,gBAAA,SAAAC,EAAAC,GAAA,OAAAC,KAAAC,MAAAD,KAAAE,OAAA,GAAAH,EAAAD,GAAAA,CAAA,CAAA,EAEAK,MAAA,CACAtL,SAAAA,SACA4G,YAAAA,YACAtG,OAAAA,OACAE,gBAAAA,gBACAK,SAAAA,SACAU,QAAAA,QACAwD,SAAAA,SACAK,SAAAA,SACAtD,SAAAA,SACAY,UAAAA,UACAK,SAAAA,SACAO,UAAAA,UACAY,gBAAAA,gBACAC,SAAAA,SACAqB,UAAAA,UACAS,mBAAAA,mBACAkB,cAAAA,cACAa,UAAAA,UACAQ,UAAAA,UACAO,SAAAA,SACAL,SAAAA,SACAU,iBAAAA,iBACAK,eAAAA,eACAG,cAAAA,cACAM,SAAAA,SACAQ,aAAAA,aACAM,gBAAAA,gBACA1F,YAAAA,YACAoC,eAAAA,eACAG,OAAAA,MACA,ECxQA0D,YAAA,SAAAC,EAAAC,EAAAxI,EAAAwC,EAAAiG,GAAA,MAAA,CACA3F,IAAAyF,EAAA,GAAAE,EAAAC,YAAA,GAAA,GACA3F,KAAAwF,EAAA,GAAAE,EAAAC,YAAA,GAAA,CACA,CAAA,EAEAC,gBAAA,SAAA/C,EAAAgD,EAAAC,GACA,IAAAC,EAAA7L,SAAA8L,KAEAnD,EAAAoD,UAAA1L,OAAA2L,EAAAC,MAAAL,EAAA,EAAAD,CAAA,CAAA,EAEAE,EAAA3L,iBAAA,eAAA,SAAAgM,GACA,UADAA,EAAAC,OAAAC,SAEAzD,EAAAoD,UAAA1L,OAAA2L,EAAAC,MAAAL,EAAA,EAAAD,CAAA,CAAA,CAEA,CAAA,CACA,EAEAU,iBAAA,SAAAd,GACA,IAAAe,EAAA,GASA,OARAf,EAAAgB,QAAA,SAAAjL,GACAgL,GAAA,sHAAApL,OAEAI,EAAAsH,aAAAtH,EAAAmB,MAAA,uBAAA,EAAAvB,OACAI,EAAAkL,WAAA,KAAA,EAAAtL,OAAA,WAAAuL,QAAAnL,EAAA0D,KAAA,EAAA1D,EAAA0D,MAAA,GAAA1D,EAAA0D,MAAA,+BAAA,CAGA,CAAA,EACA,yDAAA9D,OAGAb,OAAAqM,MAAAnB,EAAA,GAAAoB,SAAA,EAAAC,QAAA,EAAAvM,OAAAqM,MAAAnB,EAAA,GAAAoB,SAAA,EAAAE,OAAA,SAAA,EAAAtB,EAAA,GAAAoB,UAAA,sBAAA,EAAAzL,OAEAoL,EAAA,cAAA,CAEA,EC3BAQ,2BAAA,WACA,IAIAnB,EAJAoB,EAAA/M,SAAAgN,cAAA,mCAAA,EAEAD,IAEApB,EAAAP,MAAA/J,QAAA0L,EAAA,SAAA,EACApE,EAAAtI,OAAA4M,QAAAC,KAAAH,CAAA,EAwGArB,gBAAA/C,EAAAgD,EAtGA,WAAA,MAAA,CACAwB,QAAA,CACAC,QAAA,OACAC,QAAA,CAAA,EAAA,IACAC,gBAAAlC,MAAAnH,SAAA,EAAA,KACA2E,YAAAwC,MAAAnH,SAAA,EAAA,KACAsJ,UAAA,CAAA9K,MAAA2I,MAAAnH,SAAA,EAAA,KAAA,EACAuJ,YAAA,EACAC,mBAAA,EACAC,SAAA,SAAApC,EAAAC,EAAAxI,EAAAwC,EAAAiG,GACA,OAAAH,YAAAC,EAAAC,EAAAxI,EAAAwC,EAAAiG,CAAA,CACA,EACAmC,YAAA,CACAC,KAAA,MACA,EACAC,UAAAxB,gBACA,EACAyB,MAAA,CACAF,KAAA,WACAG,YAAA,CAAA,EACAC,SAAA,CACAC,UAAA,CACAxL,MAAA2I,MAAAnH,SAAA,EAAA,KACA2J,KAAA,OACA,CACA,EACAM,SAAA,CAAAC,KAAA,CAAA,CAAA,EACAC,UAAA,CACA3L,MAAA2I,MAAAnH,SAAA,EAAA,KACAoK,OAAA,GACAR,UAAA,SAAA7I,GAAA,OAAA3E,OAAAqM,MAAA1H,CAAA,EAAA6H,OAAA,QAAA,CAAA,CACA,EACAyB,UAAA,CACAH,KAAA,CAAA,CACA,CACA,EACAI,MAAA,CACAX,KAAA,QACAU,UAAA,CACAL,UAAA,CACAxL,MAAA2I,MAAAnH,SAAA,EAAA,IACA,CACA,EACA8J,YAAA,CAAA,EACAK,UAAA,CACAD,KAAA,CAAA,EACA1L,MAAA2I,MAAAnH,SAAA,EAAA,KACAoK,OAAA,EACA,EACAH,SAAA,CAAAC,KAAA,CAAA,CAAA,EACAH,SAAA,CAAAG,KAAA,CAAA,CAAA,CACA,EACAK,UAAA,CACAZ,KAAA,YACAO,KAAA,CAAA,EACAM,UAAA,EACAC,YAAA,EACAC,OAAA,CACA,CACAC,GAAA,EACAC,GAAA,EACApM,MAAA2I,MAAA5I,UAAA4I,MAAAvI,SAAA,SAAA,EAAA,EAAA,CACA,EACA,CACA+L,GAAA,EACAC,GAAA,EACApM,MAAA2I,MAAA5I,UAAA4I,MAAAvI,SAAA,SAAA,EAAA,EAAA,CACA,EAEA,EACAiM,OAAA,CACA,CACAlB,KAAA,OACA9K,KAAA,QACAiM,OAAA,GACAC,OAAA,OACAf,UAAA,CACAxL,MAAA2I,MAAAvI,SAAA,SAAA,EACA4D,MAAA,CACA,EACAwI,SAAA,CACAD,OAAA,CAAA,OAAA,QACAE,MAAA,CAAAf,KAAA,CAAA,CAAA,EACA5M,KAAA,CAAA,CAAAuM,MAAA,CAAA,EAAA,CAAAA,MAAA,CAAA,EAAA,CAAAA,MAAA,CAAA,EAAA,CAAAA,MAAA,CAAA,EACA,EACAqB,UAAA,GACA5N,KAAA,CACA,CAAA,aAAA,KACA,CAAA,aAAA,KACA,CAAA,aAAA,KACA,CAAA,aAAA,KACA,CAAA,aAAA,KACA,CAAA,aAAA,KACA,CAAA,aAAA,KACA,CAAA,aAAA,KACA,CAAA,aAAA,KAEA,GAEA6N,KAAA,CAAAC,MAAA,GAAAvJ,KAAA,EAAAwJ,OAAA,EAAAzJ,IAAA,EAAA0J,aAAA,CAAA,CAAA,CACA,CAAA,CAEA,EAEA,EChHAC,wBAAA,WACA,IAIA7D,EAGA8D,EAPAC,EAAA1P,SAAAgN,cAAA,gCAAA,EAEA0C,IAEA/D,EAAAP,MAAA/J,QAAAqO,EAAA,SAAA,EACA/G,EAAAtI,OAAA4M,QAAAC,KAAAwC,CAAA,EAEAD,EAAA,CACA,UACA,WACA,QACA,QACA,MACA,OACA,OACA,SACA,YACA,UACA,WACA,YAyJA/D,gBAAA/C,EAAAgD,EAtJA,WAAA,MAAA,CACAwB,QAAA,CACAC,QAAA,OACAO,YAAA,CACAC,KAAA,QACA+B,WAAA,CACAlN,MAAA2I,MAAAnH,SAAA,EAAA,IACA,EACAiL,MAAA,CACAf,KAAA,CAAA,EACAb,gBAAAlC,MAAAnH,SAAA,EAAA,KACAxB,MAAA2I,MAAAnH,SAAA,EAAA,IACA,CACA,EACAoJ,QAAA,CAAA,EAAA,IACAC,gBAAAlC,MAAAnH,SAAA,EAAA,KACA2E,YAAAwC,MAAAnH,SAAA,EAAA,KACAsJ,UAAA,CAAA9K,MAAA2I,MAAAnH,SAAA,EAAA,KAAA,EACAuJ,YAAA,EACAC,mBAAA,EACAI,UAAAxB,gBACA,EACAuD,QAAA,CACA/J,IAAA,EACAgK,QAAA,CACAC,SAAA,CAAA3B,KAAA,CAAA,CAAA,EACA4B,UAAA,CACA5B,KAAA,CAAA,EACAP,KAAA,CAAA,OAAA,MACA,EACAoC,QAAA,CAAA7B,KAAA,CAAA,CAAA,EACA8B,YAAA,CAAA9B,KAAA,CAAA,CAAA,CACA,EACA+B,UAAA,CACAtH,YAAAwC,MAAAnH,SAAA,EAAA,KACAuJ,YAAA,CACA,EAEAzJ,SAAA,CACAmM,UAAA,CACAC,SAAA/E,MAAAnH,SAAA,EAAA,IACA,CACA,CACA,EACAmM,OAAA,CACAvK,IAAA,GACAtE,KAAA,CAAA,cAAA,gBAAA,uBACAgM,UAAA,CACA9K,MAAA2I,MAAAnH,SAAA,EAAA,IACA,CACA,EACA6J,MAAA,CACA,CACAF,KAAA,WACArM,KAAAkO,EACArB,UAAA,CACA3L,MAAA2I,MAAAnH,SAAA,EAAA,KACA4J,UAAA,SAAA7I,GAAA,OAAAA,EAAAqL,MAAA,EAAA,CAAA,CAAA,CACA,EACA1C,YAAA,CACAC,KAAA,QACA,EACAI,SAAA,CACAG,KAAA,CAAA,EACAF,UAAA,CACAxL,MAAA2I,MAAAnH,SAAA,EAAA,IACA,CACA,CACA,GAEAsK,MAAA,CACA,CACAX,KAAA,QACA7C,IAAA,EACAC,IAAA,IACAb,SAAA,GACAiE,UAAA,CACA3L,MAAA2I,MAAAnH,SAAA,EAAA,KACA4J,UAAA,YACA,EACAS,UAAA,CACAH,KAAA,CAAA,EACAF,UAAA,CACAxL,MAAA2I,MAAAnH,SAAA,EAAA,IACA,CACA,CACA,EACA,CACA2J,KAAA,QACA7C,IAAA,EACAC,IAAA,GACAb,SAAA,EACAiE,UAAA,CACA3L,MAAA2I,MAAAnH,SAAA,EAAA,KACA4J,UAAA,YACA,EAEAS,UAAA,CACAH,KAAA,CAAA,EACAF,UAAA,CACAxL,MAAA2I,MAAAnH,SAAA,EAAA,IACA,CACA,CACA,GAEA6K,OAAA,CACA,CACAhM,KAAA,cACA8K,KAAA,MACArM,KAAA,CAAA,EAAA,IAAA,EAAA,KAAA,KAAA,KAAA,MAAA,MAAA,KAAA,GAAA,IAAA,KACA+O,UAAA,CACA7N,MAAA2I,MAAAvI,SAAA,SAAA,EACA0N,gBAAA,CAAA,EAAA,EAAA,EAAA,EACA,CACA,EACA,CACAzN,KAAA,gBACA8K,KAAA,MACArM,KAAA,CAAA,IAAA,IAAA,EAAA,KAAA,KAAA,KAAA,MAAA,MAAA,KAAA,KAAA,EAAA,KACA+O,UAAA,CACA7N,MAAA2I,MAAAvI,SAAA,MAAA,EACA0N,gBAAA,CAAA,EAAA,EAAA,EAAA,EACA,CACA,EACA,CACAzN,KAAA,sBACA8K,KAAA,OACA4C,WAAA,EACAjP,KAAA,CAAA,EAAA,IAAA,IAAA,IAAA,IAAA,KAAA,KAAA,KAAA,GAAA,KAAA,GAAA,KACA0M,UAAA,CACAxL,MAAA2I,MAAAvI,SAAA,SAAA,CACA,EACAyN,UAAA,CACA7N,MAAA2I,MAAAnH,SAAA,EAAA,KACA2E,YAAAwC,MAAAvI,SAAA,SAAA,EACA2K,YAAA,CACA,EACAwB,OAAA,SACAyB,WAAA,EACA,GAEArB,KAAA,CACAC,MAAA,EACAvJ,KAAA,EACAwJ,OAAA,EACAzJ,IAAA,MACA0J,aAAA,CAAA,CACA,CACA,CAAA,CAEA,EAEA,EC/KAmB,4BAAA,WACA,IAIA/E,EAJAgF,EAAA3Q,SAAAgN,cAAA,oCAAA,EAEA2D,IAEAhF,EAAAP,MAAA/J,QAAAsP,EAAA,SAAA,EACAhI,EAAAtI,OAAA4M,QAAAC,KAAAyD,CAAA,EA0DAjF,gBAAA/C,EAAAgD,EAxDA,WAAA,MAAA,CACAwB,QAAA,CACAC,QAAA,OACAO,YAAA,CACAC,KAAA,QACA,EACAP,QAAA,CAAA,EAAA,IACAC,gBAAAlC,MAAAnH,SAAA,EAAA,KACA2E,YAAAwC,MAAAnH,SAAA,EAAA,KACAsJ,UAAA,CAAA9K,MAAA2I,MAAAnH,SAAA,EAAA,KAAA,EACAuJ,YAAA,EACAC,mBAAA,EACAI,UAAAxB,gBACA,EACA+C,KAAA,CACAvJ,IAAA,EACAyJ,OAAA,EACAxJ,KAAA,EACAuJ,MAAA,CACA,EACAvB,MAAA,CACAF,KAAA,QACAF,SAAA,MACAY,UAAA,CACAL,UAAA,CACAL,KAAA,SACAnL,MAAA2I,MAAAnH,SAAA,EAAA,IACA,CACA,CACA,EACAsK,MAAA,CACAX,KAAA,WACAI,SAAA,CAAAG,KAAA,CAAA,CAAA,EACAC,UAAA,CAAAD,KAAA,CAAA,CAAA,EACAD,SAAA,CAAAC,KAAA,CAAA,CAAA,EACAG,UAAA,CAAAH,KAAA,CAAA,CAAA,EACA5M,KAAA,CAAA,MAAA,OAAA,QAAA,QAAA,MAAA,OAAA,OAAA,QAAA,MAAA,MACA,EACAuN,OAAA,CACA,CACAhM,KAAA,OACA8K,KAAA,MACAgD,MAAA,QACA1B,MAAA,CACAf,KAAA,CAAA,EACAN,UAAA,MACApL,MAAA,MACA,EACA6N,UAAA,CACA7N,MAAA2I,MAAAvI,SAAA,SAAA,CACA,EACAtB,KAAA,CAAA,CAAA,IAAA,CAAA,IAAA,GAAA,IAAA,CAAA,IAAA,IAAA,CAAA,IAAA,IAAA,CAAA,IAAA,IACA,EAEA,CAAA,CAEA,EAEA,EClEAsP,wBAAA,WACA,IAIAlF,EACAhD,EAEApH,EAoEAuP,EA3EAC,EAAA/Q,SAAAgN,cAAA,gCAAA,EAEA+D,IAEApF,EAAAP,MAAA/J,QAAA0P,EAAA,SAAA,EACApI,EAAAtI,OAAA4M,QAAAC,KAAA6D,CAAA,EAEAxP,EAAAf,MAAA4J,KAAA5J,MAAA,CAAA,EAAAwQ,KAAA,CAAA,EAAAC,IAAA,WAAA,OAAAhG,KAAAiG,MAAA,IAAAjG,KAAAE,OAAA,CAAA,CAAA,CAAA,EAkEAO,gBAAA/C,EAAAgD,EAhEA,WAAA,MAAA,CACAmC,MAAA,CACA9C,IAAA,UACAsD,UAAA,CACAL,UAAA,CACAxL,MAAA2I,MAAAnH,SAAA,EAAA,IACA,CACA,EACAmK,UAAA,CACA3L,MAAA2I,MAAAnH,SAAA,EAAA,IACA,CACA,EACAsK,MAAA,CACAX,KAAA,WACArM,KAAA,CAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,KACA4P,QAAA,CAAA,EACA/C,UAAA,CACA3L,MAAA2I,MAAAnH,SAAA,EAAA,IACA,EACA+J,SAAA,CACAG,KAAA,CAAA,EACAF,UAAA,CACAxL,MAAA2I,MAAAnH,SAAA,EAAA,IACA,CACA,EACAiK,SAAA,CACAC,KAAA,CAAA,CACA,EACAiD,kBAAA,IACAC,wBAAA,IACArG,IAAA,CACA,EACA8D,OAAA,CACA,CACAwC,aAAA,CAAA,EACAxO,KAAA,IACA8K,KAAA,MACArM,KAAAA,EACA2N,MAAA,CACAf,KAAA,CAAA,EACAT,SAAA,QACAjL,MAAA2I,MAAAnH,SAAA,EAAA,KACAsN,WAAA,IACAC,eAAA,CAAA,CACA,EACAlB,UAAA,CACA7N,MAAA2I,MAAAvI,SAAA,SAAA,EACA0N,gBAAA,CAAA,EAAA,EAAA,EAAA,EACA,CACA,GAEAa,kBAAA,EACAC,wBAAA,IACAI,gBAAA,SACAC,sBAAA,SACAtC,KAAA,CACAC,MAAA,MACAvJ,KAAA,EACAwJ,OAAA,EACAzJ,IAAA,EACA0J,aAAA,CAAA,CACA,CACA,CAAA,CAEA,EAEAuB,EAAA,WACAvP,EAAAA,EAAA0P,IAAA,SAAAU,GAAA,MACA,GAAA1G,KAAAE,OAAA,EACAwG,EAAA1G,KAAAiG,MAAA,IAAAjG,KAAAE,OAAA,CAAA,EACAwG,EAAA1G,KAAAiG,MAAA,IAAAjG,KAAAE,OAAA,CAAA,CAAA,CACA,EAEAxC,EAAAoD,UAAA,CACA+C,OAAA,CACA,CACAvN,KAAAA,CACA,EAEA,CAAA,CACA,EAEApB,WAAA,WACA2Q,EAAA,CACA,EAAA,CAAA,EACAc,YAAA,WACAd,EAAA,CACA,EAAA,GAAA,EAEA,ECnGAe,0BAAA,WACA,IAIAlG,EAJAmG,EAAA9R,SAAAgN,cAAA,kCAAA,EAEA8E,IAEAnG,EAAAP,MAAA/J,QAAAyQ,EAAA,SAAA,EACAnJ,EAAAtI,OAAA4M,QAAAC,KAAA4E,CAAA,EA0EApG,gBAAA/C,EAAAgD,EAxEA,WAAA,MAAA,CACAlJ,MAAA,CAAA2I,MAAAvI,SAAA,SAAA,EAAAuI,MAAAvI,SAAA,MAAA,GACAsK,QAAA,CACAC,QAAA,OACAO,YAAA,CACAC,KAAA,QACA,EACAP,QAAA,CAAA,EAAA,IACAC,gBAAAlC,MAAAnH,SAAA,EAAA,KACA2E,YAAAwC,MAAAnH,SAAA,EAAA,KACAsJ,UAAA,CAAA9K,MAAA2I,MAAAnH,SAAA,EAAA,KAAA,EACAuJ,YAAA,EACAC,mBAAA,EACAI,UAAAxB,gBACA,EACAyB,MAAA,CACAF,KAAA,QACAQ,UAAA,CACAP,UAAA,SAAA7I,GAAA,MAAA,GAAA9D,OAAA8D,EAAA,IAAA,GAAA,CAAA,EACAvC,MAAA2I,MAAAnH,SAAA,EAAA,IACA,EACA+J,SAAA,CACAG,KAAA,CAAA,EACAF,UAAA,CACAxL,MAAA2I,MAAAnH,SAAA,EAAA,KACA2J,KAAA,OACA,CACA,EACAU,UAAA,CACAL,UAAA,CACAL,KAAA,SACAnL,MAAA2I,MAAAnH,SAAA,EAAA,IACA,CACA,CACA,EACAsK,MAAA,CACAX,KAAA,WACAI,SAAA,CACAG,KAAA,CAAA,EACAF,UAAA,CACAxL,MAAA2I,MAAAnH,SAAA,EAAA,KACA2J,KAAA,OACA,CACA,EACAQ,UAAA,CACA3L,MAAA2I,MAAAnH,SAAA,EAAA,IACA,EACAiK,SAAA,CAAAC,KAAA,CAAA,CAAA,EACAG,UAAA,CAAAH,KAAA,CAAA,CAAA,EACA5M,KAAA,CAAA,SAAA,YAAA,MAAA,QAAA,QACA,EACAuN,OAAA,CACA,CACAhM,KAAA,OACA8K,KAAA,MACArM,KAAA,CAAA,MAAA,MAAA,MAAA,OAAA,QACA+O,UAAA,CACAC,gBAAA,CAAA,EAAA,EAAA,EAAA,EACA,CACA,EACA,CACAzN,KAAA,OACA8K,KAAA,MACArM,KAAA,CAAA,MAAA,MAAA,KAAA,OAAA,QACA+O,UAAA,CACAC,gBAAA,CAAA,EAAA,EAAA,EAAA,EACA,CACA,GAEAnB,KAAA,CAAAC,MAAA,GAAAvJ,KAAA,MAAAwJ,OAAA,MAAAzJ,IAAA,CAAA,CACA,CAAA,CAEA,EAEA,EClFAkM,2BAAA,WACA,IAAAC,EAAAhS,SAAAgN,cAAA,mCAAA,EAEA,GAAAgF,EAAA,CAWA,IATA,IAAArG,EAAAP,MAAA/J,QAAA2Q,EAAA,SAAA,EACArJ,EAAAtI,OAAA4M,QAAAC,KAAA8E,CAAA,EAEAC,EAAA,GACAC,EAAA,GACAC,EAAA,GACAC,EAAA,GACAC,EAAA,GAEA/H,EAAA,EAAAA,EAAA,GAAAA,GAAA,EACA2H,EAAAK,KAAA,QAAApR,OAAAoJ,EAAA,CAAA,CAAA,EACA4H,EAAAI,MAAA,EAAArH,KAAAE,OAAA,GAAApB,QAAA,CAAA,CAAA,EACAoI,EAAAG,MAAA,EAAArH,KAAAE,OAAA,GAAApB,QAAA,CAAA,CAAA,EACAqI,EAAAE,MAAArH,KAAAE,OAAA,EAAA,IAAApB,QAAA,CAAA,CAAA,EACAsI,EAAAC,KAAA,CAAArH,KAAAE,OAAA,EAAApB,QAAA,CAAA,CAAA,EAGA,IAAAwI,EAAA,CACAjC,UAAA,CACAkC,WAAA,GACAC,YAAArH,MAAA5I,UAAA4I,MAAAvI,SAAA,MAAA,EAAA,EAAA,CACA,CACA,EAsGA6I,gBAAA/C,EAAAgD,EApGA,WAAA,MAAA,CACAlJ,MAAA,CACA2I,MAAAvI,SAAA,SAAA,EACAuI,MAAAvI,SAAA,MAAA,EACAuI,MAAAvI,SAAA,SAAA,EACAuI,MAAAvI,SAAA,QAAA,GAEAuN,OAAA,CACA7O,KAAA,CAAA,OAAA,OAAA,OAAA,QACAgM,UAAA,CACA9K,MAAA2I,MAAAnH,SAAA,EAAA,IACA,EACA6B,KAAA,CACA,EACA8J,QAAA,CACAC,QAAA,CACAE,UAAA,CACAnC,KAAA,CAAA,QAAA,QACA,CACA,EACAsC,UAAA,CACAtH,YAAAwC,MAAAnH,SAAA,EAAA,KACAuJ,YAAA,CACA,CACA,EACAL,QAAA,CACAC,QAAA,OACAC,QAAA,CAAA,EAAA,IACAC,gBAAAlC,MAAAnH,SAAA,EAAA,KACA2E,YAAAwC,MAAAnH,SAAA,EAAA,KACAuJ,YAAA,EACAC,mBAAA,EACAE,YAAA,CACAC,KAAA,MACA,CACA,EACAE,MAAA,CACAvM,KAAA0Q,EACA3D,UAAA,CAAAH,KAAA,CAAA,CAAA,EACAuE,UAAA,CAAAvE,KAAA,CAAA,CAAA,EAEAC,UAAA,CACA3L,MAAA2I,MAAAnH,SAAA,EAAA,IACA,EAEA+J,SAAA,CACAC,UAAA,CACAxL,MAAA2I,MAAAnH,SAAA,EAAA,IACA,CACA,CACA,EACAsK,MAAA,CACAD,UAAA,CACAL,UAAA,CACAxL,MAAA2I,MAAAnH,SAAA,EAAA,IACA,CACA,EACAmK,UAAA,CACA3L,MAAA2I,MAAAnH,SAAA,EAAA,IACA,CACA,EACA6K,OAAA,CACA,CACAhM,KAAA,OACA8K,KAAA,MACAgD,MAAA,MACA7M,SAAAwO,EACAhR,KAAA2Q,CACA,EACA,CACApP,KAAA,OACA8K,KAAA,MACAgD,MAAA,MACA7M,SAAAwO,EACAhR,KAAA4Q,CACA,EACA,CACArP,KAAA,OACA8K,KAAA,MACAgD,MAAA,MACA7M,SAAAwO,EACAhR,KAAA6Q,CACA,EACA,CACAtP,KAAA,OACA8K,KAAA,MACAgD,MAAA,MACA7M,SAAAwO,EACAhR,KAAA8Q,CACA,GAEAjD,KAAA,CACAvJ,IAAA,MACAyJ,OAAA,GACAxJ,KAAA,EACAuJ,MAAA,EACAE,aAAA,CAAA,CACA,CACA,CAAA,CAEA,CACA,CACA,ECnIAoD,4BAAA,WACA,IAIAhH,EAGA8D,EAeAmD,EAEAC,EAxBAC,EAAA9S,SAAAgN,cAAA,oCAAA,EAEA8F,IAEAnH,EAAAP,MAAA/J,QAAAyR,EAAA,SAAA,EACAnK,EAAAtI,OAAA4M,QAAAC,KAAA4F,CAAA,EAEArD,EAAA,CACA,UACA,WACA,QACA,QACA,MACA,OACA,OACA,SACA,YACA,UACA,WACA,aAGAmD,EAAA,IAcAG,QAZAF,EAAA,SAAAG,GAAA,OACAC,OAAAjC,KAAAgC,CAAA,EAAAE,OACA,SAAAC,EAAAC,GAAA,OAAAC,cAAAA,cAAA,GACAF,CAAA,EAAA,GAAAG,gBAAA,GACAF,EAAAJ,EAAAI,GAAAnC,IAAA,SAAAjM,EAAAuO,GAAA,MAAA,CACAzQ,KAAA2M,EAAA8D,GACAvO,MAAAA,CACA,CAAA,CAAA,CAAA,CAAA,CAAA,EAEA,EACA,CAAA,GAEA,CACAwO,KAAA,CACA,MAAA,OAAA,KAAA,OAAA,OAAA,OAAA,OAAA,MAAA,MAAA,QAAA,OAAA,OAEAC,KAAA,CACA,KAAA,OAAA,QAAA,OAAA,OAAA,OAAA,OAAA,OAAA,MAAA,QAAA,MACA,SAEAC,KAAA,CACA,OAAA,OAAA,QAAA,OAAA,MAAA,QAAA,MAAA,OAAA,OAAA,QAAA,OACA,SAEAC,KAAA,CACA,OAAA,OAAA,QAAA,OAAA,OAAA,QAAA,OAAA,QAAA,MAAA,QAAA,QACA,SAEAC,KAAA,CACA,OAAA,OAAA,QAAA,OAAA,MAAA,OAAA,OAAA,QAAA,OAAA,QAAA,QACA,SAEAC,KAAA,CACA,OAAA,OAAA,QAAA,OAAA,QAAA,QAAA,QAAA,OAAA,OAAA,OAAA,QACA,SAEAC,KAAA,CACA,OAAA,OAAA,QAAA,OAAA,OAAA,QAAA,QAAA,OAAA,OAAA,QAAA,QACA,QAEA,CAAA,EAEAlB,EAAAmB,OAAAlB,EAAA,CACAW,KAAA,CACA,QAAA,QAAA,QAAA,QAAA,QAAA,OAAA,QAAA,QAAA,OAAA,SACA,QAAA,QAEAC,KAAA,CACA,QAAA,QAAA,QAAA,QAAA,QAAA,QAAA,QAAA,QAAA,QAAA,SACA,QAAA,SAEAC,KAAA,CACA,OAAA,QAAA,QAAA,QAAA,QAAA,QAAA,QAAA,QAAA,QAAA,SACA,SAAA,SAEAC,KAAA,CACA,QAAA,QAAA,QAAA,QAAA,QAAA,QAAA,QAAA,QAAA,QAAA,SACA,SAAA,SAEAC,KAAA,CACA,QAAA,QAAA,QAAA,OAAA,KAAA,QAAA,QAAA,QAAA,QAAA,SACA,SAAA,SAEAC,KAAA,CACA,QAAA,QAAA,SAAA,KAAA,QAAA,QAAA,QAAA,QAAA,QAAA,SACA,SAAA,SAEAC,KAAA,CACA,QAAA,QAAA,SAAA,QAAA,QAAA,SAAA,QAAA,QAAA,QAAA,SACA,SAAA,QAEA,CAAA,EAEAlB,EAAAoB,OAAAnB,EAAA,CACAW,KAAA,CACA,QAAA,QAAA,QAAA,QAAA,QAAA,QAAA,QAAA,QAAA,OAAA,QACA,OAAA,SAEAC,KAAA,CACA,QAAA,QAAA,QAAA,QAAA,QAAA,QAAA,QAAA,QAAA,QAAA,QACA,QAAA,SAEAC,KAAA,CACA,QAAA,QAAA,QAAA,QAAA,QAAA,QAAA,QAAA,QAAA,QAAA,QACA,QAAA,SAEAC,KAAA,CACA,QAAA,QAAA,QAAA,QAAA,QAAA,QAAA,QAAA,QAAA,QAAA,SACA,QAAA,SAEAC,KAAA,CACA,QAAA,QAAA,QAAA,QAAA,QAAA,QAAA,QAAA,QAAA,QAAA,SACA,QAAA,SAEAC,KAAA,CACA,SAAA,QAAA,QAAA,QAAA,QAAA,QAAA,QAAA,QAAA,QAAA,SACA,SAAA,SAEAC,KAAA,CACA,SAAA,QAAA,QAAA,QAAA,QAAA,QAAA,QAAA,QAAA,SAAA,SACA,SAAA,QAEA,CAAA,EAyLApI,gBAAA/C,EAAAgD,EAvLA,WAAA,MAAA,CACAsI,WAAA,CACAC,SAAA,CACAC,SAAA,WACAC,SAAA,CAAA,EACAC,aAAA,IACA9S,KAAA,CACA,aACA,aACA,aACA,aACA,aACA,aACA,cAEA2N,MAAA,CACArB,UAAA,SAAAyG,GAAA,OAAA,IAAArM,KAAAqM,CAAA,EAAAC,YAAA,CAAA,CACA,EACAtG,UAAA,CACAxL,MAAA2I,MAAAvI,SAAA,MAAA,CACA,EACAyN,UAAA,CACA7N,MAAA2I,MAAAvI,SAAA,WAAA,CACA,EACA2R,gBAAA,CACA/R,MAAA2I,MAAAvI,SAAA,SAAA,EACA2P,WAAA,EACAiC,cAAA,EACAC,cAAA,CACA,EACAC,aAAA,CACAlS,MAAA2I,MAAAvI,SAAA,MAAA,CACA,CACA,EACA+R,MAAA,CACArH,UAAA,CACA9K,MAAA2I,MAAAnH,SAAA,EAAA,IACA,CACA,EACAkJ,QAAA,CACAC,QAAA,OACAO,YAAA,CACAC,KAAA,QACA,EACAP,QAAA,CAAA,EAAA,IACAC,gBAAAlC,MAAAnH,SAAA,EAAA,KACA2E,YAAAwC,MAAAnH,SAAA,EAAA,KACAsJ,UAAA,CAAA9K,MAAA2I,MAAAnH,SAAA,EAAA,KAAA,EACAuJ,YAAA,EACAC,mBAAA,EACAI,UAAAxB,gBACA,EACA+D,OAAA,CACAtK,KAAA,QACAvE,KAAA,CAAA,mBAAA,qBAAA,qBACAgM,UAAA,CACA9K,MAAA2I,MAAAnH,SAAA,EAAA,IACA,CACA,EACA4Q,WAAA,CAAA,EACA/G,MAAA,CACA,CACAF,KAAA,WACArM,KAAAkO,EACAnB,UAAA,CAAAH,KAAA,CAAA,CAAA,EACAC,UAAA,CACA3L,MAAA2I,MAAAnH,SAAA,EAAA,IACA,EACA+J,SAAA,CACAC,UAAA,CACAxL,MAAA2I,MAAAnH,SAAA,EAAA,IACA,CACA,CACA,GAEAsK,MAAA,CACA,CACAX,KAAA,QACAQ,UAAA,CACAP,UAAA,SAAA7I,GAAA,MAAA,GAAA9D,OAAA8D,EAAA,IAAA,GAAA,CAAA,EACAvC,MAAA2I,MAAAnH,SAAA,EAAA,IACA,EACAqK,UAAA,CACAL,UAAA,CACAxL,MAAA2I,MAAAnH,SAAA,EAAA,IACA,CACA,CACA,GAEA6K,OAAA,CACA,CACAhM,KAAA,mBACA8K,KAAA,MACA0C,UAAA,CACA7N,MAAA2I,MAAAvI,SAAA,SAAA,EACA0N,gBAAA,CAAA,EAAA,EAAA,EAAA,EACA,CACA,EACA,CACAzN,KAAA,qBACA8K,KAAA,MACA0C,UAAA,CACA7N,MAAA2I,MAAAvI,SAAA,MAAA,EACA0N,gBAAA,CAAA,EAAA,EAAA,EAAA,EACA,CACA,EACA,CACAzN,KAAA,oBACA8K,KAAA,MACA0C,UAAA,CACA7N,MAAA2I,MAAAvI,SAAA,SAAA,EACA0N,gBAAA,CAAA,EAAA,EAAA,EAAA,EACA,CACA,GAEAnB,KAAA,CACAvJ,IAAA,MACAyJ,OAAA,MACAxJ,KAAA,EACAuJ,MAAA,GACAE,aAAA,CAAA,CACA,CACA,EACAuF,QAAA,CACA,CACAF,MAAA,CAAA/T,KAAA,MAAA,EACAiO,OAAA,CACA,CAAAvN,KAAAqR,EAAAoB,OAAA,KAAA,EACA,CAAAzS,KAAAqR,EAAAmB,OAAA,KAAA,EACA,CAAAxS,KAAAqR,EAAAG,OAAA,KAAA,EAEA,EACA,CACA6B,MAAA,CAAA/T,KAAA,MAAA,EACAiO,OAAA,CACA,CAAAvN,KAAAqR,EAAAoB,OAAA,KAAA,EACA,CAAAzS,KAAAqR,EAAAmB,OAAA,KAAA,EACA,CAAAxS,KAAAqR,EAAAG,OAAA,KAAA,EAEA,EACA,CACA6B,MAAA,CAAA/T,KAAA,MAAA,EACAiO,OAAA,CACA,CAAAvN,KAAAqR,EAAAoB,OAAA,KAAA,EACA,CAAAzS,KAAAqR,EAAAmB,OAAA,KAAA,EACA,CAAAxS,KAAAqR,EAAAG,OAAA,KAAA,EAEA,EACA,CACA6B,MAAA,CAAA/T,KAAA,MAAA,EACAiO,OAAA,CACA,CAAAvN,KAAAqR,EAAAoB,OAAA,KAAA,EACA,CAAAzS,KAAAqR,EAAAmB,OAAA,KAAA,EACA,CAAAxS,KAAAqR,EAAAG,OAAA,KAAA,EAEA,EACA,CACA6B,MAAA,CAAA/T,KAAA,MAAA,EACAiO,OAAA,CACA,CAAAvN,KAAAqR,EAAAoB,OAAA,KAAA,EACA,CAAAzS,KAAAqR,EAAAmB,OAAA,KAAA,EACA,CAAAxS,KAAAqR,EAAAG,OAAA,KAAA,EAEA,EACA,CACA6B,MAAA,CAAA/T,KAAA,MAAA,EACAiO,OAAA,CACA,CAAAvN,KAAAqR,EAAAoB,OAAA,KAAA,EACA,CAAAzS,KAAAqR,EAAAmB,OAAA,KAAA,EACA,CAAAxS,KAAAqR,EAAAG,OAAA,KAAA,EAEA,EACA,CACA6B,MAAA,CAAA/T,KAAA,MAAA,EACAiO,OAAA,CACA,CAAAvN,KAAAqR,EAAAoB,OAAA,KAAA,EACA,CAAAzS,KAAAqR,EAAAmB,OAAA,KAAA,EACA,CAAAxS,KAAAqR,EAAAG,OAAA,KAAA,EAEA,EAEA,CAAA,CAEA,EAEA,EC1TAgC,0BAAA,WACA,IAIApJ,EAGAjB,EAPAsK,EAAAhV,SAAAgN,cAAA,iCAAA,EAEAgI,IAEArJ,EAAAP,MAAA/J,QAAA2T,EAAA,SAAA,EACArM,EAAAtI,OAAA4M,QAAAC,KAAA8H,CAAA,EAEAtK,EAAA,CACA,aACA,aACA,aACA,aACA,aACA,aACA,aACA,aACA,aACA,aACA,cAuHAgB,gBAAA/C,EAAAgD,EApHA,WAAA,MAAA,CACAyE,OAAA,CACA7O,KAAA,CAAA,cAAA,UACAgM,UAAA,CACA9K,MAAA2I,MAAAnH,SAAA,EAAA,IACA,CACA,EACAkJ,QAAA,CACAC,QAAA,OACAC,QAAA,CAAA,EAAA,IACAC,gBAAAlC,MAAAnH,SAAA,EAAA,KACA2E,YAAAwC,MAAAnH,SAAA,EAAA,KACAsJ,UAAA,CAAA9K,MAAA2I,MAAAnH,SAAA,EAAA,KAAA,EACAuJ,YAAA,EACAK,UAAA,SAAAtC,GACA0J,EAAA,MAAA1J,EAAA,GAAAvG,MAAAuG,EAAA,GAAAA,EAAA,GACA,MAAA,GAAArK,OAAAb,OAAAqM,MAAAuI,EAAAnS,IAAA,EAAA+J,OAAA,QAAA,EAAA,OAAA,EAAA3L,OAAA+T,EAAAzI,WAAA,IAAA,EAAAtL,OAAA+T,EAAAjQ,KAAA,CACA,EACAyI,mBAAA,EACAE,YAAA,CACAC,KAAA,QACA,CACA,EACAE,MAAA,CACAF,KAAA,WACArM,KAAAmJ,EACAsD,SAAA,CACAC,UAAA,CACAxL,MAAA2I,MAAAnH,SAAA,EAAA,KACA2J,KAAA,OACA,CACA,EACAM,SAAA,CAAAC,KAAA,CAAA,CAAA,EACAC,UAAA,CACA3L,MAAA2I,MAAAnH,SAAA,EAAA,KACA4J,UAAA,SAAA7I,GAAA,OAAA3E,OAAAqM,MAAA1H,CAAA,EAAA6H,OAAA,QAAA,CAAA,EACAwB,OAAA,EACA,EACAC,UAAA,CACAH,KAAA,CAAA,CACA,CACA,EACAI,MAAA,CACAX,KAAA,QACAG,YAAA,CAAA,EACAK,UAAA,CACAD,KAAA,CAAA,EACA1L,MAAA2I,MAAAnH,SAAA,EAAA,KACAoK,OAAA,EACA,EACAC,UAAA,CACAH,KAAA,CAAA,EACAF,UAAA,CACAxL,MAAA2I,MAAAnH,SAAA,EAAA,IACA,CACA,EACAiK,SAAA,CAAAC,KAAA,CAAA,CAAA,EACAH,SAAA,CAAAG,KAAA,CAAA,CAAA,EACApD,IAAA,GACA,EACA+D,OAAA,CACA,CACAhM,KAAA,SACA8K,KAAA,MACAgD,MAAA,QACAN,UAAA,CACA4E,eAAA,cACAzS,MAAA,aACA,EACAsB,SAAA,CACAuM,UAAA,CACA4E,eAAA,cACAzS,MAAA,aACA,CACA,EACAlB,KAAA,CAAA,EAAA,IAAA,KAAA,KAAA,KAAA,KAAA,KAAA,KAAA,KAAA,KAAA,KACA,EACA,CACAuB,KAAA,SACA8K,KAAA,MACAgD,MAAA,QACA1B,MAAA,CACAf,KAAA,CAAA,EACAT,SAAA,MACAjL,MAAA2I,MAAAnH,SAAA,EAAA,IACA,EACA1C,KAAA,CAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,KACA+O,UAAA,CACA7N,MAAA2I,MAAAvI,SAAA,SAAA,EACA0N,gBAAA,CAAA,EAAA,EAAA,EAAA,EACA,CACA,EACA,CACAzN,KAAA,cACA8K,KAAA,MACAgD,MAAA,QACA1B,MAAA,CACAf,KAAA,CAAA,EACAT,SAAA,SACAjL,MAAA2I,MAAAnH,SAAA,EAAA,IACA,EACA1C,KAAA,CAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,KACA+O,UAAA,CACA7N,MAAA2I,MAAAvI,SAAA,SAAA,EACA0N,gBAAA,CAAA,EAAA,EAAA,EAAA,EACA,CACA,GAEAnB,KAAA,CACAC,MAAA,KACAvJ,KAAA,MACAwJ,OAAA,MACAzJ,IAAA,KACA,CACA,CAAA,CAEA,EAEA,EC5IAsP,yBAAA,WACA,IAIAxJ,EAGA8D,EAeAlO,EAtBA6T,EAAApV,SAAAgN,cAAA,iCAAA,EAEAoI,IAEAzJ,EAAAP,MAAA/J,QAAA+T,EAAA,SAAA,EACAzM,EAAAtI,OAAA4M,QAAAC,KAAAkI,CAAA,EAEA3F,EAAA,CACA,UACA,WACA,QACA,QACA,MACA,OACA,OACA,SACA,YACA,UACA,WACA,YAGAlO,EAAA,CAAA,KAAA,KAAA,KAAA,KAAA,KAAA,KAAA,KAAA,KAAA,KAAA,KAAA,KAAA,MAwEAmK,gBAAA/C,EAAAgD,EAtEA,WAAA,MAAA,CACAwB,QAAA,CACAC,QAAA,OACAC,QAAA,CAAA,EAAA,IACAC,gBAAAlC,MAAAnH,SAAA,EAAA,KACA2E,YAAAwC,MAAAnH,SAAA,EAAA,KACAsJ,UAAA,CAAA9K,MAAA2I,MAAAnH,SAAA,EAAA,KAAA,EACAuJ,YAAA,EACAK,UAAAxB,iBACAoB,mBAAA,EACAE,YAAA,CACAC,KAAA,MACA,CACA,EACAE,MAAA,CACAF,KAAA,WACArM,KAAAkO,EACAzB,SAAA,CACAC,UAAA,CACAxL,MAAA2I,MAAAnH,SAAA,EAAA,KACA2J,KAAA,OACA,CACA,EACAM,SAAA,CAAAC,KAAA,CAAA,CAAA,EACAC,UAAA,CACA3L,MAAA2I,MAAAnH,SAAA,EAAA,KACA4J,UAAA,SAAA7I,GAAA,OAAAA,EAAAhD,UAAA,EAAA,CAAA,CAAA,EACAqM,OAAA,EACA,EACAC,UAAA,CACAH,KAAA,CAAA,CACA,CACA,EACAI,MAAA,CACAX,KAAA,QACAG,YAAA,CAAA,EACAK,UAAA,CACAD,KAAA,CAAA,EACA1L,MAAA2I,MAAAnH,SAAA,EAAA,KACAoK,OAAA,EACA,EACAC,UAAA,CACAH,KAAA,CAAA,EACAF,UAAA,CACAxL,MAAA2I,MAAAnH,SAAA,EAAA,IACA,CACA,EACAiK,SAAA,CAAAC,KAAA,CAAA,CAAA,EACAH,SAAA,CAAAG,KAAA,CAAA,CAAA,EACApD,IAAA,GACA,EACA+D,OAAA,CACA,CACAlB,KAAA,MACA9K,KAAA,QACAvB,KAAAA,EACA0M,UAAA,CAAAxL,MAAA2I,MAAAvI,SAAA,SAAA,CAAA,EACAyN,UAAA,CACA7N,MAAA2I,MAAAvI,SAAA,SAAA,EACA0N,gBAAA,CAAA,EAAA,EAAA,EAAA,EACA,EACA8E,WAAA,CAAA,EACArG,OAAA,SACAD,OAAA,CAAA,EACAuG,eAAA,CAAA,CACA,GAEAlG,KAAA,CAAAC,MAAA,KAAAvJ,KAAA,MAAAwJ,OAAA,MAAAzJ,IAAA,IAAA,CACA,CAAA,CAEA,EAEA,ECjGA0P,iCAAA,WACA,IAIA5J,EAGApK,EAPAiU,EAAAxV,SAAAgN,cAAA,mCAAA,EAEAwI,IAEA7J,EAAAP,MAAA/J,QAAAmU,EAAA,SAAA,EACA7M,EAAAtI,OAAA4M,QAAAC,KAAAsI,CAAA,EAEAjU,EAAA,CACA,CAAA,YAAA,QAAA,QAAA,OAAA,SACA,CAAA,YAAA,KAAA,OAAA,QAAA,SACA,CAAA,YAAA,QAAA,OAAA,QAAA,SACA,CAAA,YAAA,QAAA,QAAA,QAAA,QACA,CAAA,YAAA,QAAA,QAAA,QAAA,SACA,CAAA,YAAA,QAAA,QAAA,QAAA,SACA,CAAA,WAAA,QAAA,QAAA,QAAA,SACA,CAAA,WAAA,QAAA,QAAA,QAAA,SACA,CAAA,WAAA,KAAA,QAAA,OAAA,SACA,CAAA,WAAA,QAAA,QAAA,OAAA,SACA,CAAA,WAAA,QAAA,QAAA,QAAA,SACA,CAAA,WAAA,QAAA,OAAA,OAAA,SACA,CAAA,YAAA,QAAA,QAAA,QAAA,QACA,CAAA,YAAA,QAAA,QAAA,QAAA,SACA,CAAA,YAAA,QAAA,QAAA,QAAA,SACA,CAAA,YAAA,QAAA,QAAA,QAAA,SACA,CAAA,YAAA,QAAA,QAAA,QAAA,SACA,CAAA,YAAA,QAAA,QAAA,QAAA,SACA,CAAA,YAAA,QAAA,QAAA,QAAA,SACA,CAAA,YAAA,QAAA,QAAA,QAAA,SACA,CAAA,YAAA,QAAA,QAAA,QAAA,SACA,CAAA,WAAA,QAAA,QAAA,QAAA,SACA,CAAA,WAAA,QAAA,OAAA,QAAA,SACA,CAAA,WAAA,QAAA,QAAA,OAAA,SACA,CAAA,WAAA,QAAA,QAAA,OAAA,SACA,CAAA,WAAA,QAAA,QAAA,QAAA,SACA,CAAA,WAAA,QAAA,QAAA,QAAA,SACA,CAAA,YAAA,QAAA,QAAA,QAAA,SACA,CAAA,YAAA,QAAA,OAAA,QAAA,SACA,CAAA,YAAA,QAAA,QAAA,QAAA,SACA,CAAA,YAAA,QAAA,QAAA,QAAA,SACA,CAAA,YAAA,QAAA,OAAA,KAAA,SACA,CAAA,YAAA,QAAA,QAAA,QAAA,SACA,CAAA,YAAA,QAAA,QAAA,QAAA,SACA,CAAA,YAAA,QAAA,QAAA,QAAA,SACA,CAAA,YAAA,QAAA,QAAA,OAAA,SACA,CAAA,YAAA,OAAA,QAAA,QAAA,MACA,CAAA,YAAA,QAAA,QAAA,QAAA,SACA,CAAA,YAAA,QAAA,QAAA,QAAA,SACA,CAAA,YAAA,QAAA,QAAA,KAAA,SACA,CAAA,YAAA,QAAA,OAAA,QAAA,SACA,CAAA,YAAA,QAAA,QAAA,QAAA,SACA,CAAA,WAAA,QAAA,OAAA,QAAA,SACA,CAAA,WAAA,OAAA,QAAA,QAAA,SACA,CAAA,WAAA,QAAA,QAAA,QAAA,SACA,CAAA,WAAA,QAAA,QAAA,QAAA,SACA,CAAA,WAAA,QAAA,QAAA,QAAA,SACA,CAAA,YAAA,QAAA,QAAA,QAAA,SACA,CAAA,YAAA,QAAA,QAAA,QAAA,SACA,CAAA,YAAA,QAAA,QAAA,QAAA,UAiHAmK,gBAAA/C,EAAAgD,EA9GA,WAAA,MAAA,CACAwB,QAAA,CACAC,QAAA,OACAC,QAAA,CAAA,EAAA,IACAC,gBAAAlC,MAAAnH,SAAA,EAAA,KACA2E,YAAAwC,MAAAnH,SAAA,EAAA,KACAsJ,UAAA,CAAA9K,MAAA2I,MAAAnH,SAAA,EAAA,KAAA,EACAuJ,YAAA,EACAC,mBAAA,EACAE,YAAA,CACAC,KAAA,MACA,CACA,EACAgC,QAAA,CACA/J,IAAA,EACAgK,QAAA,CACA4F,SAAA,CACAjF,WAAA,CAAA,CACA,EACAR,QAAA,CAAA7B,KAAA,CAAA,CAAA,CACA,EACA+B,UAAA,CACAtH,YAAAwC,MAAAnH,SAAA,EAAA,KACAuJ,YAAA,CACA,EAEAzJ,SAAA,CACAmM,UAAA,CACAC,SAAA/E,MAAAnH,SAAA,EAAA,IACA,CACA,CACA,EACAwR,SAAA,CACA,CACA7H,KAAA,SACA8H,MAAA,EACAC,IAAA,IACAC,aAAA,EACA,GAEA9H,MAAA,CACAF,KAAA,WACArM,KAAAA,EAAA0P,IAAA,SAAAU,GAAA,OAAAA,EAAA,EAAA,CAAA,EACAkE,MAAA,CAAA,EACAvH,UAAA,CAAAH,KAAA,CAAA,CAAA,EACA2H,YAAA,GACA/K,IAAA,UACAC,IAAA,UACA+C,YAAA,CAAA,EACAJ,YAAA,CACAM,UAAA,CACAxL,MAAA2I,MAAAnH,SAAA,EAAA,KACA2J,KAAA,QACA,CACA,EACAI,SAAA,CACAC,UAAA,CACAxL,MAAA2I,MAAAnH,SAAA,EAAA,KACA2J,KAAA,OACA,CACA,EACAM,SAAA,CAAAC,KAAA,CAAA,CAAA,EACAC,UAAA,CACA3L,MAAA2I,MAAAnH,SAAA,EAAA,KACA4J,UAAA,SAAA7I,GAAA,OAAA3E,OAAAqM,MAAA1H,EAAA,YAAA,EAAA6H,OAAA,QAAA,CAAA,EACAwB,OAAA,GACAkD,WAAA,GACA,CACA,EACAhD,MAAA,CACAsH,MAAA,CAAA,EACAlI,YAAA,CAAAQ,KAAA,CAAA,CAAA,EACAG,UAAA,CACAL,UAAA,CACAxL,MAAA2I,MAAAnH,SAAA,EAAA,KACA2J,KAAA,QACA,CACA,EACAG,YAAA,CAAA,EACAK,UAAA,CACAD,KAAA,CAAA,EACA1L,MAAA2I,MAAAnH,SAAA,EAAA,KACAoK,OAAA,GACAkD,WAAA,GACA,EACArD,SAAA,CAAAC,KAAA,CAAA,CAAA,EACAH,SAAA,CAAAG,KAAA,CAAA,CAAA,CACA,EACAW,OAAA,CACA,CACAlB,KAAA,cACA9K,KAAA,SACAvB,KAAAA,EAAA0P,IAAA,SAAAU,GAAA,OAAAA,EAAAtB,MAAA,CAAA,CAAA,CAAA,EACAC,UAAA,CACA7N,MAAA2I,MAAAvI,SAAA,SAAA,EACAkT,OAAA3K,MAAAvI,SAAA,SAAA,EACA+F,YAAAwC,MAAAvI,SAAA,SAAA,EACAmT,aAAA5K,MAAAvI,SAAA,SAAA,CACA,CACA,GAEAuM,KAAA,CACAC,MAAA,EACAvJ,KAAA,EACAwJ,OAAA,EACAzJ,IAAA,MACA0J,aAAA,CAAA,CACA,CACA,CAAA,CAEA,EAEA,EC7KA0G,2BAAA,WACA,IAIAtK,EAGAU,EAPA6J,EAAAlW,SAAAgN,cAAA,mCAAA,EAEAkJ,IAEAvK,EAAAP,MAAA/J,QAAA6U,EAAA,SAAA,EACAvN,EAAAtI,OAAA4M,QAAAC,KAAAgJ,CAAA,EAEA7J,EAAA,SAAAd,GAAA,MAAA,yHAAArK,OAGAqK,EAAA,GAAA9I,MAAA,yBAAA,EAAAvB,OACAqK,EAAA,GAAAzI,KAAA,KAAA,EAAA5B,OAAAqK,EAAA,GAAAvG,MAAA,yCAAA,CAAA,EAmDA0G,gBAAA/C,EAAAgD,EA9CA,WAAA,MAAA,CACAwB,QAAA,CACAC,QAAA,OACAC,QAAA,CAAA,EAAA,IACAC,gBAAAlC,MAAAnH,SAAA,EAAA,KACA2E,YAAAwC,MAAAnH,SAAA,EAAA,KACAsJ,UAAA,CAAA9K,MAAA2I,MAAAnH,SAAA,EAAA,KAAA,EACAuJ,YAAA,EACAK,UAAAxB,EACAoB,mBAAA,EACAE,YAAA,CACAC,KAAA,MACA,CACA,EACAuI,OAAA,OACArH,OAAA,CACA,CACAhM,KAAA,WACA8K,KAAA,QACAU,UAAA,CACAL,UAAA,CACAxL,MAAA2I,MAAAnH,SAAA,EAAA,IACA,CACA,EACAmK,UAAA,CACA3L,MAAA2I,MAAAnH,SAAA,EAAA,IACA,EACAkI,OAAA,CACA0B,UAAA,SACA,EACA+G,MAAA,CACAnS,MAAA2I,MAAAnH,SAAA,EAAA,IACA,EACA1C,KAAA,CACA,CACAyD,MAAA,GACAlC,KAAA,QACAqJ,OAAA,CACA1J,MAAA2I,MAAAnH,SAAA,EAAA,IACA,CACA,EAEA,EAEA,CAAA,CAEA,EAEA,ECjEAmS,qBAAA,WACA,IAIAzK,EAGA8D,EAeAlO,EAEA8K,EAxBAgK,EAAArW,SAAAgN,cAAA,4BAAA,EAEAqJ,IAEA1K,EAAAP,MAAA/J,QAAAgV,EAAA,SAAA,EACA1N,EAAAtI,OAAA4M,QAAAC,KAAAmJ,CAAA,EAEA5G,EAAA,CACA,UACA,WACA,QACA,QACA,MACA,OACA,OACA,SACA,YACA,UACA,WACA,YAGAlO,EAAA,CAAA,KAAA,KAAA,KAAA,KAAA,KAAA,KAAA,KAAA,KAAA,KAAA,KAAA,KAAA,MAEA8K,EAAA,SAAAd,GAAA,MAAA,yHAAArK,OAGAqK,EAAA,GAAA3C,YAAA,yBAAA,EAAA1H,OACAqK,EAAA,GAAAzI,KAAA,KAAA,EAAA5B,OAAAqK,EAAA,GAAAvG,MAAA,yCAAA,CAAA,EAmFA0G,gBAAA/C,EAAAgD,EA9EA,WAAA,MAAA,CACAwB,QAAA,CACAC,QAAA,OACAC,QAAA,CAAA,EAAA,IACAC,gBAAAlC,MAAAnH,SAAA,EAAA,KACA2E,YAAAwC,MAAAnH,SAAA,EAAA,KACAsJ,UAAA,CAAA9K,MAAA2I,MAAAnH,SAAA,EAAA,KAAA,EACAuJ,YAAA,EACAK,UAAAxB,EACAoB,mBAAA,EACAC,SAAA,SAAApC,EAAAC,EAAAxI,EAAAwC,EAAAiG,GACA,OAAAH,YAAAC,EAAAC,EAAAxI,EAAAwC,EAAAiG,CAAA,CACA,EACAmC,YAAA,CACAC,KAAA,MACA,CACA,EACAE,MAAA,CACAF,KAAA,WACArM,KAAAkO,EACA1B,YAAA,CAAA,EACAC,SAAA,CACAC,UAAA,CACAxL,MAAA2I,MAAAnH,SAAA,EAAA,IACA,CACA,EACAiK,SAAA,CAAAC,KAAA,CAAA,CAAA,EACAC,UAAA,CACA3L,MAAA2I,MAAAnH,SAAA,EAAA,KACA4J,UAAA,SAAA7I,GAAA,OAAAA,EAAAhD,UAAA,EAAA,CAAA,CAAA,EACAqM,OAAA,EACA,EACAC,UAAA,CACAH,KAAA,CAAA,CACA,CACA,EACAI,MAAA,CACAX,KAAA,QACAU,UAAA,CACAL,UAAA,CACAL,KAAA,SACAnL,MAAA2I,MAAAnH,SAAA,EAAA,IACA,CACA,EACA8J,YAAA,CAAA,EACAK,UAAA,CACAD,KAAA,CAAA,EACA1L,MAAA2I,MAAAnH,SAAA,EAAA,KACAoK,OAAA,EACA,EACAH,SAAA,CAAAC,KAAA,CAAA,CAAA,EACAH,SAAA,CAAAG,KAAA,CAAA,CAAA,EACApD,IAAA,GACA,EACA+D,OAAA,CACA,CACAlB,KAAA,OACArM,KAAAA,EACA+O,UAAA,CACA7N,MAAA2I,MAAAnH,SAAA,EAAA,KACA2E,YAAAwC,MAAAvI,SAAA,SAAA,EACA2K,YAAA,CACA,EACAS,UAAA,CACAxL,MAAA2I,MAAAvI,SAAA,SAAA,CACA,EACAwS,WAAA,CAAA,EACArG,OAAA,SACAyB,WAAA,GACA1B,OAAA,CAAA,EACAuG,eAAA,CAAA,CACA,GAEAlG,KAAA,CACAC,MAAA,KAAAvJ,KAAA,MAAAwJ,OAAA,MAAAzJ,IAAA,IACA,CACA,CAAA,CAEA,EAEA,EClHAyQ,uBAAA,WACA,IAIA3K,EAGApK,EAPAgV,EAAAvW,SAAAgN,cAAA,8BAAA,EAEAuJ,IAEA5K,EAAAP,MAAA/J,QAAAkV,EAAA,SAAA,EACA5N,EAAAtI,OAAA4M,QAAAC,KAAAqJ,CAAA,EAEAhV,EAAA,CACA,CACA,CAAA,MAAA,GAAA,SAAA,YAAA,MACA,CAAA,MAAA,KAAA,SAAA,SAAA,MACA,CAAA,KAAA,GAAA,WAAA,QAAA,MACA,CAAA,MAAA,GAAA,QAAA,UAAA,MACA,CAAA,MAAA,KAAA,SAAA,SAAA,MACA,CAAA,MAAA,KAAA,SAAA,UAAA,MACA,CAAA,KAAA,KAAA,UAAA,QAAA,MACA,CAAA,MAAA,KAAA,UAAA,QAAA,MACA,CAAA,MAAA,GAAA,SAAA,cAAA,MACA,CAAA,MAAA,KAAA,QAAA,cAAA,MACA,CAAA,MAAA,KAAA,QAAA,SAAA,MACA,CAAA,MAAA,KAAA,SAAA,SAAA,MACA,CAAA,MAAA,KAAA,UAAA,SAAA,MACA,CAAA,MAAA,KAAA,SAAA,iBAAA,MACA,CAAA,MAAA,KAAA,UAAA,gBAAA,OAEA,CACA,CAAA,MAAA,KAAA,SAAA,YAAA,MACA,CAAA,MAAA,KAAA,SAAA,SAAA,MACA,CAAA,MAAA,KAAA,WAAA,QAAA,MACA,CAAA,MAAA,KAAA,QAAA,UAAA,MACA,CAAA,MAAA,KAAA,SAAA,SAAA,MACA,CAAA,MAAA,KAAA,SAAA,UAAA,MACA,CAAA,KAAA,KAAA,WAAA,QAAA,MACA,CAAA,MAAA,KAAA,UAAA,QAAA,MACA,CAAA,MAAA,KAAA,SAAA,cAAA,MACA,CAAA,MAAA,KAAA,QAAA,cAAA,MACA,CAAA,MAAA,KAAA,QAAA,SAAA,MACA,CAAA,MAAA,KAAA,SAAA,SAAA,MACA,CAAA,MAAA,MAAA,UAAA,SAAA,MACA,CAAA,MAAA,KAAA,SAAA,iBAAA,MACA,CAAA,MAAA,KAAA,UAAA,gBAAA,QA2GAmK,gBAAA/C,EAAAgD,EAvGA,WAAA,MAAA,CACAiJ,MAAA,CACA/T,KAAA,wCACAiF,KAAA,EACAD,IAAA,EACA0H,UAAA,CACA9K,MAAA2I,MAAAnH,SAAA,EAAA,KACAsN,WAAA,GACA,CACA,EACAnB,OAAA,CACAf,MAAA,EACAxJ,IAAA,MACAtE,KAAA,CAAA,OAAA,QACAgM,UAAA,CACA9K,MAAA2I,MAAAnH,SAAA,EAAA,IACA,CACA,EACA6J,MAAA,CACAM,UAAA,CACA3L,MAAA2I,MAAAnH,SAAA,EAAA,KACA4J,UAAA,SAAA7I,GAAA,MAAA,GAAA9D,OAAA8D,EAAA,IAAA,GAAA,CAAA,CACA,EACAgJ,SAAA,CACAG,KAAA,CAAA,EACAF,UAAA,CACAxL,MAAA2I,MAAAnH,SAAA,EAAA,IACA,CACA,EAEAqK,UAAA,CACAH,KAAA,CAAA,EACAF,UAAA,CACAxL,MAAA2I,MAAAnH,SAAA,EAAA,IACA,CACA,CACA,EACAsK,MAAA,CACAsH,MAAA,CAAA,EACAzH,UAAA,CACA3L,MAAA2I,MAAAnH,SAAA,EAAA,IACA,EACAqK,UAAA,CACAH,KAAA,CAAA,EACAF,UAAA,CACAxL,MAAA2I,MAAAnH,SAAA,EAAA,IACA,CACA,EAEA+J,SAAA,CACAG,KAAA,CAAA,EACAF,UAAA,CACAxL,MAAA2I,MAAAnH,SAAA,EAAA,IACA,CACA,CACA,EACA6K,OAAA,CACA,CACAhM,KAAA,OACAvB,KAAAA,EAAA,GACAqM,KAAA,UACA6C,WAAA,SAAAzL,GAAA,OAAAiG,KAAAuL,KAAAxR,EAAA,EAAA,EAAA,GAAA,EACAjB,SAAA,CACA0S,MAAA,SACAvH,MAAA,CACAzM,MAAA2I,MAAAnH,SAAA,EAAA,KACAkK,KAAA,CAAA,EACAN,UAAA,SAAA6I,GAAA,OAAAA,EAAAnV,KAAA,EAAA,EACAmM,SAAA,KACA,CACA,EACA4C,UAAA,CACA7N,MAAA2I,MAAA5I,UAAA4I,MAAAvI,SAAA,SAAA,EAAA,EAAA,CACA,CACA,EACA,CACAC,KAAA,OACAvB,KAAAA,EAAA,GACAqM,KAAA,UACA6C,WAAA,SAAAzL,GAAA,OAAAiG,KAAAuL,KAAAxR,EAAA,EAAA,EAAA,GAAA,EACAjB,SAAA,CACA0S,MAAA,SACAvH,MAAA,CACAzM,MAAA2I,MAAAnH,SAAA,EAAA,KACAkK,KAAA,CAAA,EACAN,UAAA,SAAA6I,GAAA,OAAAA,EAAAnV,KAAA,EAAA,EACAmM,SAAA,KACA,CACA,EACA4C,UAAA,CACA7N,MAAA2I,MAAA5I,UAAA4I,MAAAvI,SAAA,SAAA,EAAA,EAAA,CACA,CACA,GAEAuM,KAAA,CACAtJ,KAAA,EACAuJ,MAAA,GACAC,OAAA,EACAzJ,IAAA,MACA0J,aAAA,CAAA,CACA,CACA,CAAA,CAEA,EAEA,ECtJAoH,iCAAA,WACA,IAMAhL,EAGAiL,EAmBAC,EAEAtV,EAgEAuV,EA9FAC,EAAA/W,SAAAgN,cACA,yCACA,EAEA+J,IAEApL,EAAAP,MAAA/J,QAAA0V,EAAA,SAAA,EACApO,EAAAtI,OAAA4M,QAAAC,KAAA6J,CAAA,EAEAH,EAAA,CACAxL,MAAAvI,SAAA,SAAA,EACAuI,MAAAvI,SAAA,MAAA,EACAuI,MAAAvI,SAAA,MAAA,EACAuI,MAAAvI,SAAA,SAAA,GAeAgU,EAAAzL,MAAAZ,aAAA,EAAA,EAAAyG,IAAA,SAAAtG,GAAA,OAAAtK,OAAAqM,MAAA/B,CAAA,EAAAkC,OAAA,cAAA,CAAA,CAAA,EAkEAiK,EA9EA,SAAAE,EAAAzV,GAEA,IADA,IAAAU,EAAA,GACAqI,EAAA0M,EAAA1M,EAAA/I,EAAAb,OAAA4J,GAAA,EAAA,CAEA,IADA,IAAA2M,EAAA,EACAC,EAAA,EAAAA,EAAAF,EAAAE,GAAA,EACAD,GAAA1V,EAAA+I,EAAA4M,GAAA,GAEAjV,EAAAqQ,MAAA2E,EAAAD,GAAAjN,QAAA,CAAA,CAAA,CACA,CACA,OAAA9H,CACA,EAoEA,EAhEAV,EAAA,CACA,CAAA,SAAA,SAAA,SAAA,SAAA,QACA,CAAA,SAAA,SAAA,SAAA,SAAA,QACA,CAAA,SAAA,SAAA,SAAA,QAAA,QACA,CAAA,SAAA,SAAA,SAAA,SAAA,SACA,CAAA,SAAA,MAAA,SAAA,SAAA,QACA,CAAA,SAAA,SAAA,SAAA,SAAA,SACA,CAAA,SAAA,SAAA,SAAA,SAAA,QACA,CAAA,SAAA,SAAA,SAAA,SAAA,QACA,CAAA,SAAA,SAAA,SAAA,SAAA,QACA,CAAA,SAAA,SAAA,QAAA,SAAA,QACA,CAAA,SAAA,SAAA,SAAA,SAAA,QACA,CAAA,SAAA,SAAA,SAAA,SAAA,QACA,CAAA,SAAA,SAAA,SAAA,SAAA,QACA,CAAA,SAAA,SAAA,SAAA,SAAA,SACA,CAAA,QAAA,SAAA,SAAA,SAAA,QACA,CAAA,QAAA,QAAA,SAAA,SAAA,QACA,CAAA,SAAA,SAAA,SAAA,SAAA,SACA,CAAA,SAAA,SAAA,SAAA,SAAA,SACA,CAAA,SAAA,SAAA,SAAA,SAAA,SACA,CAAA,SAAA,SAAA,SAAA,SAAA,QACA,CAAA,SAAA,SAAA,SAAA,SAAA,QACA,CAAA,SAAA,SAAA,SAAA,SAAA,SACA,CAAA,SAAA,SAAA,SAAA,SAAA,SACA,CAAA,SAAA,SAAA,SAAA,SAAA,SACA,CAAA,SAAA,SAAA,SAAA,SAAA,OACA,CAAA,SAAA,SAAA,SAAA,SAAA,QACA,CAAA,SAAA,SAAA,SAAA,SAAA,QACA,CAAA,SAAA,SAAA,SAAA,SAAA,QACA,CAAA,QAAA,SAAA,SAAA,SAAA,QACA,CAAA,SAAA,SAAA,SAAA,SAAA,QACA,CAAA,SAAA,SAAA,SAAA,SAAA,QACA,CAAA,SAAA,SAAA,SAAA,SAAA,QACA,CAAA,SAAA,QAAA,SAAA,SAAA,QACA,CAAA,SAAA,SAAA,SAAA,SAAA,QACA,CAAA,SAAA,SAAA,SAAA,QAAA,QACA,CAAA,SAAA,SAAA,SAAA,SAAA,SACA,CAAA,SAAA,SAAA,SAAA,SAAA,QACA,CAAA,SAAA,QAAA,SAAA,SAAA,QACA,CAAA,SAAA,SAAA,SAAA,SAAA,SACA,CAAA,SAAA,SAAA,SAAA,QAAA,QACA,CAAA,SAAA,SAAA,SAAA,SAAA,QACA,CAAA,SAAA,SAAA,SAAA,SAAA,QACA,CAAA,SAAA,SAAA,SAAA,SAAA,QACA,CAAA,SAAA,SAAA,SAAA,SAAA,QACA,CAAA,QAAA,QAAA,SAAA,SAAA,SACA,CAAA,SAAA,SAAA,SAAA,SAAA,QACA,CAAA,SAAA,SAAA,SAAA,SAAA,QACA,CAAA,QAAA,SAAA,SAAA,SAAA,QACA,CAAA,SAAA,SAAA,SAAA,SAAA,QACA,CAAA,SAAA,SAAA,SAAA,SAAA,QACA,CAAA,SAAA,SAAA,SAAA,MAAA,QACA,CAAA,SAAA,SAAA,SAAA,SAAA,QACA,CAAA,SAAA,SAAA,SAAA,SAAA,QACA,CAAA,QAAA,SAAA,SAAA,SAAA,SACA,CAAA,SAAA,SAAA,SAAA,SAAA,QACA,CAAA,SAAA,SAAA,SAAA,SAAA,QACA,CAAA,SAAA,QAAA,SAAA,SAAA,QACA,CAAA,SAAA,SAAA,SAAA,SAAA,SACA,CAAA,SAAA,SAAA,SAAA,SAAA,QACA,CAAA,SAAA,SAAA,QAAA,SAAA,QACA,CAAA,SAAA,SAAA,SAAA,SAAA,QAGA,EAkLAmK,gBAAA/C,EAAAgD,EAhLA,WAAA,MAAA,CACAwL,UAAA,CAAA,EACA1U,MAAAmU,EACAxG,OAAA,CACAvK,IAAA,EACAtE,KAAA,CAAA,MAAA,MAAA,UACAgM,UAAA,CACA9K,MAAA2I,MAAAnH,SAAA,EAAA,IACA,CACA,EACAkJ,QAAA,CACAC,QAAA,OACAC,QAAA,CAAA,EAAA,IACAC,gBAAAlC,MAAAnH,SAAA,EAAA,KACA2E,YAAAwC,MAAAnH,SAAA,EAAA,KACAsJ,UAAA,CAAA9K,MAAA2I,MAAAnH,SAAA,EAAA,KAAA,EACAuJ,YAAA,EACAC,mBAAA,EACAC,SAAA,SAAApC,EAAAC,EAAAjK,EAAA8V,EAAA5L,GACA,IAAAwH,EAAA,CACAnN,IAAA,EACA,EAEA,OADAmN,EAAA,CAAA,OAAA,SAAA,EAAA1H,EAAA,GAAAE,EAAA6L,SAAA,GAAA,KAAA,EACArE,CACA,CACA,EACArF,YAAA,CACA2J,KAAA,CACA,CACAC,WAAA,CAAA,EAAA,EACA,EAEA,EACA9B,SAAA,CACA,CACA7H,KAAA,SACA2J,WAAA,CAAA,EAAA,GACAC,SAAA,CAAA,EACA9B,MAAA,GACAC,IAAA,GACA9P,IAAA,GACAU,OAAA,GACAkR,WACA,0LACAC,WAAA,MACA,EACA,CACA9J,KAAA,SACA2J,WAAA,CAAA,EAAA,GACA7B,MAAA,GACAC,IAAA,GACA9P,IAAA,GACAU,OAAA,EACA,GAEAuH,MAAA,CACA,CACAF,KAAA,WACArM,KAAAsV,EACA9I,YAAA,CAAA,EACAC,SAAA,CACAC,UAAA,CAAAxL,MAAA2I,MAAAnH,SAAA,EAAA,IAAA,CACA,EACAmK,UAAA,CACA3L,MAAA2I,MAAAnH,SAAA,EAAA,KACA4J,UAAA,SAAA7I,GAAA,OAAA3E,OAAAqM,MAAA1H,CAAA,EAAA6H,OAAA,QAAA,CAAA,CACA,EACA9B,IAAA,UACAC,IAAA,UACA2C,YAAA,CACAQ,KAAA,CAAA,CACA,CACA,EACA,CACAP,KAAA,WACA+J,UAAA,EACApW,KAAAsV,EACAhB,MAAA,CAAA,EACA9H,YAAA,CAAA,EACAO,UAAA,CAAAH,KAAA,CAAA,CAAA,EACAC,UAAA,CAAAD,KAAA,CAAA,CAAA,EACAD,SAAA,CAAAC,KAAA,CAAA,CAAA,EACAH,SAAA,CAAAC,UAAA,CAAAxL,MAAA,MAAA,CAAA,EACAqT,YAAA,GACA/K,IAAA,UACAC,IAAA,UACA2C,YAAA,CACAC,KAAA,SACAsB,MAAA,CAAAf,KAAA,CAAA,CAAA,EACAyJ,eAAA,CAAA,CACA,CACA,GAEArJ,MAAA,CACA,CACAsH,MAAA,CAAA,EACAC,YAAA,EACA9H,SAAA,CAAAG,KAAA,CAAA,CAAA,EACAG,UAAA,CACAL,UAAA,CACAxL,MAAA2I,MAAAnH,SAAA,EAAA,IACA,CACA,EACAiK,SAAA,CAAAC,KAAA,CAAA,CAAA,EACAC,UAAA,CACA3L,MAAA2I,MAAAnH,SAAA,EAAA,IACA,CACA,EACA,CACA4R,MAAA,CAAA,EACA8B,UAAA,EACA7B,YAAA,EACA1H,UAAA,CAAAD,KAAA,CAAA,CAAA,EACAH,SAAA,CAAAG,KAAA,CAAA,CAAA,EACAD,SAAA,CAAAC,KAAA,CAAA,CAAA,EACAG,UAAA,CAAAH,KAAA,CAAA,CAAA,CACA,GAEAiB,KAAA,CACA,CACAtJ,KAAA,EACAuJ,MAAA,GAEAC,OAAA,GACA/I,OAAA,IACAgJ,aAAA,CAAA,CACA,EACA,CACAzJ,KAAA,GACAuJ,MAAA,GACA9I,OAAA,GACAV,IAAA,IACA0J,aAAA,CAAA,CACA,GAEAT,OAAA,CACA,CACAhM,KAAA,SACA8K,KAAA,MACA2J,WAAA,EACA/G,WAAA,EACAF,UAAA,CACA7N,MAAA2I,MAAAvI,SAAA,SAAA,CACA,EACAkB,SAAA,CACAuM,UAAA,CACA7N,MAAA2I,MAAAvI,SAAA,SAAA,CACA,CACA,EACAtB,KAAAA,EAAA0P,IAAA,SAAAU,GAAA,OAAAA,EAAA,EAAA,CAAA,CACA,EACA,CACA/D,KAAA,cACA9K,KAAA,MACAvB,KAAAA,EACA+O,UAAA,CACA7N,MAAA2I,MAAAvI,SAAA,SAAA,EACAkT,OAAA3K,MAAAvI,SAAA,MAAA,EACA+F,YAAAwC,MAAAvI,SAAA,SAAA,EACAmT,aAAA5K,MAAAvI,SAAA,MAAA,CACA,CACA,EACA,CACAC,KAAA,MACA8K,KAAA,OACArM,KAAAuV,EACA/H,OAAA,CAAA,EACAsG,WAAA,CAAA,EACApH,UAAA,CACAxH,MAAA,EACAhE,MAAA2I,MAAAvI,SAAA,SAAA,CACA,CACA,EAEA,CAAA,CAEA,EAEA,ECnRAgV,yBAAA,WACA,IAMAlM,EANAmM,EAAA9X,SAAAgN,cACA,gCACA,EAEA8K,IAEAnM,EAAAP,MAAA/J,QAAAyW,EAAA,SAAA,EACAnP,EAAAtI,OAAA4M,QAAAC,KAAA4K,CAAA,EA2EApM,gBAAA/C,EAAAgD,EAzEA,WAAA,MAAA,CACAyE,OAAA,CACAtK,KAAA,OACAyH,UAAA,CACA9K,MAAA2I,MAAAnH,SAAA,EAAA,IACA,CACA,EACA6K,OAAA,CACA,CACAlB,KAAA,MACAuI,OAAA,CAAA,MAAA,OACA4B,OAAA,CAAA,MAAA,OACAC,kBAAA,CAAA,EACA9I,MAAA,CACAf,KAAA,CAAA,EACAT,SAAA,QACA,EACAuK,UAAA,CACA9J,KAAA,CAAA,CACA,EACA5M,KAAA,CACA,CACAyD,MAAA,KACAlC,KAAA,WACAwN,UAAA,CACA7N,MAAA2I,MAAAvI,SAAA,SAAA,CACA,CACA,EACA,CACAmC,MAAA,IACAlC,KAAA,UACAwN,UAAA,CACA7N,MAAA2I,MAAAvI,SAAA,QAAA,CACA,CACA,EACA,CACAmC,MAAA,IACAlC,KAAA,UACAwN,UAAA,CACA7N,MAAA2I,MAAAvI,SAAA,MAAA,CACA,CACA,EACA,CACAmC,MAAA,IACAlC,KAAA,WACAwN,UAAA,CACA7N,MAAA2I,MAAAvI,SAAA,SAAA,CACA,CACA,EACA,CACAmC,MAAA,IACAlC,KAAA,SACAwN,UAAA,CACA7N,MAAA2I,MAAAvI,SAAA,SAAA,CACA,CACA,EAEA,GAEAsK,QAAA,CACAC,QAAA,OACAC,QAAA,CAAA,EAAA,IACAC,gBAAAlC,MAAAnH,SAAA,EAAA,KACA2E,YAAAwC,MAAAnH,SAAA,EAAA,KACAsJ,UAAA,CAAA9K,MAAA2I,MAAAnH,SAAA,EAAA,KAAA,EACAuJ,YAAA,EACAC,mBAAA,EACAE,YAAA,CACAC,KAAA,MACA,CACA,CACA,CAAA,CAEA,EAEA,ECrFAsK,gCAAA,WACA,IAIAvM,EACAhD,EALAwP,EAAAnY,SAAAgN,cAAA,gCAAA,EAEAmL,IAEAxM,EAAAP,MAAA/J,QAAA8W,EAAA,SAAA,EACAxP,EAAAtI,OAAA4M,QAAAC,KAAAiL,CAAA,EAiFAzM,gBAAA/C,EAAAgD,EA/EA,WAAA,MAAA,CACAyE,OAAA,CACAgI,OAAA,WACAtS,KAAA,OACAyH,UAAA,CACA9K,MAAA2I,MAAAnH,SAAA,EAAA,IACA,CACA,EACA6K,OAAA,CACA,CACAlB,KAAA,MACAuI,OAAA,CAAA,MAAA,OACA4B,OAAA1X,OAAA+F,WAAA,IAAA,CAAA,MAAA,OAAA,CAAA,MAAA,OACA4R,kBAAA,CAAA,EACA1H,UAAA,CACA+H,aAAA,GACAzP,YAAAwC,MAAAnH,SAAA,EAAA,KACAuJ,YAAA,CACA,EACA0B,MAAA,CACAf,KAAA,CAAA,EACAT,SAAA,QACA,EACAuK,UAAA,CACA9J,KAAA,CAAA,CACA,EACA5M,KAAA,CACA,CACAyD,MAAA,KACAlC,KAAA,UACAwN,UAAA,CACA7N,MAAA2I,MAAAvI,SAAA,SAAA,CACA,CACA,EACA,CACAmC,MAAA,IACAlC,KAAA,QACAwN,UAAA,CACA7N,MAAA2I,MAAAvI,SAAA,QAAA,CACA,CACA,EACA,CACAmC,MAAA,IACAlC,KAAA,UACAwN,UAAA,CACA7N,MAAA2I,MAAAvI,SAAA,MAAA,CACA,CACA,EACA,CACAmC,MAAA,IACAlC,KAAA,WACAwN,UAAA,CACA7N,MAAA2I,MAAAvI,SAAA,SAAA,CACA,CACA,EACA,CACAmC,MAAA,IACAlC,KAAA,UACAwN,UAAA,CACA7N,MAAA2I,MAAAvI,SAAA,SAAA,CACA,CACA,EAEA,GAEAsK,QAAA,CACAC,QAAA,OACAC,QAAA,CAAA,EAAA,IACAC,gBAAAlC,MAAAnH,SAAA,EAAA,KACA2E,YAAAwC,MAAAnH,SAAA,EAAA,KACAsJ,UAAA,CAAA9K,MAAA2I,MAAAnH,SAAA,EAAA,KAAA,EACAuJ,YAAA,EACAC,mBAAA,EACAE,YAAA,CACAC,KAAA,MACA,CACA,CACA,CAAA,CAEA,EAEAxC,MAAAhL,OAAA,WACAC,OAAA+F,WAAA,IACAuC,EAAAoD,UAAA,CACA+C,OAAA,CACA,CACAiJ,OAAA,CAAA,MAAA,MACA,EAEA,CAAA,EAEApP,EAAAoD,UAAA,CAAA+C,OAAA,CAAA,CAAAiJ,OAAA,CAAA,MAAA,MAAA,EAAA,CAAA,CAEA,CAAA,EAEA,ECvGAO,4BAAA,WACA,IAAAC,EAAAvY,SAAAgN,cACA,oCACA,EAEA,GAAAuL,EAAA,CAsBA,IApBA,IAAA5M,EAAAP,MAAA/J,QAAAkX,EAAA,SAAA,EACA5P,EAAAtI,OAAA4M,QAAAC,KAAAqL,CAAA,EAEAhX,EAAA,GACAiX,EAAA,CAAA,IAAAvQ,KAAA,KAAA,EAAA,CAAA,EAEAjD,EAAA,IAAAiG,KAAAE,OAAA,EAEAsN,EAAA,WAGA,OAFAD,EAAA,IAAAvQ,KAAA,CAAAuQ,EAJA,KAIA,EACAxT,EAAAA,EAAA,GAAAiG,KAAAE,OAAA,EAAA,GACA,CACArI,KAAA0V,EAAAE,SAAA,EACA1T,MAAA,CACA,CAAAwT,EAAAjE,YAAA,EAAAiE,EAAAG,SAAA,EAAA,EAAAH,EAAA3N,QAAA,GAAA+N,KAAA,GAAA,EACA3N,KAAAiG,MAAAlM,CAAA,EAEA,CACA,EAEAsF,EAAA,EAAAA,EAAA,IAAAA,GAAA,EACA/I,EAAA+Q,KAAAmG,EAAA,CAAA,EAuEA/M,gBAAA/C,EAAAgD,EApEA,WAAA,MAAA,CACAwB,QAAA,CACAC,QAAA,OACAO,YAAA,CACAwJ,UAAA,CAAA,CACA,EACA9J,QAAA,CAAA,EAAA,IACAC,gBAAAlC,MAAAnH,SAAA,EAAA,KACA2E,YAAAwC,MAAAnH,SAAA,EAAA,KACAsJ,UAAA,CAAA9K,MAAA2I,MAAAnH,SAAA,EAAA,KAAA,EACAuJ,YAAA,EACAC,mBAAA,EACAI,UAAAxB,gBACA,EACAyB,MAAA,CACAF,KAAA,OACAU,UAAA,CACAH,KAAA,CAAA,CACA,EACAC,UAAA,CACA3L,MAAA2I,MAAAnH,SAAA,EAAA,IACA,EAEA+J,SAAA,CACAC,UAAA,CACAxL,MAAA2I,MAAAnH,SAAA,EAAA,IACA,CACA,EACA0J,YAAA,CACAM,UAAA,CACAxL,MAAA2I,MAAAnH,SAAA,EAAA,IACA,CACA,CACA,EACAsK,MAAA,CACAX,KAAA,QACAG,YAAA,CAAA,EAAA,QACAO,UAAA,CACAH,KAAA,CAAA,CACA,EACAC,UAAA,CACA3L,MAAA2I,MAAAnH,SAAA,EAAA,IACA,CACA,EACA6K,OAAA,CACA,CACAhM,KAAA,QACA8K,KAAA,OACAyH,WAAA,CAAA,EACAC,eAAA,CAAA,EACA/T,KAAAA,EACA0M,UAAA,CACAxL,MAAA2I,MAAAvI,SAAA,SAAA,CACA,EACAyN,UAAA,CACA7N,MAAA2I,MAAAnH,SAAA,EAAA,KACA2E,YAAAwC,MAAAvI,SAAA,SAAA,EACA2K,YAAA,CACA,EACAwB,OAAA,SACAyB,WAAA,EACA,GAEArB,KAAA,CACAC,MAAA,EAAAvJ,KAAA,KAAAwJ,OAAA,MAAAzJ,IAAA,IACA,CACA,CAAA,CAEA,EAEA+L,YAAA,WACA,IAAA,IAAAtH,EAAA,EAAAA,EAAA,EAAAA,GAAA,EACA/I,EAAAsX,MAAA,EACAtX,EAAA+Q,KAAAmG,EAAA,CAAA,EAGA9P,EAAAoD,UAAA,CACA+C,OAAA,CACA,CACAvN,KAAAA,CACA,EAEA,CAAA,CACA,EAAA,GAAA,CACA,CACA,ECpHAuX,2BAAA,WACA,IAIAnN,EAJAoN,EAAA/Y,SAAAgN,cAAA,mCAAA,EAEA+L,IAEApN,EAAAP,MAAA/J,QAAA0X,EAAA,SAAA,EACApQ,EAAAtI,OAAA4M,QAAAC,KAAA6L,CAAA,EAwFArN,gBAAA/C,EAAAgD,EAtFA,WAAA,MAAA,CACAmD,OAAA,CACA,CACAqH,OAAA,OACAvI,KAAA,QACAmK,OAAA,CAAA,MAAA,OACAiB,WAAA,IACAC,SAAA,EACAlO,IAAA,EACAC,IAAA,EACA8K,YAAA,EACA9H,SAAA,CACAC,UAAA,CACAxH,MAAA,EACAhE,MAAA,CACA,CAAA,IAAA2I,MAAAvI,SAAA,QAAA,GACA,CAAA,GAAAuI,MAAAvI,SAAA,SAAA,GACA,CAAA,IAAAuI,MAAAvI,SAAA,MAAA,GACA,CAAA,EAAAuI,MAAAvI,SAAA,SAAA,GAEA,CACA,EACAqW,QAAA,CACAC,KAAA,yCACAzY,OAAA,MACA+F,MAAA,GACA2S,aAAA,CAAA,EAAA,QACA9I,UAAA,CACA7N,MAAA,MACA,CACA,EACAyL,SAAA,CACAxN,OAAA,GACAuN,UAAA,CACAxL,MAAA,OACAgE,MAAA,CACA,CACA,EACA6H,UAAA,CACA5N,OAAA,GACAuN,UAAA,CACAxL,MAAA,OACAgE,MAAA,CACA,CACA,EACA2H,UAAA,CACA3L,MAAA2I,MAAAnH,SAAA,EAAA,KACAoV,SAAA,CAAA,GACAxL,UAAA,SAAA7I,GACA,MAAA,OAAAA,EACA,YAEA,OAAAA,EACA,OAEA,OAAAA,EACA,OAEA,OAAAA,EACA,MAEA,EACA,CACA,EACA4P,MAAA,CACAwE,aAAA,CAAA,EAAA,QACA3W,MAAA2I,MAAAnH,SAAA,EAAA,IACA,EACAkI,OAAA,CACAiN,aAAA,CAAA,EAAA,MACA5H,eAAA,CAAA,EACA3D,UAAA,SAAA7I,GACA,OAAAiG,KAAAiG,MAAA,IAAAlM,CAAA,CACA,EACAvC,MAAA,MACA,EACAlB,KAAA,CACA,CACAyD,MAAA,GACAlC,KAAA,OACA,EAEA,EAEA,CAAA,CAEA,EAEA,EChGAwW,+BAAA,WACA,IAMA3N,EANA4N,EAAAvZ,SAAAgN,cACA,wCACA,EAEAuM,IAEA5N,EAAAP,MAAA/J,QAAAkY,EAAA,SAAA,EACA5Q,EAAAtI,OAAA4M,QAAAC,KAAAqM,CAAA,EA4HA7N,gBAAA/C,EAAAgD,EA1HA,WAAA,MAAA,CACAmD,OAAA,CACA,CACAlB,KAAA,QACAoL,WAAA,GACAC,SAAA,CAAA,IACA9C,OAAA,MACA+C,QAAA,CACA/K,KAAA,CAAA,CACA,EACA4J,OAAA,CAAA,MAAA,OACAyB,SAAA,CACArL,KAAA,CAAA,EACAsL,QAAA,CAAA,EACAC,SAAA,CAAA,EACAC,KAAA,CAAA,EACArJ,UAAA,CACA7N,MAAA2I,MAAAvI,SAAA,MAAA,CACA,CACA,EACAmL,SAAA,CACAC,UAAA,CACAxH,MAAA,EACAhE,MAAA,CAAA,CAAA,EAAA2I,MAAAvI,SAAA,UAAA,GACA,CACA,EACAyL,UAAA,CACAH,KAAA,CAAA,CACA,EACAD,SAAA,CACAC,KAAA,CAAA,CACA,EACAC,UAAA,CACAD,KAAA,CAAA,CACA,EACA5M,KAAA,CAAA,IACA4K,OAAA,CACAgC,KAAA,CAAA,CACA,EACAiD,kBAAA,GACA,EACA,CACAxD,KAAA,QACAoL,WAAA,GACAC,SAAA,CAAA,IACA9C,OAAA,MACA+C,QAAA,CACA/K,KAAA,CAAA,CACA,EACA4J,OAAA,CAAA,MAAA,OACAyB,SAAA,CACArL,KAAA,CAAA,EACAsL,QAAA,CAAA,EACAC,SAAA,CAAA,EACAC,KAAA,CAAA,EACArJ,UAAA,CACA7N,MAAA2I,MAAAvI,SAAA,SAAA,CACA,CACA,EACAmL,SAAA,CACAC,UAAA,CACAxH,MAAA,EACAhE,MAAA,CAAA,CAAA,EAAA2I,MAAAvI,SAAA,UAAA,GACA,CACA,EACAyL,UAAA,CACAH,KAAA,CAAA,CACA,EACAD,SAAA,CACAC,KAAA,CAAA,CACA,EACAC,UAAA,CACAD,KAAA,CAAA,CACA,EACA5M,KAAA,CAAA,IACA4K,OAAA,CACAgC,KAAA,CAAA,CACA,EACAiD,kBAAA,GACA,EACA,CACAxD,KAAA,QACAoL,WAAA,GACAC,SAAA,CAAA,IACA9C,OAAA,MACA+C,QAAA,CACA/K,KAAA,CAAA,CACA,EACA4J,OAAA,CAAA,MAAA,OACAyB,SAAA,CACArL,KAAA,CAAA,EACAsL,QAAA,CAAA,EACAC,SAAA,CAAA,EACAC,KAAA,CAAA,EACArJ,UAAA,CACA7N,MAAA2I,MAAAvI,SAAA,SAAA,CACA,CACA,EACAmL,SAAA,CACAC,UAAA,CACAxH,MAAA,EACAhE,MAAA,CAAA,CAAA,EAAA2I,MAAAvI,SAAA,UAAA,GACA,CACA,EACAyL,UAAA,CACAH,KAAA,CAAA,CACA,EACAD,SAAA,CACAC,KAAA,CAAA,CACA,EACAC,UAAA,CACAD,KAAA,CAAA,CACA,EACA5M,KAAA,CAAA,IACA4K,OAAA,CACAgC,KAAA,CAAA,CACA,EACAiD,kBAAA,GACA,EAEA,CAAA,CAEA,EAEA,ECtIAwI,gCAAA,WACA,IAIAjO,EAGAU,EAPAwN,EAAA7Z,SAAAgN,cAAA,yCAAA,EAEA6M,IAEAlO,EAAAP,MAAA/J,QAAAwY,EAAA,SAAA,EACAlR,EAAAtI,OAAA4M,QAAAC,KAAA2M,CAAA,EAEAxN,EAAA,SAAAd,GAAA,MAAA,yHAAArK,OAGAqK,EAAA,GAAA9I,MAAA,yBAAA,EAAAvB,OACAqK,EAAA,GAAAzI,KAAA,KAAA,EAAA5B,OAAAqK,EAAA,GAAAvG,MAAA,yCAAA,CAAA,EAiHA0G,gBAAA/C,EAAAgD,EA5GA,WAAA,MAAA,CACAwB,QAAA,CACAC,QAAA,OACAC,QAAA,CAAA,EAAA,IACAC,gBAAAlC,MAAAnH,SAAA,EAAA,KACA2E,YAAAwC,MAAAnH,SAAA,EAAA,KACAsJ,UAAA,CAAA9K,MAAA2I,MAAAnH,SAAA,EAAA,KAAA,EACAuJ,YAAA,EACAK,UAAAxB,EACAoB,mBAAA,EACAE,YAAA,CACAC,KAAA,MACA,CACA,EACAkB,OAAA,CACA,CACAlB,KAAA,QACAuI,OAAA,OACA2D,OAAA,CACA3L,KAAA,CAAA,EACA4L,UAAA,CAAA,EACAvO,KAAA,GACA8E,UAAA,CACA7N,MAAA2I,MAAAvI,SAAA,SAAA,CACA,CACA,EAEA2W,SAAA,CACArL,KAAA,CAAA,EACAsL,QAAA,CAAA,EACAC,SAAA,CAAA,CACA,EACA1L,SAAA,CACA0L,SAAA,CAAA,CACA,EACAxL,SAAA,CACAC,KAAA,CAAA,CACA,EACAG,UAAA,CACAL,UAAA,CACAxH,MAAA,EACAhE,MAAA2I,MAAAnH,SAAA,EAAA,IACA,CACA,EACAmK,UAAA,CACAiL,SAAA,GACA5W,MAAA2I,MAAAnH,SAAA,EAAA,IACA,EACA1C,KAAA,CACA,CACAyD,MAAA,GACAlC,KAAA,UACA8R,MAAA,CACAwE,aAAA,CAAA,OAAA,MACA,EACAjN,OAAA,CACAiN,aAAA,CAAA,OAAA,MACA,EACA9I,UAAA,CACA7N,MAAA2I,MAAAvI,SAAA,SAAA,CACA,CACA,EACA,CACAmC,MAAA,GACAlC,KAAA,OACA8R,MAAA,CACAwE,aAAA,CAAA,KAAA,MACA,EACAjN,OAAA,CACAiN,aAAA,CAAA,KAAA,MACA,EAEA9I,UAAA,CACA7N,MAAA2I,MAAAvI,SAAA,SAAA,CACA,CACA,EACA,CACAmC,MAAA,GACAlC,KAAA,WACA8R,MAAA,CACAwE,aAAA,CAAA,MAAA,MACA,EACAjN,OAAA,CACAiN,aAAA,CAAA,MAAA,MACA,EAEA9I,UAAA,CACA7N,MAAA2I,MAAAvI,SAAA,SAAA,CACA,CACA,GAEA+R,MAAA,CACAoF,SAAA,GACAvX,MAAA2I,MAAAnH,SAAA,EAAA,IACA,EACAkI,OAAA,CACA1F,MAAA,GACAF,OAAA,GACAyT,SAAA,GACAvX,MAAA,OACA6K,gBAAA,OACA+K,aAAA,EACAxK,UAAA,UACA,CACA,EAEA,CAAA,CAEA,EAEA,EC/HAoM,8BAAA,WACA,IAIAtO,EAGAU,EAPA6N,EAAAla,SAAAgN,cAAA,sCAAA,EAEAkN,IAEAvO,EAAAP,MAAA/J,QAAA6Y,EAAA,SAAA,EACAvR,EAAAtI,OAAA4M,QAAAC,KAAAgN,CAAA,EAEA7N,EAAA,SAAAd,GAAA,MAAA,yHAAArK,OAGAqK,EAAA,GAAA9I,MAAA,yBAAA,EAAAvB,OACAqK,EAAA,GAAAzI,KAAA,KAAA,EAAA5B,OAAAqK,EAAA,GAAAvG,MAAA,yCAAA,CAAA,EAwFA0G,gBAAA/C,EAAAgD,EAnFA,WAAA,MAAA,CACAwB,QAAA,CACAC,QAAA,OACAC,QAAA,CAAA,EAAA,IACAC,gBAAAlC,MAAAnH,SAAA,EAAA,KACA2E,YAAAwC,MAAAnH,SAAA,EAAA,KACAsJ,UAAA,CAAA9K,MAAA2I,MAAAnH,SAAA,EAAA,KAAA,EACAuJ,YAAA,EACAK,UAAAxB,EACAoB,mBAAA,EACAE,YAAA,CACAC,KAAA,MACA,CACA,EACAkB,OAAA,CACA,CACAlB,KAAA,QACAmK,OAAA,CAAA,MAAA,OACA5B,OAAA,OACA6C,WAAA,IACAC,SAAA,EACAO,SAAA,CACArL,KAAA,CAAA,EACA1H,MAAA,GACA6J,UAAA,CACA7N,MAAA2I,MAAAvI,SAAA,MAAA,CACA,CACA,EACAyN,UAAA,CACA7N,MAAA2I,MAAAvI,SAAA,MAAA,EACA4P,YAAArH,MAAA5I,UAAA4I,MAAAvI,SAAA,SAAA,EAAA,EAAA,EACA2P,WAAA,GACAiC,cAAA,EACAC,cAAA,CACA,EACA1G,SAAA,CACAC,UAAA,CACAxH,MAAA,EACA,CACA,EACAyH,SAAA,CACAC,KAAA,CAAA,CACA,EACAG,UAAA,CACAL,UAAA,CACAxH,MAAA,EACAhE,MAAA2I,MAAAnH,SAAA,EAAA,IACA,CACA,EACAmK,UAAA,CACAiL,SAAA,GACA5W,MAAA2I,MAAAnH,SAAA,EAAA,IACA,EACA6V,OAAA,CACA3L,KAAA,CAAA,EACA4L,UAAA,CAAA,EACAvO,KAAA,GACA8E,UAAA,CACA7N,MAAA2I,MAAAvI,SAAA,MAAA,CACA,CACA,EACA+R,MAAA,CACAzG,KAAA,CAAA,CACA,EACAhC,OAAA,CACAqF,eAAA,CAAA,EACAwI,SAAA,GACAZ,aAAA,CAAA,EAAA,MACA,EACA7X,KAAA,CACA,CACAyD,MAAA,GACAmH,OAAA,CACA6N,SAAA,GACAvX,MAAA2I,MAAAnH,SAAA,EAAA,KACAmV,aAAA,CAAA,EAAA,MACA,CACA,EAEA,EAEA,CAAA,CAEA,EAEA,ECtGAe,0BAAA,WACA,IAIAxO,EAGAU,EAPA+N,EAAApa,SAAAgN,cAAA,kCAAA,EAEAoN,IAEAzO,EAAAP,MAAA/J,QAAA+Y,EAAA,SAAA,EACAzR,EAAAtI,OAAA4M,QAAAC,KAAAkN,CAAA,EAEA/N,EAAA,SAAAd,GAAA,MAAA,yHAAArK,OAGAqK,EAAA,GAAA9I,MAAA,yBAAA,EAAAvB,OACAqK,EAAA,GAAAzI,KAAA,KAAA,EAAA5B,OAAAqK,EAAA,GAAAvG,MAAA,yCAAA,CAAA,EAmFA0G,gBAAA/C,EAAAgD,EA9EA,WAAA,MAAA,CACAwB,QAAA,CACAC,QAAA,OACAC,QAAA,CAAA,EAAA,IACAC,gBAAAlC,MAAAnH,SAAA,EAAA,KACA2E,YAAAwC,MAAAnH,SAAA,EAAA,KACAsJ,UAAA,CAAA9K,MAAA2I,MAAAnH,SAAA,EAAA,KAAA,EACAuJ,YAAA,EACAK,UAAAxB,EACAoB,mBAAA,EACAE,YAAA,CACAC,KAAA,MACA,CACA,EACAkB,OAAA,CACA,CACAlB,KAAA,QACAuI,OAAA,OACA6C,WAAA,GACAC,SAAA,CAAA,IACAC,QAAA,CACA/K,KAAA,CAAA,CACA,EACAqL,SAAA,CACArL,KAAA,CAAA,EACAsL,QAAA,CAAA,EACAC,SAAA,CAAA,EACAC,KAAA,CAAA,EACArJ,UAAA,CACA9C,YAAA,EACA5E,YAAAwC,MAAAnH,SAAA,EAAA,IACA,CACA,EACA+J,SAAA,CACAC,UAAA,CACAxH,MAAA,EACA,CACA,EACA6H,UAAA,CACAH,KAAA,CAAA,EACAkL,SAAA,EACA3Y,OAAA,EACA,EACAwN,SAAA,CACAC,KAAA,CAAA,CACA,EACAC,UAAA,CACAD,KAAA,CAAA,EACAkL,SAAA,EACA,EACA9X,KAAA,CACA,CACAyD,MAAA,GACA4P,MAAA,CACAwE,aAAA,CAAA,KAAA,KACA,EACAjN,OAAA,CACAiN,aAAA,CAAA,KAAA,KACA,EACA9I,UAAA,CACA7N,MAAA2I,MAAAvI,SAAA,SAAA,CACA,CACA,GAEA+R,MAAA,CACAoF,SAAA,EACA,EACA7N,OAAA,CACA1F,MAAA,GACAF,OAAA,GACAyT,SAAA,GACAvX,MAAA,OACAoL,UAAA,UACA,CACA,EAEA,CAAA,CAEA,EAEA,ECjGAwM,4BAAA,WACA,IAIA1O,EACAhD,EAEA0D,EAMAiO,EAsBA/Y,EAnCAgZ,EAAAva,SAAAgN,cAAA,oCAAA,EAEAuN,IAEA5O,EAAAP,MAAA/J,QAAAkZ,EAAA,SAAA,EACA5R,EAAAtI,OAAA4M,QAAAC,KAAAqN,CAAA,EAEAlO,EAAA,SAAAd,GAAA,MAAA,mHAAArK,OAEAqK,EAAA,GAAAzI,KAAA,KAAA,EAAA5B,OAAAqK,EAAA,GAAAvG,MAAA,sCAAA,CAAA,EAIAsV,EAAA,CACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,KAEA/Y,EAAA,CACA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,GAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IACA,KAkGAmK,gBAAA/C,EAAAgD,EA/FA,WAAA,MAAA,CACAwB,QAAA,CACAC,QAAA,OACAC,QAAA,CAAA,EAAA,IACAC,gBAAAlC,MAAAnH,SAAA,EAAA,KACA2E,YAAAwC,MAAAnH,SAAA,EAAA,KACAsJ,UAAA,CAAA9K,MAAA2I,MAAAnH,SAAA,EAAA,KAAA,EACAuJ,YAAA,EACAC,mBAAA,EACAE,YAAA,CACAC,KAAA,MACA,EACAC,UAAAxB,CACA,EACAuI,MAAA,CACA/T,KAAA,mCACA0M,UAAA,CACA9K,MAAA2I,MAAAnH,SAAA,EAAA,IACA,EACA6B,KAAA,QACA,EACAgI,MAAA,CACAvM,KAAA+Y,EACAlM,UAAA,CACAoM,OAAA,CAAA,EACAjN,UAAA,CACA9K,MAAA,MACA,CACA,EACAyL,SAAA,CACAC,KAAA,CAAA,CACA,EACAH,SAAA,CACAG,KAAA,CAAA,CACA,EACAsM,EAAA,EACA,EACAlM,MAAA,CACAP,SAAA,CACAG,KAAA,CAAA,CACA,EACAD,SAAA,CACAC,KAAA,CAAA,CACA,EACAC,UAAA,CACAb,UAAA,CACA9K,MAAA2I,MAAAnH,SAAA,EAAA,IACA,CACA,EACAqK,UAAA,CACAH,KAAA,CAAA,EACAF,UAAA,CACAxL,MAAA2I,MAAAvI,SAAA,EAAA,IACA,CACA,CACA,EACA4S,SAAA,CACA,CACA7H,KAAA,QACA,GAEAkB,OAAA,CACA,CACAlB,KAAA,MACA9K,KAAA,QACA4X,eAAA,CAAA,EACApK,UAAA,CACA7N,MAAA,IAAApC,OAAA4M,QAAA0N,QAAAC,eAAA,EAAA,EAAA,EAAA,EAAA,CACA,CAAAC,OAAA,EAAApY,MAAA2I,MAAAvI,SAAA,MAAA,CAAA,EACA,CAAAgY,OAAA,GAAApY,MAAA2I,MAAAvI,SAAA,SAAA,CAAA,EACA,CAAAgY,OAAA,EAAApY,MAAA2I,MAAAvI,SAAA,SAAA,CAAA,EACA,EACA0N,gBAAA,CAAA,EAAA,EAAA,EAAA,EACA,EACAxM,SAAA,CACAuM,UAAA,CACA7N,MAAA,IAAApC,OAAA4M,QAAA0N,QAAAC,eAAA,EAAA,EAAA,EAAA,EAAA,CACA,CAAAC,OAAA,EAAApY,MAAA2I,MAAAvI,SAAA,SAAA,CAAA,EACA,CAAAgY,OAAA,GAAApY,MAAA2I,MAAAvI,SAAA,SAAA,CAAA,EACA,CAAAgY,OAAA,EAAApY,MAAA2I,MAAAvI,SAAA,MAAA,CAAA,EACA,CACA,CACA,EACAtB,KAAAA,CACA,GAEA6N,KAAA,CACAC,MAAA,EACAvJ,KAAA,EACAwJ,OAAA,EACAzJ,IAAA,MACA0J,aAAA,CAAA,CACA,CACA,CAAA,CAEA,EAGA5G,EAAAmS,GAAA,QAAA,SAAAvP,GACA5C,EAAAoS,eAAA,CACAnN,KAAA,WACAoN,WAAAV,EAAArP,KAAAD,IAAAO,EAAA0P,UAAAC,EAAA,CAAA,GACAC,SAAAb,EAAArP,KAAAF,IAAAQ,EAAA0P,UAAAC,EAAA3Z,EAAAb,OAAA,CAAA,EACA,CAAA,CACA,CAAA,EAEA,ECnJA0a,wBAAA,WAOA,IANA,IAaAzP,EAZA0P,EAAArb,SAAAgN,cADA,+BACA,EACAsO,EAAA,CAAA,MAAA,KAAA,KAAA,KAAA,KAAA,MAAA,MAAA,KAAA,KAAA,KAAA,KAAA,OACA5Q,EAAA,CAAA,SAAA,UAAA,YAAA,WAAA,SAAA,WAAA,UAEAnJ,EAAA,GACA+I,EAAA,EAAAA,EAAA,EAAAA,GAAA,EACA,IAAA,IAAA4M,EAAA,EAAAA,EAAA,GAAAA,GAAA,EACA3V,EAAA+Q,KAAA,CAAA4E,EAAA5M,EAAAc,MAAAN,gBAAA,EAAA,EAAA,EAAA,EAIAuQ,IACA1P,EAAAP,MAAA/J,QAAAga,EAAA,SAAA,EACA1S,EAAAtI,OAAA4M,QAAAC,KAAAmO,CAAA,EAyFA3P,gBAAA/C,EAAAgD,EAvFA,WAAA,MAAA,CACAwB,QAAA,CACAO,SAAA,MACAL,QAAA,CAAA,EAAA,IACAC,gBAAAlC,MAAAnH,SAAA,EAAA,KACA2E,YAAAwC,MAAAnH,SAAA,EAAA,KACAsJ,UAAA,CAAA9K,MAAA2I,MAAAnH,SAAA,EAAA,KAAA,EACAuJ,YAAA,CACA,EACA4B,KAAA,CACAC,MAAA,EACAvJ,KAAA,EACAD,IAAA,EACAyJ,OAAA,MACAC,aAAA,CAAA,CACA,EACAzB,MAAA,CACAF,KAAA,WACArM,KAAA+Z,EACA5I,UAAA,CACAvE,KAAA,CAAA,CACA,EACAC,UAAA,CACA3L,MAAA2I,MAAAnH,SAAA,EAAA,IACA,EACA+J,SAAA,CACAG,KAAA,CAAA,EACAF,UAAA,CACAxL,MAAA2I,MAAAnH,SAAA,EAAA,IACA,CACA,CACA,EACAsK,MAAA,CACAX,KAAA,WACArM,KAAAmJ,EACA0D,UAAA,CACAP,UAAA,SAAA7I,GAAA,OAAAA,EAAAhD,UAAA,EAAA,CAAA,CAAA,EACAS,MAAA2I,MAAAnH,SAAA,EAAA,IACA,EACAyO,UAAA,CACAvE,KAAA,CAAA,CACA,EACAH,SAAA,CACAG,KAAA,CAAA,EACAF,UAAA,CACAxL,MAAA2I,MAAAnH,SAAA,EAAA,IACA,CACA,CACA,EACAuK,UAAA,CACAzD,IAAA,EACAC,IAAA,GACA6J,WAAA,CAAA,EACAuD,OAAA,aACAtS,KAAA,SACAwJ,OAAA,KACA/B,UAAA,CACA9K,MAAA2I,MAAAnH,SAAA,EAAA,KACAsN,WAAA,GACA,EACAgK,QAAA,CACA9Y,MAAA,CACA2I,MAAA5I,UAAA4I,MAAAhI,UAAA,EAAAC,QAAA,CAAA,EACA+H,MAAA5I,UAAA4I,MAAAhI,UAAA,EAAAI,KAAA,CAAA,EACA4H,MAAA5I,UAAA4I,MAAAhI,UAAA,EAAAG,QAAA,CAAA,EAIA,CACA,EACAuL,OAAA,CACA,CACAlB,KAAA,UACArM,KAAAA,EACA2N,MAAA,CACAf,KAAA,CAAA,CACA,EACApK,SAAA,CACAuM,UAAA,CACAkC,WAAA,GACAC,YAAArH,MAAA5I,UAAA4I,MAAAhI,UAAA,EAAAW,SAAA,EAAA,CACA,CACA,CACA,EAEA,CAAA,CAEA,EAEA,EC1GAyX,oCAAA,WAOA,IANA,IAaA7P,EAZA0P,EAAArb,SAAAgN,cADA,qCACA,EACAsO,EAAA,CAAA,MAAA,KAAA,KAAA,KAAA,KAAA,MAAA,MAAA,KAAA,KAAA,KAAA,KAAA,OACA5Q,EAAA,CAAA,SAAA,UAAA,YAAA,WAAA,SAAA,WAAA,UAEAnJ,EAAA,GACA+I,EAAA,EAAAA,EAAA,EAAAA,GAAA,EACA,IAAA,IAAA4M,EAAA,EAAAA,EAAA,GAAAA,GAAA,EACA3V,EAAA+Q,KAAA,CAAA4E,EAAA5M,EAAAc,MAAAN,gBAAA,EAAA,EAAA,EAAA,EAIAuQ,IACA1P,EAAAP,MAAA/J,QAAAga,EAAA,SAAA,EACA1S,EAAAtI,OAAA4M,QAAAC,KAAAmO,CAAA,EA6FA3P,gBAAA/C,EAAAgD,EA3FA,WAAA,MAAA,CACA8P,cAAA,CACArQ,MAAA5I,UAAA4I,MAAAhI,UAAA,EAAAI,KAAA,CAAA,EACA4H,MAAA5I,UAAA4I,MAAAhI,UAAA,EAAAC,QAAA,CAAA,GAGA8J,QAAA,CACAO,SAAA,MACAL,QAAA,CAAA,EAAA,IACAC,gBAAAlC,MAAAnH,SAAA,EAAA,KACA2E,YAAAwC,MAAAnH,SAAA,EAAA,KACAsJ,UAAA,CAAA9K,MAAA2I,MAAAnH,SAAA,EAAA,KAAA,EACAuJ,YAAA,CACA,EACA4B,KAAA,CACAC,MAAA,EACAvJ,KAAA,EACAD,IAAA,EACAyJ,OAAA,EACAC,aAAA,CAAA,CACA,EACAzB,MAAA,CACAI,SAAA,CAAAC,KAAA,CAAA,CAAA,EACAP,KAAA,WACArM,KAAA+Z,EACA5I,UAAA,CACAvE,KAAA,CAAA,CACA,EACAC,UAAA,CACA3L,MAAA2I,MAAAnH,SAAA,EAAA,IACA,EACA+J,SAAA,CACAG,KAAA,CAAA,EACAF,UAAA,CACAxL,MAAA2I,MAAAnH,SAAA,EAAA,IACA,CACA,CACA,EACAsK,MAAA,CACAL,SAAA,CAAAC,KAAA,CAAA,CAAA,EACAP,KAAA,WACArM,KAAAmJ,EACA0D,UAAA,CACAP,UAAA,SAAA7I,GAAA,OAAAA,EAAAhD,UAAA,EAAA,CAAA,CAAA,EACAS,MAAA2I,MAAAnH,SAAA,EAAA,IACA,EACAyO,UAAA,CACAvE,KAAA,CAAA,CACA,EACAH,SAAA,CACAG,KAAA,CAAA,EACAF,UAAA,CACAxL,MAAA2I,MAAAnH,SAAA,EAAA,IACA,CACA,CACA,EACAuK,UAAA,CACAL,KAAA,CAAA,EACApD,IAAA,EACAC,IAAA,GACA6J,WAAA,CAAA,EACAuD,OAAA,aACAtS,KAAA,SACAwJ,OAAA,KACA/B,UAAA,CACA9K,MAAA2I,MAAAnH,SAAA,EAAA,KACAsN,WAAA,GACA,CACA,EAEAzC,OAAA,CACA,CACAlB,KAAA,UACArM,KAAAA,EACA2N,MAAA,CACAf,KAAA,CAAA,CACA,EACAmC,UAAA,CACA1H,YAAAwC,MAAAnH,SAAA,EAAA,KACAuJ,YAAA,CACA,EACAzJ,SAAA,CACAuM,UAAA,CACAkC,WAAA,GACAC,YAAArH,MAAA5I,UAAA4I,MAAAhI,UAAA,EAAAW,SAAA,EAAA,CACA,CACA,CACA,EAEA,CAAA,CAEA,EAEA,EC9GA2X,8BAAA,WACA,IAIA/P,EAGA8D,EAeAlO,EAtBAoa,EAAA3b,SAAAgN,cAAA,sCAAA,EAEA2O,IAEAhQ,EAAAP,MAAA/J,QAAAsa,EAAA,SAAA,EACAhT,EAAAtI,OAAA4M,QAAAC,KAAAyO,CAAA,EAEAlM,EAAA,CACA,UACA,WACA,QACA,QACA,MACA,OACA,OACA,SACA,YACA,UACA,WACA,YAGAlO,EAAA,CAAA,KAAA,KAAA,KAAA,KAAA,KAAA,KAAA,KAAA,KAAA,KAAA,KAAA,KAAA,MA4EAmK,gBAAA/C,EAAAgD,EA1EA,WAAA,MAAA,CACAwB,QAAA,CACAC,QAAA,OACAC,QAAA,CAAA,EAAA,IACAC,gBAAAlC,MAAAnH,SAAA,EAAA,KACA2E,YAAAwC,MAAAnH,SAAA,EAAA,KACAsJ,UAAA,CAAA9K,MAAA2I,MAAAnH,SAAA,EAAA,KAAA,EACAuJ,YAAA,EACAK,UAAAxB,iBACAoB,mBAAA,EACAE,YAAA,CACAC,KAAA,MACA,CACA,EACAE,MAAA,CACAF,KAAA,QACAG,YAAA,CAAA,EACAC,SAAA,CACAG,KAAA,CAAA,EACAF,UAAA,CACAxL,MAAA2I,MAAAnH,SAAA,EAAA,IACA,CACA,EACAiK,SAAA,CAAAC,KAAA,CAAA,CAAA,EACAC,UAAA,CACA3L,MAAA2I,MAAAnH,SAAA,EAAA,IACA,EACAqK,UAAA,CACAH,KAAA,CAAA,CACA,EACApD,IAAA,GACA,EACAwD,MAAA,CACAX,KAAA,WACArM,KAAAkO,EACA1B,YAAA,CAAA,EACAK,UAAA,CACAP,UAAA,SAAA7I,GAAA,OAAAA,EAAAhD,UAAA,EAAA,CAAA,CAAA,EACAmM,KAAA,CAAA,EACA1L,MAAA2I,MAAAnH,SAAA,EAAA,KACAoK,OAAA,EACA,EACAC,UAAA,CACAH,KAAA,CAAA,EACAF,UAAA,CACAxL,MAAA2I,MAAAnH,SAAA,EAAA,IACA,CACA,EACAiK,SAAA,CAAAC,KAAA,CAAA,CAAA,EACAH,SAAA,CACAC,UAAA,CACAxL,MAAA2I,MAAAnH,SAAA,EAAA,IACA,CACA,CACA,EACA6K,OAAA,CACA,CACAlB,KAAA,MACA9K,KAAA,QACAvB,KAAAA,EACA0M,UAAA,CAAAxL,MAAA2I,MAAAvI,SAAA,SAAA,CAAA,EACAyN,UAAA,CACA7N,MAAA2I,MAAAvI,SAAA,SAAA,EACA0N,gBAAA,CAAA,EAAA,EAAA,EAAA,EACA,EACA8E,WAAA,CAAA,EACArG,OAAA,SACAD,OAAA,CAAA,EACAuG,eAAA,CAAA,CACA,GAEAlG,KAAA,CAAAC,MAAA,KAAAvJ,KAAA,MAAAwJ,OAAA,MAAAzJ,IAAA,IAAA,CACA,CAAA,CAEA,EAEA,ECrGA+V,yBAAA,WACA,IAIAjQ,EAGA8D,EAeAlO,EAEA8K,EAxBAwP,EAAA7b,SAAAgN,cAAA,iCAAA,EAEA6O,IAEAlQ,EAAAP,MAAA/J,QAAAwa,EAAA,SAAA,EACAlT,EAAAtI,OAAA4M,QAAAC,KAAA2O,CAAA,EAEApM,EAAA,CACA,UACA,WACA,QACA,QACA,MACA,OACA,OACA,SACA,YACA,UACA,WACA,YAGAlO,EAAA,CAAA,KAAA,KAAA,KAAA,IAAA,KAAA,KAAA,IAAA,KAAA,KAAA,KAAA,IAAA,MAEA8K,EAAA,SAAAd,GAAA,MAAA,yHAAArK,OAGAqK,EAAA,GAAA3C,YAAA,yBAAA,EAAA1H,OACAqK,EAAA,GAAAzI,KAAA,KAAA,EAAA5B,OAAAqK,EAAA,GAAAvG,MAAA,yCAAA,CAAA,EAsGA0G,gBAAA/C,EAAAgD,EAjGA,WAAA,MAAA,CACAwB,QAAA,CACAC,QAAA,OACAC,QAAA,CAAA,EAAA,IACAC,gBAAAlC,MAAAnH,SAAA,EAAA,KACA2E,YAAAwC,MAAAnH,SAAA,EAAA,KACAsJ,UAAA,CAAA9K,MAAA2I,MAAAnH,SAAA,EAAA,KAAA,EACAuJ,YAAA,EACAK,UAAAxB,EACAoB,mBAAA,EACAE,YAAA,CACAC,KAAA,MACA,CACA,EACAE,MAAA,CACAF,KAAA,WACArM,KAAAkO,EACA1B,YAAA,CAAA,EACAC,SAAA,CACAC,UAAA,CACAxL,MAAA2I,MAAAnH,SAAA,EAAA,KACA2J,KAAA,OACA,CACA,EACAM,SAAA,CAAAC,KAAA,CAAA,CAAA,EACAC,UAAA,CACA3L,MAAA2I,MAAAnH,SAAA,EAAA,KACA4J,UAAA,SAAA7I,GAAA,OAAAA,EAAAhD,UAAA,EAAA,CAAA,CAAA,EACAqM,OAAA,EACA,EACAC,UAAA,CACAH,KAAA,CAAA,CACA,CACA,EACAI,MAAA,CACAX,KAAA,QACAU,UAAA,CACAL,UAAA,CACAxL,MAAA2I,MAAAnH,SAAA,EAAA,IACA,CACA,EACA8J,YAAA,CAAA,EACAK,UAAA,CACAD,KAAA,CAAA,EACA1L,MAAA2I,MAAAnH,SAAA,EAAA,KACAoK,OAAA,EACA,EACAH,SAAA,CAAAC,KAAA,CAAA,CAAA,EACAH,SAAA,CAAAG,KAAA,CAAA,CAAA,EACApD,IAAA,GACA,EACA+D,OAAA,CACA,CACAlB,KAAA,OACArM,KAAAA,EACA+O,UAAA,CACA7N,MAAA2I,MAAAnH,SAAA,EAAA,KACA2E,YAAAwC,MAAAvI,SAAA,SAAA,EACA2K,YAAA,CACA,EACAS,UAAA,CACAxL,MAAA2I,MAAAvI,SAAA,SAAA,CACA,EACAwS,WAAA,CAAA,EACA5E,WAAA,GACAzB,OAAA,SACAD,OAAA,CAAA,EACAuG,eAAA,CAAA,EACAnG,UAAA,CACA1M,MAAA,CACAmL,KAAA,SACAkO,EAAA,EACAC,EAAA,EACAC,GAAA,EACAC,GAAA,EACAC,WAAA,CACA,CACArB,OAAA,EACApY,MAAA2I,MAAA5I,UAAA4I,MAAAhI,UAAA,EAAAC,QAAA,EAAA,CACA,EACA,CACAwX,OAAA,EACApY,MAAA2I,MAAA5I,UAAA4I,MAAAhI,UAAA,EAAAC,QAAA,CAAA,CACA,EAEA,CACA,CACA,GAEA+L,KAAA,CACAC,MAAA,KACAvJ,KAAA,MACAwJ,OAAA,MACAzJ,IAAA,IACA,CACA,CAAA,CAEA,EAEA,ECrIAsW,6BAAA,WACA,IAIAxQ,EAGApK,EAqDA6a,EACAC,EA7DAC,EAAAtc,SAAAgN,cAAA,qCAAA,EAEAsP,IAEA3Q,EAAAP,MAAA/J,QAAAib,EAAA,SAAA,EACA3T,EAAAtI,OAAA4M,QAAAC,KAAAoP,CAAA,EAuDAF,GArDA7a,EAAA,CACA,CAAA,aAAA,KACA,CAAA,aAAA,KACA,CAAA,aAAA,KACA,CAAA,aAAA,IACA,CAAA,aAAA,IACA,CAAA,aAAA,IACA,CAAA,aAAA,IACA,CAAA,aAAA,IACA,CAAA,aAAA,IACA,CAAA,aAAA,KACA,CAAA,aAAA,KACA,CAAA,aAAA,KACA,CAAA,aAAA,KACA,CAAA,aAAA,KACA,CAAA,aAAA,KACA,CAAA,aAAA,KACA,CAAA,aAAA,KACA,CAAA,aAAA,KACA,CAAA,aAAA,IACA,CAAA,aAAA,IACA,CAAA,aAAA,IACA,CAAA,aAAA,KACA,CAAA,aAAA,IACA,CAAA,aAAA,IACA,CAAA,aAAA,IACA,CAAA,aAAA,IACA,CAAA,aAAA,IACA,CAAA,aAAA,KACA,CAAA,aAAA,KACA,CAAA,aAAA,IACA,CAAA,aAAA,IACA,CAAA,aAAA,IACA,CAAA,aAAA,KACA,CAAA,aAAA,KACA,CAAA,aAAA,IACA,CAAA,aAAA,IACA,CAAA,aAAA,IACA,CAAA,aAAA,KACA,CAAA,aAAA,KACA,CAAA,aAAA,KACA,CAAA,aAAA,KACA,CAAA,aAAA,IACA,CAAA,aAAA,IACA,CAAA,aAAA,IACA,CAAA,aAAA,IACA,CAAA,aAAA,IACA,CAAA,aAAA,KACA,CAAA,aAAA,IACA,CAAA,aAAA,IACA,CAAA,aAAA,MAGA0P,IAAA,SAAAU,GAAA,OAAAA,EAAA,EAAA,CAAA,EACA0K,EAAA9a,EAAA0P,IAAA,SAAAU,GAAA,OAAAA,EAAA,EAAA,CAAA,EAyEAjG,gBAAA/C,EAAAgD,EAvEA,WAAA,MAAA,CACA6C,UAAA,CACAL,KAAA,CAAA,EACAP,KAAA,aACAa,UAAA,EACA1D,IAAA,EACAC,IAAAoR,EAAA1b,OAAA,EACA+B,MAAA,CAAA2I,MAAAvI,SAAA,QAAA,EAAAuI,MAAAvI,SAAA,SAAA,EACA,EACAsK,QAAA,CACAC,QAAA,OACAC,QAAA,CAAA,EAAA,IACAC,gBAAAlC,MAAAnH,SAAA,EAAA,KACA2E,YAAAwC,MAAAnH,SAAA,EAAA,KACAsJ,UAAA,CAAA9K,MAAA2I,MAAAnH,SAAA,EAAA,KAAA,EACAuJ,YAAA,EACAC,mBAAA,EACAE,YAAA,CACAC,KAAA,MACA,EACAC,UAAAxB,gBACA,EACAyB,MAAA,CACAF,KAAA,WACArM,KAAA6a,EACAhO,UAAA,CACAP,UAAA,SAAA7I,GAAA,OAAA3E,OAAAqM,MAAA1H,CAAA,EAAA6H,OAAA,QAAA,CAAA,EACApK,MAAA2I,MAAAnH,SAAA,EAAA,KACAoK,OAAA,EACA,EACAL,SAAA,CACAC,UAAA,CACAxL,MAAA2I,MAAAnH,SAAA,EAAA,KACA2J,KAAA,OACA,CACA,EACAD,YAAA,CACAM,UAAA,CACAxL,MAAA2I,MAAAnH,SAAA,EAAA,IACA,CACA,CACA,EACAsK,MAAA,CACAX,KAAA,QACAQ,UAAA,CACAD,KAAA,CAAA,EACA1L,MAAA2I,MAAAnH,SAAA,EAAA,KACAoK,OAAA,EACA,EACAC,UAAA,CACAL,UAAA,CACAxL,MAAA2I,MAAAnH,SAAA,EAAA,KACA2J,KAAA,QACA,CACA,CACA,EACAwB,KAAA,CAAAC,MAAA,KAAAvJ,KAAA,KAAAwJ,OAAA,MAAAzJ,IAAA,IAAA,EACAiJ,OAAA,CACAhM,KAAA,QACA8K,KAAA,OACAyH,WAAA,CAAA,EACA5E,WAAA,GACAzB,OAAA,SACAzN,KAAA8a,EACA/L,UAAA,CACA7N,MAAA2I,MAAAnH,SAAA,EAAA,KACAuJ,YAAA,CACA,CACA,CACA,CAAA,CAEA,EAEA,ECzIA+O,wBAAA,WACA,IAIA5Q,EAJA6Q,EAAAxc,SAAAgN,cAAA,gCAAA,EAEAwP,IAEA7Q,EAAAP,MAAA/J,QAAAmb,EAAA,SAAA,EACA7T,EAAAtI,OAAA4M,QAAAC,KAAAsP,CAAA,EA+FA9Q,gBAAA/C,EAAAgD,EA7FA,WAAA,MAAA,CACAwB,QAAA,CACAC,QAAA,OACAC,QAAA,CAAA,EAAA,IACAC,gBAAAlC,MAAAnH,SAAA,EAAA,KACA2E,YAAAwC,MAAAnH,SAAA,EAAA,KACAuJ,YAAA,EACAC,mBAAA,EACAE,YAAA,CACAC,KAAA,MACA,EACAC,UAAAxB,gBACA,EACAyB,MAAA,CACAF,KAAA,WACAI,SAAA,CACAC,UAAA,CACAxL,MAAA2I,MAAAnH,SAAA,EAAA,IACA,CACA,EACAmK,UAAA,CACA3L,MAAA2I,MAAAnH,SAAA,EAAA,IACA,EACAqK,UAAA,CAAAH,KAAA,CAAA,CAAA,EACA5M,KAAAf,MAAA4J,KAAA5J,MAAA,EAAA,EAAAwQ,KAAA,CAAA,EAAAC,IAAA,SAAAU,GAAA,OAAAA,EAAA,CAAA,CAAA,CACA,EACApD,MAAA,CACAX,KAAA,MACAQ,UAAA,CACA3L,MAAA2I,MAAAnH,SAAA,EAAA,IACA,EACAqK,UAAA,CACAL,UAAA,CACAxL,MAAA2I,MAAAnH,SAAA,EAAA,IACA,CACA,CACA,EACA6K,OAAA,CACA,CACAhM,KAAA,aACA8K,KAAA,OACArM,KAAA,CAAA,EAAA,EAAA,EAAA,GAAA,GAAA,IAAA,IAAA,KAAA,MACAkP,WAAA,EACAH,UAAA,CACA7N,MAAA2I,MAAAnH,SAAA,EAAA,KACA2E,YAAAwC,MAAAvI,SAAA,QAAA,EACA2K,YAAA,CACA,EACAS,UAAA,CACAxL,MAAA2I,MAAAvI,SAAA,QAAA,CACA,EACAmM,OAAA,QACA,EACA,CACAlM,KAAA,aACA8K,KAAA,OACArM,KAAA,CAAA,EAAA,EAAA,EAAA,EAAA,GAAA,GAAA,GAAA,IAAA,KACAkP,WAAA,EACAH,UAAA,CACA7N,MAAA2I,MAAAnH,SAAA,EAAA,KACA2E,YAAAwC,MAAAvI,SAAA,SAAA,EACA2K,YAAA,CACA,EACAS,UAAA,CACAxL,MAAA2I,MAAAvI,SAAA,SAAA,CACA,EACAmM,OAAA,QACA,EACA,CACAlM,KAAA,eACA8K,KAAA,OACArM,KAAA,CAAA,GAAA,IAAA,EAAA,EAAA,EAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA,IAAA,EAAA,IAAA,EAAA,KACAkP,WAAA,EACAH,UAAA,CACA7N,MAAA2I,MAAAnH,SAAA,EAAA,KACA2E,YAAAwC,MAAAvI,SAAA,MAAA,EACA2K,YAAA,CACA,EACAS,UAAA,CACAxL,MAAA2I,MAAAvI,SAAA,MAAA,CACA,EACAmM,OAAA,QACA,GAEAI,KAAA,CACAC,MAAA,GACAvJ,KAAA,EACAwJ,OAAA,EACAzJ,IAAA,GACA0J,aAAA,CAAA,CACA,CACA,CAAA,CAEA,EAEA,ECvGAkN,2BAAA,WACA,IAIA9Q,EAGAjB,EAPAgS,EAAA1c,SAAAgN,cAAA,mCAAA,EAEA0P,IAEA/Q,EAAAP,MAAA/J,QAAAqb,EAAA,SAAA,EACA/T,EAAAtI,OAAA4M,QAAAC,KAAAwP,CAAA,EAEAhS,EAAA,CAAA,SAAA,UAAA,YAAA,WAAA,SAAA,WAAA,UAqKAgB,gBAAA/C,EAAAgD,EAnKA,WAAA,MAAA,CACAlJ,MAAA,CACA2I,MAAAvI,SAAA,SAAA,EACAuI,MAAAvI,SAAA,SAAA,GAGAuN,OAAA,CACA7O,KAAA,CACA,CACAuB,KAAA,MACAyK,UAAA,CACA9K,MAAA2I,MAAAnH,SAAA,EAAA,IACA,CACA,EACA,CACAnB,KAAA,MACAyK,UAAA,CACA9K,MAAA2I,MAAAnH,SAAA,EAAA,IACA,CACA,EAEA,EACAkJ,QAAA,CACAC,QAAA,OACAC,QAAA,CAAA,EAAA,IACAC,gBAAAlC,MAAAnH,SAAA,EAAA,KACA2E,YAAAwC,MAAAnH,SAAA,EAAA,KACAsJ,UAAA,CAAA9K,MAAA2I,MAAAnH,SAAA,EAAA,KAAA,EACAuJ,YAAA,EACAC,mBAAA,EACAC,SAAA,SAAApC,EAAAC,EAAAxI,EAAAwC,EAAAiG,GACA,OAAAH,YAAAC,EAAAC,EAAAxI,EAAAwC,EAAAiG,CAAA,CACA,EACAmC,YAAA,CACAC,KAAA,MACA,EACAC,UAAAxB,gBACA,EACAyB,MAAA,CACAF,KAAA,WACArM,KAAAmJ,EACAqD,YAAA,CAAA,EACAC,SAAA,CACAC,UAAA,CACAxL,MAAA2I,MAAAnH,SAAA,EAAA,KACA2J,KAAA,OACA,CACA,EACAM,SAAA,CAAAC,KAAA,CAAA,CAAA,EACAC,UAAA,CACAP,UAAA,SAAA7I,GAAA,OAAAA,EAAAhD,UAAA,EAAA,CAAA,CAAA,EACAS,MAAA2I,MAAAnH,SAAA,EAAA,KACAoK,OAAA,EACA,EACAC,UAAA,CACAH,KAAA,CAAA,CACA,CACA,EACAI,MAAA,CACAX,KAAA,QACAU,UAAA,CACAL,UAAA,CACAxL,MAAA2I,MAAAnH,SAAA,EAAA,IACA,CACA,EACA8J,YAAA,CAAA,EACAK,UAAA,CACAD,KAAA,CAAA,EACA1L,MAAA2I,MAAAnH,SAAA,EAAA,KACAoK,OAAA,EACA,EACAH,SAAA,CAAAC,KAAA,CAAA,CAAA,EACAH,SAAA,CAAAG,KAAA,CAAA,CAAA,CACA,EACAW,OAAA,CACA,CACAhM,KAAA,MACA8K,KAAA,OACArM,KAAA,CAAA,GAAA,GAAA,GAAA,GAAA,GAAA,EAAA,IACAkP,WAAA,GACAH,UAAA,CACA7N,MAAA2I,MAAAnH,SAAA,EAAA,KACA2E,YAAAwC,MAAAvI,SAAA,SAAA,EACA2K,YAAA,CACA,EACAS,UAAA,CACAxL,MAAA2I,MAAAvI,SAAA,SAAA,CACA,EACAmM,OAAA,SACA2N,UAAA,CACArM,UAAA,CACA7N,MAAA2I,MAAAvI,SAAA,SAAA,CACA,EACAtB,KAAA,CACA,CAAAqM,KAAA,MAAA9K,KAAA,KAAA,EACA,CAAA8K,KAAA,MAAA9K,KAAA,KAAA,EAEA,EACAmM,SAAA,CACAhB,UAAA,CACAxL,MAAA2I,MAAAvI,SAAA,SAAA,CACA,EACAqM,MAAA,CACAzM,MAAA2I,MAAAnH,SAAA,EAAA,IACA,EACA1C,KAAA,CAAA,CAAAqM,KAAA,UAAA9K,KAAA,SAAA,EACA,CACA,EACA,CACAA,KAAA,MACA8K,KAAA,OACArM,KAAA,CAAA,EAAA,CAAA,EAAA,EAAA,EAAA,EAAA,EAAA,GACAkP,WAAA,GACAH,UAAA,CACA7N,MAAA2I,MAAAnH,SAAA,EAAA,KACA2E,YAAAwC,MAAAvI,SAAA,QAAA,EACA2K,YAAA,CACA,EACAS,UAAA,CACAxL,MAAA2I,MAAAvI,SAAA,QAAA,CACA,EACAmM,OAAA,SACA2N,UAAA,CACArM,UAAA,CACA7N,MAAA2I,MAAAvI,SAAA,QAAA,CACA,EACAqM,MAAA,CACAzM,MAAA,MACA,EACAlB,KAAA,CAAA,CAAAuB,KAAA,gBAAAkC,MAAA,CAAA,EAAA8I,MAAA,EAAAS,MAAA,CAAA,GAAA,EACA,EACAU,SAAA,CACAhB,UAAA,CACAxL,MAAA2I,MAAAvI,SAAA,QAAA,CACA,EACAqM,MAAA,CACAzM,MAAA2I,MAAAnH,SAAA,EAAA,IACA,EACA1C,KAAA,CACA,CAAAqM,KAAA,UAAA9K,KAAA,SAAA,EACA,CACA,CACAkM,OAAA,OACA8M,EAAA,MACAvN,MAAA,KACA,EACA,CACAS,OAAA,SACAE,MAAA,CACAxB,SAAA,QACAG,UAAA,KACA,EACAD,KAAA,MACA9K,KAAA,eACA,GAGA,CACA,GAEAsM,KAAA,CAAAC,MAAA,KAAAvJ,KAAA,KAAAwJ,OAAA,MAAAzJ,IAAA,KAAA,CACA,CAAA,CAEA,EAEA,EC/KA+W,yBAAA,WACA,IAIAjR,EAGAjB,EAPAmS,EAAA7c,SAAAgN,cAAA,iCAAA,EAEA6P,IAEAlR,EAAAP,MAAA/J,QAAAwb,EAAA,SAAA,EACAlU,EAAAtI,OAAA4M,QAAAC,KAAA2P,CAAA,EAEAnS,EAAA,CAAA,SAAA,UAAA,YAAA,WAAA,SAAA,WAAA,UAiIAgB,gBAAA/C,EAAAgD,EA/HA,WAAA,MAAA,CACAlJ,MAAA,CAAA2I,MAAAvI,SAAA,SAAA,EAAAuI,MAAAvI,SAAA,SAAA,GACAuN,OAAA,CACA7O,KAAA,CACA,CACAuB,KAAA,MACAyK,UAAA,CACA9K,MAAA2I,MAAAnH,SAAA,EAAA,IACA,CACA,EACA,CACAnB,KAAA,MACAyK,UAAA,CACA9K,MAAA2I,MAAAnH,SAAA,EAAA,IACA,CACA,EAEA,EACAkJ,QAAA,CACAC,QAAA,OACAC,QAAA,CAAA,EAAA,IACAC,gBAAAlC,MAAAnH,SAAA,EAAA,KACA2E,YAAAwC,MAAAnH,SAAA,EAAA,KACAsJ,UAAA,CAAA9K,MAAA2I,MAAAnH,SAAA,EAAA,KAAA,EACAuJ,YAAA,EAEAC,mBAAA,EACAC,SAAA,SAAApC,EAAAC,EAAAxI,EAAAwC,EAAAiG,GACA,OAAAH,YAAAC,EAAAC,EAAAxI,EAAAwC,EAAAiG,CAAA,CACA,EACAmC,YAAA,CACAC,KAAA,MACA,CACA,EACAE,MAAA,CACAF,KAAA,WACArM,KAAAmJ,EACAqD,YAAA,CAAA,EACAC,SAAA,CACAC,UAAA,CACAxL,MAAA2I,MAAAnH,SAAA,EAAA,KACA2J,KAAA,OACA,CACA,EACAM,SAAA,CAAAC,KAAA,CAAA,CAAA,EACAC,UAAA,CACAP,UAAA,SAAA7I,GAAA,OAAAA,EAAAhD,UAAA,EAAA,CAAA,CAAA,EACAS,MAAA2I,MAAAnH,SAAA,EAAA,KACAoK,OAAA,EACA,EACAC,UAAA,CACAH,KAAA,CAAA,CACA,CACA,EACAI,MAAA,CACAX,KAAA,QACAU,UAAA,CACAL,UAAA,CACAxL,MAAA2I,MAAAnH,SAAA,EAAA,IACA,CACA,EACA8J,YAAA,CAAA,EACAK,UAAA,CACAD,KAAA,CAAA,EACA1L,MAAA2I,MAAAnH,SAAA,EAAA,KACAoK,OAAA,EACA,EACAH,SAAA,CAAAC,KAAA,CAAA,CAAA,EACAH,SAAA,CAAAG,KAAA,CAAA,CAAA,CACA,EACAW,OAAA,CACA,CACAhM,KAAA,MACA8K,KAAA,OACArM,KAAA,CAAA,GAAA,GAAA,GAAA,GAAA,GAAA,EAAA,IACAob,UAAA,CACApb,KAAA,CACA,CAAAqM,KAAA,MAAA9K,KAAA,KAAA,EACA,CAAA8K,KAAA,MAAA9K,KAAA,KAAA,EAEA,EACAmM,SAAA,CACAC,MAAA,CACAzM,MAAA2I,MAAAnH,SAAA,EAAA,IACA,EACA1C,KAAA,CAAA,CAAAqM,KAAA,UAAA9K,KAAA,SAAA,EACA,CACA,EACA,CACAA,KAAA,MACA8K,KAAA,OACArM,KAAA,CAAA,EAAA,CAAA,EAAA,EAAA,EAAA,EAAA,EAAA,GACAob,UAAA,CACAzN,MAAA,CACAzM,MAAA,MACA,EACAlB,KAAA,CAAA,CAAAuB,KAAA,gBAAAkC,MAAA,CAAA,EAAA8I,MAAA,EAAAS,MAAA,CAAA,GAAA,EACA,EACAU,SAAA,CACAC,MAAA,CACAzM,MAAA2I,MAAAnH,SAAA,EAAA,IACA,EACA1C,KAAA,CACA,CAAAqM,KAAA,UAAA9K,KAAA,SAAA,EACA,CACA,CACAkM,OAAA,OACA8M,EAAA,MACAvN,MAAA,KACA,EACA,CACAS,OAAA,SACAE,MAAA,CACAxB,SAAA,QACAG,UAAA,KACA,EACAD,KAAA,MACA9K,KAAA,eACA,GAGA,CACA,GAEAsM,KAAA,CAAAC,MAAA,KAAAvJ,KAAA,KAAAwJ,OAAA,MAAAzJ,IAAA,KAAA,CACA,CAAA,CAEA,EAEA,EC3IAiX,iCAAA,WACA,IAIAnR,EACAhD,EALAoU,EAAA/c,SAAAgN,cAAA,0CAAA,EAEA+P,IAEApR,EAAAP,MAAA/J,QAAA0b,EAAA,SAAA,EACApU,EAAAtI,OAAA4M,QAAAC,KAAA6P,CAAA,EAkJArR,gBAAA/C,EAAAgD,EAhJA,WAAA,MAAA,CACAlJ,MAAA,CACA2I,MAAAvI,SAAA,QAAA,EACAuI,MAAAvI,SAAA,SAAA,EACAuI,MAAAvI,SAAA,MAAA,EACAuI,MAAAvI,SAAA,SAAA,GAEAuN,OAAA,CACAvK,IAAA,EACA0H,UAAA,CACA9K,MAAA2I,MAAAnH,SAAA,EAAA,IACA,CACA,EACAkJ,QAAA,CACAC,QAAA,OACA4P,YAAA,CAAA,CACA,EACAtb,QAAA,CACAub,OAAA,CACA,CAAA,UAAA,OAAA,OAAA,OAAA,OAAA,OAAA,QACA,CAAA,WAAA,KAAA,KAAA,KAAA,KAAA,KAAA,MACA,CAAA,eAAA,KAAA,KAAA,KAAA,KAAA,KAAA,MACA,CAAA,eAAA,KAAA,KAAA,KAAA,KAAA,KAAA,MACA,CAAA,iBAAA,KAAA,KAAA,KAAA,GAAA,KAAA,MAEA,EACAnP,MAAA,CACAF,KAAA,WACAI,SAAA,CACAC,UAAA,CACAxL,MAAA2I,MAAAnH,SAAA,EAAA,IACA,CACA,EACAmK,UAAA,CACA3L,MAAA2I,MAAAnH,SAAA,EAAA,IACA,EACA0J,YAAA,CACAM,UAAA,CACAxL,MAAA2I,MAAAnH,SAAA,EAAA,IACA,CACA,CACA,EACAsK,MAAA,CACAoJ,UAAA,EACAvJ,UAAA,CACA3L,MAAA2I,MAAAnH,SAAA,EAAA,IACA,EACAqK,UAAA,CACAL,UAAA,CACAxL,MAAA2I,MAAAnH,SAAA,EAAA,IACA,CACA,CACA,EACA6K,OAAA,CACA,CACAlB,KAAA,OACAmB,OAAA,CAAA,EACAmO,eAAA,MACAnZ,SAAA,CAAA0S,MAAA,QAAA,EACAhG,WAAA,GACAH,UAAA,CACA7N,MAAA2I,MAAAnH,SAAA,EAAA,KACA2E,YAAAwC,MAAAvI,SAAA,QAAA,EACA2K,YAAA,CACA,EACAS,UAAA,CACAxL,MAAA2I,MAAAvI,SAAA,QAAA,CACA,EACAmM,OAAA,QACA,EACA,CACApB,KAAA,OACAmB,OAAA,CAAA,EACAmO,eAAA,MACAnZ,SAAA,CAAA0S,MAAA,QAAA,EACAhG,WAAA,GACAH,UAAA,CACA7N,MAAA2I,MAAAnH,SAAA,EAAA,KACA2E,YAAAwC,MAAAvI,SAAA,MAAA,EACA2K,YAAA,CACA,EACAS,UAAA,CACAxL,MAAA2I,MAAAvI,SAAA,MAAA,CACA,EACAmM,OAAA,QACA,EACA,CACApB,KAAA,OACAmB,OAAA,CAAA,EACAmO,eAAA,MACAnZ,SAAA,CAAA0S,MAAA,QAAA,EACAhG,WAAA,GACAH,UAAA,CACA7N,MAAA2I,MAAAnH,SAAA,EAAA,KACA2E,YAAAwC,MAAAvI,SAAA,SAAA,EACA2K,YAAA,CACA,EACAS,UAAA,CACAxL,MAAA2I,MAAAvI,SAAA,SAAA,CACA,EACAmM,OAAA,QACA,EACA,CACApB,KAAA,OACAmB,OAAA,CAAA,EACAmO,eAAA,MACAnZ,SAAA,CAAA0S,MAAA,QAAA,EACAhG,WAAA,GACAH,UAAA,CACA7N,MAAA2I,MAAAnH,SAAA,EAAA,KACA2E,YAAAwC,MAAAvI,SAAA,SAAA,EACA2K,YAAA,CACA,EACAS,UAAA,CACAxL,MAAA2I,MAAAvI,SAAA,SAAA,CACA,EACAmM,OAAA,QACA,EACA,CACApB,KAAA,MACAuP,GAAA,MACAhH,OAAA,MACA4B,OAAA,CAAA,MAAA,OACAhU,SAAA,CAAA0S,MAAA,MAAA,EACAvH,MAAA,CACArB,UAAA,sBACApL,MAAA2I,MAAAnH,SAAA,EAAA,IACA,EACAmZ,OAAA,CACAC,SAAA,UACArY,MAAA,OACAmI,QAAA,MACA,CACA,GAEAiC,KAAA,CACAC,MAAA,GACAvJ,KAAA,EACAwJ,OAAA,EACAzJ,IAAA,MACA0J,aAAA,CAAA,CACA,CACA,CAAA,CAEA,EAEA5G,EAAAmS,GAAA,oBAAA,SAAAwC,GACA,IAAAC,EAAAD,EAAAE,SAAA,GACAD,IACA9O,EAAA8O,EAAAvY,MAAA,EACA2D,EAAAoD,UAAA,CACA+C,OAAA,CACAqO,GAAA,MACAjO,MAAA,CACArB,UAAA,WAAA3M,OAAAuN,EAAA,WAAA,CACA,EACA2O,OAAA,CACApY,MAAAyJ,EACAtB,QAAAsB,CACA,CACA,CACA,CAAA,EAEA,CAAA,EAEA,EC7KAgP,kBAAA,WACA,IA0DA9R,EACAhD,EA3DA+U,EAAA1d,SAAAgN,cAAA,yBAAA,EAEAzL,EAAA,CACA,CAAAuB,KAAA,UAAAkC,MAAA,OAAA,EACA,CAAAlC,KAAA,SAAAkC,MAAA,MAAA,EACA,CAAAlC,KAAA,UAAAkC,MAAA,OAAA,EACA,CAAAlC,KAAA,WAAAkC,MAAA,OAAA,EACA,CAAAlC,KAAA,aAAAkC,MAAA,QAAA,EACA,CAAAlC,KAAA,WAAAkC,MAAA,OAAA,EACA,CAAAlC,KAAA,cAAAkC,MAAA,OAAA,EACA,CAAAlC,KAAA,WAAAkC,MAAA,MAAA,EACA,CAAAlC,KAAA,uBAAAkC,MAAA,MAAA,EACA,CAAAlC,KAAA,UAAAkC,MAAA,QAAA,EACA,CAAAlC,KAAA,UAAAkC,MAAA,OAAA,EACA,CAAAlC,KAAA,SAAAkC,MAAA,OAAA,EACA,CAAAlC,KAAA,QAAAkC,MAAA,OAAA,EACA,CAAAlC,KAAA,WAAAkC,MAAA,QAAA,EACA,CAAAlC,KAAA,UAAAkC,MAAA,OAAA,EACA,CAAAlC,KAAA,OAAAkC,MAAA,OAAA,EACA,CAAAlC,KAAA,SAAAkC,MAAA,OAAA,EACA,CAAAlC,KAAA,WAAAkC,MAAA,OAAA,EACA,CAAAlC,KAAA,YAAAkC,MAAA,OAAA,EACA,CAAAlC,KAAA,QAAAkC,MAAA,OAAA,EACA,CAAAlC,KAAA,WAAAkC,MAAA,OAAA,EACA,CAAAlC,KAAA,gBAAAkC,MAAA,OAAA,EACA,CAAAlC,KAAA,WAAAkC,MAAA,OAAA,EACA,CAAAlC,KAAA,YAAAkC,MAAA,OAAA,EACA,CAAAlC,KAAA,cAAAkC,MAAA,OAAA,EACA,CAAAlC,KAAA,WAAAkC,MAAA,OAAA,EACA,CAAAlC,KAAA,UAAAkC,MAAA,OAAA,EACA,CAAAlC,KAAA,WAAAkC,MAAA,OAAA,EACA,CAAAlC,KAAA,SAAAkC,MAAA,OAAA,EACA,CAAAlC,KAAA,gBAAAkC,MAAA,OAAA,EACA,CAAAlC,KAAA,aAAAkC,MAAA,OAAA,EACA,CAAAlC,KAAA,aAAAkC,MAAA,OAAA,EACA,CAAAlC,KAAA,WAAAkC,MAAA,QAAA,EACA,CAAAlC,KAAA,iBAAAkC,MAAA,OAAA,EACA,CAAAlC,KAAA,eAAAkC,MAAA,MAAA,EACA,CAAAlC,KAAA,OAAAkC,MAAA,QAAA,EACA,CAAAlC,KAAA,WAAAkC,MAAA,OAAA,EACA,CAAAlC,KAAA,SAAAkC,MAAA,OAAA,EACA,CAAAlC,KAAA,eAAAkC,MAAA,QAAA,EACA,CAAAlC,KAAA,eAAAkC,MAAA,OAAA,EACA,CAAAlC,KAAA,iBAAAkC,MAAA,OAAA,EACA,CAAAlC,KAAA,eAAAkC,MAAA,MAAA,EACA,CAAAlC,KAAA,YAAAkC,MAAA,OAAA,EACA,CAAAlC,KAAA,QAAAkC,MAAA,QAAA,EACA,CAAAlC,KAAA,OAAAkC,MAAA,OAAA,EACA,CAAAlC,KAAA,UAAAkC,MAAA,MAAA,EACA,CAAAlC,KAAA,WAAAkC,MAAA,OAAA,EACA,CAAAlC,KAAA,aAAAkC,MAAA,OAAA,EACA,CAAAlC,KAAA,gBAAAkC,MAAA,OAAA,EACA,CAAAlC,KAAA,YAAAkC,MAAA,OAAA,EACA,CAAAlC,KAAA,UAAAkC,MAAA,MAAA,EACA,CAAAlC,KAAA,cAAAkC,MAAA,OAAA,GAGA0Y,IACA/R,EAAAP,MAAA/J,QAAAqc,EAAA,SAAA,EACA/U,EAAAtI,OAAA4M,QAAAC,KAAAwQ,CAAA,EAkEAhS,gBAAA/C,EAAAgD,EAhEA,WAAA,MAAA,CACAwB,QAAA,CACAC,QAAA,OACAC,QAAA,CAAA,EAAA,IACAC,gBAAAlC,MAAAnH,SAAA,EAAA,KACA2E,YAAAwC,MAAAnH,SAAA,EAAA,KACAsJ,UAAA,CAAA9K,MAAA2I,MAAAnH,SAAA,EAAA,KAAA,EACAuJ,YAAA,EACAC,mBAAA,EACAI,UAAA,SAAAtC,GAAA,MAAA,WAAArK,OAAAqK,EAAAhK,KAAAuB,KAAA,cAAA,EAAA5B,OAAAqK,EAAAhK,KAAAyD,KAAA,CAAA,CACA,EACA4K,QAAA,CACAzB,KAAA,CAAA,EACA0B,QAAA,CACAG,QAAA,EACA,CACA,EACAxB,UAAA,CACA1I,KAAA,QACAiF,IAAA,IACAC,IAAA,KACAuQ,QAAA,CACA9Y,MAAA,CAAA2I,MAAAvI,SAAA,SAAA,EAAAuI,MAAAvI,SAAA,MAAA,EACA,EACAhC,KAAA,CAAA,OAAA,OACAgU,WAAA,CAAA,EACAtH,UAAA,CACA9K,MAAA2I,MAAAnH,SAAA,EAAA,IACA,EACA4J,UAAA,SAAA7I,GAAA,MAAA,GAAA9D,OAAA8D,EAAA,IAAA,GAAA,CAAA,CACA,EACA8J,OAAA,CACA,CACAhJ,KAAA,GACAhD,KAAA,mBACA8K,KAAA,MACA+P,KAAA,IACAC,KAAA,CAAA,EACAC,WAAA,CACA9S,IAAA,EACAC,IAAA,CACA,EACAsF,UAAA,CACA1H,YAAAwC,MAAAnH,SAAA,EAAA,IACA,EACAiL,MAAA,CACAzM,MAAA,MACA,EACAwO,IAAA,MACAlN,SAAA,CACAmL,MAAA,CACAf,KAAA,CAAA,EACA1L,MAAA,MACA,EAEA6N,UAAA,CACAwN,UAAA1S,MAAAvI,SAAA,SAAA,CACA,CACA,EACAtB,KAAAA,CACA,EAEA,CAAA,CAEA,EACAvB,SAAAgN,cAAA,gBAAA,EAAA9M,iBAAA,QAAA,WACAyI,EAAAoS,eAAA,CACAnN,KAAA,SACA,CAAA,CACA,CAAA,EAEA,ECrIAmQ,2BAAA,WACA,IAGApS,EACAhD,EAEAqV,EA6BAC,EA2DArS,EA9FAsS,EAAAle,SAAAgN,cAAA,oCAAA,EAEAkR,IACAvS,EAAAP,MAAA/J,QAAA6c,EAAA,SAAA,EACAvV,EAAAtI,OAAA4M,QAAAC,KAAAgR,CAAA,EAEAF,EAAA,CACA,CACAhZ,MAAA,OACAlC,KAAA,oBACAwN,UAAA,CAAA7N,MAAA2I,MAAAvI,SAAA,SAAA,CAAA,EACAqM,MAAA,CACAiP,KAAA,CACAC,IAAA,CACA3b,MAAA,SACA,CACA,CACA,CACA,EACA,CACAuC,MAAA,OACAlC,KAAA,oBACAwN,UAAA,CACA7N,MAAA2I,MAAA5I,UAAA4I,MAAAvI,SAAA,MAAA,EAAA,GAAA,CACA,EACAqM,MAAA,CACAiP,KAAA,CACAC,IAAA,CACA3b,MAAA,SACA,CACA,CACA,CACA,GAGAwb,EAAA,CACA,CACAjZ,MAAA,MACAlC,KAAA,oBACAwN,UAAA,CACA7N,MAAA2I,MAAA5I,UAAA4I,MAAAvI,SAAA,SAAA,EAAA,EAAA,CACA,CACA,EACA,CACAmC,MAAA,MACAlC,KAAA,gBACAwN,UAAA,CACA7N,MAAA2I,MAAA5I,UAAA4I,MAAAvI,SAAA,SAAA,EAAA,EAAA,CACA,CACA,EACA,CACAmC,MAAA,MACAlC,KAAA,cACAwN,UAAA,CACA7N,MAAA2I,MAAA5I,UAAA4I,MAAAvI,SAAA,SAAA,EAAA,EAAA,CACA,CACA,EACA,CACAmC,MAAA,MACAlC,KAAA,eACAwN,UAAA,CACA7N,MAAA2I,MAAA5I,UAAA4I,MAAAvI,SAAA,MAAA,EAAA,EAAA,CACA,CACA,EACA,CACAmC,MAAA,MACAlC,KAAA,aACAwN,UAAA,CACA7N,MAAA2I,MAAA5I,UAAA4I,MAAAvI,SAAA,MAAA,EAAA,GAAA,CACA,CACA,EACA,CACAmC,MAAA,MACAlC,KAAA,uBACAwN,UAAA,CACA7N,MAAA2I,MAAA5I,UAAA4I,MAAAvI,SAAA,MAAA,EAAA,EAAA,CACA,CACA,EACA,CACAmC,MAAA,MACAlC,KAAA,kBACAwN,UAAA,CACA7N,MAAA2I,MAAA5I,UAAA4I,MAAAvI,SAAA,MAAA,EAAA,EAAA,CACA,CACA,EACA,CACAmC,MAAA,MACAlC,KAAA,qBACAwN,UAAA,CACA7N,MAAA2I,MAAA5I,UAAA4I,MAAAvI,SAAA,MAAA,EAAA,EAAA,CACA,CACA,GAGA+I,EAAA,WAAA,MAAA,CACAuB,QAAA,CACAC,QAAA,OACAE,gBAAAlC,MAAAnH,SAAA,EAAA,KACAsJ,UAAA,CAAA9K,MAAA2I,MAAAnH,SAAA,EAAA,KAAA,EACA4J,UAAA,qBACA,EACAiB,OAAA,CACA,CACAhM,KAAA,qBACA8K,KAAA,MACAyQ,aAAA,SACAlI,OAAA,CAAA,MAAA,OACAjH,MAAA,CACAf,KAAA,CAAA,CACA,EACA8J,UAAA,CACA9J,KAAA,CAAA,CACA,EACAmC,UAAA,CACA1H,YAAAwC,MAAAvI,SAAA,UAAA,EACA2K,YAAA,CACA,EAEAjM,KAAA0c,CACA,EACA,CACAnb,KAAA,qBACA8K,KAAA,MACAuI,OAAA,CAAA,MAAA,OACAmI,SAAA,GACArG,UAAA,CACAvX,OAAA,EACAyN,KAAA,CAAA,CACA,EACAe,MAAA,CACArB,UAAA,aACAsQ,KAAA,CACAC,IAAA,CACApE,SAAA,GACAzI,WAAA,OACAgN,WAAA,EACA,CACA,CACA,EACAhd,KAAAyc,CACA,EAEA,CAAA,EASA3d,OAAAH,iBAAA,SAPA,SAAAse,IACApT,MAAArF,mBAAAmY,CAAA,IACAxS,gBAAA/C,EAAAgD,EAAAC,CAAA,EACAvL,OAAAoe,oBAAA,SAAAD,CAAA,EAEA,CAEA,EAEA,EC1JAE,oBAAA,WACA,IAIA/S,EACAhD,EALAgW,EAAA3e,SAAAgN,cAAA,2BAAA,EAEA2R,IAEAhT,EAAAP,MAAA/J,QAAAsd,EAAA,SAAA,EACAhW,EAAAtI,OAAA4M,QAAAC,KAAAyR,CAAA,EA6EAjT,gBAAA/C,EAAAgD,EA3EA,WAAA,MAAA,CACAyE,OAAA,CACAtK,KAAA,OACAyH,UAAA,CACA9K,MAAA2I,MAAAnH,SAAA,EAAA,IACA,CACA,EACA6K,OAAA,CACA,CACAlB,KAAA,MACAuI,OAAA9V,OAAA+F,WAAA,IAAA,MAAA,MACA8I,MAAA,CACAzM,MAAA2I,MAAAnH,SAAA,EAAA,IACA,EACA8T,OAAA,CAAA,MAAA,OACAxW,KAAA,CACA,CACAyD,MAAA,KACAlC,KAAA,WACAwN,UAAA,CACA7N,MAAA2I,MAAAvI,SAAA,SAAA,CACA,CACA,EACA,CACAmC,MAAA,IACAlC,KAAA,UACAwN,UAAA,CACA7N,MAAA2I,MAAAvI,SAAA,QAAA,CACA,CACA,EACA,CACAmC,MAAA,IACAlC,KAAA,UACAwN,UAAA,CACA7N,MAAA2I,MAAAvI,SAAA,MAAA,CACA,CACA,EACA,CACAmC,MAAA,IACAlC,KAAA,WACAwN,UAAA,CACA7N,MAAA2I,MAAAvI,SAAA,SAAA,CACA,CACA,EACA,CACAmC,MAAA,IACAlC,KAAA,SACAwN,UAAA,CACA7N,MAAA2I,MAAAvI,SAAA,SAAA,CACA,CACA,GAEAkB,SAAA,CACAuM,UAAA,CACAkC,WAAA,GACAiC,cAAA,EACAhC,YAAArH,MAAA5I,UAAA4I,MAAAnH,SAAA,EAAA,KAAA,EAAA,CACA,CACA,CACA,GAEAkJ,QAAA,CACAC,QAAA,OACAC,QAAA,CAAA,EAAA,IACAC,gBAAAlC,MAAAnH,SAAA,EAAA,KACA2E,YAAAwC,MAAAnH,SAAA,EAAA,KACAsJ,UAAA,CAAA9K,MAAA2I,MAAAnH,SAAA,EAAA,KAAA,EACAuJ,YAAA,EACAC,mBAAA,EACAE,YAAA,CACAC,KAAA,MACA,CACA,CACA,CAAA,CAEA,EAGAxC,MAAAhL,OAAA,WACAC,OAAA+F,WAAA,IACAuC,EAAAoD,UAAA,CACA+C,OAAA,CACA,CACAqH,OAAA,KACA,EAEA,CAAA,EAEAxN,EAAAoD,UAAA,CACA+C,OAAA,CACA,CACAqH,OAAA,KACA,EAEA,CAAA,CAEA,CAAA,EAEA,EC1GAyI,6BAAA,WACA,IAsEAjT,EACAhD,EAvEAkW,EAAA7e,SAAAgN,cAAA,8BAAA,EAEAzL,EAAA,CACA,CACAyD,MAAA,IACAlC,KAAA,UACAwN,UAAA,CACA7N,MAAA2I,MAAA5I,UAAA4I,MAAAhI,UAAA,EAAAC,QAAA,EAAA,CACA,CACA,EACA,CACA2B,MAAA,KACAlC,KAAA,cACAwN,UAAA,CACA7N,MAAA2I,MAAAvI,SAAA,QAAA,CACA,CACA,EACA,CACAmC,MAAA,IACAlC,KAAA,QACAwN,UAAA,CACA7N,MAAA2I,MAAAvI,SAAA,SAAA,CACA,CACA,EACA,CACAmC,MAAA,IACAlC,KAAA,UACAwN,UAAA,CACA7N,MAAA2I,MAAAvI,SAAA,WAAA,CACA,CACA,EACA,CACAmC,MAAA,IACAlC,KAAA,WACAwN,UAAA,CACA7N,MAAA2I,MAAAvI,SAAA,SAAA,CACA,CACA,EACA,CACAmC,MAAA,IACAlC,KAAA,mBACAwN,UAAA,CACA7N,MAAA2I,MAAA5I,UAAA4I,MAAAhI,UAAA,EAAAK,QAAA,EAAA,CACA,CACA,EACA,CACAuB,MAAA,IACAlC,KAAA,UACAwN,UAAA,CACA7N,MAAA2I,MAAAvI,SAAA,SAAA,CACA,CACA,EACA,CACAmC,MAAA,IACAlC,KAAA,WACAwN,UAAA,CACA7N,MAAA2I,MAAAvI,SAAA,MAAA,CACA,CACA,EACA,CACAmC,MAAA,IACAlC,KAAA,eACAwN,UAAA,CACA7N,MAAA2I,MAAA5I,UAAA4I,MAAAhI,UAAA,EAAAC,QAAA,EAAA,CACA,CACA,GAGAwb,IAEAlT,EAAAP,MAAA/J,QAAAwd,EAAA,SAAA,EACAlW,EAAAtI,OAAA4M,QAAAC,KAAA2R,CAAA,EAuDAnT,gBAAA/C,EAAAgD,EArDA,WAAA,MAAA,CACAiJ,MAAA,CACA,CACA/T,KAAA,uBACAiF,KAAA,SACAyH,UAAA,CACA9K,MAAA2I,MAAAnH,SAAA,EAAA,IACA,CACA,EACA,CACA6a,QAAA,kBACAhZ,KAAA,MACAD,IAAA,MACAkZ,UAAA,SACAC,aAAA,CACAvc,MAAA2I,MAAAnH,SAAA,EAAA,IACA,CACA,GAGAkJ,QAAA,CACAC,QAAA,OACAC,QAAA,CAAA,EAAA,IACAC,gBAAAlC,MAAAnH,SAAA,EAAA,KACA2E,YAAAwC,MAAAnH,SAAA,EAAA,KACAsJ,UAAA,CAAA9K,MAAA2I,MAAAnH,SAAA,EAAA,KAAA,EACAuJ,YAAA,EACAC,mBAAA,EACAE,YAAA,CACAC,KAAA,MACA,CACA,EAEAkB,OAAA,CACA,CACAlB,KAAA,MACAuI,OAAA9V,OAAA+F,WAAA,IAAA,MAAA,MACA2R,OAAA,CAAA,MAAA,OACAxW,KAAAA,EACA2N,MAAA,CACAxB,SAAA,QACAuR,QAAA,OACA5Q,OAAA,GACA5L,MAAA2I,MAAAnH,SAAA,EAAA,IACA,EACA6B,KAAA,KACAuJ,MAAA,KACAxJ,IAAA,EACAyJ,OAAA,CACA,EAEA,CAAA,CAEA,EAGAlE,MAAAhL,OAAA,WACAC,OAAA+F,WAAA,IACAuC,EAAAoD,UAAA,CACA+C,OAAA,CAAA,CAAAqH,OAAA,KAAA,EACA,CAAA,EAEAxN,EAAAoD,UAAA,CACA+C,OAAA,CAAA,CAAAqH,OAAA,KAAA,EACA,CAAA,CAEA,CAAA,EAEA,EC9IA+I,8BAAA,WACA,IAIAvT,EACAhD,EAEApH,EAPA4d,EAAAnf,SAAAgN,cAAA,+BAAA,EAEAmS,IAEAxT,EAAAP,MAAA/J,QAAA8d,EAAA,SAAA,EACAxW,EAAAtI,OAAA4M,QAAAC,KAAAiS,CAAA,EAEA5d,EAAA,CACA,CACAyD,MAAA,IACAlC,KAAA,UACAwN,UAAA,CACA7N,MAAA2I,MAAA5I,UAAA4I,MAAAhI,UAAA,EAAAC,QAAA,EAAA,CACA,CACA,EACA,CACA2B,MAAA,KACAlC,KAAA,cACAwN,UAAA,CACA7N,MAAA2I,MAAAvI,SAAA,QAAA,CACA,CACA,EACA,CACAmC,MAAA,IACAlC,KAAA,QACAwN,UAAA,CACA7N,MAAA2I,MAAAvI,SAAA,SAAA,CACA,CACA,EACA,CACAmC,MAAA,IACAlC,KAAA,UACAwN,UAAA,CACA7N,MAAA2I,MAAAvI,SAAA,WAAA,CACA,CACA,EACA,CACAmC,MAAA,IACAlC,KAAA,WACAwN,UAAA,CACA7N,MAAA2I,MAAAvI,SAAA,SAAA,CACA,CACA,EACA,CACAmC,MAAA,IACAlC,KAAA,mBACAwN,UAAA,CACA7N,MAAA2I,MAAA5I,UAAA4I,MAAAhI,UAAA,EAAAK,QAAA,EAAA,CACA,CACA,EACA,CACAuB,MAAA,IACAlC,KAAA,UACAwN,UAAA,CACA7N,MAAA2I,MAAAvI,SAAA,SAAA,CACA,CACA,EACA,CACAmC,MAAA,IACAlC,KAAA,WACAwN,UAAA,CACA7N,MAAA2I,MAAAvI,SAAA,MAAA,CACA,CACA,EACA,CACAmC,MAAA,IACAlC,KAAA,eACAwN,UAAA,CACA7N,MAAA2I,MAAA5I,UAAA4I,MAAAhI,UAAA,EAAAC,QAAA,EAAA,CACA,CACA,GAwDAqI,gBAAA/C,EAAAgD,EArDA,WAAA,MAAA,CACAiJ,MAAA,CACA,CACA/T,KAAA,wBACAiF,KAAA,SACAyH,UAAA,CACA9K,MAAA2I,MAAAnH,SAAA,EAAA,IACA,CACA,EACA,CACA6a,QAAA,uBACAhZ,KAAA,MACAD,IAAA,MACAkZ,UAAA,SACAC,aAAA,CACAvc,MAAA2I,MAAAnH,SAAA,EAAA,IACA,CACA,GAGAkJ,QAAA,CACAC,QAAA,OACAC,QAAA,CAAA,EAAA,IACAC,gBAAAlC,MAAAnH,SAAA,EAAA,KACA2E,YAAAwC,MAAAnH,SAAA,EAAA,KACAsJ,UAAA,CAAA9K,MAAA2I,MAAAnH,SAAA,EAAA,KAAA,EACAuJ,YAAA,EACAC,mBAAA,EACAE,YAAA,CACAC,KAAA,MACA,CACA,EAEAkB,OAAA,CACA,CACAlB,KAAA,MACAuI,OAAA9V,OAAA+F,WAAA,IAAA,MAAA,MACA2R,OAAA,CAAA,MAAA,OACAxW,KAAAA,EACA2N,MAAA,CACAxB,SAAA,QACAuR,QAAA,YACAG,YAAA,EACA3c,MAAA2I,MAAAnH,SAAA,EAAA,IACA,EACA6B,KAAA,KACAuJ,MAAA,KACAxJ,IAAA,EACAyJ,OAAA,CACA,EAEA,CAAA,CAEA,EAGAlE,MAAAhL,OAAA,WACAC,OAAA+F,WAAA,IACAuC,EAAAoD,UAAA,CACA+C,OAAA,CAAA,CAAAqH,OAAA,KAAA,EACA,CAAA,EAEAxN,EAAAoD,UAAA,CACA+C,OAAA,CAAA,CAAAqH,OAAA,KAAA,EACA,CAAA,CAEA,CAAA,EAEA,EC/IAjE,MAAA,CACA,CACAlN,MAAA,KACAlC,KAAA,UACAwN,UAAA,CACA7N,MAAA2I,MAAAvI,SAAA,QAAA,CACA,CACA,EACA,CACAmC,MAAA,IACAlC,KAAA,QACAwN,UAAA,CACA7N,MAAA2I,MAAAvI,SAAA,SAAA,CACA,CACA,EACA,CACAmC,MAAA,IACAlC,KAAA,UACAwN,UAAA,CACA7N,MAAA2I,MAAAvI,SAAA,WAAA,CACA,CACA,EACA,CACAmC,MAAA,IACAlC,KAAA,WACAwN,UAAA,CACA7N,MAAA2I,MAAAvI,SAAA,SAAA,CACA,CACA,EACA,CACAmC,MAAA,IACAlC,KAAA,UACAwN,UAAA,CACA7N,MAAA2I,MAAAvI,SAAA,SAAA,CACA,CACA,EACA,CACAmC,MAAA,IACAlC,KAAA,WACAwN,UAAA,CACA7N,MAAA2I,MAAAvI,SAAA,MAAA,CACA,CACA,GAGAsP,MAAA,CACA,CACAnN,MAAA,KACAlC,KAAA,WACAwN,UAAA,CACA7N,MAAA2I,MAAAvI,SAAA,SAAA,CACA,CACA,EACA,CACAmC,MAAA,IACAlC,KAAA,UACAwN,UAAA,CACA7N,MAAA2I,MAAAvI,SAAA,QAAA,CACA,CACA,EACA,CACAmC,MAAA,IACAlC,KAAA,UACAwN,UAAA,CACA7N,MAAA2I,MAAAvI,SAAA,MAAA,CACA,CACA,EACA,CACAmC,MAAA,IACAlC,KAAA,WACAwN,UAAA,CACA7N,MAAA2I,MAAAvI,SAAA,SAAA,CACA,CACA,EACA,CACAmC,MAAA,IACAlC,KAAA,SACAwN,UAAA,CACA7N,MAAA2I,MAAAvI,SAAA,SAAA,CACA,CACA,GAEAwc,cAAA,CAAAlJ,OAAA,KAAA,EACAmJ,YAAA,CAAAnJ,OAAA,KAAA,EAEAoJ,4BAAA,WACA,IAIA5T,EACAhD,EALA6W,EAAAxf,SAAAgN,cAAA,4BAAA,EAEAwS,IAEA7T,EAAAP,MAAA/J,QAAAme,EAAA,SAAA,EACA7W,EAAAtI,OAAA4M,QAAAC,KAAAsS,CAAA,EAiDA9T,gBAAA/C,EAAAgD,EA/CA,WAAA,MAAA,CACAiJ,MAAA,CACA,CACA/T,KAAA,qBACAiF,KAAA,SACAyH,UAAA,CACA9K,MAAA2I,MAAAnH,SAAA,EAAA,IACA,CACA,GAGAkJ,QAAA,CACAC,QAAA,OACAC,QAAA,CAAA,EAAA,IACAC,gBAAAlC,MAAAnH,SAAA,EAAA,KACA2E,YAAAwC,MAAAnH,SAAA,EAAA,KACAsJ,UAAA,CAAA9K,MAAA2I,MAAAnH,SAAA,EAAA,KAAA,EACAuJ,YAAA,EACAC,mBAAA,EACAE,YAAA,CACAC,KAAA,MACA,CACA,EAEAkB,OAAA,CACA,CACAlB,KAAA,MACAuI,OAAA9V,OAAA+F,WAAA,IAAA,MAAA,MACA2R,OAAA,CAAA,MAAA,OACAxW,KAAA2Q,MACAhD,MAAA,CACAf,KAAA,CAAA,CACA,CACA,EACA,CACAP,KAAA,MACAuI,OAAA9V,OAAA+F,WAAA,IAAA,MAAA,MACA2R,OAAA,CAAA,MAAA,OACAC,kBAAA,CAAA,EACA9I,MAAA,CACAf,KAAA,CAAA,CACA,EACA5M,KAAA4Q,KACA,EAEA,CAAA,CAEA,EAGA/G,MAAAhL,OAAA,WACAC,OAAA+F,WAAA,IACAuC,EAAAoD,UAAA,CACA+C,OAAA,CAAAwQ,YAAAA,YACA,CAAA,EAEA3W,EAAAoD,UAAA,CACA+C,OAAA,CAAAuQ,cAAAA,cACA,CAAA,CAEA,CAAA,EAEA,EC1JAI,sBAAA,WACA,IAIA9T,EAJA+T,EAAA1f,SAAAgN,cAAA,6BAAA,EAEA0S,IAEA/T,EAAAP,MAAA/J,QAAAqe,EAAA,SAAA,EACA/W,EAAAtI,OAAA4M,QAAAC,KAAAwS,CAAA,EA+DAhU,gBAAA/C,EAAAgD,EA7DA,WAAA,MAAA,CACAyE,OAAA,CACAgI,OAAA,WACAtS,KAAA,OACAyH,UAAA,CACA9K,MAAA2I,MAAAnH,SAAA,EAAA,IACA,CACA,EACAkJ,QAAA,CACAC,QAAA,OACAC,QAAA,CAAA,EAAA,IACAC,gBAAAlC,MAAAnH,SAAA,EAAA,KACA2E,YAAAwC,MAAAnH,SAAA,EAAA,KACAsJ,UAAA,CAAA9K,MAAA2I,MAAAnH,SAAA,EAAA,KAAA,EACAuJ,YAAA,EACAC,mBAAA,EACAE,YAAA,CACAC,KAAA,MACA,CACA,EAEA+R,MAAA,CACAC,UAAA,CACA,CAAA9c,KAAA,YAAAkI,IAAA,IAAA,EACA,CAAAlI,KAAA,QAAAkI,IAAA,IAAA,EACA,CAAAlI,KAAA,OAAAkI,IAAA,GAAA,EACA,CAAAlI,KAAA,UAAAkI,IAAA,IAAA,EACA,CAAAlI,KAAA,OAAAkI,IAAA,IAAA,EACA,CAAAlI,KAAA,SAAAkI,IAAA,IAAA,GAEAmL,OAAA,IACA7H,UAAA,CACAL,UAAA,CACAxL,MAAA2I,MAAA5I,UAAA4I,MAAAnH,SAAA,EAAA,IAAA,CACA,CACA,CACA,EAEA6K,OAAA,CACA,CACAlB,KAAA,QACArM,KAAA,CACA,CACAyD,MAAA,CAAA,KAAA,IAAA,IAAA,KAAA,IAAA,MACAlC,KAAA,SACAwN,UAAA,CACA7N,MAAA2I,MAAAvI,SAAA,SAAA,CACA,CACA,EACA,CACAmC,MAAA,CAAA,IAAA,KAAA,KAAA,KAAA,KAAA,MACAlC,KAAA,SACAwN,UAAA,CACA7N,MAAA2I,MAAAvI,SAAA,SAAA,CACA,CACA,EAEA,EAEA,CAAA,CAEA,EAEA,ECvEAgd,gCAAA,WACA,IAoBAlU,EACAhD,EArBA+W,EAAA1f,SAAAgN,cAAA,gCAAA,EACA,SAAA8S,EAAAvU,GACA,IAAAwU,EAAA,CACA,CAAA,YAAA,QAAA,MAAA,UAAA,OAAA,SACA,CAAA,WAAA,OAAA,UAAA,UAAA,YAAA,YAEAC,EAAAzU,EAAAmD,YACA,MAAA,aAAAxN,OAAAqK,EAAAzI,KAAA,+DAAA,EAAA5B,OAEA6e,EAAAxU,EAAAmD,aAAA,GAAA,aAAA,EAAAxN,OAAAqK,EAAAvG,MAAA,GAAA,wBAAA,EAAA9D,OACA6e,EAAAC,GAAA,GAAA,aAAA,EAAA9e,OAAAqK,EAAAvG,MAAA,GAAA,wBAAA,EAAA9D,OACA6e,EAAAC,GAAA,GAAA,aAAA,EAAA9e,OAAAqK,EAAAvG,MAAA,GAAA,wBAAA,EAAA9D,OACA6e,EAAAC,GAAA,GAAA,aAAA,EAAA9e,OAAAqK,EAAAvG,MAAA,GAAA,wBAAA,EAAA9D,OACA6e,EAAAC,GAAA,GAAA,aAAA,EAAA9e,OAAAqK,EAAAvG,MAAA,GAAA,wBAAA,EAAA9D,OACA6e,EAAAC,GAAA,GAAA,aAAA,EAAA9e,OAAAqK,EAAAvG,MAAA,GAAA,oBAAA,CAEA,CAEA0a,IAEA/T,EAAAP,MAAA/J,QAAAqe,EAAA,SAAA,EACA/W,EAAAtI,OAAA4M,QAAAC,KAAAwS,CAAA,EAmJAhU,gBAAA/C,EAAAgD,EAjJA,WAAA,MAAA,CACAyE,OAAA,CACAgI,OAAA,WACAtS,KAAA,OACAyH,UAAA,CACA9K,MAAA2I,MAAAnH,SAAA,EAAA,IACA,CACA,EACAkJ,QAAA,CACAC,QAAA,OACAC,QAAA,CAAA,EAAA,IACAC,gBAAAlC,MAAAnH,SAAA,EAAA,KACA2E,YAAAwC,MAAAnH,SAAA,EAAA,KACAsJ,UAAA,CAAA9K,MAAA2I,MAAAnH,SAAA,EAAA,KAAA,EACAuJ,YAAA,EACAC,mBAAA,EACAE,YAAA,CACAC,KAAA,MACA,EACAC,UAAAiS,CACA,EAEAH,MAAA,CACA,CACAxJ,OAAA9V,OAAA+F,WAAA,IAAA,GAAA,IACA4S,WAAA,GACAlD,YAAA,EACAmK,MAAA,SACAlI,OAAA1X,OAAA+F,WAAA,IAAA,CAAA,MAAA,OAAA,CAAA,MAAA,OACAwZ,UAAA,CACA,CAAA9c,KAAA,QAAAkI,IAAA,IAAA,EACA,CAAAlI,KAAA,OAAAkI,IAAA,IAAA,EACA,CAAAlI,KAAA,UAAAkI,IAAA,GAAA,EACA,CAAAlI,KAAA,MAAAkI,IAAA,IAAA,EACA,CAAAlI,KAAA,QAAAkI,IAAA,IAAA,EACA,CAAAlI,KAAA,YAAAkI,IAAA,IAAA,GAEAlI,KAAA,CACA+K,UAAA,UACAN,UAAA,CACA9K,MAAA2I,MAAAnH,SAAA,EAAA,IACA,CACA,EACAqK,UAAA,CACAL,UAAA,CACAxL,MAAA2I,MAAA5I,UAAA4I,MAAAnH,SAAA,EAAA,IAAA,CACA,CACA,CACA,EAEA,CACA2b,UAAA,CACA,CAAA/e,KAAA,WAAAmK,IAAA,GAAA,EACA,CAAAnK,KAAA,OAAAmK,IAAA,GAAA,EACA,CAAAnK,KAAA,UAAAmK,IAAA,GAAA,EACA,CAAAnK,KAAA,UAAAmK,IAAA,GAAA,EACA,CAAAnK,KAAA,YAAAmK,IAAA,GAAA,EACA,CAAAnK,KAAA,UAAAmK,IAAA,EAAA,GAEAmL,OAAA9V,OAAA+F,WAAA,IAAA,GAAA,IACA2R,OAAA1X,OAAA+F,WAAA,IAAA,CAAA,MAAA,OAAA,CAAA,MAAA,OACAkI,UAAA,CACAL,UAAA,CACAxL,MAAA2I,MAAA5I,UAAA4I,MAAAnH,SAAA,EAAA,IAAA,CACA,CACA,EACAnB,KAAA,CACAyK,UAAA,CACA9K,MAAA2I,MAAA5I,UAAA4I,MAAAnH,SAAA,EAAA,IAAA,EACAqJ,gBAAAlC,MAAA5I,UAAA4I,MAAAnH,SAAA,EAAA,IAAA,EACAoU,aAAA,EACAhL,QAAA,CAAA,EAAA,EACA,CACA,CACA,GAGAyB,OAAA,CACA,CACAlB,KAAA,QACArM,KAAA,CACA,CACAyD,MAAA,CAAA,KAAA,IAAA,IAAA,IAAA,IAAA,MACAlC,KAAA,SACAwN,UAAA,CACA7N,MAAA2I,MAAAvI,SAAA,MAAA,CACA,EACAsM,UAAA,CACA1M,MAAA2I,MAAA5I,UAAA4I,MAAAhI,UAAA,EAAAI,KAAA,EAAA,CACA,CACA,EACA,CACAwB,MAAA,CAAA,IAAA,KAAA,KAAA,KAAA,KAAA,MACAlC,KAAA,SACAwN,UAAA,CACA7N,MAAA2I,MAAAvI,SAAA,SAAA,CACA,EACAsM,UAAA,CACA1M,MAAA2I,MAAA5I,UAAA4I,MAAAhI,UAAA,EAAAG,QAAA,EAAA,CACA,CACA,EAEA,EAEA,CACAqK,KAAA,QACAsS,WAAA,EACA3e,KAAA,CACA,CACAyD,MAAA,CAAA,IAAA,IAAA,IAAA,IAAA,GAAA,IACAlC,KAAA,SACAkM,OAAA,OACAyB,WAAA,GACAxC,UAAA,CACAL,KAAA,QACA,EACA0C,UAAA,CACA7N,MAAA2I,MAAAvI,SAAA,SAAA,CACA,EACAsM,UAAA,CACA1M,MAAA2I,MAAA5I,UAAA4I,MAAAhI,UAAA,EAAAK,QAAA,EAAA,CACA,EACAyL,MAAA,CACAf,KAAA,CAAA,EACAN,UAAA,SAAAtC,GACA,OAAAA,EAAAvG,KACA,EACAvC,MAAA2I,MAAAnH,SAAA,EAAA,IACA,CACA,EACA,CACAe,MAAA,CAAA,IAAA,GAAA,GAAA,GAAA,GAAA,IACAlC,KAAA,SACAwN,UAAA,CACA7N,MAAA2I,MAAAvI,SAAA,QAAA,CACA,EACAsM,UAAA,CACA1M,MAAA2I,MAAA5I,UAAA4I,MAAAhI,UAAA,EAAAM,OAAA,EAAA,CACA,CACA,EAEA,EAEA,CAAA,CAEA,EAEA0H,MAAAhL,OAAA,WACAC,OAAA+F,WAAA,IACAuC,EAAAoD,UAAA,CACA4T,MAAA,CACA,CACA5H,OAAA,CAAA,MAAA,MACA,EACA,CACAA,OAAA,CAAA,MAAA,MACA,EAEA,CAAA,EAEApP,EAAAoD,UAAA,CACA4T,MAAA,CACA,CACA5H,OAAA,CAAA,MAAA,MACA,EACA,CACAA,OAAA,CAAA,MAAA,MACA,EAEA,CAAA,EAGA1X,OAAA+F,WAAA,IACAuC,EAAAoD,UAAA,CACA4T,MAAA,CACA,CACAxJ,OAAA,EACA,EACA,CACAA,OAAA,EACA,EAEA,CAAA,EAEAxN,EAAAoD,UAAA,CACA4T,MAAA,CACA,CACAxJ,OAAA,GACA,EACA,CACAA,OAAA,GACA,EAEA,CAAA,CAEA,CAAA,EAEA,EC7NAgK,8BAAA,WACA,IAIAxU,EACAhD,EAEA8G,EAeA2Q,EAtBAV,EAAA1f,SAAAgN,cAAA,8BAAA,EAEA0S,IAEA/T,EAAAP,MAAA/J,QAAAqe,EAAA,SAAA,EACA/W,EAAAtI,OAAA4M,QAAAC,KAAAwS,CAAA,EAEAjQ,EAAA,CACA,UACA,WACA,QACA,QACA,MACA,OACA,OACA,SACA,YACA,UACA,WACA,YAGA2Q,EAAA,WACA,OAAA/f,OAAA+F,WAAA,MAAA,IAAA/F,OAAA+F,WACA,CACA,CAAA,MAAA,OACA,CAAA,MAAA,OACA,CAAA,MAAA,QAGA/F,OAAA+F,WAAA,IACA,CACA,CAAA,MAAA,OACA,CAAA,MAAA,OACA,CAAA,MAAA,QAGA,CACA,CAAA,MAAA,OACA,CAAA,MAAA,OACA,CAAA,MAAA,OAEA,EAmJAsF,gBAAA/C,EAAAgD,EAjJA,WAAA,MAAA,CACAyE,OAAA,CACAtK,KAAA,OACAyH,UAAA,CACA9K,MAAA2I,MAAAnH,SAAA,EAAA,IACA,CACA,EACAkJ,QAAA,CACAC,QAAA,OACAC,QAAA,CAAA,EAAA,IACAC,gBAAAlC,MAAAnH,SAAA,EAAA,KACA2E,YAAAwC,MAAAnH,SAAA,EAAA,KACAsJ,UAAA,CAAA9K,MAAA2I,MAAAnH,SAAA,EAAA,KAAA,EACAuJ,YAAA,EACAC,mBAAA,EACAE,YAAA,CACAC,KAAA,MACA,CACA,EAEA+R,MAAA,CACA,CACAC,UAAA,CACA,CAAA/e,KAAA,QAAAmK,IAAA,GAAA,EACA,CAAAnK,KAAA,UAAAmK,IAAA,GAAA,EACA,CAAAnK,KAAA,YAAAmK,IAAA,GAAA,EACA,CAAAnK,KAAA,WAAAmK,IAAA,GAAA,GAEA+M,OAAAqI,EAAA,EAAA,GACAjK,OAAA,GACA7H,UAAA,CACAL,UAAA,CACAxL,MAAA2I,MAAA5I,UAAA4I,MAAAnH,SAAA,EAAA,IAAA,CACA,CACA,CACA,EACA,CACA2b,UAAA,CACA,CAAA/e,KAAA,WAAAmK,IAAA,GAAA,EACA,CAAAnK,KAAA,gBAAAmK,IAAA,GAAA,EACA,CAAAnK,KAAA,SAAAmK,IAAA,GAAA,EACA,CAAAnK,KAAA,cAAAmK,IAAA,GAAA,EACA,CAAAnK,KAAA,SAAAmK,IAAA,GAAA,GAEAmL,OAAA,GACA4B,OAAAqI,EAAA,EAAA,GACA9R,UAAA,CACAL,UAAA,CACAxL,MAAA2I,MAAA5I,UAAA4I,MAAAnH,SAAA,EAAA,IAAA,CACA,CACA,CACA,EACA,CACA2b,UAAAnQ,EAAAwB,IAAA,SAAAoP,GAAA,MAAA,CACAxf,KAAAwf,EACArV,IAAA,GACA,CAAA,CAAA,EACA+M,OAAAqI,EAAA,EAAA,GACAjK,OAAA,GACA7H,UAAA,CACAL,UAAA,CACAxL,MAAA2I,MAAA5I,UAAA4I,MAAAnH,SAAA,EAAA,IAAA,CACA,CACA,CACA,GAGA6K,OAAA,CACA,CACAlB,KAAA,QACAT,QAAA,CACAC,QAAA,MACA,EACA+B,UAAA,CACA1M,MAAA2I,MAAA5I,UAAA4I,MAAAhI,UAAA,EAAAI,KAAA,EAAA,CACA,EACAjC,KAAA,CACA,CACAyD,MAAA,CAAA,GAAA,GAAA,GAAA,IACAlC,KAAA,aACAwN,UAAA,CACA7N,MAAA2I,MAAAvI,SAAA,MAAA,CACA,CACA,EAEA,EACA,CACA+K,KAAA,QACAsS,WAAA,EACA3e,KAAA,CACA,CACAyD,MAAA,CAAA,GAAA,GAAA,GAAA,GAAA,IACAlC,KAAA,wBACAwN,UAAA,CACA7N,MAAA2I,MAAA5I,UAAA4I,MAAAhI,UAAA,EAAAC,QAAA,EAAA,CACA,EACA8L,UAAA,CACA1M,MAAA2I,MAAA5I,UAAA4I,MAAAhI,UAAA,EAAAC,QAAA,EAAA,CACA,CACA,EACA,CACA2B,MAAA,CAAA,GAAA,GAAA,GAAA,GAAA,IACAlC,KAAA,gBACAwN,UAAA,CACA7N,MAAA2I,MAAAvI,SAAA,SAAA,CACA,EACAsM,UAAA,CACA1M,MAAA2I,MAAA5I,UAAA4I,MAAAhI,UAAA,EAAAG,QAAA,EAAA,CACA,CACA,EAEA,EACA,CACAqK,KAAA,QACAsS,WAAA,EACA/Q,UAAA,GACAhC,QAAA,CACAgB,KAAA,CAAA,CACA,EACA5M,KAAA,CACA,CACAuB,KAAA,gBACAkC,MAAA,CAAA,IAAA,IAAA,EAAA,KAAA,KAAA,KAAA,KAAA,KAAA,KAAA,KAAA,EAAA,KACAsL,UAAA,CACA7N,MAAA2I,MAAAvI,SAAA,SAAA,CACA,EACAsM,UAAA,CACA1M,MAAA2I,MAAA5I,UAAA4I,MAAAhI,UAAA,EAAAC,QAAA,EAAA,CACA,CACA,EACA,CACAP,KAAA,cACAkC,MAAA,CAAA,EAAA,IAAA,EAAA,KAAA,KAAA,KAAA,KAAA,KAAA,KAAA,GAAA,IAAA,KACAsL,UAAA,CACA7N,MAAA2I,MAAAvI,SAAA,SAAA,CACA,EACAsM,UAAA,CACA1M,MAAA2I,MAAA5I,UAAA4I,MAAAhI,UAAA,EAAAK,QAAA,EAAA,CACA,CACA,EAEA,EAEA,CAAA,CAEA,EAGA2H,MAAAhL,OAAA,WACAuI,EAAAoD,UAAA,CACA4T,MAAAS,EAAA,EAAAnP,IAAA,SAAAU,GAAA,MAAA,CACAoG,OAAApG,CACA,CAAA,CAAA,CACA,CAAA,CACA,CAAA,EAEA,ECzMA2O,6BAAA,WACA,IAIA3U,EAJA4U,EAAAvgB,SAAAgN,cAAA,qCAAA,EAEAuT,IAEA5U,EAAAP,MAAA/J,QAAAkf,EAAA,SAAA,EACA5X,EAAAtI,OAAA4M,QAAAC,KAAAqT,CAAA,EA4FA7U,gBAAA/C,EAAAgD,EA1FA,WAAA,MAAA,CACAwB,QAAA,CACAC,QAAA,OACAO,YAAA,CACAC,KAAA,MACA,EACAP,QAAA,CAAA,EAAA,IACAC,gBAAAlC,MAAAnH,SAAA,EAAA,KACA2E,YAAAwC,MAAAnH,SAAA,EAAA,KACAsJ,UAAA,CAAA9K,MAAA2I,MAAAnH,SAAA,EAAA,KAAA,EACAuJ,YAAA,EACAC,mBAAA,CACA,EACAK,MAAA,CACAM,UAAA,CACA3L,MAAA2I,MAAAnH,SAAA,EAAA,IACA,EACA+J,SAAA,CACAG,KAAA,CAAA,EACAF,UAAA,CACAxL,MAAA2I,MAAAnH,SAAA,EAAA,IACA,CACA,EACAqK,UAAA,CACAH,KAAA,CAAA,EACAF,UAAA,CACAxL,MAAA2I,MAAAnH,SAAA,EAAA,IACA,CACA,CACA,EACAsK,MAAA,CACAH,UAAA,CACA3L,MAAA2I,MAAAnH,SAAA,EAAA,IACA,EACAqK,UAAA,CACAH,KAAA,CAAA,EACAF,UAAA,CACAxL,MAAA2I,MAAAnH,SAAA,EAAA,IACA,CACA,EAEA+J,SAAA,CACAG,KAAA,CAAA,EACAF,UAAA,CACAxL,MAAA2I,MAAAnH,SAAA,EAAA,IACA,CACA,CACA,EACA6K,OAAA,CACA,CAEAvN,KAAA,CACA,CAAA,GAAA,MACA,CAAA,KAAA,MACA,CAAA,GAAA,MACA,CAAA,KAAA,MACA,CAAA,GAAA,MACA,CAAA,GAAA,MACA,CAAA,KAAA,MACA,CAAA,GAAA,MACA,CAAA,GAAA,MACA,CAAA,KAAA,MACA,CAAA,KAAA,KACA,CAAA,KAAA,KACA,CAAA,KAAA,MACA,CAAA,KAAA,MACA,CAAA,KAAA,MACA,CAAA,KAAA,MACA,CAAA,KAAA,MACA,CAAA,KAAA,MACA,CAAA,GAAA,MACA,CAAA,GAAA,MACA,CAAA,KAAA,MACA,CAAA,KAAA,OAEAqM,KAAA,UACA0C,UAAA,CACA7N,MAAA2I,MAAAvI,SAAA,QAAA,CACA,CACA,GAEAuM,KAAA,CACAC,MAAA,EACAvJ,KAAA,EACAwJ,OAAA,EACAzJ,IAAA,EACA0J,aAAA,CAAA,CACA,CACA,CAAA,CAEA,EAEA,ECpGAiR,+BAAA,WACA,IAIA7U,EACAhD,EAEA8X,EAuDA3S,EAmBAS,EAmBAmS,EA2BAC,EAOAC,EAtIAC,EAAA7gB,SAAAgN,cAAA,uCAAA,EAEA6T,IAEAlV,EAAAP,MAAA/J,QAAAwf,EAAA,SAAA,EACAlY,EAAAtI,OAAA4M,QAAAC,KAAA2T,CAAA,EAEAJ,EAAA,CACA,CACA,CAAA,GAAA,MACA,CAAA,EAAA,MACA,CAAA,GAAA,MACA,CAAA,EAAA,MACA,CAAA,GAAA,MACA,CAAA,GAAA,MACA,CAAA,EAAA,MACA,CAAA,EAAA,MACA,CAAA,GAAA,OACA,CAAA,EAAA,MACA,CAAA,EAAA,OAEA,CACA,CAAA,GAAA,MACA,CAAA,EAAA,MACA,CAAA,GAAA,MACA,CAAA,EAAA,MACA,CAAA,GAAA,MACA,CAAA,GAAA,KACA,CAAA,EAAA,MACA,CAAA,EAAA,KACA,CAAA,GAAA,MACA,CAAA,EAAA,MACA,CAAA,EAAA,OAEA,CACA,CAAA,GAAA,MACA,CAAA,EAAA,MACA,CAAA,GAAA,OACA,CAAA,EAAA,MACA,CAAA,GAAA,MACA,CAAA,GAAA,MACA,CAAA,EAAA,MACA,CAAA,EAAA,MACA,CAAA,GAAA,MACA,CAAA,EAAA,MACA,CAAA,EAAA,OAEA,CACA,CAAA,EAAA,MACA,CAAA,EAAA,MACA,CAAA,EAAA,MACA,CAAA,EAAA,MACA,CAAA,EAAA,MACA,CAAA,EAAA,MACA,CAAA,EAAA,MACA,CAAA,GAAA,MACA,CAAA,EAAA,MACA,CAAA,EAAA,MACA,CAAA,EAAA,QAIA3S,EAAA,WAAA,MAAA,CACAM,UAAA,CACA3L,MAAA2I,MAAAnH,SAAA,EAAA,IACA,EACA+J,SAAA,CACAG,KAAA,CAAA,EACAF,UAAA,CACAxL,MAAA2I,MAAAnH,SAAA,EAAA,IACA,CACA,EAEAqK,UAAA,CACAH,KAAA,CAAA,EACAF,UAAA,CACAxL,MAAA2I,MAAAnH,SAAA,EAAA,IACA,CACA,CACA,CAAA,EAqBAyc,EAAA,CACAvJ,UAAA,EApBA5I,EAAA,WAAA,MAAA,CACAH,UAAA,CACA3L,MAAA2I,MAAAnH,SAAA,EAAA,IACA,EACAqK,UAAA,CACAH,KAAA,CAAA,EACAF,UAAA,CACAxL,MAAA2I,MAAAnH,SAAA,EAAA,IACA,CACA,EAEA+J,SAAA,CACAG,KAAA,CAAA,EACAF,UAAA,CACAxL,MAAA2I,MAAAnH,SAAA,EAAA,IACA,CACA,CACA,CAAA,GAIAiL,MAAA,CACArB,UAAA,kBACAiT,MAAA,QACAre,MAAA2I,MAAAnH,SAAA,EAAA,KACAsN,WAAA,GACA,EACAtD,UAAA,CACAL,KAAA,OACA,EACAT,QAAA,CACAU,UAAA,iBACA,EACAtM,KAAA,CACA,CACA,CACAwf,MAAA,CAAA,EAAA,GACA/R,OAAA,MACA,EACA,CACA+R,MAAA,CAAA,GAAA,IACA/R,OAAA,MACA,GAGA,EACA2R,EAAA,CACA,CAAA7a,KAAA,KAAAD,IAAA,MAAAY,MAAA,MAAAF,OAAA,KAAA,EACA,CAAA8I,MAAA,KAAAxJ,IAAA,MAAAY,MAAA,MAAAF,OAAA,KAAA,EACA,CAAAT,KAAA,KAAAwJ,OAAA,KAAA7I,MAAA,MAAAF,OAAA,KAAA,EACA,CAAA8I,MAAA,KAAAC,OAAA,KAAA7I,MAAA,MAAAF,OAAA,KAAA,GAGAqa,EAAA,CACA,CAAA9a,KAAA,EAAAuJ,MAAA,EAAAxJ,IAAA,KAAAU,OAAA,KAAA,EACA,CAAAT,KAAA,EAAAuJ,MAAA,EAAAxJ,IAAA,MAAAU,OAAA,KAAA,EACA,CAAAT,KAAA,EAAAuJ,MAAA,EAAAC,OAAA,MAAA/I,OAAA,KAAA,EACA,CAAAT,KAAA,EAAAuJ,MAAA,EAAAC,OAAA,GAAA/I,OAAA,KAAA,GAgFAmF,gBAAA/C,EAAAgD,EA7EA,WAAA,MAAA,CACAlJ,MAAA,CACA2I,MAAAvI,SAAA,SAAA,EACAuI,MAAAvI,SAAA,SAAA,EACAuI,MAAAvI,SAAA,SAAA,EACAuI,MAAAvI,SAAA,QAAA,GAEAsK,QAAA,CACAC,QAAA,OACAO,YAAA,CACAC,KAAA,MACA,EACAP,QAAA,CAAA,EAAA,IACAC,gBAAAlC,MAAAnH,SAAA,EAAA,KACA2E,YAAAwC,MAAAnH,SAAA,EAAA,KACAsJ,UAAA,CAAA9K,MAAA2I,MAAAnH,SAAA,EAAA,KAAA,EACAuJ,YAAA,EACAC,mBAAA,EACAI,UAAA,kBACA,EACA+G,MAAA,CACA/T,KAAA,qBACAiF,KAAA,SACAD,IAAA,EACA0H,UAAA,CACA9K,MAAA2I,MAAAnH,SAAA,EAAA,IACA,CACA,EACAmL,KAAA/O,OAAA+F,WAAA,IAAAwa,EAAAD,EACA7S,MAAA,CAAAuF,cAAA,CACAsE,UAAA,EAAA5M,IAAA,EAAAC,IAAA,EAAA,EAAA8C,EAAA,CAAA,EAAAuF,cAAA,CACAsE,UAAA,EAAA5M,IAAA,EAAAC,IAAA,EAAA,EAAA8C,EAAA,CAAA,EAAAuF,cAAA,CACAsE,UAAA,EAAA5M,IAAA,EAAAC,IAAA,EAAA,EAAA8C,EAAA,CAAA,EAAAuF,cAAA,CACAsE,UAAA,EAAA5M,IAAA,EAAAC,IAAA,EAAA,EAAA8C,EAAA,CAAA,GAEAS,MAAA,CAAA8E,cAAA,CACAsE,UAAA,EAAA5M,IAAA,EAAAC,IAAA,EAAA,EAAAuD,EAAA,CAAA,EAAA8E,cAAA,CACAsE,UAAA,EAAA5M,IAAA,EAAAC,IAAA,EAAA,EAAAuD,EAAA,CAAA,EAAA8E,cAAA,CACAsE,UAAA,EAAA5M,IAAA,EAAAC,IAAA,EAAA,EAAAuD,EAAA,CAAA,EAAA8E,cAAA,CACAsE,UAAA,EAAA5M,IAAA,EAAAC,IAAA,EAAA,EAAAuD,EAAA,CAAA,GAEAO,OAAA,CACA,CACAhM,KAAA,IACA8K,KAAA,UACA2J,WAAA,EACA/G,WAAA,EACAjP,KAAAkf,EAAA,GACAxR,SAAAyR,CACA,EACA,CACA5d,KAAA,KACA8K,KAAA,UACA2J,WAAA,EACA/G,WAAA,EACAjP,KAAAkf,EAAA,GACAxR,SAAAyR,CACA,EACA,CACA5d,KAAA,MACA8K,KAAA,UACA2J,WAAA,EACA/G,WAAA,EACAjP,KAAAkf,EAAA,GACAxR,SAAAyR,CACA,EACA,CACA5d,KAAA,KACA8K,KAAA,UACA2J,WAAA,EACA/G,WAAA,EACAjP,KAAAkf,EAAA,GACAxR,SAAAyR,CACA,EAEA,CAAA,CAEA,EAEAtV,MAAAhL,OAAA,WACAC,OAAA+F,WAAA,IACAuC,EAAAoD,UAAA,CACAqD,KAAAwR,CACA,CAAA,EAEAjY,EAAAoD,UAAA,CACAqD,KAAAuR,CACA,CAAA,CAEA,CAAA,EAEA,ECzOAK,kCAAA,WACA,IAAAC,EAAAjhB,SAAAgN,cACA,2CACA,EAEA,GAAAiU,EAAA,CAmCA,IAjCA,IAAAtV,EAAAP,MAAA/J,QAAA4f,EAAA,SAAA,EACAtY,EAAAtI,OAAA4M,QAAAC,KAAA+T,CAAA,EAEA3F,EAAA,CACA,OACA,MACA,MACA,MACA,MACA,MACA,MACA,MACA,MACA,MACA,OACA,OACA,OACA,MACA,MACA,MACA,MACA,MACA,MACA,MACA,MACA,MACA,OACA,QAGA5Q,EAAA,CAAA,WAAA,SAAA,WAAA,YAAA,UAAA,SAAA,UAEAnJ,EAAA,GACA+I,EAAA,EAAAA,EAAA,EAAAA,GAAA,EACA,IAAA,IAAA4M,EAAA,EAAAA,EAAA,GAAAA,GAAA,EACA3V,EAAA+Q,KAAA,CAAA4E,EAAA5M,EAAAc,MAAAN,gBAAA,EAAA,EAAA,EAAA,EA6EAY,gBAAA/C,EAAAgD,EAzEA,WAAA,MAAA,CACAwB,QAAA,CACAC,QAAA,OACAO,YAAA,CACAC,KAAA,MACA,EACAP,QAAA,CAAA,EAAA,IACAC,gBAAAlC,MAAAnH,SAAA,EAAA,KACA2E,YAAAwC,MAAAnH,SAAA,EAAA,KACAsJ,UAAA,CAAA9K,MAAA2I,MAAAnH,SAAA,EAAA,KAAA,EACAuJ,YAAA,EACAC,mBAAA,EACAC,SAAA,MACAG,UAAA,SAAAtC,GAAA,MAAA,iBAAArK,OACAwJ,EAAAa,EAAAvG,MAAA,IAAA,sBAAA,EAAA9D,OACAoa,EAAA/P,EAAAvG,MAAA,IAAA,KAAA,EAAA9D,OAAAqK,EAAAvG,MAAA,GAAA,cAAA,CAAA,CAEA,EACA8I,MAAA,CACAF,KAAA,WACArM,KAAA+Z,EACAvN,YAAA,CAAA,EACAO,UAAA,CACAH,KAAA,CAAA,EACAF,UAAA,CACAxL,MAAA2I,MAAAnH,SAAA,EAAA,IACA,CACA,EACA+J,SAAA,CACAG,KAAA,CAAA,CACA,EACAD,SAAA,CACAD,UAAA,CACAxL,MAAA2I,MAAAnH,SAAA,EAAA,IACA,CACA,CACA,EACAsK,MAAA,CACAX,KAAA,WACArM,KAAAmJ,EACAsD,SAAA,CACAG,KAAA,CAAA,CACA,EACAD,SAAA,CACAD,UAAA,CACAxL,MAAA2I,MAAAnH,SAAA,EAAA,IACA,CACA,EACAmK,UAAA,CACAC,OAAA,EACA,CACA,EACAS,OAAA,CACA,CACAhM,KAAA,aACA8K,KAAA,UACA6C,WAAA,SAAA2C,GAAA,OAAA,EAAAA,EAAA,EAAA,EACA7R,KAAAA,EACA2f,eAAA,SAAAC,GAAA,OAAA,EAAAA,CAAA,EACA7Q,UAAA,CACA7N,MAAA2I,MAAAvI,SAAA,SAAA,CACA,CACA,GAEAuM,KAAA,CACAC,MAAA,GACAvJ,KAAA,EACAwJ,OAAA,EACAzJ,IAAA,EACA0J,aAAA,CAAA,CACA,CACA,CAAA,CAEA,CACA,CACA,ECzHA6R,4BAAA,WACA,IAIAzV,EAGAjB,EAPA2W,EAAArhB,SAAAgN,cAAA,oCAAA,EAEAqU,IAEA1V,EAAAP,MAAA/J,QAAAggB,EAAA,SAAA,EACA1Y,EAAAtI,OAAA4M,QAAAC,KAAAmU,CAAA,EAEA3W,EAAA,CAAA,SAAA,UAAA,YAAA,WAAA,SAAA,WAAA,UA2JAgB,gBAAA/C,EAAAgD,EAzJA,WAAA,MAAA,CACAwB,QAAA,CACAC,QAAA,OACAC,QAAA,CAAA,EAAA,IACAC,gBAAAlC,MAAAnH,SAAA,EAAA,KACA2E,YAAAwC,MAAAnH,SAAA,EAAA,KACAsJ,UAAA,CAAA9K,MAAA2I,MAAAnH,SAAA,EAAA,KAAA,EACAuJ,YAAA,EACAC,mBAAA,EACAC,SAAA,SAAApC,EAAAC,EAAAxI,EAAAwC,EAAAiG,GACA,OAAAH,YAAAC,EAAAC,EAAAxI,EAAAwC,EAAAiG,CAAA,CACA,EACAmC,YAAA,CACAC,KAAA,MACA,EACAC,UAAAxB,gBACA,EACAyB,MAAA,CACAF,KAAA,WACArM,KAAAmJ,EACAqD,YAAA,CAAA,EACAC,SAAA,CACAC,UAAA,CACAxL,MAAA2I,MAAAnH,SAAA,EAAA,KACA2J,KAAA,OACA,CACA,EACAM,SAAA,CAAAC,KAAA,CAAA,CAAA,EACAC,UAAA,CACA3L,MAAA2I,MAAAnH,SAAA,EAAA,KACAoK,OAAA,GACAR,UAAA,SAAA7I,GAAA,OAAAA,EAAAhD,UAAA,EAAA,CAAA,CAAA,CACA,EACAsM,UAAA,CACAH,KAAA,CAAA,CACA,CACA,EACAI,MAAA,CACAX,KAAA,QACAU,UAAA,CACAL,UAAA,CACAxL,MAAA2I,MAAAnH,SAAA,EAAA,IACA,CACA,EACA8J,YAAA,CAAA,EACAK,UAAA,CACAD,KAAA,CAAA,EACA1L,MAAA2I,MAAAnH,SAAA,EAAA,KACAoK,OAAA,EACA,EACAH,SAAA,CAAAC,KAAA,CAAA,CAAA,EACAH,SAAA,CAAAG,KAAA,CAAA,CAAA,CACA,EACAW,OAAA,CACA,CACAhM,KAAA,eACA8K,KAAA,OACA6C,WAAA,GACAG,MAAA,UACArP,KAAA,CAAA,IAAA,IAAA,IAAA,IAAA,GAAA,IAAA,KACA4N,UAAA,CACA1M,MAAA2I,MAAA5I,UAAA4I,MAAAvI,SAAA,MAAA,EAAA,EAAA,CACA,EACAyN,UAAA,CACA7N,MAAA2I,MAAAnH,SAAA,EAAA,KACA2E,YAAAwC,MAAAvI,SAAA,MAAA,EACA2K,YAAA,CACA,EACAS,UAAA,CACAxL,MAAA2I,MAAAvI,SAAA,MAAA,CACA,EACAmM,OAAA,QACA,EACA,CACAlM,KAAA,WACA8K,KAAA,OACA6C,WAAA,GACAG,MAAA,UACArP,KAAA,CAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,KACA4N,UAAA,CACA1M,MAAA2I,MAAA5I,UAAA4I,MAAAvI,SAAA,SAAA,EAAA,EAAA,CACA,EACAyN,UAAA,CACA7N,MAAA2I,MAAAnH,SAAA,EAAA,KACA2E,YAAAwC,MAAAvI,SAAA,SAAA,EACA2K,YAAA,CACA,EACAS,UAAA,CACAxL,MAAA2I,MAAAvI,SAAA,SAAA,CACA,EACAmM,OAAA,QACA,EACA,CACAlM,KAAA,eACA8K,KAAA,OACA6C,WAAA,GACAG,MAAA,UACArP,KAAA,CAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,KACA4N,UAAA,CACA1M,MAAA2I,MAAA5I,UAAA4I,MAAAvI,SAAA,QAAA,EAAA,EAAA,CACA,EACAyN,UAAA,CACA7N,MAAA2I,MAAAnH,SAAA,EAAA,KACA2E,YAAAwC,MAAAvI,SAAA,QAAA,EACA2K,YAAA,CACA,EACAS,UAAA,CACAxL,MAAA2I,MAAAvI,SAAA,QAAA,CACA,EACAmM,OAAA,QACA,EACA,CACAlM,KAAA,iBACA8K,KAAA,OACA6C,WAAA,GACAG,MAAA,UACArP,KAAA,CAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,KACA4N,UAAA,CACA1M,MAAA2I,MAAA5I,UAAA4I,MAAAvI,SAAA,SAAA,EAAA,EAAA,CACA,EACAyN,UAAA,CACA7N,MAAA2I,MAAAnH,SAAA,EAAA,KACA2E,YAAAwC,MAAAvI,SAAA,SAAA,EACA2K,YAAA,CACA,EACAS,UAAA,CACAxL,MAAA2I,MAAAvI,SAAA,SAAA,CACA,EACAmM,OAAA,QACA,EACA,CACAlM,KAAA,eACA8K,KAAA,OACA6C,WAAA,GACAG,MAAA,UACArP,KAAA,CAAA,IAAA,IAAA,IAAA,IAAA,KAAA,KAAA,MACA4N,UAAA,CACA1M,MAAA2I,MAAA5I,UAAA4I,MAAAvI,SAAA,SAAA,EAAA,EAAA,CACA,EACAyN,UAAA,CACA7N,MAAA2I,MAAAnH,SAAA,EAAA,KACA2E,YAAAwC,MAAAvI,SAAA,SAAA,EACA2K,YAAA,CACA,EACAS,UAAA,CACAxL,MAAA2I,MAAAvI,SAAA,SAAA,CACA,EACAmM,OAAA,QACA,GAEAI,KAAA,CAAAC,MAAA,GAAAvJ,KAAA,EAAAwJ,OAAA,EAAAzJ,IAAA,EAAA0J,aAAA,CAAA,CAAA,CACA,CAAA,CAEA,EAEA,ECrKA+R,kCAAA,WACA,IAMA3V,EAGAjB,EATA6W,EAAAvhB,SAAAgN,cACA,0CACA,EAEAuU,IAEA5V,EAAAP,MAAA/J,QAAAkgB,EAAA,SAAA,EACA5Y,EAAAtI,OAAA4M,QAAAC,KAAAqU,CAAA,EAEA7W,EAAA,CAAA,SAAA,UAAA,YAAA,WAAA,SAAA,WAAA,UAwJAgB,gBAAA/C,EAAAgD,EAtJA,WAAA,MAAA,CACAlJ,MAAA,CACA2I,MAAAvI,SAAA,MAAA,EACAuI,MAAAvI,SAAA,QAAA,EACAuI,MAAAvI,SAAA,SAAA,EACAuI,MAAAvI,SAAA,SAAA,EACAuI,MAAAvI,SAAA,SAAA,GAEAsK,QAAA,CACAC,QAAA,OACAO,YAAA,CACAC,KAAA,QACA,EACAP,QAAA,CAAA,EAAA,IACAC,gBAAAlC,MAAAnH,SAAA,EAAA,KACA2E,YAAAwC,MAAAnH,SAAA,EAAA,KACAsJ,UAAA,CAAA9K,MAAA2I,MAAAnH,SAAA,EAAA,KAAA,EACAuJ,YAAA,EACAC,mBAAA,EACAI,UAAAxB,gBACA,EACAuD,QAAA,CACAC,QAAA,CACAE,UAAA,CACAnC,KAAA,CAAA,QAAA,QACA,CACA,EACAyB,MAAA,CACA,EACAe,OAAA,CACA7O,KAAA,CAAA,SAAA,UAAA,eAAA,WAAA,iBACAgM,UAAA,CACA9K,MAAA2I,MAAAnH,SAAA,EAAA,IACA,EACA6B,KAAA,CACA,EACAgI,MAAA,CACAF,KAAA,QACAI,SAAA,CACAG,KAAA,CAAA,EACAF,UAAA,CACAxL,MAAA2I,MAAAnH,SAAA,EAAA,IACA,CACA,EACAiK,SAAA,CAAAC,KAAA,CAAA,CAAA,EACAC,UAAA,CACA3L,MAAA2I,MAAAnH,SAAA,EAAA,IACA,EACAqK,UAAA,CACAL,UAAA,CACAE,KAAA,CAAA,EACA1L,MAAA2I,MAAAnH,SAAA,EAAA,IACA,CACA,CACA,EACAsK,MAAA,CACAX,KAAA,WACArM,KAAAmJ,EACAsD,SAAA,CACAC,UAAA,CACAE,KAAA,CAAA,EACA1L,MAAA2I,MAAAnH,SAAA,EAAA,IACA,CACA,EACAiK,SAAA,CAAAC,KAAA,CAAA,CAAA,EACAC,UAAA,CACA3L,MAAA2I,MAAAnH,SAAA,EAAA,KACA4J,UAAA,SAAA7I,GAAA,OAAAA,EAAAhD,UAAA,EAAA,CAAA,CAAA,CACA,CACA,EACA8M,OAAA,CACA,CACAhM,KAAA,SACA8K,KAAA,MACAgD,MAAA,QACA1B,MAAA,CACAf,KAAA,CAAA,EACAZ,UAAA,CACA9K,MAAA,MACA,CACA,EACAsB,SAAA,CACA0S,MAAA,QACA,EACAlV,KAAA,CAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IACA,EACA,CACAuB,KAAA,UACA8K,KAAA,MACAgD,MAAA,QACA1B,MAAA,CACAf,KAAA,CAAA,CACA,EACApK,SAAA,CACA0S,MAAA,QACA,EACAlV,KAAA,CAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IACA,EACA,CACAuB,KAAA,eACA8K,KAAA,MACAgD,MAAA,QACA1B,MAAA,CACAf,KAAA,CAAA,EACAZ,UAAA,CACA9K,MAAA,MACA,CACA,EACAsB,SAAA,CACA0S,MAAA,QACA,EACAlV,KAAA,CAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IACA,EACA,CACAuB,KAAA,WACA8K,KAAA,MACAgD,MAAA,QACA1B,MAAA,CACAf,KAAA,CAAA,EACAZ,UAAA,CACA9K,MAAA,MACA,CACA,EACAsB,SAAA,CACA0S,MAAA,QACA,EACAlV,KAAA,CAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IACA,EACA,CACAuB,KAAA,gBACA8K,KAAA,MACAgD,MAAA,QACA1B,MAAA,CACAf,KAAA,CAAA,CACA,EACApK,SAAA,CACA0S,MAAA,QACA,EACAlV,KAAA,CAAA,IAAA,IAAA,IAAA,IAAA,KAAA,KAAA,KACA,GAEA6N,KAAA,CACAC,MAAA,GACAvJ,KAAA,EACAwJ,OAAA,EACAzJ,IAAA,MACA0J,aAAA,CAAA,CACA,CACA,CAAA,CAEA,EAEA,ECpKAiS,4BAAA,WACA,IAIA7V,EAGAjB,EAPA+W,EAAAzhB,SAAAgN,cAAA,oCAAA,EAEAyU,IAEA9V,EAAAP,MAAA/J,QAAAogB,EAAA,SAAA,EACA9Y,EAAAtI,OAAA4M,QAAAC,KAAAuU,CAAA,EAEA/W,EAAA,CAAA,SAAA,UAAA,YAAA,WAAA,SAAA,WAAA,UA6IAgB,gBAAA/C,EAAAgD,EA3IA,WAAA,MAAA,CACAwB,QAAA,CACAC,QAAA,OACAC,QAAA,CAAA,EAAA,IACAC,gBAAAlC,MAAAnH,SAAA,EAAA,KACA2E,YAAAwC,MAAAnH,SAAA,EAAA,KACAsJ,UAAA,CAAA9K,MAAA2I,MAAAnH,SAAA,EAAA,KAAA,EACAuJ,YAAA,EACAC,mBAAA,EACAC,SAAA,SAAApC,EAAAC,EAAAxI,EAAAwC,EAAAiG,GACA,OAAAH,YAAAC,EAAAC,EAAAxI,EAAAwC,EAAAiG,CAAA,CACA,EACAmC,YAAA,CACAC,KAAA,MACA,EACAC,UAAAxB,gBACA,EACAyB,MAAA,CACAF,KAAA,WACArM,KAAAmJ,EACAqD,YAAA,CAAA,EACAC,SAAA,CACAC,UAAA,CACAxL,MAAA2I,MAAAnH,SAAA,EAAA,KACA2J,KAAA,OACA,CACA,EACAM,SAAA,CAAAC,KAAA,CAAA,CAAA,EACAC,UAAA,CACA3L,MAAA2I,MAAAnH,SAAA,EAAA,KACAoK,OAAA,GACAR,UAAA,SAAA7I,GAAA,OAAAA,EAAAhD,UAAA,EAAA,CAAA,CAAA,CACA,EACAsM,UAAA,CACAH,KAAA,CAAA,CACA,CACA,EACAI,MAAA,CACAX,KAAA,QACAU,UAAA,CACAL,UAAA,CACAxL,MAAA2I,MAAAnH,SAAA,EAAA,KACA2J,KAAA,QACA,CACA,EACAG,YAAA,CAAA,EACAK,UAAA,CACAD,KAAA,CAAA,EACA1L,MAAA2I,MAAAnH,SAAA,EAAA,KACAoK,OAAA,EACA,EACAH,SAAA,CAAAC,KAAA,CAAA,CAAA,EACAH,SAAA,CAAAG,KAAA,CAAA,CAAA,CACA,EACAW,OAAA,CACA,CACAhM,KAAA,eACA8K,KAAA,OACA6C,WAAA,EACAH,UAAA,CACA7N,MAAA2I,MAAAnH,SAAA,EAAA,KACA2E,YAAAwC,MAAAvI,SAAA,MAAA,EACA2K,YAAA,CACA,EACAS,UAAA,CACAxL,MAAA2I,MAAAvI,SAAA,MAAA,CACA,EACAmM,OAAA,SACA4B,MAAA,UACArP,KAAA,CAAA,IAAA,IAAA,IAAA,IAAA,GAAA,IAAA,IACA,EACA,CACAuB,KAAA,WACA8K,KAAA,OACA6C,WAAA,GACAH,UAAA,CACA7N,MAAA2I,MAAAnH,SAAA,EAAA,KACA2E,YAAAwC,MAAAvI,SAAA,SAAA,EACA2K,YAAA,CACA,EACAS,UAAA,CACAxL,MAAA2I,MAAAvI,SAAA,SAAA,CACA,EACAmM,OAAA,SACA4B,MAAA,UACArP,KAAA,CAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IACA,EACA,CACAuB,KAAA,eACA8K,KAAA,OACA6C,WAAA,GACAH,UAAA,CACA7N,MAAA2I,MAAAnH,SAAA,EAAA,KACA2E,YAAAwC,MAAAvI,SAAA,QAAA,EACA2K,YAAA,CACA,EACAS,UAAA,CACAxL,MAAA2I,MAAAvI,SAAA,QAAA,CACA,EACAmM,OAAA,SACA4B,MAAA,UACArP,KAAA,CAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IACA,EACA,CACAuB,KAAA,iBACA8K,KAAA,OACA6C,WAAA,GACAH,UAAA,CACA7N,MAAA2I,MAAAnH,SAAA,EAAA,KACA2E,YAAAwC,MAAAvI,SAAA,SAAA,EACA2K,YAAA,CACA,EACAS,UAAA,CACAxL,MAAA2I,MAAAvI,SAAA,SAAA,CACA,EACAmM,OAAA,SACA4B,MAAA,UACArP,KAAA,CAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IACA,EACA,CACAuB,KAAA,eACA8K,KAAA,OACA6C,WAAA,GACAH,UAAA,CACA7N,MAAA2I,MAAAnH,SAAA,EAAA,KACA2E,YAAAwC,MAAAvI,SAAA,SAAA,EACA2K,YAAA,CACA,EACAS,UAAA,CACAxL,MAAA2I,MAAAvI,SAAA,SAAA,CACA,EACAmM,OAAA,SACA4B,MAAA,UACArP,KAAA,CAAA,IAAA,IAAA,IAAA,IAAA,KAAA,KAAA,KACA,GAEA6N,KAAA,CAAAC,MAAA,GAAAvJ,KAAA,EAAAwJ,OAAA,EAAAzJ,IAAA,EAAA0J,aAAA,CAAA,CAAA,CACA,CAAA,CAEA,EAEA,EC3JAmS,gCAAA,WACA,IAGA/V,EAEAsG,EACAC,EACAC,EACAC,EACAC,EAEAE,EAXAoP,EAAA3hB,SAAAgN,cAAA,wCAAA,EAEA2U,IACAhW,EAAAP,MAAA/J,QAAAsgB,EAAA,SAAA,EACAhZ,EAAAtI,OAAA4M,QAAAC,KAAAyU,CAAA,EACA1P,EAAA,CAAA,MAAA,MAAA,MAAA,MAAA,MAAA,MAAA,OACAC,EAAA,CAAA,GAAA,GAAA,GAAA,GAAA,GAAA,GAAA,IACAC,EAAA,CAAA,GAAA,GAAA,GAAA,GAAA,GAAA,GAAA,IACAC,EAAA,CAAA,GAAA,GAAA,GAAA,GAAA,GAAA,GAAA,IACAC,EAAA,CAAA,GAAA,GAAA,GAAA,GAAA,GAAA,GAAA,IAEAE,EAAA,CACAjC,UAAA,CACAmC,YAAArH,MAAA5I,UAAA4I,MAAAvI,SAAA,MAAA,EAAA,EAAA,CACA,CACA,EAuGA6I,gBAAA/C,EAAAgD,EArGA,WAAA,MAAA,CACAlJ,MAAA,CACA2I,MAAAvI,SAAA,SAAA,EACAuI,MAAAvI,SAAA,MAAA,EACA,SAAAuI,MAAAzD,OAAA,EAAA,UAAA,UACA,SAAAyD,MAAAzD,OAAA,EAAA,UAAA,WAEAwF,QAAA,CACAC,QAAA,OACAC,QAAA,CAAA,EAAA,IACAC,gBAAAlC,MAAAnH,SAAA,EAAA,KACA2E,YAAAwC,MAAAnH,SAAA,EAAA,KACAsJ,UAAA,CAAA9K,MAAA2I,MAAAnH,SAAA,EAAA,IAAA,EACAuJ,YAAA,EACAC,mBAAA,EACAE,YAAA,CACAC,KAAA,MACA,CACA,EACAwC,OAAA,CACA7O,KAAA,CAAA,SAAA,OAAA,SAAA,OACAgM,UAAA,CACA9K,MAAA2I,MAAAnH,SAAA,EAAA,IACA,CACA,EACA6J,MAAA,CACAvM,KAAA0Q,EACA3D,UAAA,CAAAH,KAAA,CAAA,CAAA,EACAuE,UAAA,CAAAvE,KAAA,CAAA,CAAA,EAEAC,UAAA,CACA3L,MAAA2I,MAAAnH,SAAA,EAAA,KACAoK,OAAA,CACA,EAEAL,SAAA,CACAC,UAAA,CACAxL,MAAA2I,MAAAnH,SAAA,EAAA,KACA2J,KAAA,QACA,CACA,EACAM,SAAA,CACAC,KAAA,CAAA,CACA,CACA,EACAI,MAAA,CACAD,UAAA,CACAL,UAAA,CACAxL,MAAA2I,MAAAnH,SAAA,EAAA,KACA2J,KAAA,QACA,CACA,EACAQ,UAAA,CACA3L,MAAA2I,MAAAnH,SAAA,EAAA,IACA,EACAyJ,SAAA,OACA,EACAoB,OAAA,CACA,CACAhM,KAAA,SACA8K,KAAA,MACAgD,MAAA,MACA7M,SAAAwO,EACAhR,KAAA2Q,CACA,EACA,CACApP,KAAA,OACA8K,KAAA,MACAgD,MAAA,MACA7M,SAAAwO,EACAhR,KAAA4Q,CACA,EACA,CACArP,KAAA,SACA8K,KAAA,MACAgD,MAAA,MACA7M,SAAAwO,EACAhR,KAAA6Q,CACA,EACA,CACAtP,KAAA,MACA8K,KAAA,MACAgD,MAAA,MACA7M,SAAAwO,EACAhR,KAAA8Q,EACA/B,UAAA,CACA+H,aAAA,CAAA,EAAA,EAAA,EAAA,EACA,CACA,GAGAiG,SAAA,OACAlP,KAAA,CACAvJ,IAAA,KACAyJ,OAAA,GACAxJ,KAAA,EACAuJ,MAAA,EACAE,aAAA,CAAA,CACA,CACA,CAAA,CAEA,EAEA,ECrHAqS,yBAAA,WACA,IAIAjW,EAGAjB,EAPAmX,EAAA7hB,SAAAgN,cAAA,iCAAA,EAEA6U,IAEAlW,EAAAP,MAAA/J,QAAAwgB,EAAA,SAAA,EACAlZ,EAAAtI,OAAA4M,QAAAC,KAAA2U,CAAA,EAEAnX,EAAA,CAAA,SAAA,UAAA,YAAA,WAAA,SAAA,WAAA,UAgHAgB,gBAAA/C,EAAAgD,EA9GA,WAAA,MAAA,CACAlJ,MAAA,CAAA2I,MAAAvI,SAAA,QAAA,EAAAuI,MAAAvI,SAAA,SAAA,EAAAuI,MAAAvI,SAAA,SAAA,GAEAsK,QAAA,CACAC,QAAA,OACAC,QAAA,CAAA,EAAA,IACAC,gBAAAlC,MAAAnH,SAAA,EAAA,KACA2E,YAAAwC,MAAAnH,SAAA,EAAA,KACAsJ,UAAA,CAAA9K,MAAA2I,MAAAnH,SAAA,EAAA,KAAA,EACAuJ,YAAA,EACAC,mBAAA,EACAI,UAAAxB,iBACAqB,SAAA,SAAApC,EAAAC,EAAAxI,EAAAwC,EAAAiG,GACA,OAAAH,YAAAC,EAAAC,EAAAxI,EAAAwC,EAAAiG,CAAA,CACA,CACA,EACAsC,MAAA,CACAF,KAAA,WACArM,KAAAmJ,EACAqD,YAAA,CAAA,EACAC,SAAA,CACAC,UAAA,CACAxL,MAAA2I,MAAAnH,SAAA,EAAA,KACA2J,KAAA,OACA,CACA,EACAM,SAAA,CAAAC,KAAA,CAAA,CAAA,EACAC,UAAA,CACAP,UAAA,SAAA7I,GAAA,OAAAA,EAAAhD,UAAA,EAAA,CAAA,CAAA,EACAS,MAAA2I,MAAAnH,SAAA,EAAA,KACAoK,OAAA,EACA,EACAC,UAAA,CACAH,KAAA,CAAA,CACA,EACAR,YAAA,CACAM,UAAA,CACAxL,MAAA2I,MAAAnH,SAAA,EAAA,IACA,CACA,CACA,EACAsK,MAAA,CACAX,KAAA,QACAU,UAAA,CACAL,UAAA,CACAxL,MAAA2I,MAAAnH,SAAA,EAAA,IACA,CACA,EACA8J,YAAA,CAAA,EACAK,UAAA,CACAD,KAAA,CAAA,EACA1L,MAAA2I,MAAAnH,SAAA,EAAA,KACAoK,OAAA,EACA,EACAH,SAAA,CAAAC,KAAA,CAAA,CAAA,EACAH,SAAA,CAAAG,KAAA,CAAA,CAAA,CACA,EACAW,OAAA,CACA,CACAhM,KAAA,aACA8K,KAAA,OACAkU,KAAA,QACArR,WAAA,GACAH,UAAA,CACA7N,MAAA2I,MAAAnH,SAAA,EAAA,KACA2E,YAAAwC,MAAAvI,SAAA,SAAA,EACA2K,YAAA,CACA,EACAS,UAAA,CACAxL,MAAA2I,MAAAvI,SAAA,SAAA,CACA,EACAmM,OAAA,SACAzN,KAAA,CAAA,IAAA,IAAA,IAAA,IAAA,GAAA,IAAA,IACA,EACA,CACAuB,KAAA,cACA8K,KAAA,OACAkU,KAAA,SACArR,WAAA,GACAH,UAAA,CACA7N,MAAA2I,MAAAnH,SAAA,EAAA,KACA2E,YAAAwC,MAAAvI,SAAA,SAAA,EACA2K,YAAA,CACA,EACAS,UAAA,CACAxL,MAAA2I,MAAAvI,SAAA,SAAA,CACA,EACAmM,OAAA,SACAzN,KAAA,CAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IACA,EACA,CACAuB,KAAA,WACA8K,KAAA,OACAkU,KAAA,MACArR,WAAA,GACAH,UAAA,CACA7N,MAAA2I,MAAAnH,SAAA,EAAA,KACA2E,YAAAwC,MAAAvI,SAAA,QAAA,EACA2K,YAAA,CACA,EACAS,UAAA,CACAxL,MAAA2I,MAAAvI,SAAA,QAAA,CACA,EACAmM,OAAA,SACAzN,KAAA,CAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IACA,GAEA6N,KAAA,CAAAC,MAAA,KAAAvJ,KAAA,KAAAwJ,OAAA,MAAAzJ,IAAA,IAAA,CACA,CAAA,CAEA,EAEA,ElDhFA/F,SAAAsW,oBAAA,EmDMAtW,SAAA8b,wBAAA,EACA9b,SAAA4e,mBAAA,EACA5e,SAAAqV,wBAAA,EnDRArV,SAAA+X,wBAAA,EmDUA/X,SAAA0hB,2BAAA,EnDRA1hB,SAAAshB,2BAAA,EmDUAthB,SAAA2c,0BAAA,EnDRA3c,SAAAgN,0BAAA,EmDUAhN,SAAA8c,wBAAA,EACA9c,SAAA8hB,wBAAA,EnDVA9hB,SAAAqc,4BAAA,EAEArc,SAAAwY,2BAAA,EmDWAxY,SAAA4b,6BAAA,EACA5b,SAAA4Q,2BAAA,EACA5Q,SAAA+R,yBAAA,EACA/R,SAAAiV,yBAAA,EACAjV,SAAAwhB,iCAAA,EACAxhB,SAAA+Q,uBAAA,EACA/Q,SAAAua,2BAAA,EACAva,SAAA0P,uBAAA,EACA1P,SAAAyV,gCAAA,EACAzV,SAAA6W,gCAAA,EACA7W,SAAA2d,iBAAA,EACA3d,SAAAwgB,4BAAA,EACAxgB,SAAAwW,sBAAA,EnDXAxW,SAAA0gB,8BAAA,EAEA1gB,SAAAkhB,iCAAA,EmDYAlhB,SAAAmW,0BAAA,EACAnW,SAAAma,6BAAA,EACAna,SAAAqa,yBAAA,EACAra,SAAAwZ,8BAAA,EACAxZ,SAAA8Z,+BAAA,EACA9Z,SAAAgZ,0BAAA,EACAhZ,SAAAyc,uBAAA,EACAzc,SAAAgd,gCAAA,EACAhd,SAAA6S,2BAAA,EACA7S,SAAAoY,+BAAA,EnDZApY,SAAAof,6BAAA,EAEApf,SAAA2f,qBAAA,EmDaA3f,SAAA+f,+BAAA,EACA/f,SAAAqgB,6BAAA,EACArgB,SAAAyf,2BAAA,EACAzf,SAAAsb,uBAAA,EACAtb,SAAA0b,mCAAA,EACA1b,SAAAiS,0BAAA,EACAjS,SAAA8e,4BAAA,EACA9e,SAAA4hB,+BAAA,EACA5hB,SAAAie,0BAAA", "file": "echart-example.js", "sourcesContent": ["/* -------------------------------------------------------------------------- */\r\n/*                                    Utils                                   */\r\n/* -------------------------------------------------------------------------- */\r\nconst docReady = fn => {\r\n  // see if DOM is already available\r\n  if (document.readyState === 'loading') {\r\n    document.addEventListener('DOMContentLoaded', fn);\r\n  } else {\r\n    setTimeout(fn, 1);\r\n  }\r\n};\r\n\r\nconst resize = fn => window.addEventListener('resize', fn);\r\n\r\nconst isIterableArray = array => Array.isArray(array) && !!array.length;\r\n\r\nconst camelize = str => {\r\n  const text = str.replace(/[-_\\s.]+(.)?/g, (match, capture) => {\r\n    if (capture) {\r\n      return capture.toUpperCase();\r\n    }\r\n    return '';\r\n  });\r\n  return `${text.substr(0, 1).toLowerCase()}${text.substr(1)}`;\r\n};\r\n\r\nconst getData = (el, data) => {\r\n  try {\r\n    return JSON.parse(el.dataset[camelize(data)]);\r\n  } catch (e) {\r\n    return el.dataset[camelize(data)];\r\n  }\r\n};\r\n\r\n/* ----------------------------- Colors function ---------------------------- */\r\n\r\nconst hexToRgb = hexValue => {\r\n  let hex;\r\n  hexValue.indexOf('#') === 0 ? (hex = hexValue.substring(1)) : (hex = hexValue);\r\n  // Expand shorthand form (e.g. \"03F\") to full form (e.g. \"0033FF\")\r\n  const shorthandRegex = /^#?([a-f\\d])([a-f\\d])([a-f\\d])$/i;\r\n  const result = /^#?([a-f\\d]{2})([a-f\\d]{2})([a-f\\d]{2})$/i.exec(\r\n    hex.replace(shorthandRegex, (m, r, g, b) => r + r + g + g + b + b)\r\n  );\r\n  return result\r\n    ? [parseInt(result[1], 16), parseInt(result[2], 16), parseInt(result[3], 16)]\r\n    : null;\r\n};\r\n\r\nconst rgbaColor = (color = '#fff', alpha = 0.5) => `rgba(${hexToRgb(color)}, ${alpha})`;\r\n\r\n/* --------------------------------- Colors --------------------------------- */\r\n\r\nconst getColor = (name, dom = document.documentElement) =>\r\n  getComputedStyle(dom).getPropertyValue(`--falcon-${name}`).trim();\r\n\r\nconst getColors = dom => ({\r\n  primary: getColor('primary', dom),\r\n  secondary: getColor('secondary', dom),\r\n  success: getColor('success', dom),\r\n  info: getColor('info', dom),\r\n  warning: getColor('warning', dom),\r\n  danger: getColor('danger', dom),\r\n  light: getColor('light', dom),\r\n  dark: getColor('dark', dom),\r\n  white: getColor('white', dom),\r\n  black: getColor('black', dom),\r\n  emphasis: getColor('emphasis-color', dom)\r\n});\r\n\r\nconst getSubtleColors = dom => ({\r\n  primary: getColor('primary-bg-subtle', dom),\r\n  secondary: getColor('secondary-bg-subtle', dom),\r\n  success: getColor('success-bg-subtle', dom),\r\n  info: getColor('info-bg-subtle', dom),\r\n  warning: getColor('warning-bg-subtle', dom),\r\n  danger: getColor('danger-bg-subtle', dom),\r\n  light: getColor('light-bg-subtle', dom),\r\n  dark: getColor('dark-bg-subtle', dom)\r\n});\r\n\r\nconst getGrays = dom => ({\r\n  100: getColor('gray-100', dom),\r\n  200: getColor('gray-200', dom),\r\n  300: getColor('gray-300', dom),\r\n  400: getColor('gray-400', dom),\r\n  500: getColor('gray-500', dom),\r\n  600: getColor('gray-600', dom),\r\n  700: getColor('gray-700', dom),\r\n  800: getColor('gray-800', dom),\r\n  900: getColor('gray-900', dom),\r\n  1000: getColor('gray-1000', dom),\r\n  1100: getColor('gray-1100', dom)\r\n});\r\n\r\nconst hasClass = (el, className) => {\r\n  !el && false;\r\n  return el.classList.value.includes(className);\r\n};\r\n\r\nconst addClass = (el, className) => {\r\n  el.classList.add(className);\r\n};\r\n\r\nconst removeClass = (el, className) => {\r\n  el.classList.remove(className);\r\n};\r\n\r\nconst getOffset = el => {\r\n  const rect = el.getBoundingClientRect();\r\n  const scrollLeft = window.pageXOffset || document.documentElement.scrollLeft;\r\n  const scrollTop = window.pageYOffset || document.documentElement.scrollTop;\r\n  return { top: rect.top + scrollTop, left: rect.left + scrollLeft };\r\n};\r\n\r\nfunction isScrolledIntoView(el) {\r\n  const rect = el.getBoundingClientRect();\r\n  const windowHeight = window.innerHeight || document.documentElement.clientHeight;\r\n  const windowWidth = window.innerWidth || document.documentElement.clientWidth;\r\n\r\n  const vertInView = rect.top <= windowHeight && rect.top + rect.height >= 0;\r\n  const horInView = rect.left <= windowWidth && rect.left + rect.width >= 0;\r\n\r\n  return vertInView && horInView;\r\n}\r\n\r\nconst breakpoints = {\r\n  xs: 0,\r\n  sm: 576,\r\n  md: 768,\r\n  lg: 992,\r\n  xl: 1200,\r\n  xxl: 1540\r\n};\r\n\r\nconst getBreakpoint = el => {\r\n  const classes = el && el.classList.value;\r\n  let breakpoint;\r\n  if (classes) {\r\n    breakpoint =\r\n      breakpoints[\r\n        classes\r\n          .split(' ')\r\n          .filter(cls => cls.includes('navbar-expand-'))\r\n          .pop()\r\n          .split('-')\r\n          .pop()\r\n      ];\r\n  }\r\n  return breakpoint;\r\n};\r\n\r\nconst getSystemTheme = () => (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light');\r\n\r\nconst isDark = () => (localStorage.getItem('theme') === 'auto' ? getSystemTheme() : localStorage.getItem('theme'));\r\n/* --------------------------------- Cookie --------------------------------- */\r\n\r\nconst setCookie = (name, value, expire) => {\r\n  const expires = new Date();\r\n  expires.setTime(expires.getTime() + expire);\r\n  document.cookie = `${name}=${value};expires=${expires.toUTCString()}`;\r\n};\r\n\r\nconst getCookie = name => {\r\n  const keyValue = document.cookie.match(`(^|;) ?${name}=([^;]*)(;|$)`);\r\n  return keyValue ? keyValue[2] : keyValue;\r\n};\r\n\r\nconst settings = {\r\n  tinymce: {\r\n    theme: 'oxide'\r\n  },\r\n  chart: {\r\n    borderColor: 'rgba(255, 255, 255, 0.8)'\r\n  }\r\n};\r\n\r\n/* -------------------------- Chart Initialization -------------------------- */\r\n\r\nconst newChart = (chart, config) => {\r\n  const ctx = chart.getContext('2d');\r\n  return new window.Chart(ctx, config);\r\n};\r\n\r\n/* ---------------------------------- Store --------------------------------- */\r\n\r\nconst getItemFromStore = (key, defaultValue, store = localStorage) => {\r\n  try {\r\n    return JSON.parse(store.getItem(key)) || defaultValue;\r\n  } catch {\r\n    return store.getItem(key) || defaultValue;\r\n  }\r\n};\r\n\r\nconst setItemToStore = (key, payload, store = localStorage) => store.setItem(key, payload);\r\nconst getStoreSpace = (store = localStorage) =>\r\n  parseFloat((escape(encodeURIComponent(JSON.stringify(store))).length / (1024 * 1024)).toFixed(2));\r\n\r\n/* get Dates between */\r\n\r\nconst getDates = (startDate, endDate, interval = 1000 * 60 * 60 * 24) => {\r\n  const duration = endDate - startDate;\r\n  const steps = duration / interval;\r\n  return Array.from({ length: steps + 1 }, (v, i) => new Date(startDate.valueOf() + interval * i));\r\n};\r\n\r\nconst getPastDates = duration => {\r\n  let days;\r\n\r\n  switch (duration) {\r\n    case 'week':\r\n      days = 7;\r\n      break;\r\n    case 'month':\r\n      days = 30;\r\n      break;\r\n    case 'year':\r\n      days = 365;\r\n      break;\r\n\r\n    default:\r\n      days = duration;\r\n  }\r\n\r\n  const date = new Date();\r\n  const endDate = date;\r\n  const startDate = new Date(new Date().setDate(date.getDate() - (days - 1)));\r\n  return getDates(startDate, endDate);\r\n};\r\n\r\n/* Get Random Number */\r\nconst getRandomNumber = (min, max) => Math.floor(Math.random() * (max - min) + min);\r\n\r\nconst utils = {\r\n  docReady,\r\n  breakpoints,\r\n  resize,\r\n  isIterableArray,\r\n  camelize,\r\n  getData,\r\n  hasClass,\r\n  addClass,\r\n  hexToRgb,\r\n  rgbaColor,\r\n  getColor,\r\n  getColors,\r\n  getSubtleColors,\r\n  getGrays,\r\n  getOffset,\r\n  isScrolledIntoView,\r\n  getBreakpoint,\r\n  setCookie,\r\n  getCookie,\r\n  newChart,\r\n  settings,\r\n  getItemFromStore,\r\n  setItemToStore,\r\n  getStoreSpace,\r\n  getDates,\r\n  getPastDates,\r\n  getRandomNumber,\r\n  removeClass,\r\n  getSystemTheme,\r\n  isDark\r\n};\r\n\r\nexport default utils;\r\n", "const getPosition = (pos, params, dom, rect, size) => ({\r\n  top: pos[1] - size.contentSize[1] - 10,\r\n  left: pos[0] - size.contentSize[0] / 2\r\n});\r\n\r\nconst echartSetOption = (chart, userOptions, getDefaultOptions) => {\r\n  const themeController = document.body;\r\n  // Merge user options with lodash\r\n  chart.setOption(window._.merge(getDefaultOptions(), userOptions));\r\n\r\n  themeController.addEventListener('clickControl', ({ detail: { control } }) => {\r\n    if (control === 'theme') {\r\n      chart.setOption(window._.merge(getDefaultOptions(), userOptions));\r\n    }\r\n  });\r\n};\r\n\r\nconst tooltipFormatter = params => {\r\n  let tooltipItem = '';\r\n  params.forEach(el => {\r\n    tooltipItem += `<div class='ms-1'>\r\n        <h6 class=\"text-700\">\r\n          <span class=\"fas fa-circle me-1 fs-11\" style=\"color:${el.borderColor ? el.borderColor : el.color}\"></span>\r\n          ${el.seriesName} : ${typeof el.value === 'object' ? el.value[1] : el.value}\r\n        </h6>\r\n      </div>`;\r\n  });\r\n  return `\r\n    <div>\r\n      <p class='mb-2 text-600'>\r\n        ${window.dayjs(params[0].axisValue).isValid() ? window.dayjs(params[0].axisValue).format('MMMM DD') : params[0].axisValue}\r\n      </p>\r\n      ${tooltipItem}\r\n    </div>`;\r\n};\r\n\r\nexport default { getPosition, echartSetOption, tooltipFormatter };\r\n", "import utils from '../../../utils';\r\nimport { getPosition, echartSetOption, tooltipFormatter } from '../echarts-utils';\r\n\r\n/* -------------------------------------------------------------------------- */\r\n/*                      Echarts Area Pieces Chart                             */\r\n/* -------------------------------------------------------------------------- */\r\n\r\nconst echartsAreaPiecesChartInit = () => {\r\n  const $areaPiecesChartEl = document.querySelector('.echart-area-pieces-chart-example');\r\n\r\n  if ($areaPiecesChartEl) {\r\n    // Get options from data attribute\r\n    const userOptions = utils.getData($areaPiecesChartEl, 'options');\r\n    const chart = window.echarts.init($areaPiecesChartEl);\r\n\r\n    const getDefaultOptions = () => ({\r\n      tooltip: {\r\n        trigger: 'axis',\r\n        padding: [7, 10],\r\n        backgroundColor: utils.getGrays()['100'],\r\n        borderColor: utils.getGrays()['300'],\r\n        textStyle: { color: utils.getGrays()['1100'] },\r\n        borderWidth: 1,\r\n        transitionDuration: 0,\r\n        position(pos, params, dom, rect, size) {\r\n          return getPosition(pos, params, dom, rect, size);\r\n        },\r\n        axisPointer: {\r\n          type: 'none'\r\n        },\r\n        formatter: tooltipFormatter\r\n      },\r\n      xAxis: {\r\n        type: 'category',\r\n        boundaryGap: false,\r\n        axisLine: {\r\n          lineStyle: {\r\n            color: utils.getGrays()['300'],\r\n            type: 'solid'\r\n          }\r\n        },\r\n        axisTick: { show: false },\r\n        axisLabel: {\r\n          color: utils.getGrays()['400'],\r\n          margin: 15,\r\n          formatter: value => window.dayjs(value).format('MMM DD')\r\n        },\r\n        splitLine: {\r\n          show: false\r\n        }\r\n      },\r\n      yAxis: {\r\n        type: 'value',\r\n        splitLine: {\r\n          lineStyle: {\r\n            color: utils.getGrays()['200']\r\n          }\r\n        },\r\n        boundaryGap: false,\r\n        axisLabel: {\r\n          show: true,\r\n          color: utils.getGrays()['400'],\r\n          margin: 15\r\n        },\r\n        axisTick: { show: false },\r\n        axisLine: { show: false }\r\n      },\r\n      visualMap: {\r\n        type: 'piecewise',\r\n        show: false,\r\n        dimension: 0,\r\n        seriesIndex: 0,\r\n        pieces: [\r\n          {\r\n            gt: 1,\r\n            lt: 3,\r\n            color: utils.rgbaColor(utils.getColor('primary'), 0.4)\r\n          },\r\n          {\r\n            gt: 5,\r\n            lt: 7,\r\n            color: utils.rgbaColor(utils.getColor('primary'), 0.4)\r\n          }\r\n        ]\r\n      },\r\n      series: [\r\n        {\r\n          type: 'line',\r\n          name: 'Total',\r\n          smooth: 0.6,\r\n          symbol: 'none',\r\n          lineStyle: {\r\n            color: utils.getColor('primary'),\r\n            width: 5\r\n          },\r\n          markLine: {\r\n            symbol: ['none', 'none'],\r\n            label: { show: false },\r\n            data: [{ xAxis: 1 }, { xAxis: 3 }, { xAxis: 5 }, { xAxis: 7 }]\r\n          },\r\n          areaStyle: {},\r\n          data: [\r\n            ['2019-10-10', 200],\r\n            ['2019-10-11', 560],\r\n            ['2019-10-12', 750],\r\n            ['2019-10-13', 580],\r\n            ['2019-10-14', 250],\r\n            ['2019-10-15', 300],\r\n            ['2019-10-16', 450],\r\n            ['2019-10-17', 300],\r\n            ['2019-10-18', 100]\r\n          ]\r\n        }\r\n      ],\r\n      grid: { right: 20, left: 5, bottom: 5, top: 8, containLabel: true }\r\n    });\r\n\r\n    echartSetOption(chart, userOptions, getDefaultOptions);\r\n  }\r\n};\r\n\r\nexport default echartsAreaPiecesChartInit;\r\n", "import utils from '../../../utils';\r\nimport { echartSetOption, tooltipFormatter } from '../echarts-utils';\r\n\r\n/* -------------------------------------------------------------------------- */\r\n/*                             Echarts Bar Chart                             */\r\n/* -------------------------------------------------------------------------- */\r\n\r\nconst echartsBarLineChartInit = () => {\r\n  const $barLineChartEl = document.querySelector('.echart-bar-line-chart-example');\r\n\r\n  if ($barLineChartEl) {\r\n    // Get options from data attribute\r\n    const userOptions = utils.getData($barLineChartEl, 'options');\r\n    const chart = window.echarts.init($barLineChartEl);\r\n\r\n    const months = [\r\n      'January',\r\n      'February',\r\n      'March',\r\n      'April',\r\n      'May',\r\n      'June',\r\n      'July',\r\n      'August',\r\n      'September',\r\n      'October',\r\n      'November',\r\n      'December'\r\n    ];\r\n\r\n    const getDefaultOptions = () => ({\r\n      tooltip: {\r\n        trigger: 'axis',\r\n        axisPointer: {\r\n          type: 'cross',\r\n          crossStyle: {\r\n            color: utils.getGrays()['500']\r\n          },\r\n          label: {\r\n            show: true,\r\n            backgroundColor: utils.getGrays()['600'],\r\n            color: utils.getGrays()['100']\r\n          }\r\n        },\r\n        padding: [7, 10],\r\n        backgroundColor: utils.getGrays()['100'],\r\n        borderColor: utils.getGrays()['300'],\r\n        textStyle: { color: utils.getGrays()['1100'] },\r\n        borderWidth: 1,\r\n        transitionDuration: 0,\r\n        formatter: tooltipFormatter\r\n      },\r\n      toolbox: {\r\n        top: 0,\r\n        feature: {\r\n          dataView: { show: false },\r\n          magicType: {\r\n            show: true,\r\n            type: ['line', 'bar']\r\n          },\r\n          restore: { show: true },\r\n          saveAsImage: { show: true }\r\n        },\r\n        iconStyle: {\r\n          borderColor: utils.getGrays()['700'],\r\n          borderWidth: 1\r\n        },\r\n\r\n        emphasis: {\r\n          iconStyle: {\r\n            textFill: utils.getGrays()['600']\r\n          }\r\n        }\r\n      },\r\n      legend: {\r\n        top: 40,\r\n        data: ['Evaporation', 'Precipitation', 'Average temperature'],\r\n        textStyle: {\r\n          color: utils.getGrays()['600']\r\n        }\r\n      },\r\n      xAxis: [\r\n        {\r\n          type: 'category',\r\n          data: months,\r\n          axisLabel: {\r\n            color: utils.getGrays()['600'],\r\n            formatter: value => value.slice(0, 3)\r\n          },\r\n          axisPointer: {\r\n            type: 'shadow'\r\n          },\r\n          axisLine: {\r\n            show: true,\r\n            lineStyle: {\r\n              color: utils.getGrays()['300']\r\n            }\r\n          }\r\n        }\r\n      ],\r\n      yAxis: [\r\n        {\r\n          type: 'value',\r\n          min: 0,\r\n          max: 250,\r\n          interval: 50,\r\n          axisLabel: {\r\n            color: utils.getGrays()['600'],\r\n            formatter: '{value} ml'\r\n          },\r\n          splitLine: {\r\n            show: true,\r\n            lineStyle: {\r\n              color: utils.getGrays()['200']\r\n            }\r\n          }\r\n        },\r\n        {\r\n          type: 'value',\r\n          min: 0,\r\n          max: 25,\r\n          interval: 5,\r\n          axisLabel: {\r\n            color: utils.getGrays()['600'],\r\n            formatter: '{value} °C'\r\n          },\r\n\r\n          splitLine: {\r\n            show: true,\r\n            lineStyle: {\r\n              color: utils.getGrays()['200']\r\n            }\r\n          }\r\n        }\r\n      ],\r\n      series: [\r\n        {\r\n          name: 'Evaporation',\r\n          type: 'bar',\r\n          data: [2.0, 4.9, 7.0, 23.2, 25.6, 76.7, 135.6, 162.2, 32.6, 20.0, 6.4, 3.3],\r\n          itemStyle: {\r\n            color: utils.getColor('primary'),\r\n            barBorderRadius: [3, 3, 0, 0]\r\n          }\r\n        },\r\n        {\r\n          name: 'Precipitation',\r\n          type: 'bar',\r\n          data: [2.6, 5.9, 9.0, 26.4, 28.7, 70.7, 175.6, 182.2, 48.7, 18.8, 6.0, 2.3],\r\n          itemStyle: {\r\n            color: utils.getColor('info'),\r\n            barBorderRadius: [3, 3, 0, 0]\r\n          }\r\n        },\r\n        {\r\n          name: 'Average temperature',\r\n          type: 'line',\r\n          yAxisIndex: 1,\r\n          data: [2.0, 2.2, 3.3, 4.5, 6.3, 10.2, 20.3, 23.4, 23.0, 16.5, 12.0, 6.2],\r\n          lineStyle: {\r\n            color: utils.getColor('warning')\r\n          },\r\n          itemStyle: {\r\n            color: utils.getGrays()['100'],\r\n            borderColor: utils.getColor('warning'),\r\n            borderWidth: 2\r\n          },\r\n          symbol: 'circle',\r\n          symbolSize: 10\r\n        }\r\n      ],\r\n      grid: {\r\n        right: 5,\r\n        left: 5,\r\n        bottom: 5,\r\n        top: '23%',\r\n        containLabel: true\r\n      }\r\n    });\r\n\r\n    echartSetOption(chart, userOptions, getDefaultOptions);\r\n  }\r\n};\r\n\r\nexport default echartsBarLineChartInit;\r\n", "import utils from '../../../utils';\r\nimport { echartSetOption, tooltipFormatter } from '../echarts-utils';\r\n\r\n/* -------------------------------------------------------------------------- */\r\n/*                             Echarts Bar Chart                             */\r\n/* -------------------------------------------------------------------------- */\r\n\r\nconst echartsBarNegativeChartInit = () => {\r\n  const $barNegativeChartEl = document.querySelector('.echart-bar-chart-negative-example');\r\n\r\n  if ($barNegativeChartEl) {\r\n    // Get options from data attribute\r\n    const userOptions = utils.getData($barNegativeChartEl, 'options');\r\n    const chart = window.echarts.init($barNegativeChartEl);\r\n\r\n    const getDefaultOptions = () => ({\r\n      tooltip: {\r\n        trigger: 'axis',\r\n        axisPointer: {\r\n          type: 'shadow'\r\n        },\r\n        padding: [7, 10],\r\n        backgroundColor: utils.getGrays()['100'],\r\n        borderColor: utils.getGrays()['300'],\r\n        textStyle: { color: utils.getGrays()['1100'] },\r\n        borderWidth: 1,\r\n        transitionDuration: 0,\r\n        formatter: tooltipFormatter\r\n      },\r\n      grid: {\r\n        top: 5,\r\n        bottom: 5,\r\n        left: 5,\r\n        right: 5\r\n      },\r\n      xAxis: {\r\n        type: 'value',\r\n        position: 'top',\r\n        splitLine: {\r\n          lineStyle: {\r\n            type: 'dashed',\r\n            color: utils.getGrays()['200']\r\n          }\r\n        }\r\n      },\r\n      yAxis: {\r\n        type: 'category',\r\n        axisLine: { show: false },\r\n        axisLabel: { show: false },\r\n        axisTick: { show: false },\r\n        splitLine: { show: false },\r\n        data: ['Ten', 'Nine', 'Eight', 'Seven', 'Six', 'Five', 'Four', 'Three', 'Two', 'One']\r\n      },\r\n      series: [\r\n        {\r\n          name: 'Cost',\r\n          type: 'bar',\r\n          stack: 'total',\r\n          label: {\r\n            show: true,\r\n            formatter: '{b}',\r\n            color: '#fff'\r\n          },\r\n          itemStyle: {\r\n            color: utils.getColor('primary')\r\n          },\r\n          data: [-0.12, -0.19, 0.2, 0.44, -0.23, 0.08, -0.17, 0.47, -0.36, 0.18]\r\n        }\r\n      ]\r\n    });\r\n\r\n    echartSetOption(chart, userOptions, getDefaultOptions);\r\n  }\r\n};\r\n\r\nexport default echartsBarNegativeChartInit;\r\n", "import utils from '../../../utils';\r\nimport { echartSetOption } from '../echarts-utils';\r\n\r\n/* -------------------------------------------------------------------------- */\r\n/*                         Echarts Bar Race Chart                             */\r\n/* -------------------------------------------------------------------------- */\r\n\r\nconst echartsBarRaceChartInit = () => {\r\n  const $barRaceChartEl = document.querySelector('.echart-bar-race-chart-example');\r\n\r\n  if ($barRaceChartEl) {\r\n    // Get options from data attribute\r\n    const userOptions = utils.getData($barRaceChartEl, 'options');\r\n    const chart = window.echarts.init($barRaceChartEl);\r\n\r\n    let data = Array.from(Array(7).keys()).map(() => Math.round(Math.random() * 200));\r\n\r\n    const getDefaultOptions = () => ({\r\n      xAxis: {\r\n        max: 'dataMax',\r\n        splitLine: {\r\n          lineStyle: {\r\n            color: utils.getGrays()['200']\r\n          }\r\n        },\r\n        axisLabel: {\r\n          color: utils.getGrays()['500']\r\n        }\r\n      },\r\n      yAxis: {\r\n        type: 'category',\r\n        data: ['A', 'B', 'C', 'D', 'E', 'F', 'G'],\r\n        inverse: true,\r\n        axisLabel: {\r\n          color: utils.getGrays()['500']\r\n        },\r\n        axisLine: {\r\n          show: true,\r\n          lineStyle: {\r\n            color: utils.getGrays()['300']\r\n          }\r\n        },\r\n        axisTick: {\r\n          show: false\r\n        },\r\n        animationDuration: 300,\r\n        animationDurationUpdate: 300,\r\n        max: 4 // only the largest 5 bars will be displayed\r\n      },\r\n      series: [\r\n        {\r\n          realtimeSort: true,\r\n          name: 'X',\r\n          type: 'bar',\r\n          data,\r\n          label: {\r\n            show: true,\r\n            position: 'right',\r\n            color: utils.getGrays()['700'],\r\n            fontWeight: 500,\r\n            valueAnimation: true\r\n          },\r\n          itemStyle: {\r\n            color: utils.getColor('primary'),\r\n            barBorderRadius: [0, 3, 3, 0]\r\n          }\r\n        }\r\n      ],\r\n      animationDuration: 0,\r\n      animationDurationUpdate: 3000,\r\n      animationEasing: 'linear',\r\n      animationEasingUpdate: 'linear',\r\n      grid: {\r\n        right: '10%',\r\n        left: 5,\r\n        bottom: 5,\r\n        top: 5,\r\n        containLabel: true\r\n      }\r\n    });\r\n\r\n    echartSetOption(chart, userOptions, getDefaultOptions);\r\n\r\n    const run = () => {\r\n      data = data.map(item =>\r\n        (Math.random() > 0.9\r\n          ? item + Math.round(Math.random() * 2000)\r\n          : item + Math.round(Math.random() * 200)\r\n        ));\r\n\r\n      chart.setOption({\r\n        series: [\r\n          {\r\n            data\r\n          }\r\n        ]\r\n      });\r\n    };\r\n\r\n    setTimeout(() => {\r\n      run();\r\n    }, 0);\r\n    setInterval(() => {\r\n      run();\r\n    }, 3000);\r\n  }\r\n};\r\n\r\nexport default echartsBarRaceChartInit;\r\n", "import utils from '../../../utils';\r\nimport { echartSetOption, tooltipFormatter } from '../echarts-utils';\r\n\r\n/* -------------------------------------------------------------------------- */\r\n/*                             Echarts Bar Chart                             */\r\n/* -------------------------------------------------------------------------- */\r\n\r\nconst echartsBarSeriesChartInit = () => {\r\n  const $barSeriesChartEl = document.querySelector('.echart-bar-chart-series-example');\r\n\r\n  if ($barSeriesChartEl) {\r\n    // Get options from data attribute\r\n    const userOptions = utils.getData($barSeriesChartEl, 'options');\r\n    const chart = window.echarts.init($barSeriesChartEl);\r\n\r\n    const getDefaultOptions = () => ({\r\n      color: [utils.getColor('primary'), utils.getColor('info')],\r\n      tooltip: {\r\n        trigger: 'axis',\r\n        axisPointer: {\r\n          type: 'shadow'\r\n        },\r\n        padding: [7, 10],\r\n        backgroundColor: utils.getGrays()['100'],\r\n        borderColor: utils.getGrays()['300'],\r\n        textStyle: { color: utils.getGrays()['1100'] },\r\n        borderWidth: 1,\r\n        transitionDuration: 0,\r\n        formatter: tooltipFormatter\r\n      },\r\n      xAxis: {\r\n        type: 'value',\r\n        axisLabel: {\r\n          formatter: value => `${value / 1000}k`,\r\n          color: utils.getGrays()['500']\r\n        },\r\n        axisLine: {\r\n          show: true,\r\n          lineStyle: {\r\n            color: utils.getGrays()['300'],\r\n            type: 'solid'\r\n          }\r\n        },\r\n        splitLine: {\r\n          lineStyle: {\r\n            type: 'dashed',\r\n            color: utils.getGrays()['200']\r\n          }\r\n        }\r\n      },\r\n      yAxis: {\r\n        type: 'category',\r\n        axisLine: {\r\n          show: true,\r\n          lineStyle: {\r\n            color: utils.getGrays()['300'],\r\n            type: 'solid'\r\n          }\r\n        },\r\n        axisLabel: {\r\n          color: utils.getGrays()['500']\r\n        },\r\n        axisTick: { show: false },\r\n        splitLine: { show: false },\r\n        data: ['Brazil', 'Indonesia', 'USA', 'India', 'China']\r\n      },\r\n      series: [\r\n        {\r\n          name: '2011',\r\n          type: 'bar',\r\n          data: [18203, 23489, 29034, 104970, 131744],\r\n          itemStyle: {\r\n            barBorderRadius: [0, 3, 3, 0]\r\n          }\r\n        },\r\n        {\r\n          name: '2012',\r\n          type: 'bar',\r\n          data: [19325, 23438, 31000, 121594, 134141],\r\n          itemStyle: {\r\n            barBorderRadius: [0, 3, 3, 0]\r\n          }\r\n        }\r\n      ],\r\n      grid: { right: 15, left: '12%', bottom: '10%', top: 5 }\r\n    });\r\n\r\n    echartSetOption(chart, userOptions, getDefaultOptions);\r\n  }\r\n};\r\n\r\nexport default echartsBarSeriesChartInit;\r\n", "import utils from '../../../utils';\r\nimport { echartSetOption } from '../echarts-utils';\r\n\r\n/* -------------------------------------------------------------------------- */\r\n/*                             Echarts Bar Chart                             */\r\n/* -------------------------------------------------------------------------- */\r\n\r\nconst echartsBarStackedChartInit = () => {\r\n  const $barStackedChartEl = document.querySelector('.echart-bar-stacked-chart-example');\r\n\r\n  if ($barStackedChartEl) {\r\n    // Get options from data attribute\r\n    const userOptions = utils.getData($barStackedChartEl, 'options');\r\n    const chart = window.echarts.init($barStackedChartEl);\r\n\r\n    const xAxisData = [];\r\n    const data1 = [];\r\n    const data2 = [];\r\n    const data3 = [];\r\n    const data4 = [];\r\n\r\n    for (let i = 0; i < 10; i += 1) {\r\n      xAxisData.push(`Class${i + 1}`);\r\n      data1.push((Math.random() * 2).toFixed(2));\r\n      data2.push((Math.random() * 5).toFixed(2));\r\n      data3.push((Math.random() + 0.3).toFixed(2));\r\n      data4.push(-Math.random().toFixed(2));\r\n    }\r\n\r\n    const emphasisStyle = {\r\n      itemStyle: {\r\n        shadowBlur: 10,\r\n        shadowColor: utils.rgbaColor(utils.getColor('dark'), 0.3)\r\n      }\r\n    };\r\n\r\n    const getDefaultOptions = () => ({\r\n      color: [\r\n        utils.getColor('primary'),\r\n        utils.getColor('info'),\r\n        utils.getColor('warning'),\r\n        utils.getColor('danger')\r\n      ],\r\n      legend: {\r\n        data: ['Bar1', 'Bar2', 'Bar3', 'Bar4'],\r\n        textStyle: {\r\n          color: utils.getGrays()['700']\r\n        },\r\n        left: 0\r\n      },\r\n      toolbox: {\r\n        feature: {\r\n          magicType: {\r\n            type: ['stack', 'tiled']\r\n          }\r\n        },\r\n        iconStyle: {\r\n          borderColor: utils.getGrays()['700'],\r\n          borderWidth: 1\r\n        }\r\n      },\r\n      tooltip: {\r\n        trigger: 'item',\r\n        padding: [7, 10],\r\n        backgroundColor: utils.getGrays()['100'],\r\n        borderColor: utils.getGrays()['300'],\r\n        borderWidth: 1,\r\n        transitionDuration: 0,\r\n        axisPointer: {\r\n          type: 'none'\r\n        }\r\n      },\r\n      xAxis: {\r\n        data: xAxisData,\r\n        splitLine: { show: false },\r\n        splitArea: { show: false },\r\n\r\n        axisLabel: {\r\n          color: utils.getGrays()['600']\r\n        },\r\n\r\n        axisLine: {\r\n          lineStyle: {\r\n            color: utils.getGrays()['400']\r\n          }\r\n        }\r\n      },\r\n      yAxis: {\r\n        splitLine: {\r\n          lineStyle: {\r\n            color: utils.getGrays()['200']\r\n          }\r\n        },\r\n        axisLabel: {\r\n          color: utils.getGrays()['600']\r\n        }\r\n      },\r\n      series: [\r\n        {\r\n          name: 'Bar1',\r\n          type: 'bar',\r\n          stack: 'one',\r\n          emphasis: emphasisStyle,\r\n          data: data1\r\n        },\r\n        {\r\n          name: 'Bar2',\r\n          type: 'bar',\r\n          stack: 'one',\r\n          emphasis: emphasisStyle,\r\n          data: data2\r\n        },\r\n        {\r\n          name: 'Bar3',\r\n          type: 'bar',\r\n          stack: 'two',\r\n          emphasis: emphasisStyle,\r\n          data: data3\r\n        },\r\n        {\r\n          name: 'Bar4',\r\n          type: 'bar',\r\n          stack: 'two',\r\n          emphasis: emphasisStyle,\r\n          data: data4\r\n        }\r\n      ],\r\n      grid: {\r\n        top: '10%',\r\n        bottom: 10,\r\n        left: 5,\r\n        right: 7,\r\n        containLabel: true\r\n      }\r\n    });\r\n\r\n    echartSetOption(chart, userOptions, getDefaultOptions);\r\n  }\r\n};\r\n\r\nexport default echartsBarStackedChartInit;\r\n", "import utils from '../../../utils';\r\nimport { echartSetOption, tooltipFormatter } from '../echarts-utils';\r\n\r\n/* -------------------------------------------------------------------------- */\r\n/*                         Echarts Bar Timeline Chart                         */\r\n/* -------------------------------------------------------------------------- */\r\n\r\nconst echartsBarTimelineChartInit = () => {\r\n  const $barTimelineChartEl = document.querySelector('.echart-bar-timeline-chart-example');\r\n\r\n  if ($barTimelineChartEl) {\r\n    // Get options from data attribute\r\n    const userOptions = utils.getData($barTimelineChartEl, 'options');\r\n    const chart = window.echarts.init($barTimelineChartEl);\r\n\r\n    const months = [\r\n      'January',\r\n      'February',\r\n      'March',\r\n      'April',\r\n      'May',\r\n      'June',\r\n      'July',\r\n      'August',\r\n      'September',\r\n      'October',\r\n      'November',\r\n      'December'\r\n    ];\r\n\r\n    const dataMap = {};\r\n\r\n    const dataFormatter = obj =>\r\n      Object.keys(obj).reduce(\r\n        (acc, val) => ({\r\n          ...acc,\r\n          [val]: obj[val].map((value, index) => ({\r\n            name: months[index],\r\n            value\r\n          }))\r\n        }),\r\n        {}\r\n      );\r\n\r\n    dataMap.dataTI = dataFormatter({\r\n      2005: [\r\n        88.68, 112.38, 1400, 262.42, 589.56, 882.41, 625.61, 684.6, 90.26, 1461.51, 892.83, 966.5\r\n      ],\r\n      2006: [\r\n        88.8, 103.35, 1461.81, 276.77, 634.94, 939.43, 672.76, 750.14, 93.81, 1545.05, 925.1,\r\n        1011.03\r\n      ],\r\n      2007: [\r\n        101.26, 110.19, 1804.72, 311.97, 762.1, 1133.42, 783.8, 915.38, 101.84, 1816.31, 986.02,\r\n        1200.18\r\n      ],\r\n      2008: [\r\n        112.83, 122.58, 2034.59, 313.58, 907.95, 1302.02, 916.72, 1088.94, 111.8, 2100.11, 1095.96,\r\n        1418.09\r\n      ],\r\n      2009: [\r\n        118.29, 128.85, 2207.34, 477.59, 929.6, 1414.9, 980.57, 1154.33, 113.82, 2261.86, 1163.08,\r\n        1495.45\r\n      ],\r\n      2010: [\r\n        124.36, 145.58, 2562.81, 554.48, 1095.28, 1631.08, 1050.15, 1302.9, 114.15, 2540.1, 1360.56,\r\n        1729.02\r\n      ],\r\n      2011: [\r\n        136.27, 159.72, 2905.73, 641.42, 1306.3, 1915.57, 1277.44, 1701.5, 124.94, 3064.78, 1583.04,\r\n        2015.31\r\n      ]\r\n    });\r\n\r\n    dataMap.dataSI = dataFormatter({\r\n      2005: [\r\n        2026.51, 2135.07, 5271.57, 2357.04, 1773.21, 3869.4, 1580.83, 2971.68, 4381.2, 10524.96,\r\n        7164.75, 2245.9\r\n      ],\r\n      2006: [\r\n        2191.43, 2457.08, 6110.43, 2755.66, 2374.96, 4566.83, 1915.29, 3365.31, 4969.95, 12282.89,\r\n        8511.51, 2711.18\r\n      ],\r\n      2007: [\r\n        2509.4, 2892.53, 7201.88, 3454.49, 3193.67, 5544.14, 2475.45, 3695.58, 5571.06, 14471.26,\r\n        10154.25, 3370.96\r\n      ],\r\n      2008: [\r\n        2626.41, 3709.78, 8701.34, 4242.36, 4376.19, 7158.84, 3097.12, 4319.75, 6085.84, 16993.34,\r\n        11567.42, 4198.93\r\n      ],\r\n      2009: [\r\n        2855.55, 3987.84, 8959.83, 3993.8, 5114, 7906.34, 3541.92, 4060.72, 6001.78, 18566.37,\r\n        11908.49, 4905.22\r\n      ],\r\n      2010: [\r\n        3388.38, 4840.23, 10707.68, 5234, 6367.69, 9976.82, 4506.31, 5025.15, 7218.32, 21753.93,\r\n        14297.93, 6436.62\r\n      ],\r\n      2011: [\r\n        3752.48, 5928.32, 13126.86, 6635.26, 8037.69, 12152.15, 5611.48, 5962.41, 7927.89, 25203.28,\r\n        16555.58, 8309.38\r\n      ]\r\n    });\r\n\r\n    dataMap.dataPI = dataFormatter({\r\n      2005: [\r\n        4854.33, 1658.19, 3340.54, 1611.07, 1542.26, 3295.45, 1413.83, 1857.42, 4776.2, 6612.22,\r\n        5360.1, 2137.77\r\n      ],\r\n      2006: [\r\n        5837.55, 1902.31, 3895.36, 1846.18, 1934.35, 3798.26, 1687.07, 2096.35, 5508.48, 7914.11,\r\n        6281.86, 2390.29\r\n      ],\r\n      2007: [\r\n        7236.15, 2250.04, 4600.72, 2257.99, 2467.41, 4486.74, 2025.44, 2493.04, 6821.11, 9730.91,\r\n        7613.46, 2789.78\r\n      ],\r\n      2008: [\r\n        8375.76, 2886.65, 5276.04, 2759.46, 3212.06, 5207.72, 2412.26, 2905.68, 7872.23, 11888.53,\r\n        8799.31, 3234.64\r\n      ],\r\n      2009: [\r\n        9179.19, 3405.16, 6068.31, 2886.92, 3696.65, 5891.25, 2756.26, 3371.95, 8930.85, 13629.07,\r\n        9918.78, 3662.15\r\n      ],\r\n      2010: [\r\n        10600.84, 4238.65, 7123.77, 3412.38, 4209.03, 6849.37, 3111.12, 4040.55, 9833.51, 17131.45,\r\n        12063.82, 4193.69\r\n      ],\r\n      2011: [\r\n        12363.18, 5219.24, 8483.17, 3960.87, 5015.89, 8158.98, 3679.91, 4918.09, 11142.86, 20842.21,\r\n        14180.23, 4975.96\r\n      ]\r\n    });\r\n\r\n    const getDefaultOptions = () => ({\r\n      baseOption: {\r\n        timeline: {\r\n          axisType: 'category',\r\n          autoPlay: false,\r\n          playInterval: 1000,\r\n          data: [\r\n            '2005-01-01',\r\n            '2006-01-01',\r\n            '2007-01-01',\r\n            '2008-01-01',\r\n            '2009-01-01',\r\n            '2010-01-01',\r\n            '2011-01-01'\r\n          ],\r\n          label: {\r\n            formatter: s => new Date(s).getFullYear()\r\n          },\r\n          lineStyle: {\r\n            color: utils.getColor('info')\r\n          },\r\n          itemStyle: {\r\n            color: utils.getColor('secondary')\r\n          },\r\n          checkpointStyle: {\r\n            color: utils.getColor('primary'),\r\n            shadowBlur: 0,\r\n            shadowOffsetX: 0,\r\n            shadowOffsetY: 0\r\n          },\r\n          controlStyle: {\r\n            color: utils.getColor('info')\r\n          }\r\n        },\r\n        title: {\r\n          textStyle: {\r\n            color: utils.getGrays()['700']\r\n          }\r\n        },\r\n        tooltip: {\r\n          trigger: 'axis',\r\n          axisPointer: {\r\n            type: 'shadow'\r\n          },\r\n          padding: [7, 10],\r\n          backgroundColor: utils.getGrays()['100'],\r\n          borderColor: utils.getGrays()['300'],\r\n          textStyle: { color: utils.getGrays()['1100'] },\r\n          borderWidth: 1,\r\n          transitionDuration: 0,\r\n          formatter: tooltipFormatter\r\n        },\r\n        legend: {\r\n          left: 'right',\r\n          data: ['Primary industry', 'Secondary industry', 'Tertiary Industry'],\r\n          textStyle: {\r\n            color: utils.getGrays()['700']\r\n          }\r\n        },\r\n        calculable: true,\r\n        xAxis: [\r\n          {\r\n            type: 'category',\r\n            data: months,\r\n            splitLine: { show: false },\r\n            axisLabel: {\r\n              color: utils.getGrays()['600']\r\n            },\r\n            axisLine: {\r\n              lineStyle: {\r\n                color: utils.getGrays()['400']\r\n              }\r\n            }\r\n          }\r\n        ],\r\n        yAxis: [\r\n          {\r\n            type: 'value',\r\n            axisLabel: {\r\n              formatter: value => `${value / 1000}k`,\r\n              color: utils.getGrays()['600']\r\n            },\r\n            splitLine: {\r\n              lineStyle: {\r\n                color: utils.getGrays()['200']\r\n              }\r\n            }\r\n          }\r\n        ],\r\n        series: [\r\n          {\r\n            name: 'Primary industry',\r\n            type: 'bar',\r\n            itemStyle: {\r\n              color: utils.getColor('primary'),\r\n              barBorderRadius: [3, 3, 0, 0]\r\n            }\r\n          },\r\n          {\r\n            name: 'Secondary industry',\r\n            type: 'bar',\r\n            itemStyle: {\r\n              color: utils.getColor('info'),\r\n              barBorderRadius: [3, 3, 0, 0]\r\n            }\r\n          },\r\n          {\r\n            name: 'Tertiary Industry',\r\n            type: 'bar',\r\n            itemStyle: {\r\n              color: utils.getColor('warning'),\r\n              barBorderRadius: [3, 3, 0, 0]\r\n            }\r\n          }\r\n        ],\r\n        grid: {\r\n          top: '10%',\r\n          bottom: '15%',\r\n          left: 5,\r\n          right: 10,\r\n          containLabel: true\r\n        }\r\n      },\r\n      options: [\r\n        {\r\n          title: { text: '2005' },\r\n          series: [\r\n            { data: dataMap.dataPI['2005'] },\r\n            { data: dataMap.dataSI['2005'] },\r\n            { data: dataMap.dataTI['2005'] }\r\n          ]\r\n        },\r\n        {\r\n          title: { text: '2006' },\r\n          series: [\r\n            { data: dataMap.dataPI['2006'] },\r\n            { data: dataMap.dataSI['2006'] },\r\n            { data: dataMap.dataTI['2006'] }\r\n          ]\r\n        },\r\n        {\r\n          title: { text: '2007' },\r\n          series: [\r\n            { data: dataMap.dataPI['2007'] },\r\n            { data: dataMap.dataSI['2007'] },\r\n            { data: dataMap.dataTI['2007'] }\r\n          ]\r\n        },\r\n        {\r\n          title: { text: '2008' },\r\n          series: [\r\n            { data: dataMap.dataPI['2008'] },\r\n            { data: dataMap.dataSI['2008'] },\r\n            { data: dataMap.dataTI['2008'] }\r\n          ]\r\n        },\r\n        {\r\n          title: { text: '2009' },\r\n          series: [\r\n            { data: dataMap.dataPI['2009'] },\r\n            { data: dataMap.dataSI['2009'] },\r\n            { data: dataMap.dataTI['2009'] }\r\n          ]\r\n        },\r\n        {\r\n          title: { text: '2010' },\r\n          series: [\r\n            { data: dataMap.dataPI['2010'] },\r\n            { data: dataMap.dataSI['2010'] },\r\n            { data: dataMap.dataTI['2010'] }\r\n          ]\r\n        },\r\n        {\r\n          title: { text: '2011' },\r\n          series: [\r\n            { data: dataMap.dataPI['2011'] },\r\n            { data: dataMap.dataSI['2011'] },\r\n            { data: dataMap.dataTI['2011'] }\r\n          ]\r\n        }\r\n      ]\r\n    });\r\n\r\n    echartSetOption(chart, userOptions, getDefaultOptions);\r\n  }\r\n};\r\n\r\nexport default echartsBarTimelineChartInit;\r\n", "import utils from '../../../utils';\r\nimport { echartSetOption } from '../echarts-utils';\r\n\r\n/* -------------------------------------------------------------------------- */\r\n/*                             Echarts Bar Chart                             */\r\n/* -------------------------------------------------------------------------- */\r\n\r\nconst echartsWaterFallChartInit = () => {\r\n  const $waterfallChartEl = document.querySelector('.echart-nightfall-chart-example');\r\n\r\n  if ($waterfallChartEl) {\r\n    // Get options from data attribute\r\n    const userOptions = utils.getData($waterfallChartEl, 'options');\r\n    const chart = window.echarts.init($waterfallChartEl);\r\n\r\n    const days = [\r\n      '2021-06-05',\r\n      '2021-06-06',\r\n      '2021-06-07',\r\n      '2021-06-08',\r\n      '2021-06-09',\r\n      '2021-06-10',\r\n      '2021-06-11',\r\n      '2021-06-12',\r\n      '2021-06-13',\r\n      '2021-06-14',\r\n      '2021-06-15'\r\n    ];\r\n\r\n    const getDefaultOptions = () => ({\r\n      legend: {\r\n        data: ['Expenditure', 'Income'],\r\n        textStyle: {\r\n          color: utils.getGrays()['600']\r\n        }\r\n      },\r\n      tooltip: {\r\n        trigger: 'axis',\r\n        padding: [7, 10],\r\n        backgroundColor: utils.getGrays()['100'],\r\n        borderColor: utils.getGrays()['300'],\r\n        textStyle: { color: utils.getGrays()['1100'] },\r\n        borderWidth: 1,\r\n        formatter: params => {\r\n          const tar = params[1].value !== '-' ? params[1] : params[2];\r\n          return `${window.dayjs(tar.name).format('MMM DD')}<br/>${tar.seriesName}: ${tar.value}`;\r\n        },\r\n        transitionDuration: 0,\r\n        axisPointer: {\r\n          type: 'shadow'\r\n        }\r\n      },\r\n      xAxis: {\r\n        type: 'category',\r\n        data: days,\r\n        axisLine: {\r\n          lineStyle: {\r\n            color: utils.getGrays()['300'],\r\n            type: 'solid'\r\n          }\r\n        },\r\n        axisTick: { show: false },\r\n        axisLabel: {\r\n          color: utils.getGrays()['400'],\r\n          formatter: value => window.dayjs(value).format('MMM DD'),\r\n          margin: 15\r\n        },\r\n        splitLine: {\r\n          show: false\r\n        }\r\n      },\r\n      yAxis: {\r\n        type: 'value',\r\n        boundaryGap: true,\r\n        axisLabel: {\r\n          show: true,\r\n          color: utils.getGrays()['400'],\r\n          margin: 15\r\n        },\r\n        splitLine: {\r\n          show: true,\r\n          lineStyle: {\r\n            color: utils.getGrays()['200']\r\n          }\r\n        },\r\n        axisTick: { show: false },\r\n        axisLine: { show: false },\r\n        min: 600\r\n      },\r\n      series: [\r\n        {\r\n          name: 'Assist',\r\n          type: 'bar',\r\n          stack: 'Total',\r\n          itemStyle: {\r\n            barBorderColor: 'transparent',\r\n            color: 'transparent'\r\n          },\r\n          emphasis: {\r\n            itemStyle: {\r\n              barBorderColor: 'transparent',\r\n              color: 'transparent'\r\n            }\r\n          },\r\n          data: [0, 900, 1245, 1530, 1376, 1376, 1511, 1689, 1856, 1495, 1292]\r\n        },\r\n        {\r\n          name: 'Income',\r\n          type: 'bar',\r\n          stack: 'Total',\r\n          label: {\r\n            show: true,\r\n            position: 'top',\r\n            color: utils.getGrays()['600']\r\n          },\r\n          data: [900, 345, 393, '-', '-', 135, 178, 286, '-', '-', '-'],\r\n          itemStyle: {\r\n            color: utils.getColor('primary'),\r\n            barBorderRadius: [3, 3, 0, 0]\r\n          }\r\n        },\r\n        {\r\n          name: 'Expenditure',\r\n          type: 'bar',\r\n          stack: 'Total',\r\n          label: {\r\n            show: true,\r\n            position: 'bottom',\r\n            color: utils.getGrays()['600']\r\n          },\r\n          data: ['-', '-', '-', 108, 154, '-', '-', '-', 119, 361, 203],\r\n          itemStyle: {\r\n            color: utils.getColor('success'),\r\n            barBorderRadius: [3, 3, 0, 0]\r\n          }\r\n        }\r\n      ],\r\n      grid: {\r\n        right: '3%',\r\n        left: '10%',\r\n        bottom: '10%',\r\n        top: '10%'\r\n      }\r\n    });\r\n\r\n    echartSetOption(chart, userOptions, getDefaultOptions);\r\n  }\r\n};\r\n\r\nexport default echartsWaterFallChartInit;\r\n", "import utils from '../../../utils';\r\nimport { echartSetOption, tooltipFormatter } from '../echarts-utils';\r\n\r\n/* -------------------------------------------------------------------------- */\r\n/*                             Echarts Bar Chart                             */\r\n/* -------------------------------------------------------------------------- */\r\n\r\nconst echartsBasicBarChartInit = () => {\r\n  const $barChartEl = document.querySelector('.echart-basic-bar-chart-example');\r\n\r\n  if ($barChartEl) {\r\n    // Get options from data attribute\r\n    const userOptions = utils.getData($barChartEl, 'options');\r\n    const chart = window.echarts.init($barChartEl);\r\n\r\n    const months = [\r\n      'January',\r\n      'February',\r\n      'March',\r\n      'April',\r\n      'May',\r\n      'June',\r\n      'July',\r\n      'August',\r\n      'September',\r\n      'October',\r\n      'November',\r\n      'December'\r\n    ];\r\n\r\n    const data = [1272, 1301, 1402, 1216, 1086, 1236, 1219, 1330, 1367, 1416, 1297, 1204];\r\n\r\n    const getDefaultOptions = () => ({\r\n      tooltip: {\r\n        trigger: 'axis',\r\n        padding: [7, 10],\r\n        backgroundColor: utils.getGrays()['100'],\r\n        borderColor: utils.getGrays()['300'],\r\n        textStyle: { color: utils.getGrays()['1100'] },\r\n        borderWidth: 1,\r\n        formatter: tooltipFormatter,\r\n        transitionDuration: 0,\r\n        axisPointer: {\r\n          type: 'none'\r\n        }\r\n      },\r\n      xAxis: {\r\n        type: 'category',\r\n        data: months,\r\n        axisLine: {\r\n          lineStyle: {\r\n            color: utils.getGrays()['300'],\r\n            type: 'solid'\r\n          }\r\n        },\r\n        axisTick: { show: false },\r\n        axisLabel: {\r\n          color: utils.getGrays()['400'],\r\n          formatter: value => value.substring(0, 3),\r\n          margin: 15\r\n        },\r\n        splitLine: {\r\n          show: false\r\n        }\r\n      },\r\n      yAxis: {\r\n        type: 'value',\r\n        boundaryGap: true,\r\n        axisLabel: {\r\n          show: true,\r\n          color: utils.getGrays()['400'],\r\n          margin: 15\r\n        },\r\n        splitLine: {\r\n          show: true,\r\n          lineStyle: {\r\n            color: utils.getGrays()['200']\r\n          }\r\n        },\r\n        axisTick: { show: false },\r\n        axisLine: { show: false },\r\n        min: 600\r\n      },\r\n      series: [\r\n        {\r\n          type: 'bar',\r\n          name: 'Total',\r\n          data,\r\n          lineStyle: { color: utils.getColor('primary') },\r\n          itemStyle: {\r\n            color: utils.getColor('primary'),\r\n            barBorderRadius: [3, 3, 0, 0]\r\n          },\r\n          showSymbol: false,\r\n          symbol: 'circle',\r\n          smooth: false,\r\n          hoverAnimation: true\r\n        }\r\n      ],\r\n      grid: { right: '3%', left: '10%', bottom: '10%', top: '5%' }\r\n    });\r\n\r\n    echartSetOption(chart, userOptions, getDefaultOptions);\r\n  }\r\n};\r\n\r\nexport default echartsBasicBarChartInit;\r\n", "import utils from '../../../utils';\r\nimport { echartSetOption } from '../echarts-utils';\r\n\r\n/* -------------------------------------------------------------------------- */\r\n/*                             Echarts Bar Chart                             */\r\n/* -------------------------------------------------------------------------- */\r\n\r\nconst echartsBasicCandlestickChartInit = () => {\r\n  const $basicCandleStickChartEl = document.querySelector('.echart-candlestick-chart-example');\r\n\r\n  if ($basicCandleStickChartEl) {\r\n    // Get options from data attribute\r\n    const userOptions = utils.getData($basicCandleStickChartEl, 'options');\r\n    const chart = window.echarts.init($basicCandleStickChartEl);\r\n\r\n    const data = [\r\n      ['2013/1/24', 2320.26, 2320.26, 2287.3, 2362.94],\r\n      ['2013/1/25', 2300, 2291.3, 2288.26, 2308.38],\r\n      ['2013/1/28', 2295.35, 2346.5, 2295.35, 2346.92],\r\n      ['2013/1/29', 2347.22, 2358.98, 2337.35, 2363.8],\r\n      ['2013/1/30', 2360.75, 2382.48, 2347.89, 2383.76],\r\n      ['2013/1/31', 2383.43, 2385.42, 2371.23, 2391.82],\r\n      ['2013/2/1', 2377.41, 2419.02, 2369.57, 2421.15],\r\n      ['2013/2/4', 2425.92, 2428.15, 2417.58, 2440.38],\r\n      ['2013/2/5', 2411, 2433.13, 2403.3, 2437.42],\r\n      ['2013/2/6', 2432.68, 2434.48, 2427.7, 2441.73],\r\n      ['2013/2/7', 2430.69, 2418.53, 2394.22, 2433.89],\r\n      ['2013/2/8', 2416.62, 2432.4, 2414.4, 2443.03],\r\n      ['2013/2/18', 2441.91, 2421.56, 2415.43, 2444.8],\r\n      ['2013/2/19', 2420.26, 2382.91, 2373.53, 2427.07],\r\n      ['2013/2/20', 2383.49, 2397.18, 2370.61, 2397.94],\r\n      ['2013/2/21', 2378.82, 2325.95, 2309.17, 2378.82],\r\n      ['2013/2/22', 2322.94, 2314.16, 2308.76, 2330.88],\r\n      ['2013/2/25', 2320.62, 2325.82, 2315.01, 2338.78],\r\n      ['2013/2/26', 2313.74, 2293.34, 2289.89, 2340.71],\r\n      ['2013/2/27', 2297.77, 2313.22, 2292.03, 2324.63],\r\n      ['2013/2/28', 2322.32, 2365.59, 2308.92, 2366.16],\r\n      ['2013/3/1', 2364.54, 2359.51, 2330.86, 2369.65],\r\n      ['2013/3/4', 2332.08, 2273.4, 2259.25, 2333.54],\r\n      ['2013/3/5', 2274.81, 2326.31, 2270.1, 2328.14],\r\n      ['2013/3/6', 2333.61, 2347.18, 2321.6, 2351.44],\r\n      ['2013/3/7', 2340.44, 2324.29, 2304.27, 2352.02],\r\n      ['2013/3/8', 2326.42, 2318.61, 2314.59, 2333.67],\r\n      ['2013/3/11', 2314.68, 2310.59, 2296.58, 2320.96],\r\n      ['2013/3/12', 2309.16, 2286.6, 2264.83, 2333.29],\r\n      ['2013/3/13', 2282.17, 2263.97, 2253.25, 2286.33],\r\n      ['2013/3/14', 2255.77, 2270.28, 2253.31, 2276.22],\r\n      ['2013/3/15', 2269.31, 2278.4, 2250, 2312.08],\r\n      ['2013/3/18', 2267.29, 2240.02, 2239.21, 2276.05],\r\n      ['2013/3/19', 2244.26, 2257.43, 2232.02, 2261.31],\r\n      ['2013/3/20', 2257.74, 2317.37, 2257.42, 2317.86],\r\n      ['2013/3/21', 2318.21, 2324.24, 2311.6, 2330.81],\r\n      ['2013/3/22', 2321.4, 2328.28, 2314.97, 2332],\r\n      ['2013/3/25', 2334.74, 2326.72, 2319.91, 2344.89],\r\n      ['2013/3/26', 2318.58, 2297.67, 2281.12, 2319.99],\r\n      ['2013/3/27', 2299.38, 2301.26, 2289, 2323.48],\r\n      ['2013/3/28', 2273.55, 2236.3, 2232.91, 2273.55],\r\n      ['2013/3/29', 2238.49, 2236.62, 2228.81, 2246.87],\r\n      ['2013/4/1', 2229.46, 2234.4, 2227.31, 2243.95],\r\n      ['2013/4/2', 2234.9, 2227.74, 2220.44, 2253.42],\r\n      ['2013/4/3', 2232.69, 2225.29, 2217.25, 2241.34],\r\n      ['2013/4/8', 2196.24, 2211.59, 2180.67, 2212.59],\r\n      ['2013/4/9', 2215.47, 2225.77, 2215.47, 2234.73],\r\n      ['2013/4/10', 2224.93, 2226.13, 2212.56, 2233.04],\r\n      ['2013/4/11', 2236.98, 2219.55, 2217.26, 2242.48],\r\n      ['2013/4/12', 2218.09, 2206.78, 2204.44, 2226.26]\r\n    ];\r\n\r\n    const getDefaultOptions = () => ({\r\n      tooltip: {\r\n        trigger: 'axis',\r\n        padding: [7, 10],\r\n        backgroundColor: utils.getGrays()['100'],\r\n        borderColor: utils.getGrays()['300'],\r\n        textStyle: { color: utils.getGrays()['1100'] },\r\n        borderWidth: 1,\r\n        transitionDuration: 0,\r\n        axisPointer: {\r\n          type: 'none'\r\n        }\r\n      },\r\n      toolbox: {\r\n        top: 0,\r\n        feature: {\r\n          dataZoom: {\r\n            yAxisIndex: false\r\n          },\r\n          restore: { show: true }\r\n        },\r\n        iconStyle: {\r\n          borderColor: utils.getGrays()['700'],\r\n          borderWidth: 1\r\n        },\r\n\r\n        emphasis: {\r\n          iconStyle: {\r\n            textFill: utils.getGrays()['600']\r\n          }\r\n        }\r\n      },\r\n      dataZoom: [\r\n        {\r\n          type: 'inside',\r\n          start: 0,\r\n          end: 100,\r\n          minValueSpan: 10\r\n        }\r\n      ],\r\n      xAxis: {\r\n        type: 'category',\r\n        data: data.map(item => item[0]),\r\n        scale: true,\r\n        splitLine: { show: false },\r\n        splitNumber: 10,\r\n        min: 'dataMin',\r\n        max: 'dataMax',\r\n        boundaryGap: true,\r\n        axisPointer: {\r\n          lineStyle: {\r\n            color: utils.getGrays()['300'],\r\n            type: 'dashed'\r\n          }\r\n        },\r\n        axisLine: {\r\n          lineStyle: {\r\n            color: utils.getGrays()['300'],\r\n            type: 'solid'\r\n          }\r\n        },\r\n        axisTick: { show: false },\r\n        axisLabel: {\r\n          color: utils.getGrays()['600'],\r\n          formatter: value => window.dayjs(value, 'YYYY-MM-DD').format('MMM DD'),\r\n          margin: 15,\r\n          fontWeight: 500\r\n        }\r\n      },\r\n      yAxis: {\r\n        scale: true,\r\n        axisPointer: { show: false },\r\n        splitLine: {\r\n          lineStyle: {\r\n            color: utils.getGrays()['200'],\r\n            type: 'dashed'\r\n          }\r\n        },\r\n        boundaryGap: false,\r\n        axisLabel: {\r\n          show: true,\r\n          color: utils.getGrays()['600'],\r\n          margin: 15,\r\n          fontWeight: 500\r\n        },\r\n        axisTick: { show: false },\r\n        axisLine: { show: false }\r\n      },\r\n      series: [\r\n        {\r\n          type: 'candlestick',\r\n          name: 'Volume',\r\n          data: data.map(item => item.slice(1)),\r\n          itemStyle: {\r\n            color: utils.getColor('warning'),\r\n            color0: utils.getColor('primary'),\r\n            borderColor: utils.getColor('warning'),\r\n            borderColor0: utils.getColor('primary')\r\n          }\r\n        }\r\n      ],\r\n      grid: {\r\n        right: 5,\r\n        left: 5,\r\n        bottom: 5,\r\n        top: '15%',\r\n        containLabel: true\r\n      }\r\n    });\r\n\r\n    echartSetOption(chart, userOptions, getDefaultOptions);\r\n  }\r\n};\r\n\r\nexport default echartsBasicCandlestickChartInit;\r\n", "import utils from '../../../utils';\r\nimport { echartSetOption } from '../echarts-utils';\r\n\r\n/* -------------------------------------------------------------------------- */\r\n/*                             Echarts Basic Gauge Chart                      */\r\n/* -------------------------------------------------------------------------- */\r\n\r\nconst echartsBasicGaugeChartInit = () => {\r\n  const $basicGaugeChartEl = document.querySelector('.echart-basic-gauge-chart-example');\r\n\r\n  if ($basicGaugeChartEl) {\r\n    // Get options from data attribute\r\n    const userOptions = utils.getData($basicGaugeChartEl, 'options');\r\n    const chart = window.echarts.init($basicGaugeChartEl);\r\n\r\n    const tooltipFormatter = params => `\r\n      <div>\r\n          <h6 class=\"fs-10 text-700 mb-0\">\r\n            <span class=\"fas fa-circle me-1\" style='color:${params[0].color}'></span>\r\n            ${params[0].name} : ${params[0].value}\r\n          </h6>\r\n      </div>\r\n      `;\r\n\r\n    const getDefaultOptions = () => ({\r\n      tooltip: {\r\n        trigger: 'axis',\r\n        padding: [7, 10],\r\n        backgroundColor: utils.getGrays()['100'],\r\n        borderColor: utils.getGrays()['300'],\r\n        textStyle: { color: utils.getGrays()['1100'] },\r\n        borderWidth: 1,\r\n        formatter: tooltipFormatter,\r\n        transitionDuration: 0,\r\n        axisPointer: {\r\n          type: 'none'\r\n        }\r\n      },\r\n      radius: '100%',\r\n      series: [\r\n        {\r\n          name: 'Pressure',\r\n          type: 'gauge',\r\n          splitLine: {\r\n            lineStyle: {\r\n              color: utils.getGrays()['600']\r\n            }\r\n          },\r\n          axisLabel: {\r\n            color: utils.getGrays()['600']\r\n          },\r\n          detail: {\r\n            formatter: '{value}'\r\n          },\r\n          title: {\r\n            color: utils.getGrays()['600']\r\n          },\r\n          data: [\r\n            {\r\n              value: 50,\r\n              name: 'SCORE',\r\n              detail: {\r\n                color: utils.getGrays()['600']\r\n              }\r\n            }\r\n          ]\r\n        }\r\n      ]\r\n    });\r\n\r\n    echartSetOption(chart, userOptions, getDefaultOptions);\r\n  }\r\n};\r\n\r\nexport default echartsBasicGaugeChartInit;\r\n", "import utils from '../../../utils';\r\nimport { getPosition, echartSetOption } from '../echarts-utils';\r\n\r\n/* -------------------------------------------------------------------------- */\r\n/*                             Echarts Line Chart                             */\r\n/* -------------------------------------------------------------------------- */\r\n\r\nconst echartsLineChartInit = () => {\r\n  const $lineChartEl = document.querySelector('.echart-line-chart-example');\r\n\r\n  if ($lineChartEl) {\r\n    // Get options from data attribute\r\n    const userOptions = utils.getData($lineChartEl, 'options');\r\n    const chart = window.echarts.init($lineChartEl);\r\n\r\n    const months = [\r\n      'January',\r\n      'February',\r\n      'March',\r\n      'April',\r\n      'May',\r\n      'June',\r\n      'July',\r\n      'August',\r\n      'September',\r\n      'October',\r\n      'November',\r\n      'December'\r\n    ];\r\n\r\n    const data = [1272, 1301, 1402, 1216, 1086, 1236, 1219, 1330, 1367, 1416, 1297, 1204];\r\n\r\n    const tooltipFormatter = params => `\r\n      <div>\r\n          <h6 class=\"fs-10 text-700 mb-0\">\r\n            <span class=\"fas fa-circle me-1\" style='color:${params[0].borderColor}'></span>\r\n            ${params[0].name} : ${params[0].value}\r\n          </h6>\r\n      </div>\r\n      `;\r\n\r\n    const getDefaultOptions = () => ({\r\n      tooltip: {\r\n        trigger: 'axis',\r\n        padding: [7, 10],\r\n        backgroundColor: utils.getGrays()['100'],\r\n        borderColor: utils.getGrays()['300'],\r\n        textStyle: { color: utils.getGrays()['1100'] },\r\n        borderWidth: 1,\r\n        formatter: tooltipFormatter,\r\n        transitionDuration: 0,\r\n        position(pos, params, dom, rect, size) {\r\n          return getPosition(pos, params, dom, rect, size);\r\n        },\r\n        axisPointer: {\r\n          type: 'none'\r\n        }\r\n      },\r\n      xAxis: {\r\n        type: 'category',\r\n        data: months,\r\n        boundaryGap: false,\r\n        axisLine: {\r\n          lineStyle: {\r\n            color: utils.getGrays()['300']\r\n          }\r\n        },\r\n        axisTick: { show: false },\r\n        axisLabel: {\r\n          color: utils.getGrays()['400'],\r\n          formatter: value => value.substring(0, 3),\r\n          margin: 15\r\n        },\r\n        splitLine: {\r\n          show: false\r\n        }\r\n      },\r\n      yAxis: {\r\n        type: 'value',\r\n        splitLine: {\r\n          lineStyle: {\r\n            type: 'dashed',\r\n            color: utils.getGrays()['200']\r\n          }\r\n        },\r\n        boundaryGap: false,\r\n        axisLabel: {\r\n          show: true,\r\n          color: utils.getGrays()['400'],\r\n          margin: 15\r\n        },\r\n        axisTick: { show: false },\r\n        axisLine: { show: false },\r\n        min: 600\r\n      },\r\n      series: [\r\n        {\r\n          type: 'line',\r\n          data,\r\n          itemStyle: {\r\n            color: utils.getGrays()['100'],\r\n            borderColor: utils.getColor('primary'),\r\n            borderWidth: 2\r\n          },\r\n          lineStyle: {\r\n            color: utils.getColor('primary')\r\n          },\r\n          showSymbol: false,\r\n          symbol: 'circle',\r\n          symbolSize: 10,\r\n          smooth: false,\r\n          hoverAnimation: true\r\n        }\r\n      ],\r\n      grid: {\r\n        right: '3%', left: '10%', bottom: '10%', top: '5%'\r\n      }\r\n    });\r\n\r\n    echartSetOption(chart, userOptions, getDefaultOptions);\r\n  }\r\n};\r\n\r\nexport default echartsLineChartInit;\r\n", "import utils from '../../../utils';\r\nimport { echartSetOption } from '../echarts-utils';\r\n\r\n/* -------------------------------------------------------------------------- */\r\n/*                           Echarts Bubble Chart                             */\r\n/* -------------------------------------------------------------------------- */\r\n\r\nconst echartsBubbleChartInit = () => {\r\n  const $bubbleChartEl = document.querySelector('.echart-bubble-chart-example');\r\n\r\n  if ($bubbleChartEl) {\r\n    // Get options from data attribute\r\n    const userOptions = utils.getData($bubbleChartEl, 'options');\r\n    const chart = window.echarts.init($bubbleChartEl);\r\n\r\n    const data = [\r\n      [\r\n        [28604, 77, 17096869, 'Australia', 1990],\r\n        [31163, 77.4, 27662440, 'Canada', 1990],\r\n        [1516, 68, 1154605773, 'China', 1990],\r\n        [28599, 75, 4986705, 'Finland', 1990],\r\n        [29476, 77.1, 56943299, 'France', 1990],\r\n        [31476, 75.4, 78958237, 'Germany', 1990],\r\n        [1777, 57.7, *********, 'India', 1990],\r\n        [29550, 79.1, *********, 'Japan', 1990],\r\n        [12087, 72, 42972254, 'South Korea', 1990],\r\n        [24021, 75.4, 3397534, 'New Zealand', 1990],\r\n        [43296, 76.8, 4240375, 'Norway', 1990],\r\n        [10088, 70.8, 38195258, 'Poland', 1990],\r\n        [19349, 69.6, *********, 'Russia', 1990],\r\n        [26424, 75.7, 57110117, 'United Kingdom', 1990],\r\n        [37062, 75.4, *********, 'United States', 1990]\r\n      ],\r\n      [\r\n        [44056, 81.8, 23968973, 'Australia', 2015],\r\n        [43294, 81.7, 35939927, 'Canada', 2015],\r\n        [13334, 76.9, 1376048943, 'China', 2015],\r\n        [38923, 80.8, 5503457, 'Finland', 2015],\r\n        [37599, 81.9, 64395345, 'France', 2015],\r\n        [44053, 81.1, 80688545, 'Germany', 2015],\r\n        [5903, 66.8, 1311050527, 'India', 2015],\r\n        [36162, 83.5, *********, 'Japan', 2015],\r\n        [34644, 80.7, 50293439, 'South Korea', 2015],\r\n        [34186, 80.6, 4528526, 'New Zealand', 2015],\r\n        [64304, 81.6, 5210967, 'Norway', 2015],\r\n        [24787, 77.3, 38611794, 'Poland', 2015],\r\n        [23038, 73.13, *********, 'Russia', 2015],\r\n        [38225, 81.4, 64715810, 'United Kingdom', 2015],\r\n        [53354, 79.1, *********, 'United States', 2015]\r\n      ]\r\n    ];\r\n\r\n    const getDefaultOptions = () => ({\r\n      title: {\r\n        text: '1990 and 2015 have per capita and GDP',\r\n        left: 0,\r\n        top: 0,\r\n        textStyle: {\r\n          color: utils.getGrays()['600'],\r\n          fontWeight: 600\r\n        }\r\n      },\r\n      legend: {\r\n        right: 0,\r\n        top: '10%',\r\n        data: ['1990', '2015'],\r\n        textStyle: {\r\n          color: utils.getGrays()['600']\r\n        }\r\n      },\r\n      xAxis: {\r\n        axisLabel: {\r\n          color: utils.getGrays()['600'],\r\n          formatter: value => `${value / 1000}k`\r\n        },\r\n        axisLine: {\r\n          show: true,\r\n          lineStyle: {\r\n            color: utils.getGrays()['300']\r\n          }\r\n        },\r\n\r\n        splitLine: {\r\n          show: true,\r\n          lineStyle: {\r\n            color: utils.getGrays()['200']\r\n          }\r\n        }\r\n      },\r\n      yAxis: {\r\n        scale: true,\r\n        axisLabel: {\r\n          color: utils.getGrays()['600']\r\n        },\r\n        splitLine: {\r\n          show: true,\r\n          lineStyle: {\r\n            color: utils.getGrays()['200']\r\n          }\r\n        },\r\n\r\n        axisLine: {\r\n          show: true,\r\n          lineStyle: {\r\n            color: utils.getGrays()['300']\r\n          }\r\n        }\r\n      },\r\n      series: [\r\n        {\r\n          name: '1990',\r\n          data: data[0],\r\n          type: 'scatter',\r\n          symbolSize: value => Math.sqrt(value[2]) / 5e2,\r\n          emphasis: {\r\n            focus: 'series',\r\n            label: {\r\n              color: utils.getGrays()['600'],\r\n              show: true,\r\n              formatter: param => param.data[3],\r\n              position: 'top'\r\n            }\r\n          },\r\n          itemStyle: {\r\n            color: utils.rgbaColor(utils.getColor('primary'), 0.7)\r\n          }\r\n        },\r\n        {\r\n          name: '2015',\r\n          data: data[1],\r\n          type: 'scatter',\r\n          symbolSize: value => Math.sqrt(value[2]) / 7e2,\r\n          emphasis: {\r\n            focus: 'series',\r\n            label: {\r\n              color: utils.getGrays()['600'],\r\n              show: true,\r\n              formatter: param => param.data[3],\r\n              position: 'top'\r\n            }\r\n          },\r\n          itemStyle: {\r\n            color: utils.rgbaColor(utils.getColor('warning'), 0.7)\r\n          }\r\n        }\r\n      ],\r\n      grid: {\r\n        left: 5,\r\n        right: 10,\r\n        bottom: 5,\r\n        top: '20%',\r\n        containLabel: true\r\n      }\r\n    });\r\n\r\n    echartSetOption(chart, userOptions, getDefaultOptions);\r\n  }\r\n};\r\n\r\nexport default echartsBubbleChartInit;\r\n", "import utils from '../../../utils';\r\nimport { echartSetOption } from '../echarts-utils';\r\n\r\n/* -------------------------------------------------------------------------- */\r\n/*                             Echarts Bar Chart                             */\r\n/* -------------------------------------------------------------------------- */\r\n\r\nconst echartsCandlestickMixedChartInit = () => {\r\n  const $candleStickMixedChartEl = document.querySelector(\r\n    '.echart-candlestick-mixed-chart-example'\r\n  );\r\n\r\n  if ($candleStickMixedChartEl) {\r\n    // Get options from data attribute\r\n    const userOptions = utils.getData($candleStickMixedChartEl, 'options');\r\n    const chart = window.echarts.init($candleStickMixedChartEl);\r\n\r\n    const colorList = [\r\n      utils.getColor('primary'),\r\n      utils.getColor('info'),\r\n      utils.getColor('dark'),\r\n      utils.getColor('warning')\r\n    ];\r\n\r\n    const calculateMA = (dayCount, data) => {\r\n      const result = [];\r\n      for (let i = dayCount; i < data.length; i += 1) {\r\n        let sum = 0;\r\n        for (let j = 0; j < dayCount; j += 1) {\r\n          sum += data[i - j][1];\r\n        }\r\n        result.push((sum / dayCount).toFixed(2));\r\n      }\r\n      return result;\r\n    };\r\n\r\n    const dates = utils.getPastDates(61).map(date => window.dayjs(date).format('MMM DD, YYYY'));\r\n\r\n    const data = [\r\n      [17512.58, 17633.11, 17434.27, 17642.81, 86160000],\r\n      [17652.36, 17716.66, 17652.36, 17790.11, 79330000],\r\n      [17716.05, 17685.09, 17669.72, 17755.7, 102600000],\r\n      [17661.74, 17792.75, 17568.02, 17811.48, 104890000],\r\n      [17799.39, 17737, 17710.67, 17806.38, 85230000],\r\n      [17718.03, 17603.32, 17579.56, 17718.03, 115230000],\r\n      [17605.45, 17716.05, 17542.54, 17723.55, 99410000],\r\n      [17687.28, 17541.96, 17484.23, 17687.28, 90120000],\r\n      [17555.39, 17576.96, 17528.16, 17694.51, 79990000],\r\n      [17586.48, 17556.41, 17555.9, 17731.63, 107100000],\r\n      [17571.34, 17721.25, 17553.57, 17744.43, 81020000],\r\n      [17741.66, 17908.28, 17741.66, 17918.35, 91710000],\r\n      [17912.25, 17926.43, 17885.44, 17962.14, 84510000],\r\n      [17925.95, 17897.46, 17867.41, 17937.65, 118160000],\r\n      [17890.2, 18004.16, 17848.22, 18009.53, 89390000],\r\n      [18012.1, 18053.6, 17984.43, 18103.46, 89820000],\r\n      [18059.49, 18096.27, 18031.21, 18167.63, 100210000],\r\n      [18092.84, 17982.52, 17963.89, 18107.29, 102720000],\r\n      [17985.05, 18003.75, 17909.89, 18026.85, 134120000],\r\n      [17990.94, 17977.24, 17855.55, 17990.94, 83770000],\r\n      [17987.38, 17990.32, 17934.17, 18043.77, 92570000],\r\n      [17996.14, 18041.55, 17920.26, 18084.66, 109090000],\r\n      [18023.88, 17830.76, 17796.55, 18035.73, 100920000],\r\n      [17813.09, 17773.64, 17651.98, 17814.83, 136670000],\r\n      [17783.78, 17891.16, 17773.71, 17912.35, 80100000],\r\n      [17870.75, 17750.91, 17670.88, 17870.75, 97060000],\r\n      [17735.02, 17651.26, 17609.01, 17738.06, 95020000],\r\n      [17664.48, 17660.71, 17615.82, 17736.11, 81530000],\r\n      [17650.3, 17740.63, 17580.38, 17744.54, 80020000],\r\n      [17743.85, 17705.91, 17668.38, 17783.16, 85590000],\r\n      [17726.66, 17928.35, 17726.66, 17934.61, 75790000],\r\n      [17919.03, 17711.12, 17711.05, 17919.03, 87390000],\r\n      [17711.12, 17720.5, 17625.38, 17798.19, 88560000],\r\n      [17711.12, 17535.32, 17512.48, 17734.74, 86640000],\r\n      [17531.76, 17710.71, 17531.76, 17755.8, 88440000],\r\n      [17701.46, 17529.98, 17469.92, 17701.46, 103260000],\r\n      [17501.28, 17526.62, 17418.21, 17636.22, 79120000],\r\n      [17514.16, 17435.4, 17331.07, 17514.16, 95530000],\r\n      [17437.32, 17500.94, 17437.32, 17571.75, 111990000],\r\n      [17507.04, 17492.93, 17480.05, 17550.7, 87790000],\r\n      [17525.19, 17706.05, 17525.19, 17742.59, 86480000],\r\n      [17735.09, 17851.51, 17735.09, 17891.71, 79180000],\r\n      [17859.52, 17828.29, 17803.82, 17888.66, 68940000],\r\n      [17826.85, 17873.22, 17824.73, 17873.22, 73190000],\r\n      [17891.5, 17787.2, 17724.03, 17899.24, 147390000],\r\n      [17754.55, 17789.67, 17664.79, 17809.18, 78530000],\r\n      [17789.05, 17838.56, 17703.55, 17838.56, 75560000],\r\n      [17799.8, 17807.06, 17689.68, 17833.17, 82270000],\r\n      [17825.69, 17920.33, 17822.81, 17949.68, 71870000],\r\n      [17936.22, 17938.28, 17936.22, 18003.23, 78750000],\r\n      [17931.91, 18005.05, 17931.91, 18016, 71260000],\r\n      [17969.98, 17985.19, 17915.88, 18005.22, 69690000],\r\n      [17938.82, 17865.34, 17812.34, 17938.82, 90540000],\r\n      [17830.5, 17732.48, 17731.35, 17893.28, 101690000],\r\n      [17710.77, 17674.82, 17595.79, 17733.92, 93740000],\r\n      [17703.65, 17640.17, 17629.01, 17762.96, 94130000],\r\n      [17602.23, 17733.1, 17471.29, 17754.91, 91950000],\r\n      [17733.44, 17675.16, 17602.78, 17733.44, 248680000],\r\n      [17736.87, 17804.87, 17736.87, 17946.36, 99380000],\r\n      [17827.33, 17829.73, 17799.8, 17877.84, 85130000],\r\n      [17832.67, 17780.83, 17770.36, 17920.16, 89440000]\r\n    ];\r\n\r\n    const dataMA5 = calculateMA(5, data);\r\n\r\n    const getDefaultOptions = () => ({\r\n      animation: false,\r\n      color: colorList,\r\n      legend: {\r\n        top: 0,\r\n        data: ['MA1', 'MA5', 'Volume'],\r\n        textStyle: {\r\n          color: utils.getGrays()['600']\r\n        }\r\n      },\r\n      tooltip: {\r\n        trigger: 'axis',\r\n        padding: [7, 10],\r\n        backgroundColor: utils.getGrays()['100'],\r\n        borderColor: utils.getGrays()['300'],\r\n        textStyle: { color: utils.getGrays()['1100'] },\r\n        borderWidth: 1,\r\n        transitionDuration: 0,\r\n        position: (pos, params, el, elRect, size) => {\r\n          const obj = {\r\n            top: 60\r\n          };\r\n          obj[['left', 'right'][+(pos[0] < size.viewSize[0] / 2)]] = 5;\r\n          return obj;\r\n        }\r\n      },\r\n      axisPointer: {\r\n        link: [\r\n          {\r\n            xAxisIndex: [0, 1]\r\n          }\r\n        ]\r\n      },\r\n      dataZoom: [\r\n        {\r\n          type: 'slider',\r\n          xAxisIndex: [0, 1],\r\n          realtime: false,\r\n          start: 20,\r\n          end: 70,\r\n          top: 35,\r\n          height: 15,\r\n          handleIcon:\r\n            'path://M10.7,11.9H9.3c-4.9,0.3-8.8,4.4-8.8,9.4c0,5,3.9,9.1,8.8,9.4h1.3c4.9-0.3,8.8-4.4,8.8-9.4C19.5,16.3,15.6,12.2,10.7,11.9z M13.3,24.4H6.7V23h6.6V24.4z M13.3,19.6H6.7v-1.4h6.6V19.6z',\r\n          handleSize: '120%'\r\n        },\r\n        {\r\n          type: 'inside',\r\n          xAxisIndex: [0, 1],\r\n          start: 40,\r\n          end: 70,\r\n          top: 30,\r\n          height: 20\r\n        }\r\n      ],\r\n      xAxis: [\r\n        {\r\n          type: 'category',\r\n          data: dates,\r\n          boundaryGap: false,\r\n          axisLine: {\r\n            lineStyle: { color: utils.getGrays()['300'] }\r\n          },\r\n          axisLabel: {\r\n            color: utils.getGrays()['600'],\r\n            formatter: value => window.dayjs(value).format('MMM DD')\r\n          },\r\n          min: 'dataMin',\r\n          max: 'dataMax',\r\n          axisPointer: {\r\n            show: true\r\n          }\r\n        },\r\n        {\r\n          type: 'category',\r\n          gridIndex: 1,\r\n          data: dates,\r\n          scale: true,\r\n          boundaryGap: false,\r\n          splitLine: { show: false },\r\n          axisLabel: { show: false },\r\n          axisTick: { show: false },\r\n          axisLine: { lineStyle: { color: 'blue' } },\r\n          splitNumber: 20,\r\n          min: 'dataMin',\r\n          max: 'dataMax',\r\n          axisPointer: {\r\n            type: 'shadow',\r\n            label: { show: false },\r\n            triggerTooltip: true\r\n          }\r\n        }\r\n      ],\r\n      yAxis: [\r\n        {\r\n          scale: true,\r\n          splitNumber: 2,\r\n          axisLine: { show: false },\r\n          splitLine: {\r\n            lineStyle: {\r\n              color: utils.getGrays()['200']\r\n            }\r\n          },\r\n          axisTick: { show: false },\r\n          axisLabel: {\r\n            color: utils.getGrays()['600']\r\n          }\r\n        },\r\n        {\r\n          scale: true,\r\n          gridIndex: 1,\r\n          splitNumber: 2,\r\n          axisLabel: { show: false },\r\n          axisLine: { show: false },\r\n          axisTick: { show: false },\r\n          splitLine: { show: false }\r\n        }\r\n      ],\r\n      grid: [\r\n        {\r\n          left: 5,\r\n          right: 12,\r\n          // top: 110,\r\n          bottom: 60,\r\n          height: 160,\r\n          containLabel: true\r\n        },\r\n        {\r\n          left: 50,\r\n          right: 12,\r\n          height: 40,\r\n          top: 260,\r\n          containLabel: true\r\n        }\r\n      ],\r\n      series: [\r\n        {\r\n          name: 'Volume',\r\n          type: 'bar',\r\n          xAxisIndex: 1,\r\n          yAxisIndex: 1,\r\n          itemStyle: {\r\n            color: utils.getColor('primary')\r\n          },\r\n          emphasis: {\r\n            itemStyle: {\r\n              color: utils.getColor('primary')\r\n            }\r\n          },\r\n          data: data.map(item => item[4])\r\n        },\r\n        {\r\n          type: 'candlestick',\r\n          name: 'MA1',\r\n          data,\r\n          itemStyle: {\r\n            color: utils.getColor('success'),\r\n            color0: utils.getColor('info'),\r\n            borderColor: utils.getColor('success'),\r\n            borderColor0: utils.getColor('info')\r\n          }\r\n        },\r\n        {\r\n          name: 'MA5',\r\n          type: 'line',\r\n          data: dataMA5,\r\n          smooth: true,\r\n          showSymbol: false,\r\n          lineStyle: {\r\n            width: 1,\r\n            color: utils.getColor('primary')\r\n          }\r\n        }\r\n      ]\r\n    });\r\n\r\n    echartSetOption(chart, userOptions, getDefaultOptions);\r\n  }\r\n};\r\n\r\nexport default echartsCandlestickMixedChartInit;\r\n", "import utils from '../../../utils';\r\nimport { echartSetOption } from '../echarts-utils';\r\n\r\n/* -------------------------------------------------------------------------- */\r\n/*                             Echarts Doughnut Chart                         */\r\n/* -------------------------------------------------------------------------- */\r\n\r\nconst echartsDoughnutChartInit = () => {\r\n  const $doughnutChartEl = document.querySelector(\r\n    '.echart-doughnut-chart-example'\r\n  );\r\n\r\n  if ($doughnutChartEl) {\r\n    // Get options from data attribute\r\n    const userOptions = utils.getData($doughnutChartEl, 'options');\r\n    const chart = window.echarts.init($doughnutChartEl);\r\n\r\n    const getDefaultOptions = () => ({\r\n      legend: {\r\n        left: 'left',\r\n        textStyle: {\r\n          color: utils.getGrays()['600']\r\n        }\r\n      },\r\n      series: [\r\n        {\r\n          type: 'pie',\r\n          radius: ['40%', '70%'],\r\n          center: ['50%', '55%'],\r\n          avoidLabelOverlap: false,\r\n          label: {\r\n            show: false,\r\n            position: 'center'\r\n          },\r\n          labelLine: {\r\n            show: false\r\n          },\r\n          data: [\r\n            {\r\n              value: 1048,\r\n              name: 'Facebook',\r\n              itemStyle: {\r\n                color: utils.getColor('primary')\r\n              }\r\n            },\r\n            {\r\n              value: 735,\r\n              name: 'Youtube',\r\n              itemStyle: {\r\n                color: utils.getColor('danger')\r\n              }\r\n            },\r\n            {\r\n              value: 580,\r\n              name: 'Twitter',\r\n              itemStyle: {\r\n                color: utils.getColor('info')\r\n              }\r\n            },\r\n            {\r\n              value: 484,\r\n              name: 'Linkedin',\r\n              itemStyle: {\r\n                color: utils.getColor('success')\r\n              }\r\n            },\r\n            {\r\n              value: 300,\r\n              name: 'Github',\r\n              itemStyle: {\r\n                color: utils.getColor('warning')\r\n              }\r\n            }\r\n          ]\r\n        }\r\n      ],\r\n      tooltip: {\r\n        trigger: 'item',\r\n        padding: [7, 10],\r\n        backgroundColor: utils.getGrays()['100'],\r\n        borderColor: utils.getGrays()['300'],\r\n        textStyle: { color: utils.getGrays()['1100'] },\r\n        borderWidth: 1,\r\n        transitionDuration: 0,\r\n        axisPointer: {\r\n          type: 'none'\r\n        }\r\n      }\r\n    });\r\n\r\n    echartSetOption(chart, userOptions, getDefaultOptions);\r\n  }\r\n};\r\n\r\nexport default echartsDoughnutChartInit;\r\n", "import utils from '../../../utils';\r\nimport { echartSetOption } from '../echarts-utils';\r\n\r\n/* -------------------------------------------------------------------------- */\r\n/*                             Echarts Doughnut Chart                         */\r\n/* -------------------------------------------------------------------------- */\r\n\r\nconst echartsDoughnutRoundedChartInit = () => {\r\n  const $doughnutRoundedChartEl = document.querySelector('.echart-doughnut-rounded-chart');\r\n\r\n  if ($doughnutRoundedChartEl) {\r\n    // Get options from data attribute\r\n    const userOptions = utils.getData($doughnutRoundedChartEl, 'options');\r\n    const chart = window.echarts.init($doughnutRoundedChartEl);\r\n\r\n    const getDefaultOptions = () => ({\r\n      legend: {\r\n        orient: 'vertical',\r\n        left: 'left',\r\n        textStyle: {\r\n          color: utils.getGrays()['600']\r\n        }\r\n      },\r\n      series: [\r\n        {\r\n          type: 'pie',\r\n          radius: ['40%', '70%'],\r\n          center: window.innerWidth < 530 ? ['65%', '55%'] : ['50%', '55%'],\r\n          avoidLabelOverlap: false,\r\n          itemStyle: {\r\n            borderRadius: 10,\r\n            borderColor: utils.getGrays()['100'],\r\n            borderWidth: 2\r\n          },\r\n          label: {\r\n            show: false,\r\n            position: 'center'\r\n          },\r\n          labelLine: {\r\n            show: false\r\n          },\r\n          data: [\r\n            {\r\n              value: 1048,\r\n              name: 'Starter',\r\n              itemStyle: {\r\n                color: utils.getColor('primary')\r\n              }\r\n            },\r\n            {\r\n              value: 735,\r\n              name: 'Basic',\r\n              itemStyle: {\r\n                color: utils.getColor('danger')\r\n              }\r\n            },\r\n            {\r\n              value: 580,\r\n              name: 'Optimal',\r\n              itemStyle: {\r\n                color: utils.getColor('info')\r\n              }\r\n            },\r\n            {\r\n              value: 484,\r\n              name: 'Business',\r\n              itemStyle: {\r\n                color: utils.getColor('success')\r\n              }\r\n            },\r\n            {\r\n              value: 300,\r\n              name: 'Premium',\r\n              itemStyle: {\r\n                color: utils.getColor('warning')\r\n              }\r\n            }\r\n          ]\r\n        }\r\n      ],\r\n      tooltip: {\r\n        trigger: 'item',\r\n        padding: [7, 10],\r\n        backgroundColor: utils.getGrays()['100'],\r\n        borderColor: utils.getGrays()['300'],\r\n        textStyle: { color: utils.getGrays()['1100'] },\r\n        borderWidth: 1,\r\n        transitionDuration: 0,\r\n        axisPointer: {\r\n          type: 'none'\r\n        }\r\n      }\r\n    });\r\n\r\n    echartSetOption(chart, userOptions, getDefaultOptions);\r\n\r\n    utils.resize(() => {\r\n      if (window.innerWidth < 530) {\r\n        chart.setOption({\r\n          series: [\r\n            {\r\n              center: ['65%', '55%']\r\n            }\r\n          ]\r\n        });\r\n      } else {\r\n        chart.setOption({ series: [{ center: ['50%', '55%'] }] });\r\n      }\r\n    });\r\n  }\r\n};\r\n\r\nexport default echartsDoughnutRoundedChartInit;\r\n", "import utils from '../../../utils';\r\nimport { echartSetOption, tooltipFormatter } from '../echarts-utils';\r\n\r\n/* -------------------------------------------------------------------------- */\r\n/*                           Echarts Dynamic Line Chart                       */\r\n/* -------------------------------------------------------------------------- */\r\n\r\nconst echartsDynamicLineChartInit = () => {\r\n  const $dynamicLineChartEl = document.querySelector(\r\n    '.echart-dynamic-line-chart-example'\r\n  );\r\n\r\n  if ($dynamicLineChartEl) {\r\n    // Get options from data attribute\r\n    const userOptions = utils.getData($dynamicLineChartEl, 'options');\r\n    const chart = window.echarts.init($dynamicLineChartEl);\r\n\r\n    const data = [];\r\n    let now = +new Date(1997, 9, 3);\r\n    const oneDay = 24 * 3600 * 1000;\r\n    let value = Math.random() * 1000;\r\n\r\n    const randomData = () => {\r\n      now = new Date(+now + oneDay);\r\n      value = value + Math.random() * 21 - 10;\r\n      return {\r\n        name: now.toString(),\r\n        value: [\r\n          [now.getFullYear(), now.getMonth() + 1, now.getDate()].join('/'),\r\n          Math.round(value)\r\n        ]\r\n      };\r\n    };\r\n\r\n    for (let i = 0; i < 1000; i += 1) {\r\n      data.push(randomData());\r\n    }\r\n\r\n    const getDefaultOptions = () => ({\r\n      tooltip: {\r\n        trigger: 'axis',\r\n        axisPointer: {\r\n          animation: false\r\n        },\r\n        padding: [7, 10],\r\n        backgroundColor: utils.getGrays()['100'],\r\n        borderColor: utils.getGrays()['300'],\r\n        textStyle: { color: utils.getGrays()['1100'] },\r\n        borderWidth: 1,\r\n        transitionDuration: 0,\r\n        formatter: tooltipFormatter\r\n      },\r\n      xAxis: {\r\n        type: 'time',\r\n        splitLine: {\r\n          show: false\r\n        },\r\n        axisLabel: {\r\n          color: utils.getGrays()['500']\r\n        },\r\n\r\n        axisLine: {\r\n          lineStyle: {\r\n            color: utils.getGrays()['300']\r\n          }\r\n        },\r\n        axisPointer: {\r\n          lineStyle: {\r\n            color: utils.getGrays()['300']\r\n          }\r\n        }\r\n      },\r\n      yAxis: {\r\n        type: 'value',\r\n        boundaryGap: [0, '100%'],\r\n        splitLine: {\r\n          show: false\r\n        },\r\n        axisLabel: {\r\n          color: utils.getGrays()['500']\r\n        }\r\n      },\r\n      series: [\r\n        {\r\n          name: 'Total',\r\n          type: 'line',\r\n          showSymbol: false,\r\n          hoverAnimation: false,\r\n          data,\r\n          lineStyle: {\r\n            color: utils.getColor('primary')\r\n          },\r\n          itemStyle: {\r\n            color: utils.getGrays()['100'],\r\n            borderColor: utils.getColor('primary'),\r\n            borderWidth: 2\r\n          },\r\n          symbol: 'circle',\r\n          symbolSize: 10\r\n        }\r\n      ],\r\n      grid: {\r\n        right: 5, left: '7%', bottom: '10%', top: '5%'\r\n      }\r\n    });\r\n\r\n    echartSetOption(chart, userOptions, getDefaultOptions);\r\n\r\n    setInterval(() => {\r\n      for (let i = 0; i < 5; i += 1) {\r\n        data.shift();\r\n        data.push(randomData());\r\n      }\r\n\r\n      chart.setOption({\r\n        series: [\r\n          {\r\n            data\r\n          }\r\n        ]\r\n      });\r\n    }, 1000);\r\n  }\r\n};\r\n\r\nexport default echartsDynamicLineChartInit;\r\n", "import utils from '../../../utils';\r\nimport { echartSetOption } from '../echarts-utils';\r\n\r\n/* -------------------------------------------------------------------------- */\r\n/*                          Echarts Gauge Progress Chart                      */\r\n/* -------------------------------------------------------------------------- */\r\n\r\nconst echartsGaugeGradeChartInit = () => {\r\n  const $gaugeGradeChartEl = document.querySelector('.echart-gauge-grade-chart-example');\r\n\r\n  if ($gaugeGradeChartEl) {\r\n    // Get options from data attribute\r\n    const userOptions = utils.getData($gaugeGradeChartEl, 'options');\r\n    const chart = window.echarts.init($gaugeGradeChartEl);\r\n\r\n    const getDefaultOptions = () => ({\r\n      series: [\r\n        {\r\n          radius: '100%',\r\n          type: 'gauge',\r\n          center: ['50%', '70%'],\r\n          startAngle: 180,\r\n          endAngle: 0,\r\n          min: 0,\r\n          max: 1,\r\n          splitNumber: 8,\r\n          axisLine: {\r\n            lineStyle: {\r\n              width: 6,\r\n              color: [\r\n                [0.25, utils.getColor('danger')],\r\n                [0.5, utils.getColor('warning')],\r\n                [0.75, utils.getColor('info')],\r\n                [1, utils.getColor('success')]\r\n              ]\r\n            }\r\n          },\r\n          pointer: {\r\n            icon: 'path://M12.8,0.7l12,40.1H0.7L12.8,0.7z',\r\n            length: '12%',\r\n            width: 20,\r\n            offsetCenter: [0, '-60%'],\r\n            itemStyle: {\r\n              color: 'auto'\r\n            }\r\n          },\r\n          axisTick: {\r\n            length: 12,\r\n            lineStyle: {\r\n              color: 'auto',\r\n              width: 2\r\n            }\r\n          },\r\n          splitLine: {\r\n            length: 20,\r\n            lineStyle: {\r\n              color: 'auto',\r\n              width: 5\r\n            }\r\n          },\r\n          axisLabel: {\r\n            color: utils.getGrays()['600'],\r\n            distance: -60,\r\n            formatter: value => {\r\n              if (value === 0.875) {\r\n                return 'Excellent';\r\n              }\r\n              if (value === 0.625) {\r\n                return 'Good';\r\n              }\r\n              if (value === 0.375) {\r\n                return 'Well';\r\n              }\r\n              if (value === 0.125) {\r\n                return 'Bad';\r\n              }\r\n              return '';\r\n            }\r\n          },\r\n          title: {\r\n            offsetCenter: [0, '-20%'],\r\n            color: utils.getGrays()['600']\r\n          },\r\n          detail: {\r\n            offsetCenter: [0, '0%'],\r\n            valueAnimation: true,\r\n            formatter(value) {\r\n              return Math.round(value * 100);\r\n            },\r\n            color: 'auto'\r\n          },\r\n          data: [\r\n            {\r\n              value: 0.7,\r\n              name: 'Grade'\r\n            }\r\n          ]\r\n        }\r\n      ]\r\n    });\r\n\r\n    echartSetOption(chart, userOptions, getDefaultOptions);\r\n  }\r\n};\r\n\r\nexport default echartsGaugeGradeChartInit;\r\n", "import utils from '../../../utils';\r\nimport { echartSetOption } from '../echarts-utils';\r\n\r\n/* -------------------------------------------------------------------------- */\r\n/*                          Echarts Gauge Progress Chart                      */\r\n/* -------------------------------------------------------------------------- */\r\n\r\nconst echartsGaugeMultiRingChartInit = () => {\r\n  const $gaugeMultiRingChartEl = document.querySelector(\r\n    '.echart-gauge-multi-ring-chart-example'\r\n  );\r\n\r\n  if ($gaugeMultiRingChartEl) {\r\n    // Get options from data attribute\r\n    const userOptions = utils.getData($gaugeMultiRingChartEl, 'options');\r\n    const chart = window.echarts.init($gaugeMultiRingChartEl);\r\n\r\n    const getDefaultOptions = () => ({\r\n      series: [\r\n        {\r\n          type: 'gauge',\r\n          startAngle: 90,\r\n          endAngle: -270,\r\n          radius: '85%',\r\n          pointer: {\r\n            show: false,\r\n          },\r\n          center: ['50%', '50%'],\r\n          progress: {\r\n            show: true,\r\n            overlap: false,\r\n            roundCap: true,\r\n            clip: false,\r\n            itemStyle: {\r\n              color: utils.getColor('info'),\r\n            },\r\n          },\r\n          axisLine: {\r\n            lineStyle: {\r\n              width: 8,\r\n              color: [[1, utils.getColor('gray-200')]],\r\n            },\r\n          },\r\n          splitLine: {\r\n            show: false,\r\n          },\r\n          axisTick: {\r\n            show: false,\r\n          },\r\n          axisLabel: {\r\n            show: false,\r\n          },\r\n          data: [79],\r\n          detail: {\r\n            show: false,\r\n          },\r\n          animationDuration: 2000,\r\n        },\r\n        {\r\n          type: 'gauge',\r\n          startAngle: 90,\r\n          endAngle: -270,\r\n          radius: '70%',\r\n          pointer: {\r\n            show: false,\r\n          },\r\n          center: ['50%', '50%'],\r\n          progress: {\r\n            show: true,\r\n            overlap: false,\r\n            roundCap: true,\r\n            clip: false,\r\n            itemStyle: {\r\n              color: utils.getColor('primary'),\r\n            },\r\n          },\r\n          axisLine: {\r\n            lineStyle: {\r\n              width: 8,\r\n              color: [[1, utils.getColor('gray-200')]],\r\n            },\r\n          },\r\n          splitLine: {\r\n            show: false,\r\n          },\r\n          axisTick: {\r\n            show: false,\r\n          },\r\n          axisLabel: {\r\n            show: false,\r\n          },\r\n          data: [85],\r\n          detail: {\r\n            show: false,\r\n          },\r\n          animationDuration: 2000,\r\n        },\r\n        {\r\n          type: 'gauge',\r\n          startAngle: 90,\r\n          endAngle: -270,\r\n          radius: '55%',\r\n          pointer: {\r\n            show: false,\r\n          },\r\n          center: ['50%', '50%'],\r\n          progress: {\r\n            show: true,\r\n            overlap: false,\r\n            roundCap: true,\r\n            clip: false,\r\n            itemStyle: {\r\n              color: utils.getColor('success'),\r\n            },\r\n          },\r\n          axisLine: {\r\n            lineStyle: {\r\n              width: 8,\r\n              color: [[1, utils.getColor('gray-200')]],\r\n            },\r\n          },\r\n          splitLine: {\r\n            show: false,\r\n          },\r\n          axisTick: {\r\n            show: false,\r\n          },\r\n          axisLabel: {\r\n            show: false,\r\n          },\r\n          data: [70],\r\n          detail: {\r\n            show: false,\r\n          },\r\n          animationDuration: 2000,\r\n        },\r\n      ],\r\n    });\r\n\r\n    echartSetOption(chart, userOptions, getDefaultOptions);\r\n  }\r\n};\r\n\r\nexport default echartsGaugeMultiRingChartInit;\r\n", "import utils from '../../../utils';\r\nimport { echartSetOption } from '../echarts-utils';\r\n\r\n/* -------------------------------------------------------------------------- */\r\n/*                          Echarts Gauge Progress Chart                      */\r\n/* -------------------------------------------------------------------------- */\r\n\r\nconst echartsGaugeMultiTitleChartInit = () => {\r\n  const $gaugeMultiTitleChartEl = document.querySelector('.echart-gauge-multi-title-chart-example');\r\n\r\n  if ($gaugeMultiTitleChartEl) {\r\n    // Get options from data attribute\r\n    const userOptions = utils.getData($gaugeMultiTitleChartEl, 'options');\r\n    const chart = window.echarts.init($gaugeMultiTitleChartEl);\r\n\r\n    const tooltipFormatter = params => `\r\n      <div>\r\n          <h6 class=\"fs-10 text-700 mb-0\">\r\n            <span class=\"fas fa-circle me-1\" style='color:${params[0].color}'></span>\r\n            ${params[0].name} : ${params[0].value}\r\n          </h6>\r\n      </div>\r\n      `;\r\n\r\n    const getDefaultOptions = () => ({\r\n      tooltip: {\r\n        trigger: 'axis',\r\n        padding: [7, 10],\r\n        backgroundColor: utils.getGrays()['100'],\r\n        borderColor: utils.getGrays()['300'],\r\n        textStyle: { color: utils.getGrays()['1100'] },\r\n        borderWidth: 1,\r\n        formatter: tooltipFormatter,\r\n        transitionDuration: 0,\r\n        axisPointer: {\r\n          type: 'none'\r\n        }\r\n      },\r\n      series: [\r\n        {\r\n          type: 'gauge',\r\n          radius: '100%',\r\n          anchor: {\r\n            show: true,\r\n            showAbove: true,\r\n            size: 18,\r\n            itemStyle: {\r\n              color: utils.getColor('warning')\r\n            }\r\n          },\r\n\r\n          progress: {\r\n            show: true,\r\n            overlap: true,\r\n            roundCap: true\r\n          },\r\n          axisLine: {\r\n            roundCap: true\r\n          },\r\n          axisTick: {\r\n            show: false\r\n          },\r\n          splitLine: {\r\n            lineStyle: {\r\n              width: 2,\r\n              color: utils.getGrays()['600']\r\n            }\r\n          },\r\n          axisLabel: {\r\n            distance: 25,\r\n            color: utils.getGrays()['600']\r\n          },\r\n          data: [\r\n            {\r\n              value: 20,\r\n              name: 'Perfect',\r\n              title: {\r\n                offsetCenter: ['-40%', '80%']\r\n              },\r\n              detail: {\r\n                offsetCenter: ['-40%', '95%']\r\n              },\r\n              itemStyle: {\r\n                color: utils.getColor('primary')\r\n              }\r\n            },\r\n            {\r\n              value: 40,\r\n              name: 'Good',\r\n              title: {\r\n                offsetCenter: ['0%', '80%']\r\n              },\r\n              detail: {\r\n                offsetCenter: ['0%', '95%']\r\n              },\r\n\r\n              itemStyle: {\r\n                color: utils.getColor('success')\r\n              }\r\n            },\r\n            {\r\n              value: 60,\r\n              name: 'Commonly',\r\n              title: {\r\n                offsetCenter: ['40%', '80%']\r\n              },\r\n              detail: {\r\n                offsetCenter: ['40%', '95%']\r\n              },\r\n\r\n              itemStyle: {\r\n                color: utils.getColor('warning')\r\n              }\r\n            }\r\n          ],\r\n          title: {\r\n            fontSize: 14,\r\n            color: utils.getGrays()['600']\r\n          },\r\n          detail: {\r\n            width: 40,\r\n            height: 14,\r\n            fontSize: 14,\r\n            color: '#fff',\r\n            backgroundColor: 'auto',\r\n            borderRadius: 3,\r\n            formatter: '{value}%'\r\n          }\r\n        }\r\n      ]\r\n    });\r\n\r\n    echartSetOption(chart, userOptions, getDefaultOptions);\r\n  }\r\n};\r\n\r\nexport default echartsGaugeMultiTitleChartInit;\r\n", "import utils from '../../../utils';\r\nimport { echartSetOption } from '../echarts-utils';\r\n\r\n/* -------------------------------------------------------------------------- */\r\n/*                          Echarts Gauge Progress Chart                      */\r\n/* -------------------------------------------------------------------------- */\r\n\r\nconst echartsGaugeProgressChartInit = () => {\r\n  const $gaugeProgressChartEl = document.querySelector('.echart-gauge-progress-chart-example');\r\n\r\n  if ($gaugeProgressChartEl) {\r\n    // Get options from data attribute\r\n    const userOptions = utils.getData($gaugeProgressChartEl, 'options');\r\n    const chart = window.echarts.init($gaugeProgressChartEl);\r\n\r\n    const tooltipFormatter = params => `\r\n      <div>\r\n          <h6 class=\"fs-10 text-700 mb-0\">\r\n            <span class=\"fas fa-circle me-1\" style='color:${params[0].color}'></span>\r\n            ${params[0].name} : ${params[0].value}\r\n          </h6>\r\n      </div>\r\n      `;\r\n\r\n    const getDefaultOptions = () => ({\r\n      tooltip: {\r\n        trigger: 'axis',\r\n        padding: [7, 10],\r\n        backgroundColor: utils.getGrays()['100'],\r\n        borderColor: utils.getGrays()['300'],\r\n        textStyle: { color: utils.getGrays()['1100'] },\r\n        borderWidth: 1,\r\n        formatter: tooltipFormatter,\r\n        transitionDuration: 0,\r\n        axisPointer: {\r\n          type: 'none'\r\n        }\r\n      },\r\n      series: [\r\n        {\r\n          type: 'gauge',\r\n          center: ['50%', '60%'],\r\n          radius: '100%',\r\n          startAngle: 180,\r\n          endAngle: 0,\r\n          progress: {\r\n            show: true,\r\n            width: 18,\r\n            itemStyle: {\r\n              color: utils.getColor('info')\r\n            }\r\n          },\r\n          itemStyle: {\r\n            color: utils.getColor('info'),\r\n            shadowColor: utils.rgbaColor(utils.getColor('primary'), 0.5),\r\n            shadowBlur: 10,\r\n            shadowOffsetX: 2,\r\n            shadowOffsetY: 2\r\n          },\r\n          axisLine: {\r\n            lineStyle: {\r\n              width: 18\r\n            }\r\n          },\r\n          axisTick: {\r\n            show: false\r\n          },\r\n          splitLine: {\r\n            lineStyle: {\r\n              width: 2,\r\n              color: utils.getGrays()['600']\r\n            }\r\n          },\r\n          axisLabel: {\r\n            distance: 25,\r\n            color: utils.getGrays()['600']\r\n          },\r\n          anchor: {\r\n            show: true,\r\n            showAbove: true,\r\n            size: 25,\r\n            itemStyle: {\r\n              color: utils.getColor('info')\r\n            }\r\n          },\r\n          title: {\r\n            show: false\r\n          },\r\n          detail: {\r\n            valueAnimation: true,\r\n            fontSize: 80,\r\n            offsetCenter: [0, '70%']\r\n          },\r\n          data: [\r\n            {\r\n              value: 70,\r\n              detail: {\r\n                fontSize: 30,\r\n                color: utils.getGrays()['600'],\r\n                offsetCenter: [0, '40%']\r\n              }\r\n            }\r\n          ]\r\n        }\r\n      ]\r\n    });\r\n\r\n    echartSetOption(chart, userOptions, getDefaultOptions);\r\n  }\r\n};\r\n\r\nexport default echartsGaugeProgressChartInit;\r\n", "import utils from '../../../utils';\r\nimport { echartSetOption } from '../echarts-utils';\r\n\r\n/* -------------------------------------------------------------------------- */\r\n/*                          Echarts Gauge Progress Chart                      */\r\n/* -------------------------------------------------------------------------- */\r\n\r\nconst echartsGaugeRingChartInit = () => {\r\n  const $gaugeRingChartEl = document.querySelector('.echart-gauge-ring-chart-example');\r\n\r\n  if ($gaugeRingChartEl) {\r\n    // Get options from data attribute\r\n    const userOptions = utils.getData($gaugeRingChartEl, 'options');\r\n    const chart = window.echarts.init($gaugeRingChartEl);\r\n\r\n    const tooltipFormatter = params => `\r\n      <div>\r\n          <h6 class=\"fs-10 text-700 mb-0\">\r\n            <span class=\"fas fa-circle me-1\" style='color:${params[0].color}'></span>\r\n            ${params[0].name} : ${params[0].value}\r\n          </h6>\r\n      </div>\r\n      `;\r\n\r\n    const getDefaultOptions = () => ({\r\n      tooltip: {\r\n        trigger: 'axis',\r\n        padding: [7, 10],\r\n        backgroundColor: utils.getGrays()['100'],\r\n        borderColor: utils.getGrays()['300'],\r\n        textStyle: { color: utils.getGrays()['1100'] },\r\n        borderWidth: 1,\r\n        formatter: tooltipFormatter,\r\n        transitionDuration: 0,\r\n        axisPointer: {\r\n          type: 'none'\r\n        }\r\n      },\r\n      series: [\r\n        {\r\n          type: 'gauge',\r\n          radius: '100%',\r\n          startAngle: 90,\r\n          endAngle: -270,\r\n          pointer: {\r\n            show: false\r\n          },\r\n          progress: {\r\n            show: true,\r\n            overlap: false,\r\n            roundCap: true,\r\n            clip: false,\r\n            itemStyle: {\r\n              borderWidth: 1,\r\n              borderColor: utils.getGrays()['500']\r\n            }\r\n          },\r\n          axisLine: {\r\n            lineStyle: {\r\n              width: 18\r\n            }\r\n          },\r\n          splitLine: {\r\n            show: false,\r\n            distance: 0,\r\n            length: 10\r\n          },\r\n          axisTick: {\r\n            show: false\r\n          },\r\n          axisLabel: {\r\n            show: false,\r\n            distance: 50\r\n          },\r\n          data: [\r\n            {\r\n              value: 80,\r\n              title: {\r\n                offsetCenter: ['0%', '0%']\r\n              },\r\n              detail: {\r\n                offsetCenter: ['0%', '0%']\r\n              },\r\n              itemStyle: {\r\n                color: utils.getColor('primary')\r\n              }\r\n            }\r\n          ],\r\n          title: {\r\n            fontSize: 14\r\n          },\r\n          detail: {\r\n            width: 50,\r\n            height: 14,\r\n            fontSize: 20,\r\n            color: 'auto',\r\n            formatter: '{value}%'\r\n          }\r\n        }\r\n      ]\r\n    });\r\n\r\n    echartSetOption(chart, userOptions, getDefaultOptions);\r\n  }\r\n};\r\n\r\nexport default echartsGaugeRingChartInit;\r\n", "import utils from '../../../utils';\r\nimport { echartSetOption } from '../echarts-utils';\r\n\r\n/* -------------------------------------------------------------------------- */\r\n/*                       Echarts Gradient Bar Chart                           */\r\n/* -------------------------------------------------------------------------- */\r\n\r\nconst echartsGradientBarChartInit = () => {\r\n  const $gradientBarChartEl = document.querySelector('.echart-gradient-bar-chart-example');\r\n\r\n  if ($gradientBarChartEl) {\r\n    // Get options from data attribute\r\n    const userOptions = utils.getData($gradientBarChartEl, 'options');\r\n    const chart = window.echarts.init($gradientBarChartEl);\r\n\r\n    const tooltipFormatter = params => `<div> \r\n          <h6 class=\"fs-10 text-700 mb-0\">\r\n          <span class=\"dot me-1 fs-11  bg-primary\" ></span> ${params[0].name} : ${params[0].value} \r\n           </h6>\r\n        </div> `;\r\n\r\n    const dataAxis = [\r\n      'A',\r\n      'B',\r\n      'C',\r\n      'D',\r\n      'E',\r\n      'F',\r\n      'G',\r\n      'H',\r\n      'I',\r\n      'J',\r\n      'K',\r\n      'L',\r\n      'M',\r\n      'N',\r\n      'O',\r\n      'P',\r\n      'Q',\r\n      'R',\r\n      'S',\r\n      'T'\r\n    ];\r\n    const data = [\r\n      220, 182, 191, 234, 290, 330, 310, 123, 442, 321, 90, 149, 210, 122, 133, 334, 198, 123, 125,\r\n      220\r\n    ];\r\n\r\n    const getDefaultOptions = () => ({\r\n      tooltip: {\r\n        trigger: 'axis',\r\n        padding: [7, 10],\r\n        backgroundColor: utils.getGrays()['100'],\r\n        borderColor: utils.getGrays()['300'],\r\n        textStyle: { color: utils.getGrays()['1100'] },\r\n        borderWidth: 1,\r\n        transitionDuration: 0,\r\n        axisPointer: {\r\n          type: 'none'\r\n        },\r\n        formatter: tooltipFormatter\r\n      },\r\n      title: {\r\n        text: 'Gradient and Clickable bar chart',\r\n        textStyle: {\r\n          color: utils.getGrays()['600']\r\n        },\r\n        left: 'center'\r\n      },\r\n      xAxis: {\r\n        data: dataAxis,\r\n        axisLabel: {\r\n          inside: true,\r\n          textStyle: {\r\n            color: '#fff'\r\n          }\r\n        },\r\n        axisTick: {\r\n          show: false\r\n        },\r\n        axisLine: {\r\n          show: false\r\n        },\r\n        z: 10\r\n      },\r\n      yAxis: {\r\n        axisLine: {\r\n          show: false\r\n        },\r\n        axisTick: {\r\n          show: false\r\n        },\r\n        axisLabel: {\r\n          textStyle: {\r\n            color: utils.getGrays()['600']\r\n          }\r\n        },\r\n        splitLine: {\r\n          show: true,\r\n          lineStyle: {\r\n            color: utils.getColor()['300']\r\n          }\r\n        }\r\n      },\r\n      dataZoom: [\r\n        {\r\n          type: 'inside'\r\n        }\r\n      ],\r\n      series: [\r\n        {\r\n          type: 'bar',\r\n          name: 'Total',\r\n          showBackground: true,\r\n          itemStyle: {\r\n            color: new window.echarts.graphic.LinearGradient(0, 0, 0, 1, [\r\n              { offset: 0, color: utils.getColor('info') },\r\n              { offset: 0.5, color: utils.getColor('primary') },\r\n              { offset: 1, color: utils.getColor('primary') }\r\n            ]),\r\n            barBorderRadius: [3, 3, 0, 0]\r\n          },\r\n          emphasis: {\r\n            itemStyle: {\r\n              color: new window.echarts.graphic.LinearGradient(0, 0, 0, 1, [\r\n                { offset: 0, color: utils.getColor('primary') },\r\n                { offset: 0.7, color: utils.getColor('primary') },\r\n                { offset: 1, color: utils.getColor('info') }\r\n              ])\r\n            }\r\n          },\r\n          data\r\n        }\r\n      ],\r\n      grid: {\r\n        right: 5,\r\n        left: 5,\r\n        bottom: 5,\r\n        top: '10%',\r\n        containLabel: true\r\n      }\r\n    });\r\n\r\n    echartSetOption(chart, userOptions, getDefaultOptions);\r\n\r\n    const zoomSize = 6;\r\n    chart.on('click', params => {\r\n      chart.dispatchAction({\r\n        type: 'dataZoom',\r\n        startValue: dataAxis[Math.max(params.dataIndex - zoomSize / 2, 0)],\r\n        endValue: dataAxis[Math.min(params.dataIndex + zoomSize / 2, data.length - 1)]\r\n      });\r\n    });\r\n  }\r\n};\r\n\r\nexport default echartsGradientBarChartInit;\r\n", "import utils from '../../../utils';\r\nimport { echartSetOption } from '../echarts-utils';\r\n\r\n/* -------------------------------------------------------------------------- */\r\n/*                                Market Share                                */\r\n/* -------------------------------------------------------------------------- */\r\n\r\nconst echartsHeatMapChartInit = () => {\r\n  const ECHART_HEATMAP_CHART = '.echart-heatmap-chart-example';\r\n  const $echartHeatmapChart = document.querySelector(ECHART_HEATMAP_CHART);\r\n  const hours = ['12a', '2a', '4a', '6a', '8a', '10a', '12p', '2p', '4p', '6p', '8p', '10p'];\r\n  const days = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];\r\n\r\n  const data = [];\r\n  for (let i = 0; i < 7; i += 1) {\r\n    for (let j = 0; j < 12; j += 1) {\r\n      data.push([j, i, utils.getRandomNumber(5, 12)]);\r\n    }\r\n  }\r\n\r\n  if ($echartHeatmapChart) {\r\n    const userOptions = utils.getData($echartHeatmapChart, 'options');\r\n    const chart = window.echarts.init($echartHeatmapChart);\r\n\r\n    const getDefaultOptions = () => ({\r\n      tooltip: {\r\n        position: 'top',\r\n        padding: [7, 10],\r\n        backgroundColor: utils.getGrays()['100'],\r\n        borderColor: utils.getGrays()['300'],\r\n        textStyle: { color: utils.getGrays()['1100'] },\r\n        borderWidth: 1\r\n      },\r\n      grid: {\r\n        right: 5,\r\n        left: 5,\r\n        top: 5,\r\n        bottom: '15%',\r\n        containLabel: true\r\n      },\r\n      xAxis: {\r\n        type: 'category',\r\n        data: hours,\r\n        splitArea: {\r\n          show: true\r\n        },\r\n        axisLabel: {\r\n          color: utils.getGrays()['600']\r\n        },\r\n        axisLine: {\r\n          show: true,\r\n          lineStyle: {\r\n            color: utils.getGrays()['400']\r\n          }\r\n        }\r\n      },\r\n      yAxis: {\r\n        type: 'category',\r\n        data: days,\r\n        axisLabel: {\r\n          formatter: value => value.substring(0, 3),\r\n          color: utils.getGrays()['600']\r\n        },\r\n        splitArea: {\r\n          show: true\r\n        },\r\n        axisLine: {\r\n          show: true,\r\n          lineStyle: {\r\n            color: utils.getGrays()['400']\r\n          }\r\n        }\r\n      },\r\n      visualMap: {\r\n        min: 0,\r\n        max: 10,\r\n        calculable: true,\r\n        orient: 'horizontal',\r\n        left: 'center',\r\n        bottom: '0%',\r\n        textStyle: {\r\n          color: utils.getGrays()['600'],\r\n          fontWeight: 500\r\n        },\r\n        inRange: {\r\n          color: [\r\n            utils.rgbaColor(utils.getColors().primary, 1),\r\n            utils.rgbaColor(utils.getColors().info, 1),\r\n            utils.rgbaColor(utils.getColors().success, 1)\r\n            // utils.rgbaColor(utils.getColors()['warning'], 1),\r\n            // utils.rgbaColor(utils.getColors()['danger'], 1)\r\n          ]\r\n        }\r\n      },\r\n      series: [\r\n        {\r\n          type: 'heatmap',\r\n          data,\r\n          label: {\r\n            show: true\r\n          },\r\n          emphasis: {\r\n            itemStyle: {\r\n              shadowBlur: 10,\r\n              shadowColor: utils.rgbaColor(utils.getColors().emphasis, 0.5)\r\n            }\r\n          }\r\n        }\r\n      ]\r\n    });\r\n\r\n    echartSetOption(chart, userOptions, getDefaultOptions);\r\n  }\r\n};\r\n\r\nexport default echartsHeatMapChartInit;\r\n", "import utils from '../../../utils';\r\nimport { echartSetOption } from '../echarts-utils';\r\n\r\n/* -------------------------------------------------------------------------- */\r\n/*                                Market Share                                */\r\n/* -------------------------------------------------------------------------- */\r\n\r\nconst echartsHeatMapSingleSeriesChartInit = () => {\r\n  const ECHART_HEATMAP_CHART = '.echart-heatmap-single-series-chart';\r\n  const $echartHeatmapChart = document.querySelector(ECHART_HEATMAP_CHART);\r\n  const hours = ['12a', '2a', '4a', '6a', '8a', '10a', '12p', '2p', '4p', '6p', '8p', '10p'];\r\n  const days = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];\r\n\r\n  const data = [];\r\n  for (let i = 0; i < 7; i += 1) {\r\n    for (let j = 0; j < 12; j += 1) {\r\n      data.push([j, i, utils.getRandomNumber(1, 12)]);\r\n    }\r\n  }\r\n\r\n  if ($echartHeatmapChart) {\r\n    const userOptions = utils.getData($echartHeatmapChart, 'options');\r\n    const chart = window.echarts.init($echartHeatmapChart);\r\n\r\n    const getDefaultOptions = () => ({\r\n      gradientColor: [\r\n        utils.rgbaColor(utils.getColors().info, 1),\r\n        utils.rgbaColor(utils.getColors().primary, 1)\r\n      ],\r\n\r\n      tooltip: {\r\n        position: 'top',\r\n        padding: [7, 10],\r\n        backgroundColor: utils.getGrays()['100'],\r\n        borderColor: utils.getGrays()['300'],\r\n        textStyle: { color: utils.getGrays()['1100'] },\r\n        borderWidth: 1\r\n      },\r\n      grid: {\r\n        right: 5,\r\n        left: 5,\r\n        top: 5,\r\n        bottom: 5,\r\n        containLabel: true\r\n      },\r\n      xAxis: {\r\n        axisTick: { show: false },\r\n        type: 'category',\r\n        data: hours,\r\n        splitArea: {\r\n          show: true\r\n        },\r\n        axisLabel: {\r\n          color: utils.getGrays()['600']\r\n        },\r\n        axisLine: {\r\n          show: true,\r\n          lineStyle: {\r\n            color: utils.getGrays()['400']\r\n          }\r\n        }\r\n      },\r\n      yAxis: {\r\n        axisTick: { show: false },\r\n        type: 'category',\r\n        data: days,\r\n        axisLabel: {\r\n          formatter: value => value.substring(0, 3),\r\n          color: utils.getGrays()['600']\r\n        },\r\n        splitArea: {\r\n          show: true\r\n        },\r\n        axisLine: {\r\n          show: true,\r\n          lineStyle: {\r\n            color: utils.getGrays()['400']\r\n          }\r\n        }\r\n      },\r\n      visualMap: {\r\n        show: false,\r\n        min: 0,\r\n        max: 10,\r\n        calculable: true,\r\n        orient: 'horizontal',\r\n        left: 'center',\r\n        bottom: '0%',\r\n        textStyle: {\r\n          color: utils.getGrays()['600'],\r\n          fontWeight: 500\r\n        }\r\n      },\r\n\r\n      series: [\r\n        {\r\n          type: 'heatmap',\r\n          data,\r\n          label: {\r\n            show: true\r\n          },\r\n          itemStyle: {\r\n            borderColor: utils.getGrays()['100'],\r\n            borderWidth: 3\r\n          },\r\n          emphasis: {\r\n            itemStyle: {\r\n              shadowBlur: 10,\r\n              shadowColor: utils.rgbaColor(utils.getColors().emphasis, 0.5)\r\n            }\r\n          }\r\n        }\r\n      ]\r\n    });\r\n\r\n    echartSetOption(chart, userOptions, getDefaultOptions);\r\n  }\r\n};\r\n\r\nexport default echartsHeatMapSingleSeriesChartInit;\r\n", "import utils from '../../../utils';\r\nimport { echartSetOption, tooltipFormatter } from '../echarts-utils';\r\n\r\n/* -------------------------------------------------------------------------- */\r\n/*                       Echarts Horizontal Bar Chart                         */\r\n/* -------------------------------------------------------------------------- */\r\n\r\nconst echartsHorizontalBarChartInit = () => {\r\n  const $horizontalBarChartEl = document.querySelector('.echart-horizontal-bar-chart-example');\r\n\r\n  if ($horizontalBarChartEl) {\r\n    // Get options from data attribute\r\n    const userOptions = utils.getData($horizontalBarChartEl, 'options');\r\n    const chart = window.echarts.init($horizontalBarChartEl);\r\n\r\n    const months = [\r\n      'January',\r\n      'February',\r\n      'March',\r\n      'April',\r\n      'May',\r\n      'June',\r\n      'July',\r\n      'August',\r\n      'September',\r\n      'October',\r\n      'November',\r\n      'December'\r\n    ];\r\n\r\n    const data = [1272, 1301, 1402, 1216, 1086, 1236, 1219, 1330, 1367, 1416, 1297, 1204];\r\n\r\n    const getDefaultOptions = () => ({\r\n      tooltip: {\r\n        trigger: 'axis',\r\n        padding: [7, 10],\r\n        backgroundColor: utils.getGrays()['100'],\r\n        borderColor: utils.getGrays()['300'],\r\n        textStyle: { color: utils.getGrays()['1100'] },\r\n        borderWidth: 1,\r\n        formatter: tooltipFormatter,\r\n        transitionDuration: 0,\r\n        axisPointer: {\r\n          type: 'none'\r\n        }\r\n      },\r\n      xAxis: {\r\n        type: 'value',\r\n        boundaryGap: false,\r\n        axisLine: {\r\n          show: true,\r\n          lineStyle: {\r\n            color: utils.getGrays()['300']\r\n          }\r\n        },\r\n        axisTick: { show: true },\r\n        axisLabel: {\r\n          color: utils.getGrays()['500']\r\n        },\r\n        splitLine: {\r\n          show: false\r\n        },\r\n        min: 600\r\n      },\r\n      yAxis: {\r\n        type: 'category',\r\n        data: months,\r\n        boundaryGap: true,\r\n        axisLabel: {\r\n          formatter: value => value.substring(0, 3),\r\n          show: true,\r\n          color: utils.getGrays()['500'],\r\n          margin: 15\r\n        },\r\n        splitLine: {\r\n          show: true,\r\n          lineStyle: {\r\n            color: utils.getGrays()['200']\r\n          }\r\n        },\r\n        axisTick: { show: false },\r\n        axisLine: {\r\n          lineStyle: {\r\n            color: utils.getGrays()['300']\r\n          }\r\n        }\r\n      },\r\n      series: [\r\n        {\r\n          type: 'bar',\r\n          name: 'Total',\r\n          data,\r\n          lineStyle: { color: utils.getColor('primary') },\r\n          itemStyle: {\r\n            color: utils.getColor('primary'),\r\n            barBorderRadius: [0, 3, 3, 0]\r\n          },\r\n          showSymbol: false,\r\n          symbol: 'circle',\r\n          smooth: false,\r\n          hoverAnimation: true\r\n        }\r\n      ],\r\n      grid: { right: '3%', left: '10%', bottom: '10%', top: '5%' }\r\n    });\r\n\r\n    echartSetOption(chart, userOptions, getDefaultOptions);\r\n  }\r\n};\r\n\r\nexport default echartsHorizontalBarChartInit;\r\n", "import utils from '../../../utils';\r\nimport { echartSetOption } from '../echarts-utils';\r\n\r\n/* -------------------------------------------------------------------------- */\r\n/*                             Echarts Line Chart                             */\r\n/* -------------------------------------------------------------------------- */\r\n\r\nconst echartsLineAreaChartInit = () => {\r\n  const $lineAreaChartEl = document.querySelector('.echart-line-area-chart-example');\r\n\r\n  if ($lineAreaChartEl) {\r\n    // Get options from data attribute\r\n    const userOptions = utils.getData($lineAreaChartEl, 'options');\r\n    const chart = window.echarts.init($lineAreaChartEl);\r\n\r\n    const months = [\r\n      'January',\r\n      'February',\r\n      'March',\r\n      'April',\r\n      'May',\r\n      'June',\r\n      'July',\r\n      'August',\r\n      'September',\r\n      'October',\r\n      'November',\r\n      'December'\r\n    ];\r\n\r\n    const data = [1142, 1160, 1179, 946, 1420, 1434, 986, 1247, 1051, 1297, 927, 1282];\r\n\r\n    const tooltipFormatter = params => `\r\n      <div>\r\n          <h6 class=\"fs-10 text-700 mb-0\">\r\n            <span class=\"fas fa-circle me-1\" style='color:${params[0].borderColor}'></span>\r\n            ${params[0].name} : ${params[0].value}\r\n          </h6>\r\n      </div>\r\n      `;\r\n\r\n    const getDefaultOptions = () => ({\r\n      tooltip: {\r\n        trigger: 'axis',\r\n        padding: [7, 10],\r\n        backgroundColor: utils.getGrays()['100'],\r\n        borderColor: utils.getGrays()['300'],\r\n        textStyle: { color: utils.getGrays()['1100'] },\r\n        borderWidth: 1,\r\n        formatter: tooltipFormatter,\r\n        transitionDuration: 0,\r\n        axisPointer: {\r\n          type: 'none'\r\n        }\r\n      },\r\n      xAxis: {\r\n        type: 'category',\r\n        data: months,\r\n        boundaryGap: false,\r\n        axisLine: {\r\n          lineStyle: {\r\n            color: utils.getGrays()['300'],\r\n            type: 'solid'\r\n          }\r\n        },\r\n        axisTick: { show: false },\r\n        axisLabel: {\r\n          color: utils.getGrays()['400'],\r\n          formatter: value => value.substring(0, 3),\r\n          margin: 15\r\n        },\r\n        splitLine: {\r\n          show: false\r\n        }\r\n      },\r\n      yAxis: {\r\n        type: 'value',\r\n        splitLine: {\r\n          lineStyle: {\r\n            color: utils.getGrays()['200']\r\n          }\r\n        },\r\n        boundaryGap: false,\r\n        axisLabel: {\r\n          show: true,\r\n          color: utils.getGrays()['400'],\r\n          margin: 15\r\n        },\r\n        axisTick: { show: false },\r\n        axisLine: { show: false },\r\n        min: 600\r\n      },\r\n      series: [\r\n        {\r\n          type: 'line',\r\n          data,\r\n          itemStyle: {\r\n            color: utils.getGrays()['100'],\r\n            borderColor: utils.getColor('primary'),\r\n            borderWidth: 2\r\n          },\r\n          lineStyle: {\r\n            color: utils.getColor('primary')\r\n          },\r\n          showSymbol: false,\r\n          symbolSize: 10,\r\n          symbol: 'circle',\r\n          smooth: false,\r\n          hoverAnimation: true,\r\n          areaStyle: {\r\n            color: {\r\n              type: 'linear',\r\n              x: 0,\r\n              y: 0,\r\n              x2: 0,\r\n              y2: 1,\r\n              colorStops: [\r\n                {\r\n                  offset: 0,\r\n                  color: utils.rgbaColor(utils.getColors().primary, 0.5)\r\n                },\r\n                {\r\n                  offset: 1,\r\n                  color: utils.rgbaColor(utils.getColors().primary, 0)\r\n                }\r\n              ]\r\n            }\r\n          }\r\n        }\r\n      ],\r\n      grid: {\r\n        right: '3%',\r\n        left: '10%',\r\n        bottom: '10%',\r\n        top: '5%'\r\n      }\r\n    });\r\n\r\n    echartSetOption(chart, userOptions, getDefaultOptions);\r\n  }\r\n};\r\n\r\nexport default echartsLineAreaChartInit;\r\n", "import utils from '../../../utils';\r\nimport { echartSetOption, tooltipFormatter } from '../echarts-utils';\r\n\r\n/* -------------------------------------------------------------------------- */\r\n/*                             Echarts Line Gradient Chart                    */\r\n/* -------------------------------------------------------------------------- */\r\n\r\nconst echartsLineGradientChartInit = () => {\r\n  const $lineGradientChartEl = document.querySelector('.echart-line-gradient-chart-example');\r\n\r\n  if ($lineGradientChartEl) {\r\n    // Get options from data attribute\r\n    const userOptions = utils.getData($lineGradientChartEl, 'options');\r\n    const chart = window.echarts.init($lineGradientChartEl);\r\n\r\n    const data = [\r\n      ['2021-06-05', 116],\r\n      ['2021-06-06', 129],\r\n      ['2021-06-07', 135],\r\n      ['2021-06-08', 86],\r\n      ['2021-06-09', 73],\r\n      ['2021-06-10', 85],\r\n      ['2021-06-11', 73],\r\n      ['2021-06-12', 68],\r\n      ['2021-06-13', 92],\r\n      ['2021-06-14', 130],\r\n      ['2021-06-15', 245],\r\n      ['2021-06-16', 139],\r\n      ['2021-06-17', 115],\r\n      ['2021-06-18', 111],\r\n      ['2021-06-19', 309],\r\n      ['2021-06-20', 206],\r\n      ['2021-06-21', 137],\r\n      ['2021-06-22', 128],\r\n      ['2021-06-23', 85],\r\n      ['2021-06-24', 94],\r\n      ['2021-06-25', 71],\r\n      ['2021-06-26', 106],\r\n      ['2021-06-27', 84],\r\n      ['2021-06-28', 93],\r\n      ['2021-06-29', 85],\r\n      ['2021-06-30', 73],\r\n      ['2021-07-01', 83],\r\n      ['2021-07-02', 125],\r\n      ['2021-07-03', 107],\r\n      ['2021-07-04', 82],\r\n      ['2021-07-05', 44],\r\n      ['2021-07-06', 72],\r\n      ['2021-07-07', 106],\r\n      ['2021-07-08', 107],\r\n      ['2021-07-09', 66],\r\n      ['2021-07-10', 91],\r\n      ['2021-07-11', 92],\r\n      ['2021-07-12', 113],\r\n      ['2021-07-13', 107],\r\n      ['2021-07-14', 131],\r\n      ['2021-07-15', 111],\r\n      ['2021-07-16', 64],\r\n      ['2021-07-17', 69],\r\n      ['2021-07-18', 88],\r\n      ['2021-07-19', 77],\r\n      ['2021-07-20', 83],\r\n      ['2021-07-21', 111],\r\n      ['2021-07-22', 57],\r\n      ['2021-07-23', 55],\r\n      ['2021-07-24', 60]\r\n    ];\r\n\r\n    const dateList = data.map(item => item[0]);\r\n    const valueList = data.map(item => item[1]);\r\n\r\n    const getDefaultOptions = () => ({\r\n      visualMap: {\r\n        show: false,\r\n        type: 'continuous',\r\n        dimension: 0,\r\n        min: 0,\r\n        max: dateList.length - 1,\r\n        color: [utils.getColor('danger'), utils.getColor('warning')]\r\n      },\r\n      tooltip: {\r\n        trigger: 'axis',\r\n        padding: [7, 10],\r\n        backgroundColor: utils.getGrays()['100'],\r\n        borderColor: utils.getGrays()['300'],\r\n        textStyle: { color: utils.getGrays()['1100'] },\r\n        borderWidth: 1,\r\n        transitionDuration: 0,\r\n        axisPointer: {\r\n          type: 'none'\r\n        },\r\n        formatter: tooltipFormatter\r\n      },\r\n      xAxis: {\r\n        type: 'category',\r\n        data: dateList,\r\n        axisLabel: {\r\n          formatter: value => window.dayjs(value).format('MMM DD'),\r\n          color: utils.getGrays()['500'],\r\n          margin: 15\r\n        },\r\n        axisLine: {\r\n          lineStyle: {\r\n            color: utils.getGrays()['300'],\r\n            type: 'solid'\r\n          }\r\n        },\r\n        axisPointer: {\r\n          lineStyle: {\r\n            color: utils.getGrays()['300']\r\n          }\r\n        }\r\n      },\r\n      yAxis: {\r\n        type: 'value',\r\n        axisLabel: {\r\n          show: true,\r\n          color: utils.getGrays()['500'],\r\n          margin: 15\r\n        },\r\n        splitLine: {\r\n          lineStyle: {\r\n            color: utils.getGrays()['200'],\r\n            type: 'dashed'\r\n          }\r\n        }\r\n      },\r\n      grid: { right: '3%', left: '8%', bottom: '10%', top: '5%' },\r\n      series: {\r\n        name: 'Total',\r\n        type: 'line',\r\n        showSymbol: false,\r\n        symbolSize: 10,\r\n        symbol: 'circle',\r\n        data: valueList,\r\n        itemStyle: {\r\n          color: utils.getGrays()['100'],\r\n          borderWidth: 2\r\n        }\r\n      }\r\n    });\r\n\r\n    echartSetOption(chart, userOptions, getDefaultOptions);\r\n  }\r\n};\r\n\r\nexport default echartsLineGradientChartInit;\r\n", "import utils from '../../../utils';\r\nimport { echartSetOption, tooltipFormatter } from '../echarts-utils';\r\n\r\n/* -------------------------------------------------------------------------- */\r\n/*                           Echarts Line Log Chart                           */\r\n/* -------------------------------------------------------------------------- */\r\n\r\nconst echartsLineLogChartInit = () => {\r\n  const $lineLogChartEl = document.querySelector('.echart-line-log-chart-example');\r\n\r\n  if ($lineLogChartEl) {\r\n    // Get options from data attribute\r\n    const userOptions = utils.getData($lineLogChartEl, 'options');\r\n    const chart = window.echarts.init($lineLogChartEl);\r\n\r\n    const getDefaultOptions = () => ({\r\n      tooltip: {\r\n        trigger: 'axis',\r\n        padding: [7, 10],\r\n        backgroundColor: utils.getGrays()['100'],\r\n        borderColor: utils.getGrays()['300'],\r\n        borderWidth: 1,\r\n        transitionDuration: 0,\r\n        axisPointer: {\r\n          type: 'none'\r\n        },\r\n        formatter: tooltipFormatter\r\n      },\r\n      xAxis: {\r\n        type: 'category',\r\n        axisLine: {\r\n          lineStyle: {\r\n            color: utils.getGrays()['300']\r\n          }\r\n        },\r\n        axisLabel: {\r\n          color: utils.getGrays()['600']\r\n        },\r\n        splitLine: { show: false },\r\n        data: Array.from(Array(10).keys()).map(item => item + 1)\r\n      },\r\n      yAxis: {\r\n        type: 'log',\r\n        axisLabel: {\r\n          color: utils.getGrays()['600']\r\n        },\r\n        splitLine: {\r\n          lineStyle: {\r\n            color: utils.getGrays()['200']\r\n          }\r\n        }\r\n      },\r\n      series: [\r\n        {\r\n          name: 'Index Of 3',\r\n          type: 'line',\r\n          data: [1, 3, 9, 27, 81, 247, 741, 2223, 6669],\r\n          symbolSize: 7,\r\n          itemStyle: {\r\n            color: utils.getGrays()['100'],\r\n            borderColor: utils.getColor('danger'),\r\n            borderWidth: 2\r\n          },\r\n          lineStyle: {\r\n            color: utils.getColor('danger')\r\n          },\r\n          symbol: 'circle'\r\n        },\r\n        {\r\n          name: 'Index of 2',\r\n          type: 'line',\r\n          data: [1, 2, 4, 8, 16, 32, 64, 128, 256],\r\n          symbolSize: 7,\r\n          itemStyle: {\r\n            color: utils.getGrays()['100'],\r\n            borderColor: utils.getColor('success'),\r\n            borderWidth: 2\r\n          },\r\n          lineStyle: {\r\n            color: utils.getColor('success')\r\n          },\r\n          symbol: 'circle'\r\n        },\r\n        {\r\n          name: 'Index of 1/2',\r\n          type: 'line',\r\n          data: [1 / 2, 1 / 4, 1 / 8, 1 / 16, 1 / 32, 1 / 64, 1 / 128, 1 / 256, 1 / 512],\r\n          symbolSize: 7,\r\n          itemStyle: {\r\n            color: utils.getGrays()['100'],\r\n            borderColor: utils.getColor('info'),\r\n            borderWidth: 2\r\n          },\r\n          lineStyle: {\r\n            color: utils.getColor('info')\r\n          },\r\n          symbol: 'circle'\r\n        }\r\n      ],\r\n      grid: {\r\n        right: 10,\r\n        left: 5,\r\n        bottom: 5,\r\n        top: 10,\r\n        containLabel: true\r\n      }\r\n    });\r\n\r\n    echartSetOption(chart, userOptions, getDefaultOptions);\r\n  }\r\n};\r\n\r\nexport default echartsLineLogChartInit;\r\n", "import utils from '../../../utils';\r\nimport { getPosition, echartSetOption, tooltipFormatter } from '../echarts-utils';\r\n\r\n/* -------------------------------------------------------------------------- */\r\n/*                        Echarts Line Marker Chart                           */\r\n/* -------------------------------------------------------------------------- */\r\n\r\nconst echartsLineMarkerChartInit = () => {\r\n  const $lineMarkerChartEl = document.querySelector('.echart-line-marker-chart-example');\r\n\r\n  if ($lineMarkerChartEl) {\r\n    // Get options from data attribute\r\n    const userOptions = utils.getData($lineMarkerChartEl, 'options');\r\n    const chart = window.echarts.init($lineMarkerChartEl);\r\n\r\n    const days = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];\r\n\r\n    const getDefaultOptions = () => ({\r\n      color: [\r\n        utils.getColor('primary'),\r\n        utils.getColor('warning')\r\n        // utils.getColor('danger')\r\n      ],\r\n      legend: {\r\n        data: [\r\n          {\r\n            name: 'Max',\r\n            textStyle: {\r\n              color: utils.getGrays()['600']\r\n            }\r\n          },\r\n          {\r\n            name: 'Min',\r\n            textStyle: {\r\n              color: utils.getGrays()['600']\r\n            }\r\n          }\r\n        ]\r\n      },\r\n      tooltip: {\r\n        trigger: 'axis',\r\n        padding: [7, 10],\r\n        backgroundColor: utils.getGrays()['100'],\r\n        borderColor: utils.getGrays()['300'],\r\n        textStyle: { color: utils.getGrays()['1100'] },\r\n        borderWidth: 1,\r\n        transitionDuration: 0,\r\n        position(pos, params, dom, rect, size) {\r\n          return getPosition(pos, params, dom, rect, size);\r\n        },\r\n        axisPointer: {\r\n          type: 'none'\r\n        },\r\n        formatter: tooltipFormatter\r\n      },\r\n      xAxis: {\r\n        type: 'category',\r\n        data: days,\r\n        boundaryGap: false,\r\n        axisLine: {\r\n          lineStyle: {\r\n            color: utils.getGrays()['300'],\r\n            type: 'solid'\r\n          }\r\n        },\r\n        axisTick: { show: false },\r\n        axisLabel: {\r\n          formatter: value => value.substring(0, 3),\r\n          color: utils.getGrays()['400'],\r\n          margin: 15\r\n        },\r\n        splitLine: {\r\n          show: false\r\n        }\r\n      },\r\n      yAxis: {\r\n        type: 'value',\r\n        splitLine: {\r\n          lineStyle: {\r\n            color: utils.getGrays()['200']\r\n          }\r\n        },\r\n        boundaryGap: false,\r\n        axisLabel: {\r\n          show: true,\r\n          color: utils.getGrays()['400'],\r\n          margin: 15\r\n        },\r\n        axisTick: { show: false },\r\n        axisLine: { show: false }\r\n      },\r\n      series: [\r\n        {\r\n          name: 'Max',\r\n          type: 'line',\r\n          data: [10, 11, 13, 11, 12, 9, 12],\r\n          symbolSize: 10,\r\n          itemStyle: {\r\n            color: utils.getGrays()['100'],\r\n            borderColor: utils.getColor('primary'),\r\n            borderWidth: 2\r\n          },\r\n          lineStyle: {\r\n            color: utils.getColor('primary')\r\n          },\r\n          symbol: 'circle',\r\n          markPoint: {\r\n            itemStyle: {\r\n              color: utils.getColor('primary')\r\n            },\r\n            data: [\r\n              { type: 'max', name: 'Max' },\r\n              { type: 'min', name: 'Min' }\r\n            ]\r\n          },\r\n          markLine: {\r\n            lineStyle: {\r\n              color: utils.getColor('primary')\r\n            },\r\n            label: {\r\n              color: utils.getGrays()['600']\r\n            },\r\n            data: [{ type: 'average', name: 'average' }]\r\n          }\r\n        },\r\n        {\r\n          name: 'Min',\r\n          type: 'line',\r\n          data: [1, -2, 2, 5, 3, 2, 0],\r\n          symbolSize: 10,\r\n          itemStyle: {\r\n            color: utils.getGrays()['100'],\r\n            borderColor: utils.getColor('danger'),\r\n            borderWidth: 2\r\n          },\r\n          lineStyle: {\r\n            color: utils.getColor('danger')\r\n          },\r\n          symbol: 'circle',\r\n          markPoint: {\r\n            itemStyle: {\r\n              color: utils.getColor('danger')\r\n            },\r\n            label: {\r\n              color: '#fff'\r\n            },\r\n            data: [{ name: 'Weekly lowest', value: -2, xAxis: 1, yAxis: -1.5 }]\r\n          },\r\n          markLine: {\r\n            lineStyle: {\r\n              color: utils.getColor('danger')\r\n            },\r\n            label: {\r\n              color: utils.getGrays()['600']\r\n            },\r\n            data: [\r\n              { type: 'average', name: 'average' },\r\n              [\r\n                {\r\n                  symbol: 'none',\r\n                  x: '90%',\r\n                  yAxis: 'max'\r\n                },\r\n                {\r\n                  symbol: 'circle',\r\n                  label: {\r\n                    position: 'start',\r\n                    formatter: 'Max'\r\n                  },\r\n                  type: 'max',\r\n                  name: 'Highest point'\r\n                }\r\n              ]\r\n            ]\r\n          }\r\n        }\r\n      ],\r\n      grid: { right: '8%', left: '5%', bottom: '10%', top: '15%' }\r\n    });\r\n\r\n    echartSetOption(chart, userOptions, getDefaultOptions);\r\n  }\r\n};\r\n\r\nexport default echartsLineMarkerChartInit;\r\n", "import utils from '../../../utils';\r\nimport { getPosition, echartSetOption } from '../echarts-utils';\r\n\r\n/* -------------------------------------------------------------------------- */\r\n/*                             Echarts Line Race Chart                        */\r\n/* -------------------------------------------------------------------------- */\r\n\r\nconst echartsLineRaceChartInit = () => {\r\n  const $lineRaceChartEl = document.querySelector('.echart-line-race-chart-example');\r\n\r\n  if ($lineRaceChartEl) {\r\n    // Get options from data attribute\r\n    const userOptions = utils.getData($lineRaceChartEl, 'options');\r\n    const chart = window.echarts.init($lineRaceChartEl);\r\n\r\n    const days = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];\r\n\r\n    const getDefaultOptions = () => ({\r\n      color: [utils.getColor('primary'), utils.getColor('warning')],\r\n      legend: {\r\n        data: [\r\n          {\r\n            name: 'Max',\r\n            textStyle: {\r\n              color: utils.getGrays()['600']\r\n            }\r\n          },\r\n          {\r\n            name: 'Min',\r\n            textStyle: {\r\n              color: utils.getGrays()['600']\r\n            }\r\n          }\r\n        ]\r\n      },\r\n      tooltip: {\r\n        trigger: 'axis',\r\n        padding: [7, 10],\r\n        backgroundColor: utils.getGrays()['100'],\r\n        borderColor: utils.getGrays()['300'],\r\n        textStyle: { color: utils.getGrays()['1100'] },\r\n        borderWidth: 1,\r\n        // formatter: tooltipFormatter,\r\n        transitionDuration: 0,\r\n        position(pos, params, dom, rect, size) {\r\n          return getPosition(pos, params, dom, rect, size);\r\n        },\r\n        axisPointer: {\r\n          type: 'none'\r\n        }\r\n      },\r\n      xAxis: {\r\n        type: 'category',\r\n        data: days,\r\n        boundaryGap: false,\r\n        axisLine: {\r\n          lineStyle: {\r\n            color: utils.getGrays()['300'],\r\n            type: 'solid'\r\n          }\r\n        },\r\n        axisTick: { show: false },\r\n        axisLabel: {\r\n          formatter: value => value.substring(0, 3),\r\n          color: utils.getGrays()['400'],\r\n          margin: 15\r\n        },\r\n        splitLine: {\r\n          show: false\r\n        }\r\n      },\r\n      yAxis: {\r\n        type: 'value',\r\n        splitLine: {\r\n          lineStyle: {\r\n            color: utils.getGrays()['300']\r\n          }\r\n        },\r\n        boundaryGap: false,\r\n        axisLabel: {\r\n          show: true,\r\n          color: utils.getGrays()['400'],\r\n          margin: 15\r\n        },\r\n        axisTick: { show: false },\r\n        axisLine: { show: false }\r\n      },\r\n      series: [\r\n        {\r\n          name: 'Max',\r\n          type: 'line',\r\n          data: [10, 11, 13, 11, 12, 9, 12],\r\n          markPoint: {\r\n            data: [\r\n              { type: 'max', name: 'Max' },\r\n              { type: 'min', name: 'Min' }\r\n            ]\r\n          },\r\n          markLine: {\r\n            label: {\r\n              color: utils.getGrays()['600']\r\n            },\r\n            data: [{ type: 'average', name: 'average' }]\r\n          }\r\n        },\r\n        {\r\n          name: 'Min',\r\n          type: 'line',\r\n          data: [1, -2, 2, 5, 3, 2, 0],\r\n          markPoint: {\r\n            label: {\r\n              color: '#fff'\r\n            },\r\n            data: [{ name: 'Weekly lowest', value: -2, xAxis: 1, yAxis: -1.5 }]\r\n          },\r\n          markLine: {\r\n            label: {\r\n              color: utils.getGrays()['600']\r\n            },\r\n            data: [\r\n              { type: 'average', name: 'average' },\r\n              [\r\n                {\r\n                  symbol: 'none',\r\n                  x: '90%',\r\n                  yAxis: 'max'\r\n                },\r\n                {\r\n                  symbol: 'circle',\r\n                  label: {\r\n                    position: 'start',\r\n                    formatter: 'Max'\r\n                  },\r\n                  type: 'max',\r\n                  name: 'Highest point'\r\n                }\r\n              ]\r\n            ]\r\n          }\r\n        }\r\n      ],\r\n      grid: { right: '8%', left: '5%', bottom: '10%', top: '15%' }\r\n    });\r\n\r\n    echartSetOption(chart, userOptions, getDefaultOptions);\r\n  }\r\n};\r\n\r\nexport default echartsLineRaceChartInit;\r\n", "import utils from '../../../utils';\r\nimport { echartSetOption } from '../echarts-utils';\r\n\r\n/* -------------------------------------------------------------------------- */\r\n/*                    Echarts Line Share Dataset Chart                        */\r\n/* -------------------------------------------------------------------------- */\r\n\r\nconst echartsLineShareDatasetChartInit = () => {\r\n  const $lineShareChartEl = document.querySelector('.echart-line-share-dataset-chart-example');\r\n\r\n  if ($lineShareChartEl) {\r\n    // Get options from data attribute\r\n    const userOptions = utils.getData($lineShareChartEl, 'options');\r\n    const chart = window.echarts.init($lineShareChartEl);\r\n\r\n    const getDefaultOptions = () => ({\r\n      color: [\r\n        utils.getColor('danger'),\r\n        utils.getColor('warning'),\r\n        utils.getColor('info'),\r\n        utils.getColor('primary')\r\n      ],\r\n      legend: {\r\n        top: 0,\r\n        textStyle: {\r\n          color: utils.getGrays()['700']\r\n        }\r\n      },\r\n      tooltip: {\r\n        trigger: 'axis',\r\n        showContent: false\r\n      },\r\n      dataset: {\r\n        source: [\r\n          ['product', '2012', '2013', '2014', '2015', '2016', '2017'],\r\n          ['Milk Tea', 56.5, 82.1, 88.7, 70.1, 53.4, 85.1],\r\n          ['Matcha Latte', 51.1, 51.4, 55.1, 53.3, 73.8, 68.7],\r\n          ['Cheese Cocoa', 40.1, 62.2, 69.5, 36.4, 45.2, 32.5],\r\n          ['Walnut Brownie', 25.2, 37.1, 41.2, 18, 33.9, 49.1]\r\n        ]\r\n      },\r\n      xAxis: {\r\n        type: 'category',\r\n        axisLine: {\r\n          lineStyle: {\r\n            color: utils.getGrays()['300']\r\n          }\r\n        },\r\n        axisLabel: {\r\n          color: utils.getGrays()['600']\r\n        },\r\n        axisPointer: {\r\n          lineStyle: {\r\n            color: utils.getGrays()['300']\r\n          }\r\n        }\r\n      },\r\n      yAxis: {\r\n        gridIndex: 0,\r\n        axisLabel: {\r\n          color: utils.getGrays()['600']\r\n        },\r\n        splitLine: {\r\n          lineStyle: {\r\n            color: utils.getGrays()['200']\r\n          }\r\n        }\r\n      },\r\n      series: [\r\n        {\r\n          type: 'line',\r\n          smooth: true,\r\n          seriesLayoutBy: 'row',\r\n          emphasis: { focus: 'series' },\r\n          symbolSize: 10,\r\n          itemStyle: {\r\n            color: utils.getGrays()['100'],\r\n            borderColor: utils.getColor('danger'),\r\n            borderWidth: 2\r\n          },\r\n          lineStyle: {\r\n            color: utils.getColor('danger')\r\n          },\r\n          symbol: 'circle'\r\n        },\r\n        {\r\n          type: 'line',\r\n          smooth: true,\r\n          seriesLayoutBy: 'row',\r\n          emphasis: { focus: 'series' },\r\n          symbolSize: 10,\r\n          itemStyle: {\r\n            color: utils.getGrays()['100'],\r\n            borderColor: utils.getColor('info'),\r\n            borderWidth: 2\r\n          },\r\n          lineStyle: {\r\n            color: utils.getColor('info')\r\n          },\r\n          symbol: 'circle'\r\n        },\r\n        {\r\n          type: 'line',\r\n          smooth: true,\r\n          seriesLayoutBy: 'row',\r\n          emphasis: { focus: 'series' },\r\n          symbolSize: 10,\r\n          itemStyle: {\r\n            color: utils.getGrays()['100'],\r\n            borderColor: utils.getColor('warning'),\r\n            borderWidth: 2\r\n          },\r\n          lineStyle: {\r\n            color: utils.getColor('warning')\r\n          },\r\n          symbol: 'circle'\r\n        },\r\n        {\r\n          type: 'line',\r\n          smooth: true,\r\n          seriesLayoutBy: 'row',\r\n          emphasis: { focus: 'series' },\r\n          symbolSize: 10,\r\n          itemStyle: {\r\n            color: utils.getGrays()['100'],\r\n            borderColor: utils.getColor('primary'),\r\n            borderWidth: 2\r\n          },\r\n          lineStyle: {\r\n            color: utils.getColor('primary')\r\n          },\r\n          symbol: 'circle'\r\n        },\r\n        {\r\n          type: 'pie',\r\n          id: 'pie',\r\n          radius: '30%',\r\n          center: ['50%', '28%'],\r\n          emphasis: { focus: 'data' },\r\n          label: {\r\n            formatter: '{b}: {@2012} ({d}%)',\r\n            color: utils.getGrays()['600']\r\n          },\r\n          encode: {\r\n            itemName: 'product',\r\n            value: '2012',\r\n            tooltip: '2012'\r\n          }\r\n        }\r\n      ],\r\n      grid: {\r\n        right: 10,\r\n        left: 5,\r\n        bottom: 5,\r\n        top: '55%',\r\n        containLabel: true\r\n      }\r\n    });\r\n\r\n    echartSetOption(chart, userOptions, getDefaultOptions);\r\n\r\n    chart.on('updateAxisPointer', event => {\r\n      const xAxisInfo = event.axesInfo[0];\r\n      if (xAxisInfo) {\r\n        const dimension = xAxisInfo.value + 1;\r\n        chart.setOption({\r\n          series: {\r\n            id: 'pie',\r\n            label: {\r\n              formatter: `{b}: {@[${dimension}]} ({d}%)`\r\n            },\r\n            encode: {\r\n              value: dimension,\r\n              tooltip: dimension\r\n            }\r\n          }\r\n        });\r\n      }\r\n    });\r\n  }\r\n};\r\n\r\nexport default echartsLineShareDatasetChartInit;\r\n", "import utils from '../../../utils';\r\nimport { echartSetOption } from '../echarts-utils';\r\n\r\n/* -------------------------------------------------------------------------- */\r\n/*                                Session By Country Map                      */\r\n/* -------------------------------------------------------------------------- */\r\n\r\nconst echartsUsaMapInit = () => {\r\n  const $usaMapEl = document.querySelector('.echart-map-usa-example');\r\n\r\n  const data = [\r\n    { name: 'Alabama', value: 4822023 },\r\n    { name: 'Alaska', value: 731449 },\r\n    { name: 'Arizona', value: 6553255 },\r\n    { name: 'Arkansas', value: 2949131 },\r\n    { name: 'California', value: 38041430 },\r\n    { name: 'Colorado', value: 5187582 },\r\n    { name: 'Connecticut', value: 3590347 },\r\n    { name: 'Delaware', value: 917092 },\r\n    { name: 'District of Columbia', value: 632323 },\r\n    { name: 'Florida', value: 19317568 },\r\n    { name: 'Georgia', value: 9919945 },\r\n    { name: 'Hawaii', value: 1392313 },\r\n    { name: 'Idaho', value: 1595728 },\r\n    { name: 'Illinois', value: 12875255 },\r\n    { name: 'Indiana', value: 6537334 },\r\n    { name: 'Iowa', value: 3074186 },\r\n    { name: 'Kansas', value: 2885905 },\r\n    { name: 'Kentucky', value: 4380415 },\r\n    { name: 'Louisiana', value: 4601893 },\r\n    { name: 'Maine', value: 1329192 },\r\n    { name: 'Maryland', value: 5884563 },\r\n    { name: 'Massachusetts', value: 6646144 },\r\n    { name: 'Michigan', value: 9883360 },\r\n    { name: 'Minnesota', value: 5379139 },\r\n    { name: 'Mississippi', value: 2984926 },\r\n    { name: 'Missouri', value: 6021988 },\r\n    { name: 'Montana', value: 1005141 },\r\n    { name: 'Nebraska', value: 1855525 },\r\n    { name: 'Nevada', value: 2758931 },\r\n    { name: 'New Hampshire', value: 1320718 },\r\n    { name: 'New Jersey', value: 8864590 },\r\n    { name: 'New Mexico', value: 2085538 },\r\n    { name: 'New York', value: 19570261 },\r\n    { name: 'North Carolina', value: 9752073 },\r\n    { name: 'North Dakota', value: 699628 },\r\n    { name: 'Ohio', value: 11544225 },\r\n    { name: 'Oklahoma', value: 3814820 },\r\n    { name: 'Oregon', value: 3899353 },\r\n    { name: 'Pennsylvania', value: 12763536 },\r\n    { name: 'Rhode Island', value: 1050292 },\r\n    { name: 'South Carolina', value: 4723723 },\r\n    { name: 'South Dakota', value: 833354 },\r\n    { name: 'Tennessee', value: 6456243 },\r\n    { name: 'Texas', value: 26059203 },\r\n    { name: 'Utah', value: 2855287 },\r\n    { name: 'Vermont', value: 626011 },\r\n    { name: 'Virginia', value: 8185867 },\r\n    { name: 'Washington', value: 6897012 },\r\n    { name: 'West Virginia', value: 1855413 },\r\n    { name: 'Wisconsin', value: 5726398 },\r\n    { name: 'Wyoming', value: 576412 },\r\n    { name: 'Puerto Rico', value: 3667084 }\r\n  ];\r\n\r\n  if ($usaMapEl) {\r\n    const userOptions = utils.getData($usaMapEl, 'options');\r\n    const chart = window.echarts.init($usaMapEl);\r\n\r\n    const getDefaultOptions = () => ({\r\n      tooltip: {\r\n        trigger: 'item',\r\n        padding: [7, 10],\r\n        backgroundColor: utils.getGrays()['100'],\r\n        borderColor: utils.getGrays()['300'],\r\n        textStyle: { color: utils.getGrays()['1100'] },\r\n        borderWidth: 1,\r\n        transitionDuration: 0,\r\n        formatter: params => `<strong>${params.data.name} :</strong> ${params.data.value}`\r\n      },\r\n      toolbox: {\r\n        show: false,\r\n        feature: {\r\n          restore: {}\r\n        }\r\n      },\r\n      visualMap: {\r\n        left: 'right',\r\n        min: 500000,\r\n        max: 38000000,\r\n        inRange: {\r\n          color: [utils.getColor('primary'), utils.getColor('info')]\r\n        },\r\n        text: ['High', 'Low'],\r\n        calculable: true,\r\n        textStyle: {\r\n          color: utils.getGrays()['600']\r\n        },\r\n        formatter: value => `${value / 1000}k`\r\n      },\r\n      series: [\r\n        {\r\n          left: 10,\r\n          name: 'USA PopEstimates',\r\n          type: 'map',\r\n          zoom: 1.2,\r\n          roam: true,\r\n          scaleLimit: {\r\n            min: 1,\r\n            max: 5\r\n          },\r\n          itemStyle: {\r\n            borderColor: utils.getGrays()['300']\r\n          },\r\n          label: {\r\n            color: '#fff'\r\n          },\r\n          map: 'USA',\r\n          emphasis: {\r\n            label: {\r\n              show: true,\r\n              color: '#fff'\r\n            },\r\n\r\n            itemStyle: {\r\n              areaColor: utils.getColor('warning')\r\n            }\r\n          },\r\n          data\r\n        }\r\n      ]\r\n    });\r\n\r\n    echartSetOption(chart, userOptions, getDefaultOptions);\r\n    document.querySelector('.usa-map-reset').addEventListener('click', () => {\r\n      chart.dispatchAction({\r\n        type: 'restore'\r\n      });\r\n    });\r\n  }\r\n};\r\n\r\nexport default echartsUsaMapInit;\r\n", "import utils from '../../../utils';\r\nimport { echartSetOption } from '../echarts-utils';\r\n\r\n/* -------------------------------------------------------------------------- */\r\n/*                            Bandwidth Saved                                 */\r\n/* -------------------------------------------------------------------------- */\r\n\r\nconst echartsNestedPiesChartInit = () => {\r\n  const $echartsNestedPies = document.querySelector('.echarts-nested-pies-chart-example');\r\n\r\n  if ($echartsNestedPies) {\r\n    const userOptions = utils.getData($echartsNestedPies, 'options');\r\n    const chart = window.echarts.init($echartsNestedPies);\r\n\r\n    const marketingExpenses = [\r\n      {\r\n        value: 412600,\r\n        name: 'Offline Marketing',\r\n        itemStyle: { color: utils.getColor('primary') },\r\n        label: {\r\n          rich: {\r\n            per: {\r\n              color: '#1C4F93'\r\n            }\r\n          }\r\n        }\r\n      },\r\n      {\r\n        value: 641500,\r\n        name: 'Digital Marketing',\r\n        itemStyle: {\r\n          color: utils.rgbaColor(utils.getColor('info'), 0.35)\r\n        },\r\n        label: {\r\n          rich: {\r\n            per: {\r\n              color: '#1978A2'\r\n            }\r\n          }\r\n        }\r\n      }\r\n    ];\r\n\r\n    const detailedExpenses = [\r\n      {\r\n        value: 91600,\r\n        name: 'Event Sponsorship',\r\n        itemStyle: {\r\n          color: utils.rgbaColor(utils.getColor('primary'), 0.4)\r\n        }\r\n      },\r\n      {\r\n        value: 183000,\r\n        name: 'Outrich Event',\r\n        itemStyle: {\r\n          color: utils.rgbaColor(utils.getColor('primary'), 0.6)\r\n        }\r\n      },\r\n      {\r\n        value: 138000,\r\n        name: 'Ad Campaign',\r\n        itemStyle: {\r\n          color: utils.rgbaColor(utils.getColor('primary'), 0.8)\r\n        }\r\n      },\r\n      {\r\n        value: 183000,\r\n        name: 'Social Media',\r\n        itemStyle: {\r\n          color: utils.rgbaColor(utils.getColor('info'), 0.2)\r\n        }\r\n      },\r\n      {\r\n        value: 45900,\r\n        name: 'Google Ads',\r\n        itemStyle: {\r\n          color: utils.rgbaColor(utils.getColor('info'), 0.35)\r\n        }\r\n      },\r\n      {\r\n        value: 138000,\r\n        name: 'Influencer Marketing',\r\n        itemStyle: {\r\n          color: utils.rgbaColor(utils.getColor('info'), 0.5)\r\n        }\r\n      },\r\n      {\r\n        value: 183000,\r\n        name: 'Email Marketing',\r\n        itemStyle: {\r\n          color: utils.rgbaColor(utils.getColor('info'), 0.7)\r\n        }\r\n      },\r\n      {\r\n        value: 91600,\r\n        name: 'Generate Backlinks',\r\n        itemStyle: {\r\n          color: utils.rgbaColor(utils.getColor('info'), 0.8)\r\n        }\r\n      }\r\n    ];\r\n\r\n    const getDefaultOptions = () => ({\r\n      tooltip: {\r\n        trigger: 'item',\r\n        backgroundColor: utils.getGrays()['100'],\r\n        textStyle: { color: utils.getGrays()['1100'] },\r\n        formatter: '{b}<br/> {c} ({d}%)'\r\n      },\r\n      series: [\r\n        {\r\n          name: 'Marketing Expenses',\r\n          type: 'pie',\r\n          selectedMode: 'single',\r\n          radius: ['45%', '60%'],\r\n          label: {\r\n            show: false\r\n          },\r\n          labelLine: {\r\n            show: false\r\n          },\r\n          itemStyle: {\r\n            borderColor: utils.getColor('gray-100'),\r\n            borderWidth: 2\r\n          },\r\n\r\n          data: detailedExpenses\r\n        },\r\n        {\r\n          name: 'Marketing Expenses',\r\n          type: 'pie',\r\n          radius: ['70%', '75%'],\r\n          barWidth: 10,\r\n          labelLine: {\r\n            length: 0,\r\n            show: false\r\n          },\r\n          label: {\r\n            formatter: '{per|{d}%}',\r\n            rich: {\r\n              per: {\r\n                fontSize: 14,\r\n                fontWeight: 'bold',\r\n                lineHeight: 33\r\n              }\r\n            }\r\n          },\r\n          data: marketingExpenses\r\n        }\r\n      ]\r\n    });\r\n\r\n    const initChart = () => {\r\n      if (utils.isScrolledIntoView($echartsNestedPies)) {\r\n        echartSetOption(chart, userOptions, getDefaultOptions);\r\n        window.removeEventListener('scroll', initChart);\r\n      }\r\n    };\r\n\r\n    window.addEventListener('scroll', initChart);\r\n  }\r\n};\r\n\r\nexport default echartsNestedPiesChartInit;\r\n", "import utils from '../../../utils';\r\nimport { echartSetOption } from '../echarts-utils';\r\n\r\n/* -------------------------------------------------------------------------- */\r\n/*                             Echarts Pie Chart                              */\r\n/* -------------------------------------------------------------------------- */\r\n\r\nconst echartsPieChartInit = () => {\r\n  const $pieChartEl = document.querySelector('.echart-pie-chart-example');\r\n\r\n  if ($pieChartEl) {\r\n    // Get options from data attribute\r\n    const userOptions = utils.getData($pieChartEl, 'options');\r\n    const chart = window.echarts.init($pieChartEl);\r\n\r\n    const getDefaultOptions = () => ({\r\n      legend: {\r\n        left: 'left',\r\n        textStyle: {\r\n          color: utils.getGrays()['600']\r\n        }\r\n      },\r\n      series: [\r\n        {\r\n          type: 'pie',\r\n          radius: window.innerWidth < 530 ? '45%' : '60%',\r\n          label: {\r\n            color: utils.getGrays()['700']\r\n          },\r\n          center: ['50%', '55%'],\r\n          data: [\r\n            {\r\n              value: 1048,\r\n              name: 'Facebook',\r\n              itemStyle: {\r\n                color: utils.getColor('primary')\r\n              }\r\n            },\r\n            {\r\n              value: 735,\r\n              name: 'Youtube',\r\n              itemStyle: {\r\n                color: utils.getColor('danger')\r\n              }\r\n            },\r\n            {\r\n              value: 580,\r\n              name: 'Twitter',\r\n              itemStyle: {\r\n                color: utils.getColor('info')\r\n              }\r\n            },\r\n            {\r\n              value: 484,\r\n              name: 'Linkedin',\r\n              itemStyle: {\r\n                color: utils.getColor('success')\r\n              }\r\n            },\r\n            {\r\n              value: 300,\r\n              name: 'Github',\r\n              itemStyle: {\r\n                color: utils.getColor('warning')\r\n              }\r\n            }\r\n          ],\r\n          emphasis: {\r\n            itemStyle: {\r\n              shadowBlur: 10,\r\n              shadowOffsetX: 0,\r\n              shadowColor: utils.rgbaColor(utils.getGrays()['600'], 0.5)\r\n            }\r\n          }\r\n        }\r\n      ],\r\n      tooltip: {\r\n        trigger: 'item',\r\n        padding: [7, 10],\r\n        backgroundColor: utils.getGrays()['100'],\r\n        borderColor: utils.getGrays()['300'],\r\n        textStyle: { color: utils.getGrays()['1100'] },\r\n        borderWidth: 1,\r\n        transitionDuration: 0,\r\n        axisPointer: {\r\n          type: 'none'\r\n        }\r\n      }\r\n    });\r\n\r\n    echartSetOption(chart, userOptions, getDefaultOptions);\r\n\r\n    //- set chart radius on window resize\r\n    utils.resize(() => {\r\n      if (window.innerWidth < 530) {\r\n        chart.setOption({\r\n          series: [\r\n            {\r\n              radius: '45%'\r\n            }\r\n          ]\r\n        });\r\n      } else {\r\n        chart.setOption({\r\n          series: [\r\n            {\r\n              radius: '60%'\r\n            }\r\n          ]\r\n        });\r\n      }\r\n    });\r\n  }\r\n};\r\n\r\nexport default echartsPieChartInit;\r\n", "import utils from '../../../utils';\r\nimport { echartSetOption } from '../echarts-utils';\r\n\r\n/* -------------------------------------------------------------------------- */\r\n/*                             Echarts Doughnut Chart                         */\r\n/* -------------------------------------------------------------------------- */\r\n\r\nconst echartsPieEdgeAlignChartInit = () => {\r\n  const $echartPieAEdgeAlignChartEl = document.querySelector('.echart-pie-edge-align-chart');\r\n\r\n  const data = [\r\n    {\r\n      value: 800,\r\n      name: 'Starter',\r\n      itemStyle: {\r\n        color: utils.rgbaColor(utils.getColors().primary, 0.5)\r\n      }\r\n    },\r\n    {\r\n      value: 1048,\r\n      name: 'Starter Pro',\r\n      itemStyle: {\r\n        color: utils.getColor('danger')\r\n      }\r\n    },\r\n    {\r\n      value: 735,\r\n      name: 'Basic',\r\n      itemStyle: {\r\n        color: utils.getColor('primary')\r\n      }\r\n    },\r\n    {\r\n      value: 580,\r\n      name: 'Optimal',\r\n      itemStyle: {\r\n        color: utils.getColor('secondary')\r\n      }\r\n    },\r\n    {\r\n      value: 484,\r\n      name: 'Business',\r\n      itemStyle: {\r\n        color: utils.getColor('warning')\r\n      }\r\n    },\r\n    {\r\n      value: 600,\r\n      name: 'Classic addition',\r\n      itemStyle: {\r\n        color: utils.rgbaColor(utils.getColors().warning, 0.8)\r\n      }\r\n    },\r\n    {\r\n      value: 300,\r\n      name: 'Premium',\r\n      itemStyle: {\r\n        color: utils.getColor('success')\r\n      }\r\n    },\r\n    {\r\n      value: 300,\r\n      name: 'Platinum',\r\n      itemStyle: {\r\n        color: utils.getColor('info')\r\n      }\r\n    },\r\n    {\r\n      value: 400,\r\n      name: 'Platinum Pro',\r\n      itemStyle: {\r\n        color: utils.rgbaColor(utils.getColors().primary, 0.5)\r\n      }\r\n    }\r\n  ];\r\n\r\n  if ($echartPieAEdgeAlignChartEl) {\r\n    // Get options from data attribute\r\n    const userOptions = utils.getData($echartPieAEdgeAlignChartEl, 'options');\r\n    const chart = window.echarts.init($echartPieAEdgeAlignChartEl);\r\n\r\n    const getDefaultOptions = () => ({\r\n      title: [\r\n        {\r\n          text: 'Pie Edge Align Chart',\r\n          left: 'center',\r\n          textStyle: {\r\n            color: utils.getGrays()['600']\r\n          }\r\n        },\r\n        {\r\n          subtext: 'alignTo: \"edge\"',\r\n          left: '50%',\r\n          top: '85%',\r\n          textAlign: 'center',\r\n          subtextStyle: {\r\n            color: utils.getGrays()['700']\r\n          }\r\n        }\r\n      ],\r\n\r\n      tooltip: {\r\n        trigger: 'item',\r\n        padding: [7, 10],\r\n        backgroundColor: utils.getGrays()['100'],\r\n        borderColor: utils.getGrays()['300'],\r\n        textStyle: { color: utils.getGrays()['1100'] },\r\n        borderWidth: 1,\r\n        transitionDuration: 0,\r\n        axisPointer: {\r\n          type: 'none'\r\n        }\r\n      },\r\n\r\n      series: [\r\n        {\r\n          type: 'pie',\r\n          radius: window.innerWidth < 530 ? '45%' : '60%',\r\n          center: ['50%', '50%'],\r\n          data,\r\n          label: {\r\n            position: 'outer',\r\n            alignTo: 'edge',\r\n            margin: 20,\r\n            color: utils.getGrays()['700']\r\n          },\r\n          left: '5%',\r\n          right: '5%',\r\n          top: 0,\r\n          bottom: 0\r\n        }\r\n      ]\r\n    });\r\n\r\n    echartSetOption(chart, userOptions, getDefaultOptions);\r\n\r\n    //- set chart radius on window resize\r\n    utils.resize(() => {\r\n      if (window.innerWidth < 530) {\r\n        chart.setOption({\r\n          series: [{ radius: '45%' }]\r\n        });\r\n      } else {\r\n        chart.setOption({\r\n          series: [{ radius: '60%' }]\r\n        });\r\n      }\r\n    });\r\n  }\r\n};\r\n\r\nexport default echartsPieEdgeAlignChartInit;\r\n", "import utils from '../../../utils';\r\nimport { echartSetOption } from '../echarts-utils';\r\n\r\n/* -------------------------------------------------------------------------- */\r\n/*                             Echarts Doughnut Chart                         */\r\n/* -------------------------------------------------------------------------- */\r\n\r\nconst echartsPieLabelAlignChartInit = () => {\r\n  const $echartPieLabelAlignChartEl = document.querySelector('.echart-pie-label-align-chart');\r\n\r\n  if ($echartPieLabelAlignChartEl) {\r\n    // Get options from data attribute\r\n    const userOptions = utils.getData($echartPieLabelAlignChartEl, 'options');\r\n    const chart = window.echarts.init($echartPieLabelAlignChartEl);\r\n\r\n    const data = [\r\n      {\r\n        value: 800,\r\n        name: 'Starter',\r\n        itemStyle: {\r\n          color: utils.rgbaColor(utils.getColors().primary, 0.5)\r\n        }\r\n      },\r\n      {\r\n        value: 1048,\r\n        name: 'Starter Pro',\r\n        itemStyle: {\r\n          color: utils.getColor('danger')\r\n        }\r\n      },\r\n      {\r\n        value: 735,\r\n        name: 'Basic',\r\n        itemStyle: {\r\n          color: utils.getColor('primary')\r\n        }\r\n      },\r\n      {\r\n        value: 580,\r\n        name: 'Optimal',\r\n        itemStyle: {\r\n          color: utils.getColor('secondary')\r\n        }\r\n      },\r\n      {\r\n        value: 484,\r\n        name: 'Business',\r\n        itemStyle: {\r\n          color: utils.getColor('warning')\r\n        }\r\n      },\r\n      {\r\n        value: 600,\r\n        name: 'Classic addition',\r\n        itemStyle: {\r\n          color: utils.rgbaColor(utils.getColors().warning, 0.8)\r\n        }\r\n      },\r\n      {\r\n        value: 300,\r\n        name: 'Premium',\r\n        itemStyle: {\r\n          color: utils.getColor('success')\r\n        }\r\n      },\r\n      {\r\n        value: 300,\r\n        name: 'Platinum',\r\n        itemStyle: {\r\n          color: utils.getColor('info')\r\n        }\r\n      },\r\n      {\r\n        value: 400,\r\n        name: 'Platinum Pro',\r\n        itemStyle: {\r\n          color: utils.rgbaColor(utils.getColors().primary, 0.5)\r\n        }\r\n      }\r\n    ];\r\n\r\n    const getDefaultOptions = () => ({\r\n      title: [\r\n        {\r\n          text: 'Pie Label Align Chart',\r\n          left: 'center',\r\n          textStyle: {\r\n            color: utils.getGrays()['600']\r\n          }\r\n        },\r\n        {\r\n          subtext: 'alignTo: \"labelLine\"',\r\n          left: '50%',\r\n          top: '85%',\r\n          textAlign: 'center',\r\n          subtextStyle: {\r\n            color: utils.getGrays()['700']\r\n          }\r\n        }\r\n      ],\r\n\r\n      tooltip: {\r\n        trigger: 'item',\r\n        padding: [7, 10],\r\n        backgroundColor: utils.getGrays()['100'],\r\n        borderColor: utils.getGrays()['300'],\r\n        textStyle: { color: utils.getGrays()['1100'] },\r\n        borderWidth: 1,\r\n        transitionDuration: 0,\r\n        axisPointer: {\r\n          type: 'none'\r\n        }\r\n      },\r\n\r\n      series: [\r\n        {\r\n          type: 'pie',\r\n          radius: window.innerWidth < 530 ? '45%' : '60%',\r\n          center: ['50%', '50%'],\r\n          data,\r\n          label: {\r\n            position: 'outer',\r\n            alignTo: 'labelLine',\r\n            bleedMargin: 5,\r\n            color: utils.getGrays()['700']\r\n          },\r\n          left: '5%',\r\n          right: '5%',\r\n          top: 0,\r\n          bottom: 0\r\n        }\r\n      ]\r\n    });\r\n\r\n    echartSetOption(chart, userOptions, getDefaultOptions);\r\n\r\n    //- set chart radius on window resize\r\n    utils.resize(() => {\r\n      if (window.innerWidth < 530) {\r\n        chart.setOption({\r\n          series: [{ radius: '45%' }]\r\n        });\r\n      } else {\r\n        chart.setOption({\r\n          series: [{ radius: '60%' }]\r\n        });\r\n      }\r\n    });\r\n  }\r\n};\r\n\r\nexport default echartsPieLabelAlignChartInit;\r\n", "import utils from '../../../utils';\r\nimport { echartSetOption } from '../echarts-utils';\r\n\r\n/* -------------------------------------------------------------------------- */\r\n/*                             Echarts Doughnut Chart                         */\r\n/* -------------------------------------------------------------------------- */\r\nconst data1 = [\r\n  {\r\n    value: 1048,\r\n    name: 'Starter',\r\n    itemStyle: {\r\n      color: utils.getColor('danger')\r\n    }\r\n  },\r\n  {\r\n    value: 735,\r\n    name: 'Basic',\r\n    itemStyle: {\r\n      color: utils.getColor('primary')\r\n    }\r\n  },\r\n  {\r\n    value: 580,\r\n    name: 'Optimal',\r\n    itemStyle: {\r\n      color: utils.getColor('secondary')\r\n    }\r\n  },\r\n  {\r\n    value: 484,\r\n    name: 'Business',\r\n    itemStyle: {\r\n      color: utils.getColor('warning')\r\n    }\r\n  },\r\n  {\r\n    value: 300,\r\n    name: 'Premium',\r\n    itemStyle: {\r\n      color: utils.getColor('success')\r\n    }\r\n  },\r\n  {\r\n    value: 300,\r\n    name: 'Platinum',\r\n    itemStyle: {\r\n      color: utils.getColor('info')\r\n    }\r\n  }\r\n];\r\n\r\nconst data2 = [\r\n  {\r\n    value: 1048,\r\n    name: 'Facebook',\r\n    itemStyle: {\r\n      color: utils.getColor('primary')\r\n    }\r\n  },\r\n  {\r\n    value: 735,\r\n    name: 'Youtube',\r\n    itemStyle: {\r\n      color: utils.getColor('danger')\r\n    }\r\n  },\r\n  {\r\n    value: 580,\r\n    name: 'Twitter',\r\n    itemStyle: {\r\n      color: utils.getColor('info')\r\n    }\r\n  },\r\n  {\r\n    value: 484,\r\n    name: 'Linkedin',\r\n    itemStyle: {\r\n      color: utils.getColor('success')\r\n    }\r\n  },\r\n  {\r\n    value: 300,\r\n    name: 'Github',\r\n    itemStyle: {\r\n      color: utils.getColor('warning')\r\n    }\r\n  }\r\n];\r\nconst defaultRadius = { radius: '55%' };\r\nconst smallRadius = { radius: '48%' };\r\n\r\nconst echartsPieMultipleChartInit = () => {\r\n  const $echartPieMultipleChartEl = document.querySelector('.echart-pie-multiple-chart');\r\n\r\n  if ($echartPieMultipleChartEl) {\r\n    // Get options from data attribute\r\n    const userOptions = utils.getData($echartPieMultipleChartEl, 'options');\r\n    const chart = window.echarts.init($echartPieMultipleChartEl);\r\n\r\n    const getDefaultOptions = () => ({\r\n      title: [\r\n        {\r\n          text: 'Pie Multiple Chart',\r\n          left: 'center',\r\n          textStyle: {\r\n            color: utils.getGrays()['600']\r\n          }\r\n        }\r\n      ],\r\n\r\n      tooltip: {\r\n        trigger: 'item',\r\n        padding: [7, 10],\r\n        backgroundColor: utils.getGrays()['100'],\r\n        borderColor: utils.getGrays()['300'],\r\n        textStyle: { color: utils.getGrays()['1100'] },\r\n        borderWidth: 1,\r\n        transitionDuration: 0,\r\n        axisPointer: {\r\n          type: 'none'\r\n        }\r\n      },\r\n\r\n      series: [\r\n        {\r\n          type: 'pie',\r\n          radius: window.innerWidth < 450 ? '48%' : '55%',\r\n          center: ['25%', '50%'],\r\n          data: data1,\r\n          label: {\r\n            show: false\r\n          }\r\n        },\r\n        {\r\n          type: 'pie',\r\n          radius: window.innerWidth < 450 ? '48%' : '55%',\r\n          center: ['75%', '50%'],\r\n          avoidLabelOverlap: false,\r\n          label: {\r\n            show: false\r\n          },\r\n          data: data2\r\n        }\r\n      ]\r\n    });\r\n\r\n    echartSetOption(chart, userOptions, getDefaultOptions);\r\n\r\n    //- set chart radius on window resize\r\n    utils.resize(() => {\r\n      if (window.innerWidth < 450) {\r\n        chart.setOption({\r\n          series: [smallRadius, smallRadius]\r\n        });\r\n      } else {\r\n        chart.setOption({\r\n          series: [defaultRadius, defaultRadius]\r\n        });\r\n      }\r\n    });\r\n  }\r\n};\r\n\r\nexport default echartsPieMultipleChartInit;\r\n", "import utils from '../../../utils';\r\nimport { echartSetOption } from '../echarts-utils';\r\n\r\n/* -------------------------------------------------------------------------- */\r\n/*                             Echarts Pie Chart                              */\r\n/* -------------------------------------------------------------------------- */\r\n\r\nconst echartsRadarChartInit = () => {\r\n  const $radarChartEl = document.querySelector('.echart-radar-chart-example');\r\n\r\n  if ($radarChartEl) {\r\n    // Get options from data attribute\r\n    const userOptions = utils.getData($radarChartEl, 'options');\r\n    const chart = window.echarts.init($radarChartEl);\r\n\r\n    const getDefaultOptions = () => ({\r\n      legend: {\r\n        orient: 'vertical',\r\n        left: 'left',\r\n        textStyle: {\r\n          color: utils.getGrays()['600']\r\n        }\r\n      },\r\n      tooltip: {\r\n        trigger: 'item',\r\n        padding: [7, 10],\r\n        backgroundColor: utils.getGrays()['100'],\r\n        borderColor: utils.getGrays()['300'],\r\n        textStyle: { color: utils.getGrays()['1100'] },\r\n        borderWidth: 1,\r\n        transitionDuration: 0,\r\n        axisPointer: {\r\n          type: 'none'\r\n        }\r\n      },\r\n\r\n      radar: {\r\n        indicator: [\r\n          { name: 'Marketing', max: 6500 },\r\n          { name: 'Admin', max: 16000 },\r\n          { name: 'Tech', max: 30000 },\r\n          { name: 'Support', max: 38000 },\r\n          { name: 'Dev ', max: 52000 },\r\n          { name: 'Sales ', max: 25000 }\r\n        ],\r\n        radius: 120,\r\n        splitLine: {\r\n          lineStyle: {\r\n            color: utils.rgbaColor(utils.getGrays()['700'])\r\n          }\r\n        }\r\n      },\r\n\r\n      series: [\r\n        {\r\n          type: 'radar',\r\n          data: [\r\n            {\r\n              value: [4200, 3000, 20000, 35000, 50000, 18000],\r\n              name: 'Data A',\r\n              itemStyle: {\r\n                color: utils.getColor('primary')\r\n              }\r\n            },\r\n            {\r\n              value: [5000, 14000, 28000, 26000, 42000, 21000],\r\n              name: 'Data B',\r\n              itemStyle: {\r\n                color: utils.getColor('warning')\r\n              }\r\n            }\r\n          ]\r\n        }\r\n      ]\r\n    });\r\n\r\n    echartSetOption(chart, userOptions, getDefaultOptions);\r\n  }\r\n};\r\n\r\nexport default echartsRadarChartInit;\r\n", "import utils from '../../../utils';\r\nimport { echartSetOption } from '../echarts-utils';\r\n\r\n/* -------------------------------------------------------------------------- */\r\n/*                             Echarts Pie Chart                              */\r\n/* -------------------------------------------------------------------------- */\r\n\r\nconst echartsRadarCustomizedChartInit = () => {\r\n  const $radarChartEl = document.querySelector('.echart-radar-customized-chart');\r\n  function getFormatter(params) {\r\n    const indicators = [\r\n      ['Marketing', 'Sales', 'Dev', 'Support', 'Tech', 'Admin'],\r\n      ['Language', 'Math', 'English', 'Physics', 'Chemistry', 'Biology']\r\n    ];\r\n    const num = params.seriesIndex;\r\n    return `<strong > ${params.name} </strong>\r\n    <div class=\"fs-10 text-600\">\r\n      <strong >${indicators[params.seriesIndex][0]}</strong>: ${params.value[0]}  <br>\r\n      <strong>${indicators[num][1]}</strong>: ${params.value[1]}  <br>\r\n      <strong>${indicators[num][2]}</strong>: ${params.value[2]}  <br>\r\n      <strong>${indicators[num][3]}</strong>: ${params.value[3]}  <br>\r\n      <strong>${indicators[num][4]}</strong>: ${params.value[4]}  <br>\r\n      <strong>${indicators[num][5]}</strong>: ${params.value[5]}  <br>\r\n    </div>`;\r\n  }\r\n\r\n  if ($radarChartEl) {\r\n    // Get options from data attribute\r\n    const userOptions = utils.getData($radarChartEl, 'options');\r\n    const chart = window.echarts.init($radarChartEl);\r\n\r\n    const getDefaultOptions = () => ({\r\n      legend: {\r\n        orient: 'vertical',\r\n        left: 'left',\r\n        textStyle: {\r\n          color: utils.getGrays()['600']\r\n        }\r\n      },\r\n      tooltip: {\r\n        trigger: 'item',\r\n        padding: [7, 10],\r\n        backgroundColor: utils.getGrays()['100'],\r\n        borderColor: utils.getGrays()['300'],\r\n        textStyle: { color: utils.getGrays()['1100'] },\r\n        borderWidth: 1,\r\n        transitionDuration: 0,\r\n        axisPointer: {\r\n          type: 'none'\r\n        },\r\n        formatter: getFormatter\r\n      },\r\n\r\n      radar: [\r\n        {\r\n          radius: window.innerWidth < 576 ? 90 : 120,\r\n          startAngle: 90,\r\n          splitNumber: 4,\r\n          shape: 'circle',\r\n          center: window.innerWidth < 992 ? ['50%', '30%'] : ['25%', '50%'],\r\n          indicator: [\r\n            { name: 'Admin', max: 6500 },\r\n            { name: 'Tech', max: 16000 },\r\n            { name: 'Support', max: 30000 },\r\n            { name: 'Dev', max: 38000 },\r\n            { name: 'Sales', max: 52000 },\r\n            { name: 'Marketing', max: 25000 }\r\n          ],\r\n          name: {\r\n            formatter: '{value}',\r\n            textStyle: {\r\n              color: utils.getGrays()['700']\r\n            }\r\n          },\r\n          splitLine: {\r\n            lineStyle: {\r\n              color: utils.rgbaColor(utils.getGrays()['700'])\r\n            }\r\n          }\r\n        },\r\n\r\n        {\r\n          indicator: [\r\n            { text: 'Language', max: 150 },\r\n            { text: 'Math', max: 150 },\r\n            { text: 'English', max: 150 },\r\n            { text: 'physics', max: 120 },\r\n            { text: 'Chemistry', max: 108 },\r\n            { text: 'Biology', max: 72 }\r\n          ],\r\n          radius: window.innerWidth < 576 ? 90 : 120,\r\n          center: window.innerWidth < 992 ? ['50%', '75%'] : ['75%', '50%'],\r\n          splitLine: {\r\n            lineStyle: {\r\n              color: utils.rgbaColor(utils.getGrays()['700'])\r\n            }\r\n          },\r\n          name: {\r\n            textStyle: {\r\n              color: utils.rgbaColor(utils.getGrays()['1000']),\r\n              backgroundColor: utils.rgbaColor(utils.getGrays()['100']),\r\n              borderRadius: 3,\r\n              padding: [3, 5]\r\n            }\r\n          }\r\n        }\r\n      ],\r\n\r\n      series: [\r\n        {\r\n          type: 'radar',\r\n          data: [\r\n            {\r\n              value: [5200, 4000, 20000, 30000, 20000, 18000],\r\n              name: 'Data A',\r\n              itemStyle: {\r\n                color: utils.getColor('info')\r\n              },\r\n              areaStyle: {\r\n                color: utils.rgbaColor(utils.getColors().info, 0.3)\r\n              }\r\n            },\r\n            {\r\n              value: [5000, 12000, 28000, 26000, 32000, 21000],\r\n              name: 'Data B',\r\n              itemStyle: {\r\n                color: utils.getColor('success')\r\n              },\r\n              areaStyle: {\r\n                color: utils.rgbaColor(utils.getColors().success, 0.3)\r\n              }\r\n            }\r\n          ]\r\n        },\r\n\r\n        {\r\n          type: 'radar',\r\n          radarIndex: 1,\r\n          data: [\r\n            {\r\n              value: [130, 110, 130, 100, 99, 70],\r\n              name: 'Data C',\r\n              symbol: 'rect',\r\n              symbolSize: 12,\r\n              lineStyle: {\r\n                type: 'dashed'\r\n              },\r\n              itemStyle: {\r\n                color: utils.getColor('warning')\r\n              },\r\n              areaStyle: {\r\n                color: utils.rgbaColor(utils.getColors().warning, 0.3)\r\n              },\r\n              label: {\r\n                show: true,\r\n                formatter(params) {\r\n                  return params.value;\r\n                },\r\n                color: utils.getGrays()['700']\r\n              }\r\n            },\r\n            {\r\n              value: [100, 93, 50, 90, 70, 60],\r\n              name: 'Data D',\r\n              itemStyle: {\r\n                color: utils.getColor('danger')\r\n              },\r\n              areaStyle: {\r\n                color: utils.rgbaColor(utils.getColors().danger, 0.3)\r\n              }\r\n            }\r\n          ]\r\n        }\r\n      ]\r\n    });\r\n\r\n    echartSetOption(chart, userOptions, getDefaultOptions);\r\n    //- set chart position on Window resize\r\n    utils.resize(() => {\r\n      if (window.innerWidth < 992) {\r\n        chart.setOption({\r\n          radar: [\r\n            {\r\n              center: ['50%', '30%']\r\n            },\r\n            {\r\n              center: ['50%', '75%']\r\n            }\r\n          ]\r\n        });\r\n      } else {\r\n        chart.setOption({\r\n          radar: [\r\n            {\r\n              center: ['25%', '50%']\r\n            },\r\n            {\r\n              center: ['75%', '50%']\r\n            }\r\n          ]\r\n        });\r\n      }\r\n\r\n      if (window.innerWidth < 576) {\r\n        chart.setOption({\r\n          radar: [\r\n            {\r\n              radius: 90\r\n            },\r\n            {\r\n              radius: 90\r\n            }\r\n          ]\r\n        });\r\n      } else {\r\n        chart.setOption({\r\n          radar: [\r\n            {\r\n              radius: 120\r\n            },\r\n            {\r\n              radius: 120\r\n            }\r\n          ]\r\n        });\r\n      }\r\n    });\r\n  }\r\n};\r\n\r\nexport default echartsRadarCustomizedChartInit;\r\n", "import utils from '../../../utils';\r\nimport { echartSetOption } from '../echarts-utils';\r\n\r\n/* -------------------------------------------------------------------------- */\r\n/*                      Echarts Radar Multiple Chart                          */\r\n/* -------------------------------------------------------------------------- */\r\n\r\nconst echartsRadarMultipleChartInit = () => {\r\n  const $radarChartEl = document.querySelector('.echart-radar-multiple-chart');\r\n\r\n  if ($radarChartEl) {\r\n    // Get options from data attribute\r\n    const userOptions = utils.getData($radarChartEl, 'options');\r\n    const chart = window.echarts.init($radarChartEl);\r\n\r\n    const months = [\r\n      'January',\r\n      'February',\r\n      'March',\r\n      'April',\r\n      'May',\r\n      'June',\r\n      'July',\r\n      'August',\r\n      'September',\r\n      'October',\r\n      'November',\r\n      'December'\r\n    ];\r\n\r\n    const getCenter = () => {\r\n      if (window.innerWidth < 1540 && window.innerWidth > 992) {\r\n        return [\r\n          ['25%', '40%'],\r\n          ['50%', '75%'],\r\n          ['75%', '40%']\r\n        ];\r\n      }\r\n      if (window.innerWidth < 992) {\r\n        return [\r\n          ['50%', '20%'],\r\n          ['50%', '50%'],\r\n          ['50%', '80%']\r\n        ];\r\n      }\r\n      return [\r\n        ['15%', '50%'],\r\n        ['50%', '50%'],\r\n        ['85%', '50%']\r\n      ];\r\n    };\r\n\r\n    const getDefaultOptions = () => ({\r\n      legend: {\r\n        left: 'left',\r\n        textStyle: {\r\n          color: utils.getGrays()['600']\r\n        }\r\n      },\r\n      tooltip: {\r\n        trigger: 'item',\r\n        padding: [7, 10],\r\n        backgroundColor: utils.getGrays()['100'],\r\n        borderColor: utils.getGrays()['300'],\r\n        textStyle: { color: utils.getGrays()['1100'] },\r\n        borderWidth: 1,\r\n        transitionDuration: 0,\r\n        axisPointer: {\r\n          type: 'none'\r\n        }\r\n      },\r\n\r\n      radar: [\r\n        {\r\n          indicator: [\r\n            { text: 'Brand', max: 100 },\r\n            { text: 'content', max: 100 },\r\n            { text: 'Usability', max: 100 },\r\n            { text: 'Features', max: 100 }\r\n          ],\r\n          center: getCenter()[0],\r\n          radius: 85,\r\n          splitLine: {\r\n            lineStyle: {\r\n              color: utils.rgbaColor(utils.getGrays()['700'])\r\n            }\r\n          }\r\n        },\r\n        {\r\n          indicator: [\r\n            { text: 'Exterior', max: 100 },\r\n            { text: 'Take pictures', max: 100 },\r\n            { text: 'system', max: 100 },\r\n            { text: 'performance', max: 100 },\r\n            { text: 'screen', max: 100 }\r\n          ],\r\n          radius: 85,\r\n          center: getCenter()[1],\r\n          splitLine: {\r\n            lineStyle: {\r\n              color: utils.rgbaColor(utils.getGrays()['700'])\r\n            }\r\n          }\r\n        },\r\n        {\r\n          indicator: months.map(month => ({\r\n            text: month,\r\n            max: 100\r\n          })),\r\n          center: getCenter()[2],\r\n          radius: 85,\r\n          splitLine: {\r\n            lineStyle: {\r\n              color: utils.rgbaColor(utils.getGrays()['700'])\r\n            }\r\n          }\r\n        }\r\n      ],\r\n\r\n      series: [\r\n        {\r\n          type: 'radar',\r\n          tooltip: {\r\n            trigger: 'item'\r\n          },\r\n          areaStyle: {\r\n            color: utils.rgbaColor(utils.getColors().info, 0.5)\r\n          },\r\n          data: [\r\n            {\r\n              value: [60, 73, 85, 40],\r\n              name: 'A software',\r\n              itemStyle: {\r\n                color: utils.getColor('info')\r\n              }\r\n            }\r\n          ]\r\n        },\r\n        {\r\n          type: 'radar',\r\n          radarIndex: 1,\r\n          data: [\r\n            {\r\n              value: [85, 90, 90, 95, 95],\r\n              name: 'A staple mobile phone',\r\n              itemStyle: {\r\n                color: utils.rgbaColor(utils.getColors().primary, 0.8)\r\n              },\r\n              areaStyle: {\r\n                color: utils.rgbaColor(utils.getColors().primary, 0.3)\r\n              }\r\n            },\r\n            {\r\n              value: [95, 80, 75, 90, 93],\r\n              name: 'A fruit phone',\r\n              itemStyle: {\r\n                color: utils.getColor('success')\r\n              },\r\n              areaStyle: {\r\n                color: utils.rgbaColor(utils.getColors().success, 0.3)\r\n              }\r\n            }\r\n          ]\r\n        },\r\n        {\r\n          type: 'radar',\r\n          radarIndex: 2,\r\n          areaStyle: {},\r\n          tooltip: {\r\n            show: false\r\n          },\r\n          data: [\r\n            {\r\n              name: 'Precipitation',\r\n              value: [2.6, 5.9, 9.0, 26.4, 28.7, 70.7, 75.6, 82.2, 48.7, 18.8, 6.0, 2.3],\r\n              itemStyle: {\r\n                color: utils.getColor('primary')\r\n              },\r\n              areaStyle: {\r\n                color: utils.rgbaColor(utils.getColors().primary, 0.5)\r\n              }\r\n            },\r\n            {\r\n              name: 'Evaporation',\r\n              value: [2.0, 4.9, 7.0, 23.2, 25.6, 76.7, 35.6, 62.2, 32.6, 20.0, 6.4, 3.3],\r\n              itemStyle: {\r\n                color: utils.getColor('warning')\r\n              },\r\n              areaStyle: {\r\n                color: utils.rgbaColor(utils.getColors().warning, 0.5)\r\n              }\r\n            }\r\n          ]\r\n        }\r\n      ]\r\n    });\r\n\r\n    echartSetOption(chart, userOptions, getDefaultOptions);\r\n\r\n    // - set chart position on Window resize\r\n    utils.resize(() => {\r\n      chart.setOption({\r\n        radar: getCenter().map(item => ({\r\n          center: item\r\n        }))\r\n      });\r\n    });\r\n  }\r\n};\r\n\r\nexport default echartsRadarMultipleChartInit;\r\n", "import utils from '../../../utils';\r\nimport { echartSetOption } from '../echarts-utils';\r\n\r\n/* -------------------------------------------------------------------------- */\r\n/*                        Echarts Scatter Basic Chart                         */\r\n/* -------------------------------------------------------------------------- */\r\n\r\nconst echartsScatterBasicChartInit = () => {\r\n  const $basicScatterChartEl = document.querySelector('.echart-basic-scatter-chart-example');\r\n\r\n  if ($basicScatterChartEl) {\r\n    // Get options from data attribute\r\n    const userOptions = utils.getData($basicScatterChartEl, 'options');\r\n    const chart = window.echarts.init($basicScatterChartEl);\r\n\r\n    const getDefaultOptions = () => ({\r\n      tooltip: {\r\n        trigger: 'item',\r\n        axisPointer: {\r\n          type: 'none'\r\n        },\r\n        padding: [7, 10],\r\n        backgroundColor: utils.getGrays()['100'],\r\n        borderColor: utils.getGrays()['300'],\r\n        textStyle: { color: utils.getGrays()['1100'] },\r\n        borderWidth: 1,\r\n        transitionDuration: 0\r\n      },\r\n      xAxis: {\r\n        axisLabel: {\r\n          color: utils.getGrays()['600']\r\n        },\r\n        axisLine: {\r\n          show: true,\r\n          lineStyle: {\r\n            color: utils.getGrays()['200']\r\n          }\r\n        },\r\n        splitLine: {\r\n          show: true,\r\n          lineStyle: {\r\n            color: utils.getGrays()['200']\r\n          }\r\n        }\r\n      },\r\n      yAxis: {\r\n        axisLabel: {\r\n          color: utils.getGrays()['600']\r\n        },\r\n        splitLine: {\r\n          show: true,\r\n          lineStyle: {\r\n            color: utils.getGrays()['200']\r\n          }\r\n        },\r\n\r\n        axisLine: {\r\n          show: true,\r\n          lineStyle: {\r\n            color: utils.getGrays()['200']\r\n          }\r\n        }\r\n      },\r\n      series: [\r\n        {\r\n          // symbolSize: val => val[2] * 2,\r\n          data: [\r\n            [10.0, 8.04],\r\n            [8.07, 6.95],\r\n            [13.0, 7.58],\r\n            [9.05, 8.81],\r\n            [11.0, 8.33],\r\n            [14.0, 7.66],\r\n            [13.4, 6.81],\r\n            [10.0, 6.33],\r\n            [14.0, 8.96],\r\n            [12.5, 6.82],\r\n            [9.15, 7.2],\r\n            [11.5, 7.2],\r\n            [3.03, 4.23],\r\n            [12.2, 7.83],\r\n            [2.02, 4.47],\r\n            [1.05, 3.33],\r\n            [4.05, 4.96],\r\n            [6.03, 7.24],\r\n            [12.0, 6.26],\r\n            [12.0, 8.84],\r\n            [7.08, 5.82],\r\n            [5.02, 5.68]\r\n          ],\r\n          type: 'scatter',\r\n          itemStyle: {\r\n            color: utils.getColor('danger')\r\n          }\r\n        }\r\n      ],\r\n      grid: {\r\n        right: 8,\r\n        left: 5,\r\n        bottom: 5,\r\n        top: 8,\r\n        containLabel: true\r\n      }\r\n    });\r\n\r\n    echartSetOption(chart, userOptions, getDefaultOptions);\r\n  }\r\n};\r\n\r\nexport default echartsScatterBasicChartInit;\r\n", "import utils from '../../../utils';\r\nimport { echartSetOption } from '../echarts-utils';\r\n\r\n/* -------------------------------------------------------------------------- */\r\n/*                      Echarts Scatter Quartet Chart                         */\r\n/* -------------------------------------------------------------------------- */\r\n\r\nconst echartsScatterQuartetChartInit = () => {\r\n  const $scatterQuartetChartEl = document.querySelector('.echart-scatter-quartet-chart-example');\r\n\r\n  if ($scatterQuartetChartEl) {\r\n    // Get options from data attribute\r\n    const userOptions = utils.getData($scatterQuartetChartEl, 'options');\r\n    const chart = window.echarts.init($scatterQuartetChartEl);\r\n\r\n    const dataAll = [\r\n      [\r\n        [10.0, 8.04],\r\n        [8.0, 6.95],\r\n        [13.0, 7.58],\r\n        [9.0, 8.81],\r\n        [11.0, 8.33],\r\n        [14.0, 9.96],\r\n        [6.0, 7.24],\r\n        [4.0, 4.26],\r\n        [12.0, 10.84],\r\n        [7.0, 4.82],\r\n        [5.0, 5.68]\r\n      ],\r\n      [\r\n        [10.0, 9.14],\r\n        [8.0, 8.14],\r\n        [13.0, 8.74],\r\n        [9.0, 8.77],\r\n        [11.0, 9.26],\r\n        [14.0, 8.1],\r\n        [6.0, 6.13],\r\n        [4.0, 3.1],\r\n        [12.0, 9.13],\r\n        [7.0, 7.26],\r\n        [5.0, 4.74]\r\n      ],\r\n      [\r\n        [10.0, 7.46],\r\n        [8.0, 6.77],\r\n        [13.0, 12.74],\r\n        [9.0, 7.11],\r\n        [11.0, 7.81],\r\n        [14.0, 8.84],\r\n        [6.0, 6.08],\r\n        [4.0, 5.39],\r\n        [12.0, 8.15],\r\n        [7.0, 6.42],\r\n        [5.0, 5.73]\r\n      ],\r\n      [\r\n        [8.0, 6.58],\r\n        [8.0, 5.76],\r\n        [8.0, 7.71],\r\n        [8.0, 8.84],\r\n        [8.0, 8.47],\r\n        [8.0, 7.04],\r\n        [8.0, 5.25],\r\n        [19.0, 12.5],\r\n        [8.0, 5.56],\r\n        [8.0, 7.91],\r\n        [8.0, 6.89]\r\n      ]\r\n    ];\r\n\r\n    const xAxis = () => ({\r\n      axisLabel: {\r\n        color: utils.getGrays()['600']\r\n      },\r\n      axisLine: {\r\n        show: true,\r\n        lineStyle: {\r\n          color: utils.getGrays()['300']\r\n        }\r\n      },\r\n\r\n      splitLine: {\r\n        show: true,\r\n        lineStyle: {\r\n          color: utils.getGrays()['200']\r\n        }\r\n      }\r\n    });\r\n\r\n    const yAxis = () => ({\r\n      axisLabel: {\r\n        color: utils.getGrays()['600']\r\n      },\r\n      splitLine: {\r\n        show: true,\r\n        lineStyle: {\r\n          color: utils.getGrays()['200']\r\n        }\r\n      },\r\n\r\n      axisLine: {\r\n        show: true,\r\n        lineStyle: {\r\n          color: utils.getGrays()['300']\r\n        }\r\n      }\r\n    });\r\n\r\n    const markLineOpt = {\r\n      animation: false,\r\n      label: {\r\n        formatter: 'y = 0.5 * x + 3',\r\n        align: 'right',\r\n        color: utils.getGrays()['600'],\r\n        fontWeight: 600\r\n      },\r\n      lineStyle: {\r\n        type: 'solid'\r\n      },\r\n      tooltip: {\r\n        formatter: 'y = 0.5 * x + 3'\r\n      },\r\n      data: [\r\n        [\r\n          {\r\n            coord: [0, 3],\r\n            symbol: 'none'\r\n          },\r\n          {\r\n            coord: [20, 13],\r\n            symbol: 'none'\r\n          }\r\n        ]\r\n      ]\r\n    };\r\n    const gridMdUp = [\r\n      { left: '7%', top: '10%', width: '38%', height: '38%' },\r\n      { right: '7%', top: '10%', width: '38%', height: '38%' },\r\n      { left: '7%', bottom: '7%', width: '38%', height: '38%' },\r\n      { right: '7%', bottom: '7%', width: '38%', height: '38%' }\r\n    ];\r\n\r\n    const gridMdDown = [\r\n      { left: 6, right: 7, top: '4%', height: '20%' },\r\n      { left: 6, right: 7, top: '29%', height: '20%' },\r\n      { left: 6, right: 7, bottom: '26%', height: '20%' },\r\n      { left: 6, right: 7, bottom: 25, height: '20%' }\r\n    ];\r\n\r\n    const getDefaultOptions = () => ({\r\n      color: [\r\n        utils.getColor('primary'),\r\n        utils.getColor('success'),\r\n        utils.getColor('warning'),\r\n        utils.getColor('danger')\r\n      ],\r\n      tooltip: {\r\n        trigger: 'item',\r\n        axisPointer: {\r\n          type: 'none'\r\n        },\r\n        padding: [7, 10],\r\n        backgroundColor: utils.getGrays()['100'],\r\n        borderColor: utils.getGrays()['300'],\r\n        textStyle: { color: utils.getGrays()['1100'] },\r\n        borderWidth: 1,\r\n        transitionDuration: 0,\r\n        formatter: 'Group {a}: ({c})'\r\n      },\r\n      title: {\r\n        text: \"Anscombe's quartet\",\r\n        left: 'center',\r\n        top: 0,\r\n        textStyle: {\r\n          color: utils.getGrays()['600']\r\n        }\r\n      },\r\n      grid: window.innerWidth < 768 ? gridMdDown : gridMdUp,\r\n      xAxis: [\r\n        { gridIndex: 0, min: 0, max: 20, ...xAxis() },\r\n        { gridIndex: 1, min: 0, max: 20, ...xAxis() },\r\n        { gridIndex: 2, min: 0, max: 20, ...xAxis() },\r\n        { gridIndex: 3, min: 0, max: 20, ...xAxis() }\r\n      ],\r\n      yAxis: [\r\n        { gridIndex: 0, min: 0, max: 15, ...yAxis() },\r\n        { gridIndex: 1, min: 0, max: 15, ...yAxis() },\r\n        { gridIndex: 2, min: 0, max: 15, ...yAxis() },\r\n        { gridIndex: 3, min: 0, max: 15, ...yAxis() }\r\n      ],\r\n      series: [\r\n        {\r\n          name: 'I',\r\n          type: 'scatter',\r\n          xAxisIndex: 0,\r\n          yAxisIndex: 0,\r\n          data: dataAll[0],\r\n          markLine: markLineOpt\r\n        },\r\n        {\r\n          name: 'II',\r\n          type: 'scatter',\r\n          xAxisIndex: 1,\r\n          yAxisIndex: 1,\r\n          data: dataAll[1],\r\n          markLine: markLineOpt\r\n        },\r\n        {\r\n          name: 'III',\r\n          type: 'scatter',\r\n          xAxisIndex: 2,\r\n          yAxisIndex: 2,\r\n          data: dataAll[2],\r\n          markLine: markLineOpt\r\n        },\r\n        {\r\n          name: 'IV',\r\n          type: 'scatter',\r\n          xAxisIndex: 3,\r\n          yAxisIndex: 3,\r\n          data: dataAll[3],\r\n          markLine: markLineOpt\r\n        }\r\n      ]\r\n    });\r\n\r\n    echartSetOption(chart, userOptions, getDefaultOptions);\r\n\r\n    utils.resize(() => {\r\n      if (window.innerWidth < 768) {\r\n        chart.setOption({\r\n          grid: gridMdDown\r\n        });\r\n      } else {\r\n        chart.setOption({\r\n          grid: gridMdUp\r\n        });\r\n      }\r\n    });\r\n  }\r\n};\r\n\r\nexport default echartsScatterQuartetChartInit;\r\n", "import utils from '../../../utils';\r\nimport { echartSetOption } from '../echarts-utils';\r\n\r\n/* -------------------------------------------------------------------------- */\r\n/*                   Echarts Scatter singlr Axis Chart                        */\r\n/* -------------------------------------------------------------------------- */\r\n\r\nconst echartsScatterSingleAxisChartInit = () => {\r\n  const $scatterSingleAxisChartEl = document.querySelector(\r\n    '.echart-scatter-single-axis-chart-example'\r\n  );\r\n\r\n  if ($scatterSingleAxisChartEl) {\r\n    // Get options from data attribute\r\n    const userOptions = utils.getData($scatterSingleAxisChartEl, 'options');\r\n    const chart = window.echarts.init($scatterSingleAxisChartEl);\r\n\r\n    const hours = [\r\n      '12am',\r\n      '1am',\r\n      '2am',\r\n      '3am',\r\n      '4am',\r\n      '5am',\r\n      '6am',\r\n      '7am',\r\n      '8am',\r\n      '9am',\r\n      '10am',\r\n      '11am',\r\n      '12pm',\r\n      '1pm',\r\n      '2pm',\r\n      '3pm',\r\n      '4pm',\r\n      '5pm',\r\n      '6pm',\r\n      '7pm',\r\n      '8pm',\r\n      '9pm',\r\n      '10pm',\r\n      '11pm'\r\n    ];\r\n\r\n    const days = ['Saturday', 'Friday', 'Thursday', 'Wednesday', 'Tuesday', 'Monday', 'Sunday'];\r\n\r\n    const data = [];\r\n    for (let i = 0; i < 7; i += 1) {\r\n      for (let j = 0; j < 24; j += 1) {\r\n        data.push([j, i, utils.getRandomNumber(0, 10)]);\r\n      }\r\n    }\r\n\r\n    const getDefaultOptions = () => ({\r\n      tooltip: {\r\n        trigger: 'item',\r\n        axisPointer: {\r\n          type: 'none'\r\n        },\r\n        padding: [7, 10],\r\n        backgroundColor: utils.getGrays()['100'],\r\n        borderColor: utils.getGrays()['300'],\r\n        textStyle: { color: utils.getGrays()['1100'] },\r\n        borderWidth: 1,\r\n        transitionDuration: 0,\r\n        position: 'top',\r\n        formatter: params => `\r\n            ${days[params.value[1]]} <br/>\r\n            ${hours[params.value[0]]} : ${params.value[2]}\r\n          `\r\n      },\r\n      xAxis: {\r\n        type: 'category',\r\n        data: hours,\r\n        boundaryGap: false,\r\n        splitLine: {\r\n          show: true,\r\n          lineStyle: {\r\n            color: utils.getGrays()['200']\r\n          }\r\n        },\r\n        axisLine: {\r\n          show: false\r\n        },\r\n        axisTick: {\r\n          lineStyle: {\r\n            color: utils.getGrays()['600']\r\n          }\r\n        }\r\n      },\r\n      yAxis: {\r\n        type: 'category',\r\n        data: days,\r\n        axisLine: {\r\n          show: false\r\n        },\r\n        axisTick: {\r\n          lineStyle: {\r\n            color: utils.getGrays()['600']\r\n          }\r\n        },\r\n        axisLabel: {\r\n          margin: 15\r\n        }\r\n      },\r\n      series: [\r\n        {\r\n          name: 'Punch Card',\r\n          type: 'scatter',\r\n          symbolSize: val => val[2] * 2,\r\n          data,\r\n          animationDelay: idx => idx * 5,\r\n          itemStyle: {\r\n            color: utils.getColor('primary')\r\n          }\r\n        }\r\n      ],\r\n      grid: {\r\n        right: 12,\r\n        left: 5,\r\n        bottom: 5,\r\n        top: 5,\r\n        containLabel: true\r\n      }\r\n    });\r\n\r\n    echartSetOption(chart, userOptions, getDefaultOptions);\r\n  }\r\n};\r\n\r\nexport default echartsScatterSingleAxisChartInit;\r\n", "import utils from '../../../utils';\r\nimport { getPosition, echartSetOption, tooltipFormatter } from '../echarts-utils';\r\n\r\n/* -------------------------------------------------------------------------- */\r\n/*                    Echarts Stacked Area  Chart                             */\r\n/* -------------------------------------------------------------------------- */\r\n\r\nconst echartsStackedAreaChartInit = () => {\r\n  const $stackedAreaChartEl = document.querySelector('.echart-stacked-area-chart-example');\r\n\r\n  if ($stackedAreaChartEl) {\r\n    // Get options from data attribute\r\n    const userOptions = utils.getData($stackedAreaChartEl, 'options');\r\n    const chart = window.echarts.init($stackedAreaChartEl);\r\n\r\n    const days = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];\r\n\r\n    const getDefaultOptions = () => ({\r\n      tooltip: {\r\n        trigger: 'axis',\r\n        padding: [7, 10],\r\n        backgroundColor: utils.getGrays()['100'],\r\n        borderColor: utils.getGrays()['300'],\r\n        textStyle: { color: utils.getGrays()['1100'] },\r\n        borderWidth: 1,\r\n        transitionDuration: 0,\r\n        position(pos, params, dom, rect, size) {\r\n          return getPosition(pos, params, dom, rect, size);\r\n        },\r\n        axisPointer: {\r\n          type: 'none'\r\n        },\r\n        formatter: tooltipFormatter\r\n      },\r\n      xAxis: {\r\n        type: 'category',\r\n        data: days,\r\n        boundaryGap: false,\r\n        axisLine: {\r\n          lineStyle: {\r\n            color: utils.getGrays()['300'],\r\n            type: 'solid'\r\n          }\r\n        },\r\n        axisTick: { show: false },\r\n        axisLabel: {\r\n          color: utils.getGrays()['400'],\r\n          margin: 15,\r\n          formatter: value => value.substring(0, 3)\r\n        },\r\n        splitLine: {\r\n          show: false\r\n        }\r\n      },\r\n      yAxis: {\r\n        type: 'value',\r\n        splitLine: {\r\n          lineStyle: {\r\n            color: utils.getGrays()['200']\r\n          }\r\n        },\r\n        boundaryGap: false,\r\n        axisLabel: {\r\n          show: true,\r\n          color: utils.getGrays()['400'],\r\n          margin: 15\r\n        },\r\n        axisTick: { show: false },\r\n        axisLine: { show: false }\r\n      },\r\n      series: [\r\n        {\r\n          name: 'Matcha Latte',\r\n          type: 'line',\r\n          symbolSize: 10,\r\n          stack: 'product',\r\n          data: [120, 132, 101, 134, 90, 230, 210],\r\n          areaStyle: {\r\n            color: utils.rgbaColor(utils.getColor('info'), 0.3)\r\n          },\r\n          itemStyle: {\r\n            color: utils.getGrays()['100'],\r\n            borderColor: utils.getColor('info'),\r\n            borderWidth: 2\r\n          },\r\n          lineStyle: {\r\n            color: utils.getColor('info')\r\n          },\r\n          symbol: 'circle'\r\n        },\r\n        {\r\n          name: 'Milk Tea',\r\n          type: 'line',\r\n          symbolSize: 10,\r\n          stack: 'product',\r\n          data: [220, 182, 191, 234, 290, 330, 310],\r\n          areaStyle: {\r\n            color: utils.rgbaColor(utils.getColor('success'), 0.3)\r\n          },\r\n          itemStyle: {\r\n            color: utils.getGrays()['100'],\r\n            borderColor: utils.getColor('success'),\r\n            borderWidth: 2\r\n          },\r\n          lineStyle: {\r\n            color: utils.getColor('success')\r\n          },\r\n          symbol: 'circle'\r\n        },\r\n        {\r\n          name: 'Cheese Cocoa',\r\n          type: 'line',\r\n          symbolSize: 10,\r\n          stack: 'product',\r\n          data: [150, 232, 201, 154, 190, 330, 410],\r\n          areaStyle: {\r\n            color: utils.rgbaColor(utils.getColor('danger'), 0.3)\r\n          },\r\n          itemStyle: {\r\n            color: utils.getGrays()['100'],\r\n            borderColor: utils.getColor('danger'),\r\n            borderWidth: 2\r\n          },\r\n          lineStyle: {\r\n            color: utils.getColor('danger')\r\n          },\r\n          symbol: 'circle'\r\n        },\r\n        {\r\n          name: 'Cheese Brownie',\r\n          type: 'line',\r\n          symbolSize: 10,\r\n          stack: 'product',\r\n          data: [320, 332, 301, 334, 390, 330, 320],\r\n          areaStyle: {\r\n            color: utils.rgbaColor(utils.getColor('warning'), 0.3)\r\n          },\r\n          itemStyle: {\r\n            color: utils.getGrays()['100'],\r\n            borderColor: utils.getColor('warning'),\r\n            borderWidth: 2\r\n          },\r\n          lineStyle: {\r\n            color: utils.getColor('warning')\r\n          },\r\n          symbol: 'circle'\r\n        },\r\n        {\r\n          name: 'Matcha Cocoa',\r\n          type: 'line',\r\n          symbolSize: 10,\r\n          stack: 'product',\r\n          data: [820, 932, 901, 934, 1290, 1330, 1320],\r\n          areaStyle: {\r\n            color: utils.rgbaColor(utils.getColor('primary'), 0.3)\r\n          },\r\n          itemStyle: {\r\n            color: utils.getGrays()['100'],\r\n            borderColor: utils.getColor('primary'),\r\n            borderWidth: 2\r\n          },\r\n          lineStyle: {\r\n            color: utils.getColor('primary')\r\n          },\r\n          symbol: 'circle'\r\n        }\r\n      ],\r\n      grid: { right: 10, left: 5, bottom: 5, top: 8, containLabel: true }\r\n    });\r\n\r\n    echartSetOption(chart, userOptions, getDefaultOptions);\r\n  }\r\n};\r\n\r\nexport default echartsStackedAreaChartInit;\r\n", "import utils from '../../../utils';\r\nimport { echartSetOption, tooltipFormatter } from '../echarts-utils';\r\n\r\n/* -------------------------------------------------------------------------- */\r\n/*                             Echarts Bar Chart                             */\r\n/* -------------------------------------------------------------------------- */\r\n\r\nconst echartsHorizontalStackedChartInit = () => {\r\n  const $horizontalStackChartEl = document.querySelector(\r\n    '.echart-horizontal-stacked-chart-example'\r\n  );\r\n\r\n  if ($horizontalStackChartEl) {\r\n    // Get options from data attribute\r\n    const userOptions = utils.getData($horizontalStackChartEl, 'options');\r\n    const chart = window.echarts.init($horizontalStackChartEl);\r\n\r\n    const days = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];\r\n\r\n    const getDefaultOptions = () => ({\r\n      color: [\r\n        utils.getColor('info'),\r\n        utils.getColor('danger'),\r\n        utils.getColor('warning'),\r\n        utils.getColor('success'),\r\n        utils.getColor('primary')\r\n      ],\r\n      tooltip: {\r\n        trigger: 'axis',\r\n        axisPointer: {\r\n          type: 'shadow'\r\n        },\r\n        padding: [7, 10],\r\n        backgroundColor: utils.getGrays()['100'],\r\n        borderColor: utils.getGrays()['300'],\r\n        textStyle: { color: utils.getGrays()['1100'] },\r\n        borderWidth: 1,\r\n        transitionDuration: 0,\r\n        formatter: tooltipFormatter\r\n      },\r\n      toolbox: {\r\n        feature: {\r\n          magicType: {\r\n            type: ['stack', 'tiled']\r\n          }\r\n        },\r\n        right: 0\r\n      },\r\n      legend: {\r\n        data: ['Direct', 'Mail Ad', 'Affiliate Ad', 'Video Ad', 'Search Engine'],\r\n        textStyle: {\r\n          color: utils.getGrays()['600']\r\n        },\r\n        left: 0\r\n      },\r\n      xAxis: {\r\n        type: 'value',\r\n        axisLine: {\r\n          show: true,\r\n          lineStyle: {\r\n            color: utils.getGrays()['300']\r\n          }\r\n        },\r\n        axisTick: { show: false },\r\n        axisLabel: {\r\n          color: utils.getGrays()['500']\r\n        },\r\n        splitLine: {\r\n          lineStyle: {\r\n            show: true,\r\n            color: utils.getGrays()['200']\r\n          }\r\n        }\r\n      },\r\n      yAxis: {\r\n        type: 'category',\r\n        data: days,\r\n        axisLine: {\r\n          lineStyle: {\r\n            show: true,\r\n            color: utils.getGrays()['300']\r\n          }\r\n        },\r\n        axisTick: { show: false },\r\n        axisLabel: {\r\n          color: utils.getGrays()['500'],\r\n          formatter: value => value.substring(0, 3)\r\n        }\r\n      },\r\n      series: [\r\n        {\r\n          name: 'Direct',\r\n          type: 'bar',\r\n          stack: 'total',\r\n          label: {\r\n            show: true,\r\n            textStyle: {\r\n              color: '#fff'\r\n            }\r\n          },\r\n          emphasis: {\r\n            focus: 'series'\r\n          },\r\n          data: [320, 302, 301, 334, 390, 330, 320]\r\n        },\r\n        {\r\n          name: 'Mail Ad',\r\n          type: 'bar',\r\n          stack: 'total',\r\n          label: {\r\n            show: true\r\n          },\r\n          emphasis: {\r\n            focus: 'series'\r\n          },\r\n          data: [220, 188, 301, 250, 190, 230, 210]\r\n        },\r\n        {\r\n          name: 'Affiliate Ad',\r\n          type: 'bar',\r\n          stack: 'total',\r\n          label: {\r\n            show: true,\r\n            textStyle: {\r\n              color: '#fff'\r\n            }\r\n          },\r\n          emphasis: {\r\n            focus: 'series'\r\n          },\r\n          data: [220, 182, 191, 234, 290, 330, 310]\r\n        },\r\n        {\r\n          name: 'Video Ad',\r\n          type: 'bar',\r\n          stack: 'total',\r\n          label: {\r\n            show: true,\r\n            textStyle: {\r\n              color: '#fff'\r\n            }\r\n          },\r\n          emphasis: {\r\n            focus: 'series'\r\n          },\r\n          data: [150, 212, 201, 154, 190, 330, 410]\r\n        },\r\n        {\r\n          name: 'Search Engine',\r\n          type: 'bar',\r\n          stack: 'total',\r\n          label: {\r\n            show: true\r\n          },\r\n          emphasis: {\r\n            focus: 'series'\r\n          },\r\n          data: [820, 832, 901, 934, 1290, 1330, 1320]\r\n        }\r\n      ],\r\n      grid: {\r\n        right: 15,\r\n        left: 5,\r\n        bottom: 5,\r\n        top: '15%',\r\n        containLabel: true\r\n      }\r\n    });\r\n\r\n    echartSetOption(chart, userOptions, getDefaultOptions);\r\n  }\r\n};\r\n\r\nexport default echartsHorizontalStackedChartInit;\r\n", "import utils from '../../../utils';\r\nimport { getPosition, echartSetOption, tooltipFormatter } from '../echarts-utils';\r\n\r\n/* -------------------------------------------------------------------------- */\r\n/*                     Echarts Stacked Line Chart                             */\r\n/* -------------------------------------------------------------------------- */\r\n\r\nconst echartsStackedLineChartInit = () => {\r\n  const $stackedLineChartEl = document.querySelector('.echart-stacked-line-chart-example');\r\n\r\n  if ($stackedLineChartEl) {\r\n    // Get options from data attribute\r\n    const userOptions = utils.getData($stackedLineChartEl, 'options');\r\n    const chart = window.echarts.init($stackedLineChartEl);\r\n\r\n    const days = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];\r\n\r\n    const getDefaultOptions = () => ({\r\n      tooltip: {\r\n        trigger: 'axis',\r\n        padding: [7, 10],\r\n        backgroundColor: utils.getGrays()['100'],\r\n        borderColor: utils.getGrays()['300'],\r\n        textStyle: { color: utils.getGrays()['1100'] },\r\n        borderWidth: 1,\r\n        transitionDuration: 0,\r\n        position(pos, params, dom, rect, size) {\r\n          return getPosition(pos, params, dom, rect, size);\r\n        },\r\n        axisPointer: {\r\n          type: 'none'\r\n        },\r\n        formatter: tooltipFormatter\r\n      },\r\n      xAxis: {\r\n        type: 'category',\r\n        data: days,\r\n        boundaryGap: false,\r\n        axisLine: {\r\n          lineStyle: {\r\n            color: utils.getGrays()['300'],\r\n            type: 'solid'\r\n          }\r\n        },\r\n        axisTick: { show: false },\r\n        axisLabel: {\r\n          color: utils.getGrays()['400'],\r\n          margin: 15,\r\n          formatter: value => value.substring(0, 3)\r\n        },\r\n        splitLine: {\r\n          show: false\r\n        }\r\n      },\r\n      yAxis: {\r\n        type: 'value',\r\n        splitLine: {\r\n          lineStyle: {\r\n            color: utils.getGrays()['200'],\r\n            type: 'dashed'\r\n          }\r\n        },\r\n        boundaryGap: false,\r\n        axisLabel: {\r\n          show: true,\r\n          color: utils.getGrays()['400'],\r\n          margin: 15\r\n        },\r\n        axisTick: { show: false },\r\n        axisLine: { show: false }\r\n      },\r\n      series: [\r\n        {\r\n          name: 'Matcha Latte',\r\n          type: 'line',\r\n          symbolSize: 6,\r\n          itemStyle: {\r\n            color: utils.getGrays()['100'],\r\n            borderColor: utils.getColor('info'),\r\n            borderWidth: 2\r\n          },\r\n          lineStyle: {\r\n            color: utils.getColor('info')\r\n          },\r\n          symbol: 'circle',\r\n          stack: 'product',\r\n          data: [120, 132, 101, 134, 90, 230, 210]\r\n        },\r\n        {\r\n          name: 'Milk Tea',\r\n          type: 'line',\r\n          symbolSize: 10,\r\n          itemStyle: {\r\n            color: utils.getGrays()['100'],\r\n            borderColor: utils.getColor('success'),\r\n            borderWidth: 2\r\n          },\r\n          lineStyle: {\r\n            color: utils.getColor('success')\r\n          },\r\n          symbol: 'circle',\r\n          stack: 'product',\r\n          data: [220, 182, 191, 234, 290, 330, 310]\r\n        },\r\n        {\r\n          name: 'Cheese Cocoa',\r\n          type: 'line',\r\n          symbolSize: 10,\r\n          itemStyle: {\r\n            color: utils.getGrays()['100'],\r\n            borderColor: utils.getColor('danger'),\r\n            borderWidth: 2\r\n          },\r\n          lineStyle: {\r\n            color: utils.getColor('danger')\r\n          },\r\n          symbol: 'circle',\r\n          stack: 'product',\r\n          data: [150, 232, 201, 154, 190, 330, 410]\r\n        },\r\n        {\r\n          name: 'Cheese Brownie',\r\n          type: 'line',\r\n          symbolSize: 10,\r\n          itemStyle: {\r\n            color: utils.getGrays()['100'],\r\n            borderColor: utils.getColor('warning'),\r\n            borderWidth: 2\r\n          },\r\n          lineStyle: {\r\n            color: utils.getColor('warning')\r\n          },\r\n          symbol: 'circle',\r\n          stack: 'product',\r\n          data: [320, 332, 301, 334, 390, 330, 320]\r\n        },\r\n        {\r\n          name: 'Matcha Cocoa',\r\n          type: 'line',\r\n          symbolSize: 10,\r\n          itemStyle: {\r\n            color: utils.getGrays()['100'],\r\n            borderColor: utils.getColor('primary'),\r\n            borderWidth: 2\r\n          },\r\n          lineStyle: {\r\n            color: utils.getColor('primary')\r\n          },\r\n          symbol: 'circle',\r\n          stack: 'product',\r\n          data: [820, 932, 901, 934, 1290, 1330, 1320]\r\n        }\r\n      ],\r\n      grid: { right: 10, left: 5, bottom: 5, top: 8, containLabel: true }\r\n    });\r\n\r\n    echartSetOption(chart, userOptions, getDefaultOptions);\r\n  }\r\n};\r\n\r\nexport default echartsStackedLineChartInit;\r\n", "import utils from '../../../utils';\r\nimport { echartSetOption } from '../echarts-utils';\r\n\r\nconst echartsStackedVerticalChartInit = () => {\r\n  const $stackedVerticalChart = document.querySelector('.echart-stacked-vertival-chart-example');\r\n\r\n  if ($stackedVerticalChart) {\r\n    const userOptions = utils.getData($stackedVerticalChart, 'options');\r\n    const chart = window.echarts.init($stackedVerticalChart);\r\n    const xAxisData = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];\r\n    const data1 = [20, 18, 15, 20, 12, 15, 10];\r\n    const data2 = [30, 20, 20, 25, 20, 15, 10];\r\n    const data3 = [35, 32, 40, 50, 30, 25, 15];\r\n    const data4 = [15, 25, 20, 18, 10, 15, 25];\r\n\r\n    const emphasisStyle = {\r\n      itemStyle: {\r\n        shadowColor: utils.rgbaColor(utils.getColor('dark'), 0.3)\r\n      }\r\n    };\r\n\r\n    const getDefaultOptions = () => ({\r\n      color: [\r\n        utils.getColor('primary'),\r\n        utils.getColor('info'),\r\n        utils.isDark() === 'dark' ? '#229BD2' : '#73D3FE',\r\n        utils.isDark() === 'dark' ? '#195979' : '#A9E4FF'\r\n      ],\r\n      tooltip: {\r\n        trigger: 'item',\r\n        padding: [7, 10],\r\n        backgroundColor: utils.getGrays()['100'],\r\n        borderColor: utils.getGrays()['300'],\r\n        textStyle: { color: utils.getGrays()['900'] },\r\n        borderWidth: 1,\r\n        transitionDuration: 0,\r\n        axisPointer: {\r\n          type: 'none'\r\n        }\r\n      },\r\n      legend: {\r\n        data: ['Urgent', 'High', 'Medium', 'Low'],\r\n        textStyle: {\r\n          color: utils.getGrays()['700']\r\n        }\r\n      },\r\n      xAxis: {\r\n        data: xAxisData,\r\n        splitLine: { show: false },\r\n        splitArea: { show: false },\r\n\r\n        axisLabel: {\r\n          color: utils.getGrays()['600'],\r\n          margin: 8\r\n        },\r\n\r\n        axisLine: {\r\n          lineStyle: {\r\n            color: utils.getGrays()['300'],\r\n            type: 'dashed'\r\n          }\r\n        },\r\n        axisTick: {\r\n          show: false\r\n        }\r\n      },\r\n      yAxis: {\r\n        splitLine: {\r\n          lineStyle: {\r\n            color: utils.getGrays()['300'],\r\n            type: 'dashed'\r\n          }\r\n        },\r\n        axisLabel: {\r\n          color: utils.getGrays()['600']\r\n        },\r\n        position: 'right'\r\n      },\r\n      series: [\r\n        {\r\n          name: 'Urgent',\r\n          type: 'bar',\r\n          stack: 'one',\r\n          emphasis: emphasisStyle,\r\n          data: data1\r\n        },\r\n        {\r\n          name: 'High',\r\n          type: 'bar',\r\n          stack: 'one',\r\n          emphasis: emphasisStyle,\r\n          data: data2\r\n        },\r\n        {\r\n          name: 'Medium',\r\n          type: 'bar',\r\n          stack: 'one',\r\n          emphasis: emphasisStyle,\r\n          data: data3\r\n        },\r\n        {\r\n          name: 'Low',\r\n          type: 'bar',\r\n          stack: 'one',\r\n          emphasis: emphasisStyle,\r\n          data: data4,\r\n          itemStyle: {\r\n            borderRadius: [2, 2, 0, 0]\r\n          }\r\n        }\r\n      ],\r\n\r\n      barWidth: '15px',\r\n      grid: {\r\n        top: '8%',\r\n        bottom: 10,\r\n        left: 0,\r\n        right: 2,\r\n        containLabel: true\r\n      }\r\n    });\r\n\r\n    echartSetOption(chart, userOptions, getDefaultOptions);\r\n  }\r\n};\r\n\r\nexport default echartsStackedVerticalChartInit;\r\n", "import utils from '../../../utils';\r\nimport { getPosition, echartSetOption, tooltipFormatter } from '../echarts-utils';\r\n\r\n/* -------------------------------------------------------------------------- */\r\n/*                             Echarts Step Line Chart                        */\r\n/* -------------------------------------------------------------------------- */\r\n\r\nconst echartsStepLineChartInit = () => {\r\n  const $stepLineChartEl = document.querySelector('.echart-step-line-chart-example');\r\n\r\n  if ($stepLineChartEl) {\r\n    // Get options from data attribute\r\n    const userOptions = utils.getData($stepLineChartEl, 'options');\r\n    const chart = window.echarts.init($stepLineChartEl);\r\n\r\n    const days = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];\r\n\r\n    const getDefaultOptions = () => ({\r\n      color: [utils.getColor('danger'), utils.getColor('warning'), utils.getColor('primary')],\r\n\r\n      tooltip: {\r\n        trigger: 'axis',\r\n        padding: [7, 10],\r\n        backgroundColor: utils.getGrays()['100'],\r\n        borderColor: utils.getGrays()['300'],\r\n        textStyle: { color: utils.getGrays()['1100'] },\r\n        borderWidth: 1,\r\n        transitionDuration: 0,\r\n        formatter: tooltipFormatter,\r\n        position(pos, params, dom, rect, size) {\r\n          return getPosition(pos, params, dom, rect, size);\r\n        }\r\n      },\r\n      xAxis: {\r\n        type: 'category',\r\n        data: days,\r\n        boundaryGap: false,\r\n        axisLine: {\r\n          lineStyle: {\r\n            color: utils.getGrays()['300'],\r\n            type: 'solid'\r\n          }\r\n        },\r\n        axisTick: { show: false },\r\n        axisLabel: {\r\n          formatter: value => value.substring(0, 3),\r\n          color: utils.getGrays()['400'],\r\n          margin: 15\r\n        },\r\n        splitLine: {\r\n          show: false\r\n        },\r\n        axisPointer: {\r\n          lineStyle: {\r\n            color: utils.getGrays()['300']\r\n          }\r\n        }\r\n      },\r\n      yAxis: {\r\n        type: 'value',\r\n        splitLine: {\r\n          lineStyle: {\r\n            color: utils.getGrays()['200']\r\n          }\r\n        },\r\n        boundaryGap: false,\r\n        axisLabel: {\r\n          show: true,\r\n          color: utils.getGrays()['400'],\r\n          margin: 15\r\n        },\r\n        axisTick: { show: false },\r\n        axisLine: { show: false }\r\n      },\r\n      series: [\r\n        {\r\n          name: 'Step Start',\r\n          type: 'line',\r\n          step: 'start',\r\n          symbolSize: 10,\r\n          itemStyle: {\r\n            color: utils.getGrays()['100'],\r\n            borderColor: utils.getColor('primary'),\r\n            borderWidth: 2\r\n          },\r\n          lineStyle: {\r\n            color: utils.getColor('primary')\r\n          },\r\n          symbol: 'circle',\r\n          data: [120, 132, 101, 134, 90, 230, 210]\r\n        },\r\n        {\r\n          name: 'Step Middle',\r\n          type: 'line',\r\n          step: 'middle',\r\n          symbolSize: 10,\r\n          itemStyle: {\r\n            color: utils.getGrays()['100'],\r\n            borderColor: utils.getColor('warning'),\r\n            borderWidth: 2\r\n          },\r\n          lineStyle: {\r\n            color: utils.getColor('warning')\r\n          },\r\n          symbol: 'circle',\r\n          data: [220, 282, 201, 234, 290, 430, 410]\r\n        },\r\n        {\r\n          name: 'Step End',\r\n          type: 'line',\r\n          step: 'end',\r\n          symbolSize: 10,\r\n          itemStyle: {\r\n            color: utils.getGrays()['100'],\r\n            borderColor: utils.getColor('danger'),\r\n            borderWidth: 2\r\n          },\r\n          lineStyle: {\r\n            color: utils.getColor('danger')\r\n          },\r\n          symbol: 'circle',\r\n          data: [450, 432, 401, 454, 590, 530, 510]\r\n        }\r\n      ],\r\n      grid: { right: '3%', left: '8%', bottom: '10%', top: '5%' }\r\n    });\r\n\r\n    echartSetOption(chart, userOptions, getDefaultOptions);\r\n  }\r\n};\r\n\r\nexport default echartsStepLineChartInit;\r\n", "import { docReady } from './utils';\r\nimport echartsLineChartInit from './charts/echarts/examples/basic-line-chart';\r\nimport echartsPieChartInit from './charts/echarts/examples/pie-chart';\r\nimport echartsBasicBarChartInit from './charts/echarts/examples/basic-bar-chart';\r\nimport echartsDoughnutChartInit from './charts/echarts/examples/doughnut-chart';\r\nimport echartsLineAreaChartInit from './charts/echarts/examples/line-area-chart';\r\nimport echartsStackedLineChartInit from './charts/echarts/examples/stacked-line-chart';\r\nimport echartsStackedAreaChartInit from './charts/echarts/examples/stacked-area-chart';\r\nimport echartsLineMarkerChartInit from './charts/echarts/examples/line-marker-chart';\r\nimport echartsAreaPiecesChartInit from './charts/echarts/examples/area-pieces-chart';\r\nimport echartsLineRaceChartInit from './charts/echarts/examples/line-race-chart';\r\nimport echartsStepLineChartInit from './charts/echarts/examples/step-line-chart';\r\nimport echartsLineGradientChartInit from './charts/echarts/examples/line-gradient-chart';\r\nimport echartsDynamicLineChartInit from './charts/echarts/examples/dynamic-line-chart';\r\nimport echartsHorizontalBarChartInit from './charts/echarts/examples/horizontal-bar-chart';\r\nimport echartsBarNegativeChartInit from './charts/echarts/examples/bar-negative-chart';\r\nimport echartsBarSeriesChartInit from './charts/echarts/examples/bar-series-chart';\r\nimport echartsWaterFallChartInit from './charts/echarts/examples/bar-waterfall-chart';\r\nimport echartsHorizontalStackedChartInit from './charts/echarts/examples/stacked-horizontal-bar-chart';\r\nimport echartsBarRaceChartInit from './charts/echarts/examples/bar-race-chart';\r\nimport echartsGradientBarChartInit from './charts/echarts/examples/gradient-bar-chart';\r\nimport echartsBarLineChartInit from './charts/echarts/examples/bar-line-mixed-chart';\r\nimport echartsBasicCandlestickChartInit from './charts/echarts/examples/basic-candlestick-chart';\r\nimport echartsCandlestickMixedChartInit from './charts/echarts/examples/candle-stick-mixed-chart';\r\nimport echartsUsaMapInit from './charts/echarts/examples/map-usa';\r\nimport echartsScatterBasicChartInit from './charts/echarts/examples/scatter-basic-chart';\r\nimport echartsBubbleChartInit from './charts/echarts/examples/bubble-chart';\r\nimport echartsScatterQuartetChartInit from './charts/echarts/examples/scatter-quartet';\r\nimport echartsScatterSingleAxisChartInit from './charts/echarts/examples/scatter-single-axis-chart';\r\nimport echartsBasicGaugeChartInit from './charts/echarts/examples/basic-gauge-chart';\r\nimport echartsGaugeProgressChartInit from './charts/echarts/examples/gauge-progress-chart';\r\nimport echartsGaugeRingChartInit from './charts/echarts/examples/gauge-ring-chart';\r\nimport echartsGaugeMultiRingChartInit from './charts/echarts/examples/gauge-multi-ring-chart';\r\nimport echartsGaugeMultiTitleChartInit from './charts/echarts/examples/gauge-multi-title-chart';\r\nimport echartsGaugeGradeChartInit from './charts/echarts/examples/gauge-grade-chart';\r\nimport echartsLineLogChartInit from './charts/echarts/examples/line-log-chart';\r\nimport echartsLineShareDatasetChartInit from './charts/echarts/examples/line-share-dataset-chart';\r\nimport echartsBarTimelineChartInit from './charts/echarts/examples/bar-timeline-chart';\r\nimport echartsDoughnutRoundedChartInit from './charts/echarts/examples/doughnut-rounded-chart';\r\nimport echartsPieLabelAlignChartInit from './charts/echarts/examples/pie-label-align-chart';\r\nimport echartsRadarChartInit from './charts/echarts/examples/radar-chart';\r\nimport echartsRadarCustomizedChartInit from './charts/echarts/examples/radar-customized-chart';\r\nimport echartsRadarMultipleChartInit from './charts/echarts/examples/radar-multiple-chart';\r\nimport echartsPieMultipleChartInit from './charts/echarts/examples/pie-multiple-chart';\r\nimport echartsHeatMapChartInit from './charts/echarts/examples/heatmap-chart';\r\nimport echartsHeatMapSingleSeriesChartInit from './charts/echarts/examples/heatmap-single-series-chart';\r\nimport echartsBarStackedChartInit from './charts/echarts/examples/bar-stacked-chart';\r\nimport echartsPieEdgeAlignChartInit from './charts/echarts/examples/pie-edge-align-chart';\r\nimport echartsStackedVerticalChartInit from './charts/echarts/examples/stacked-vertical-chart';\r\nimport echartsNestedPiesChartInit from './charts/echarts/examples/nested-pies-chart';\r\n\r\n/* -------------------------------------------------------------------------- */\r\n/*                            Theme Initialization                            */\r\n/* -------------------------------------------------------------------------- */\r\ndocReady(echartsLineChartInit);\r\ndocReady(echartsLineAreaChartInit);\r\ndocReady(echartsPieChartInit);\r\ndocReady(echartsBasicBarChartInit);\r\ndocReady(echartsDoughnutChartInit);\r\ndocReady(echartsStackedLineChartInit);\r\ndocReady(echartsStackedAreaChartInit);\r\ndocReady(echartsLineMarkerChartInit);\r\ndocReady(echartsAreaPiecesChartInit);\r\ndocReady(echartsLineRaceChartInit);\r\ndocReady(echartsStepLineChartInit);\r\ndocReady(echartsLineGradientChartInit);\r\ndocReady(echartsDynamicLineChartInit);\r\ndocReady(echartsHorizontalBarChartInit);\r\ndocReady(echartsBarNegativeChartInit);\r\ndocReady(echartsBarSeriesChartInit);\r\ndocReady(echartsWaterFallChartInit);\r\ndocReady(echartsHorizontalStackedChartInit);\r\ndocReady(echartsBarRaceChartInit);\r\ndocReady(echartsGradientBarChartInit);\r\ndocReady(echartsBarLineChartInit);\r\ndocReady(echartsBasicCandlestickChartInit);\r\ndocReady(echartsCandlestickMixedChartInit);\r\ndocReady(echartsUsaMapInit);\r\ndocReady(echartsScatterBasicChartInit);\r\ndocReady(echartsBubbleChartInit);\r\ndocReady(echartsScatterQuartetChartInit);\r\ndocReady(echartsScatterSingleAxisChartInit);\r\ndocReady(echartsBasicGaugeChartInit);\r\ndocReady(echartsGaugeProgressChartInit);\r\ndocReady(echartsGaugeRingChartInit);\r\ndocReady(echartsGaugeMultiRingChartInit);\r\ndocReady(echartsGaugeMultiTitleChartInit);\r\ndocReady(echartsGaugeGradeChartInit);\r\ndocReady(echartsLineLogChartInit);\r\ndocReady(echartsLineShareDatasetChartInit);\r\ndocReady(echartsBarTimelineChartInit);\r\ndocReady(echartsDoughnutRoundedChartInit);\r\ndocReady(echartsPieLabelAlignChartInit);\r\ndocReady(echartsRadarChartInit);\r\ndocReady(echartsRadarCustomizedChartInit);\r\ndocReady(echartsRadarMultipleChartInit);\r\ndocReady(echartsPieMultipleChartInit);\r\ndocReady(echartsHeatMapChartInit);\r\ndocReady(echartsHeatMapSingleSeriesChartInit);\r\ndocReady(echartsBarStackedChartInit);\r\ndocReady(echartsPieEdgeAlignChartInit);\r\ndocReady(echartsStackedVerticalChartInit);\r\ndocReady(echartsNestedPiesChartInit);\r\n"]}