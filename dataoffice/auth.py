import time
import urllib.parse
import uuid
from functools import cached_property
from typing import Annotated, Any, OrderedDict

import msal
import requests
from dataoffice.settings import settings
from fastapi import APIRouter, Depends, Request, Response
from fastapi.responses import RedirectResponse
from pydantic import BaseModel

router = APIRouter()

SCOPES = ["User.Read"]
MAX_SESSIONS = 1000


_sessions: OrderedDict[str, "AuthSession"] = OrderedDict()


class User(BaseModel):
    username: str
    full_name: str


class AuthSession(BaseModel):
    public_id: str
    user: User | None = None
    next: str | None = None

    _access_token: str | None = None
    _access_token_expires: int | None = None
    _flow: dict[str, Any] | None = None

    @cached_property
    def _msal_app(self):
        """
        MS Documentation recommends using a separate token cache instance for each user in webapps,
        so we'll lazy load a new instance of ConfidentialClientApplication for each AuthSession.
        """
        return msal.ConfidentialClientApplication(
            settings().auth_client_id,
            settings().auth_client_secret,
            f"https://login.microsoftonline.com/{settings().auth_tenant_id}",
            token_cache=msal.SerializableTokenCache(),
        )

    def initiate_auth_code_flow(self, redirect_uri: str):
        self._flow = self._msal_app.initiate_auth_code_flow(
            scopes=SCOPES,
            redirect_uri=redirect_uri,
        )  # type: ignore
        return self._flow["auth_uri"]

    def oauth_callback(self, query_params: dict[str, str]):
        result: dict[str, Any] = self._msal_app.acquire_token_by_auth_code_flow(
            self._flow, query_params
        )  # type: ignore
        self._access_token = result["access_token"]
        self._access_token_expires = int(time.time()) + result["expires_in"]
        self.user = User(
            username=result["id_token_claims"]["preferred_username"],
            full_name=result["id_token_claims"]["name"],
        )

    def get_access_token(self):
        if self.user is None:
            return None

        if self.is_active():
            return self._access_token

        account = (
            self._msal_app.get_accounts(username=self.user.username)
            if self.user
            else None
        )  # type: ignore
        if not account:
            return None
        result: dict[str, Any] = self._msal_app.acquire_token_silent(
            scopes=SCOPES, account=None
        )  # type: ignore
        if result:
            self._access_token = result["access_token"]
            self._access_token_expires = int(time.time()) + result["expires_in"]
            return self._access_token
        else:
            self._access_token = None
            self._access_token_expires = None
            self.user = None

    def is_active(self):
        if self.user is None:
            return False
        if self._access_token is None:
            return False
        if self._access_token_expires is None:
            return False

        return int(time.time() + 30) < self._access_token_expires


def _remove_oldest_inactive_session():
    for session in _sessions.values():
        if not session.is_active():
            del _sessions[session.public_id]
            return


def _get_session(public_id: str | None) -> AuthSession:
    if public_id is None:
        public_id = str(uuid.uuid4())

    if public_id not in _sessions:
        if len(_sessions) >= MAX_SESSIONS:
            _remove_oldest_inactive_session()
        _sessions[public_id] = AuthSession(public_id=public_id)

    return _sessions[public_id]


def active_session(request: Request) -> AuthSession:
    session = _get_session(request.session.get("id"))
    token = session.get_access_token()
    if token is None:
        raise LoginRequiredException()
    return session


ActiveSessionDependency = Annotated[AuthSession, Depends(active_session)]


@router.get("/me")
def me(session: ActiveSessionDependency):
    response = requests.get(
        "https://graph.microsoft.com/v1.0/me",
        headers={"Authorization": f"Bearer {session.get_access_token()}"},
    )
    return response.json()


@router.get("/me/avatar")
def avatar(session: ActiveSessionDependency):
    response = requests.get(
        "https://graph.microsoft.com/v1.0/me/photo/$value",
        headers={"Authorization": f"Bearer {session.get_access_token()}"},
    )

    return Response(
        content=response.content, media_type=response.headers["Content-Type"]
    )


@router.get("/login")
def login(request: Request, next: str | None = None):
    redirect_uri = f"{settings().dataoffice_url}/oauth/callback"
    auth_session = _get_session(request.session.get("id"))
    request.session["id"] = auth_session.public_id
    auth_session.next = next
    auth_uri = auth_session.initiate_auth_code_flow(redirect_uri)
    return RedirectResponse(auth_uri)


@router.get("/logout")
def logout(request: Request):
    public_id: str = str(request.session.get("id"))
    if public_id and public_id in _sessions:
        del _sessions[public_id]

    return RedirectResponse(
        f"https://login.microsoftonline.com/{settings().auth_tenant_id}/oauth2/logout"
    )


@router.get("/oauth/callback")
def oauth_callback(request: Request):
    auth_session = _get_session(request.session.get("id"))
    auth_session.oauth_callback(dict(request.query_params))

    if auth_session.next:
        return RedirectResponse(auth_session.next)
    else:
        return auth_session.user


def redirect_to_login(request: Request):
    url = str(request.url)
    base_url = str(request.base_url)
    assert url.startswith(base_url)
    path_with_query = url[len(base_url) :]

    encoded_path_with_query = urllib.parse.quote(path_with_query)
    return RedirectResponse(f"/login?next=/{encoded_path_with_query}")


class LoginRequiredException(Exception):
    pass
