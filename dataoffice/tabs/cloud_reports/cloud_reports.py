import datetime
import urllib.parse

import httpx
from dataoffice.connectors import pipeline_manager
from dataoffice.connectors.pipeline_manager import Pipeline, get_pipelines_for_event
from dataoffice.settings import settings
from dataoffice.tab import Tab, TabDependencies
from starlette.responses import HTMLResponse

tab = Tab(
    title="Cloud Reports",
    template="cloud_reports/cloud_reports.html.j2",
    prefix="/cloud-reports",
    section="Scrapers",
    icon="fas fa-cloud",
)

cloud_reports_report_url = settings().cloud_reports_report_url


def _elastic_logs_view_url(pipeline: Pipeline):
    query = f'data_job.job_name.keyword : "cloud-reports" and data_job.pipeline_guids: "{pipeline.guid}"'
    query_escaped = urllib.parse.quote(query)
    return f"https://indiebi.kb.westeurope.azure.elastic-cloud.com:9243/s/dpt/app/observability-logs-explorer/?pageState=(breakdownField:log.level,query:(language:kuery,query:%27{query_escaped}%27),time:(from:now-90d,to:now),v:2)#/"


def _calculate_elapsed(pipeline: Pipeline):
    elapsed = (
        pipeline.finished_timestamp or datetime.datetime.now(datetime.timezone.utc)
    ) - pipeline.creation_timestamp
    return elapsed.total_seconds()


@tab.routes.get("/")
async def index(deps: TabDependencies):
    pipelines_page = await get_pipelines_for_event("SCRAPE_CLOUD_REPORTS_DAILY", 0, 100)

    pipelines = pipelines_page.data

    pipeline_rows = [
        {
            "pipeline": pipeline,
            "elastic_logs_view_url": _elastic_logs_view_url(pipeline),
            "elapsed": f"{_calculate_elapsed(pipeline):.1f}s",
        }
        for pipeline in pipelines
    ]

    total_count = pipelines_page.count

    return tab.render(
        deps,
        {
            "pipelines": pipeline_rows,
            "total_count": total_count,
            "cloud_reports_report_url": cloud_reports_report_url,
        },
    )


@tab.routes.post("/trigger-now")
async def trigger_now():
    try:
        await pipeline_manager.trigger_event(
            "SCRAPE_CLOUD_REPORTS_DAILY", {"dummy": "dummy"}
        )
    except httpx.HTTPStatusError as e:
        return HTMLResponse(
            f"Failed request: {e.response.status_code} {e.response.url} {e.response.text}"
        )
    except Exception as e:
        return HTMLResponse(f"Failed: {e}")
    return HTMLResponse(
        "Triggered successfully. Refresh the page to see the report on the list of recent runs."
    )
