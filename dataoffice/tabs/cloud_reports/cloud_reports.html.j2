<div class="card">
  <div class="card-body overflow-hidden p-lg-6">
    <div class="row align-items-center">
      <div class="col-12">
        <iframe src="{{ cloud_reports_report_url }}" style="width: 100%; height: 50vh"></iframe>
        <h2 class="my-3">Recent runs of cloud-reports</h2>
        <p>
          Cloud Reports run twice a day on a schedule, but can be run manually at any time.
          <button class="btn btn-falcon-primary mx-2 mb-1" type="button" hx-post="/cloud-reports/trigger-now" hx-target="#trigger-result" hx-indicator="#trigger-indicator">Run Now</button>
        <div id="trigger-result"></div>
        <div class="spinner-border spinner-border-sm htmx-indicator" role="status" id="trigger-indicator">
          <span class="visually-hidden">Loading...</span>
        </div>
        </p>

        <div class="table-responsive scrollbar">
          <table class="table table-hover table-striped overflow-hidden">
            <thead>
              <tr>
                <th scope="col">GUID</th>
                <th scope="col">Status</th>
                <th scope="col">Created</th>
                <th scope="col">Finished</th>
                <th scope="col">Elapsed</th>
                <th scope="col">Logs</th>
              </tr>
            </thead>
            <tbody>
              {% for row in pipelines: %}
              <tr class="align-middle">
                <td class="text-nowrap">
                  {{ row.pipeline.guid }}
                </td>
                <td class="text-nowrap">{{ row.pipeline.status.value }}</td>
                <td class="text-nowrap">{{ row.pipeline.creation_timestamp | humanize_date | safe }}</td>
                <td class="text-nowrap">{{ row.pipeline.finished_timestamp | humanize_date | safe }}</td>
                <td class="text-nowrap">{{ row.elapsed }}</td>
                <td class="text-nowrap"><a href="{{ row.elastic_logs_view_url }}" target="_blank">Elastic <i class="ps-1 fas fa-external-link-alt text-primary"></i></a></td>
                {# <td><span class="badge badge rounded-pill d-block p-2 badge-subtle-success">Completed<span
                      class="ms-1 fas fa-check" data-fa-transform="shrink-2"></span></span>
                </td> #}
              </tr>
              {% endfor %}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>
</div>
