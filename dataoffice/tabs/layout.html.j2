<!DOCTYPE html>
<html data-bs-theme="light" lang="en-US" dir="ltr" data-env="{{ 'prod' if env.startswith('prod') else 'dev' if env.startswith('dev') else 'local' }}">

<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1">


  <!-- ===============================================-->
  <!--    Document Title-->
  <!-- ===============================================-->
  <title>{{ title }}</title>


  <!-- ===============================================-->
  <!--    Favicons-->
  <!-- ===============================================-->
  <link id="favicon" rel="icon" type="image/png" href="/static/favicon_local.png" />
  <script>
    (function () {
      const env = document.documentElement.getAttribute('data-env');
      const favicon = document.getElementById('favicon');
      favicon.href = `/static/favicon_${env}.png`;
    })();
  </script>
  <meta name="theme-color" content="#ffffff">
  <script src="{{ url_for('static', path='config.js') }}"></script>
  <script src="{{ url_for('static', path='theme/vendors/simplebar/simplebar.min.js') }}"></script>


  <!-- ===============================================-->
  <!--    Stylesheets-->
  <!-- ===============================================-->
  <link rel="preconnect" href="https://fonts.gstatic.com">
  <link
    href="https://fonts.googleapis.com/css?family=Open+Sans:300,400,500,600,700%7cPoppins:300,400,500,600,700,800,900&amp;display=swap"
    rel="stylesheet">
  <link href="{{ url_for('static', path='theme/vendors/simplebar/simplebar.min.css') }}" rel="stylesheet">
  <link href="{{ url_for('static', path='theme/vendors/bootstrap/bootstrap-icons.css') }}" rel="stylesheet" id="bootstrap-icons">
  <link href="{{ url_for('static', path='theme/assets/css/theme-rtl.css') }}" rel="stylesheet" id="style-rtl">
  <link href="{{ url_for('static', path='theme/assets/css/theme.css') }}" rel="stylesheet" id="style-default">
  <link href="{{ url_for('static', path='theme/assets/css/user-rtl.css') }}" rel="stylesheet" id="user-style-rtl">
  <link href="{{ url_for('static', path='theme/assets/css/user.css') }}" rel="stylesheet" id="user-style-default">
  <link href="{{ url_for('static', path='dataoffice.css') }}" rel="stylesheet" id="user-style-default">
  <script>
    var isRTL = JSON.parse(localStorage.getItem('isRTL'));
    if (isRTL) {
      var linkDefault = document.getElementById('style-default');
      var userLinkDefault = document.getElementById('user-style-default');
      linkDefault.setAttribute('disabled', true);
      userLinkDefault.setAttribute('disabled', true);
      document.querySelector('html').setAttribute('dir', 'rtl');
    } else {
      var linkRTL = document.getElementById('style-rtl');
      var userLinkRTL = document.getElementById('user-style-rtl');
      linkRTL.setAttribute('disabled', true);
      userLinkRTL.setAttribute('disabled', true);
    }
  </script>
</head>


<body>

  <!-- ===============================================-->
  <!--    Main Content-->
  <!-- ===============================================-->
  <main class="main" id="top">
    <div class="container-fluid" data-layout="container">
      <script>
        var isFluid = JSON.parse(localStorage.getItem('isFluid'));
        if (isFluid) {
          var container = document.querySelector('[data-layout]');
          container.classList.remove('container');
          container.classList.add('container-fluid');
        }
      </script>
      {% if not embed: %}
      <nav class="navbar navbar-light navbar-vertical navbar-expand-xl navbar-card">
        <script>
          var navbarStyle = localStorage.getItem("navbarStyle");
          if (navbarStyle && navbarStyle !== 'transparent') {
            document.querySelector('.navbar-vertical').classList.add(`navbar-${navbarStyle}`);
          }
        </script>
        <div class="logo-container d-flex align-items-center">
          {# <div class="toggle-icon-wrapper">

            <button class="btn navbar-toggler-humburger-icon navbar-vertical-toggle" data-bs-toggle="tooltip"
              data-bs-placement="left" title="Toggle Navigation"><span class="navbar-toggle-icon"><span
                  class="toggle-line"></span></span></button>

          </div> #}
          <a class="navbar-brand" href="/">
            <div class="text-end py-2">
              <img
                class="me-2 dataoffice-logo"
                src="/static/logo-light_local.png"
                height="50"
                alt="dataoffice" />
              <div class="logo-env-tag">
                <span class="env-tag"></span>
              </div>
            </div>
          </a>
        </div>
        <div class="collapse navbar-collapse" id="navbarVerticalCollapse" style="position: relative; margin-top: 0;">
          <div class="navbar-vertical-content scrollbar">
            <ul class="navbar-nav flex-column mb-3" id="navbarVerticalNav">

              {% for section_title, sidebar_section in sidebar.items() %}
              <li class="nav-item">
                <!-- label-->
                {% if section_title != 'Home' %}
                <div class="row navbar-vertical-label-wrapper mt-3 mb-2">
                  <div class="col-auto navbar-vertical-label">{{ section_title }}
                  </div>
                  <div class="col ps-0">
                    <hr class="mb-0 navbar-vertical-divider" />
                  </div>
                </div>
                {% endif %}

                {% for sidebar_item in sidebar_section %}
                <a class="nav-link" href="{{ sidebar_item.href }}" @click="navigate('{{ sidebar_item.href }}')"
                  role="button">
                  <div class="d-flex align-items-center">
                    <span class="nav-link-icon"><span class="{{ sidebar_item.icon }}"></span></span>
                    <span class="nav-link-text ps-1">
                      {{ sidebar_item.title }}
                      {% if sidebar_item.subtitle %}<small>{{ sidebar_item.subtitle }}</small>{% endif %}
                    </span>
                  </div>
                </a>
                {% endfor %}
              </li>

              {% endfor %}
            </ul>

            <div class="settings my-3">
              <div class="card shadow-none">
                <div class="card-body alert mb-0" role="alert">
                  <div class="text-center"><img
                      src="{{ url_for('static', path='theme/assets/img/icons/spot-illustrations/navbar-vertical.png') }}"
                      alt="" width="80" />
                    <p class="fs-11 mt-2">Made with 🔥 by <a href="#!">DPT</a></p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </nav>
      {% endif %}
      <div class="content {{ 'pt-3' if embed else '' }}"> {# extra top padding in embed mode prevents unnecessary scrollbar from appearing #}
        {% if not embed: %}
        <nav class="navbar navbar-light navbar-glass navbar-top navbar-expand">

          <button class="btn navbar-toggler-humburger-icon navbar-toggler me-1 me-sm-3" type="button"
            data-bs-toggle="collapse" data-bs-target="#navbarVerticalCollapse" aria-controls="navbarVerticalCollapse"
            aria-expanded="false" aria-label="Toggle Navigation"><span class="navbar-toggle-icon"><span
                class="toggle-line"></span></span></button>
          <ul class="navbar-nav align-items-center d-none d-lg-block">
            {# -- Search Box
            <li class="nav-item">
              <div class="search-box" data-list='{"valueNames":["title"]}'>
                <form class="position-relative" data-bs-toggle="search" data-bs-display="static">
                  <input class="form-control search-input fuzzy-search" type="search" placeholder="Search..."
                    aria-label="Search" />
                  <span class="fas fa-search search-box-icon"></span>

                </form>
                <div class="btn-close-falcon-container position-absolute end-0 top-50 translate-middle shadow-none"
                  data-bs-dismiss="search">
                  <button class="btn btn-link btn-close-falcon p-0" aria-label="Close"></button>
                </div>
                <div class="dropdown-menu border font-base start-0 mt-2 py-0 overflow-hidden w-100">
                  <div class="scrollbar list py-3" style="max-height: 24rem;">
                    <h6 class="dropdown-header fw-medium text-uppercase px-x1 fs-11 pt-0 pb-2">Recently Browsed</h6><a
                      class="dropdown-item fs-10 px-x1 py-1 hover-primary"
                      href="{{ url_for('static', path='theme/app/events/event-detail.html') }}">
                      <div class="d-flex align-items-center">
                        <span class="fas fa-circle me-2 text-300 fs-11"></span>

                        <div class="fw-normal title">Pages <span class="fas fa-chevron-right mx-1 text-500 fs-11"
                            data-fa-transform="shrink-2"></span> Events</div>
                      </div>
                    </a>
                    <a class="dropdown-item fs-10 px-x1 py-1 hover-primary"
                      href="{{ url_for('static', path='theme/app/e-commerce/customers.html') }}">
                      <div class="d-flex align-items-center">
                        <span class="fas fa-circle me-2 text-300 fs-11"></span>

                        <div class="fw-normal title">E-commerce <span class="fas fa-chevron-right mx-1 text-500 fs-11"
                            data-fa-transform="shrink-2"></span> Customers</div>
                      </div>
                    </a>

                    <hr class="text-200 dark__text-900" />
                    <h6 class="dropdown-header fw-medium text-uppercase px-x1 fs-11 pt-0 pb-2">Suggested Filter</h6><a
                      class="dropdown-item px-x1 py-1 fs-9"
                      href="{{ url_for('static', path='theme/app/e-commerce/customers.html') }}">
                      <div class="d-flex align-items-center"><span
                          class="badge fw-medium text-decoration-none me-2 badge-subtle-warning">customers:</span>
                        <div class="flex-1 fs-10 title">All customers list</div>
                      </div>
                    </a>
                    <a class="dropdown-item px-x1 py-1 fs-9"
                      href="{{ url_for('static', path='theme/app/events/event-detail.html') }}">
                      <div class="d-flex align-items-center"><span
                          class="badge fw-medium text-decoration-none me-2 badge-subtle-success">events:</span>
                        <div class="flex-1 fs-10 title">Latest events in current month</div>
                      </div>
                    </a>
                    <a class="dropdown-item px-x1 py-1 fs-9"
                      href="{{ url_for('static', path='theme/app/e-commerce/product/product-grid.html') }}">
                      <div class="d-flex align-items-center"><span
                          class="badge fw-medium text-decoration-none me-2 badge-subtle-info">products:</span>
                        <div class="flex-1 fs-10 title">Most popular products</div>
                      </div>
                    </a>

                    <hr class="text-200 dark__text-900" />
                    <h6 class="dropdown-header fw-medium text-uppercase px-x1 fs-11 pt-0 pb-2">Files</h6><a
                      class="dropdown-item px-x1 py-2" href="#!">
                      <div class="d-flex align-items-center">
                        <div class="file-thumbnail me-2"><img class="border h-100 w-100 object-fit-cover rounded-3"
                            src="{{ url_for('static', path='theme/assets/img/products/3-thumb.png') }}" alt="" /></div>
                        <div class="flex-1">
                          <h6 class="mb-0 title">iPhone</h6>
                          <p class="fs-11 mb-0 d-flex"><span class="fw-semi-bold">Antony</span><span
                              class="fw-medium text-600 ms-2">27 Sep at 10:30 AM</span></p>
                        </div>
                      </div>
                    </a>
                    <a class="dropdown-item px-x1 py-2" href="#!">
                      <div class="d-flex align-items-center">
                        <div class="file-thumbnail me-2"><img class="img-fluid"
                            src="{{ url_for('static', path='theme/assets/img/icons/zip.png') }}" alt="" /></div>
                        <div class="flex-1">
                          <h6 class="mb-0 title">Falcon v1.8.2</h6>
                          <p class="fs-11 mb-0 d-flex"><span class="fw-semi-bold">John</span><span
                              class="fw-medium text-600 ms-2">30 Sep at 12:30 PM</span></p>
                        </div>
                      </div>
                    </a>

                    <hr class="text-200 dark__text-900" />
                    <h6 class="dropdown-header fw-medium text-uppercase px-x1 fs-11 pt-0 pb-2">Members</h6><a
                      class="dropdown-item px-x1 py-2"
                      href="{{ url_for('static', path='theme/pages/user/profile.html') }}">
                      <div class="d-flex align-items-center">
                        <div class="avatar avatar-l status-online me-2">
                          <img class="rounded-circle" src="{{ url_for('static', path='theme/assets/img/team/1.jpg') }}"
                            alt="" />

                        </div>
                        <div class="flex-1">
                          <h6 class="mb-0 title">Anna Karinina</h6>
                          <p class="fs-11 mb-0 d-flex">Technext Limited</p>
                        </div>
                      </div>
                    </a>
                    <a class="dropdown-item px-x1 py-2"
                      href="{{ url_for('static', path='theme/pages/user/profile.html') }}">
                      <div class="d-flex align-items-center">
                        <div class="avatar avatar-l me-2">
                          <img class="rounded-circle" src="{{ url_for('static', path='theme/assets/img/team/2.jpg') }}"
                            alt="" />

                        </div>
                        <div class="flex-1">
                          <h6 class="mb-0 title">Antony Hopkins</h6>
                          <p class="fs-11 mb-0 d-flex">Brain Trust</p>
                        </div>
                      </div>
                    </a>
                    <a class="dropdown-item px-x1 py-2"
                      href="{{ url_for('static', path='theme/pages/user/profile.html') }}">
                      <div class="d-flex align-items-center">
                        <div class="avatar avatar-l me-2">
                          <img class="rounded-circle" src="{{ url_for('static', path='theme/assets/img/team/3.jpg') }}"
                            alt="" />

                        </div>
                        <div class="flex-1">
                          <h6 class="mb-0 title">Emma Watson</h6>
                          <p class="fs-11 mb-0 d-flex">Google</p>
                        </div>
                      </div>
                    </a>

                  </div>
                  <div class="text-center mt-n3">
                    <p class="fallback fw-bold fs-8 d-none">No Result Found.</p>
                  </div>
                </div>
              </div>
            </li>
            #}
          </ul>
          <ul class="navbar-nav navbar-nav-icons ms-auto flex-row align-items-center">
            <li class="nav-item ps-2 pe-0">
              <div class="dropdown theme-control-dropdown"><a
                  class="nav-link d-flex align-items-center dropdown-toggle fa-icon-wait fs-9 pe-1 py-0" href="#"
                  role="button" id="themeSwitchDropdown" data-bs-toggle="dropdown" aria-haspopup="true"
                  aria-expanded="false"><span class="fas fa-sun fs-7" data-fa-transform="shrink-2"
                    data-theme-dropdown-toggle-icon="light"></span><span class="fas fa-moon fs-7"
                    data-fa-transform="shrink-3" data-theme-dropdown-toggle-icon="dark"></span><span
                    class="fas fa-adjust fs-7" data-fa-transform="shrink-2"
                    data-theme-dropdown-toggle-icon="auto"></span></a>
                <div class="dropdown-menu dropdown-menu-end dropdown-caret border py-0 mt-3"
                  aria-labelledby="themeSwitchDropdown">
                  <div class="bg-white dark__bg-1000 rounded-2 py-2">
                    <button class="dropdown-item d-flex align-items-center gap-2" type="button" value="light"
                      data-theme-control="theme"><span class="fas fa-sun"></span>Light<span
                        class="fas fa-check dropdown-check-icon ms-auto text-600"></span></button>
                    <button class="dropdown-item d-flex align-items-center gap-2" type="button" value="dark"
                      data-theme-control="theme"><span class="fas fa-moon" data-fa-transform=""></span>Dark<span
                        class="fas fa-check dropdown-check-icon ms-auto text-600"></span></button>
                    <button class="dropdown-item d-flex align-items-center gap-2" type="button" value="auto"
                      data-theme-control="theme"><span class="fas fa-adjust" data-fa-transform=""></span>Auto<span
                        class="fas fa-check dropdown-check-icon ms-auto text-600"></span></button>
                  </div>
                </div>
              </div>
            </li>
            {#-- top right corner notifications
            <li class="nav-item dropdown">
              <a class="nav-link notification-indicator notification-indicator-primary px-0 fa-icon-wait" id="navbarDropdownNotification" role="button" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false" data-hide-on-body-scroll="data-hide-on-body-scroll"><span class="fas fa-bell" data-fa-transform="shrink-6" style="font-size: 33px;"></span></a>
              <div class="dropdown-menu dropdown-caret dropdown-caret dropdown-menu-end dropdown-menu-card dropdown-menu-notification dropdown-caret-bg" aria-labelledby="navbarDropdownNotification">
                <div class="card card-notification shadow-none">
                  <div class="card-header">
                    <div class="row justify-content-between align-items-center">
                      <div class="col-auto">
                        <h6 class="card-header-title mb-0">Notifications</h6>
                      </div>
                      <div class="col-auto ps-0 ps-sm-3"><a class="card-link fw-normal" href="#">Mark all as read</a></div>
                    </div>
                  </div>
                  <div class="scrollbar-overlay" style="max-height:19rem">
                    <div class="list-group list-group-flush fw-normal fs-10">
                      <div class="list-group-title border-bottom">NEW</div>
                      <div class="list-group-item">
                        <a class="notification notification-flush notification-unread" href="#!">
                          <div class="notification-avatar">
                            <div class="avatar avatar-2xl me-3">
                              <img class="rounded-circle" src="{{ url_for('static', path='theme/assets/img/team/1-thumb.png') }}" alt="" />

                            </div>
                          </div>
                          <div class="notification-body">
                            <p class="mb-1"><strong>Emma Watson</strong> replied to your comment : "Hello world 😍"</p>
                            <span class="notification-time"><span class="me-2" role="img" aria-label="Emoji">💬</span>Just now</span>

                          </div>
                        </a>

                      </div>
                      <div class="list-group-item">
                        <a class="notification notification-flush notification-unread" href="#!">
                          <div class="notification-avatar">
                            <div class="avatar avatar-2xl me-3">
                              <div class="avatar-name rounded-circle"><span>AB</span></div>
                            </div>
                          </div>
                          <div class="notification-body">
                            <p class="mb-1"><strong>Albert Brooks</strong> reacted to <strong>Mia Khalifa's</strong> status</p>
                            <span class="notification-time"><span class="me-2 fab fa-gratipay text-danger"></span>9hr</span>

                          </div>
                        </a>

                      </div>
                      <div class="list-group-title border-bottom">EARLIER</div>
                      <div class="list-group-item">
                        <a class="notification notification-flush" href="#!">
                          <div class="notification-avatar">
                            <div class="avatar avatar-2xl me-3">
                              <img class="rounded-circle" src="{{ url_for('static', path='theme/assets/img/icons/weather-sm.jpg') }}" alt="" />

                            </div>
                          </div>
                          <div class="notification-body">
                            <p class="mb-1">The forecast today shows a low of 20&#8451; in California. See today's weather.</p>
                            <span class="notification-time"><span class="me-2" role="img" aria-label="Emoji">🌤️</span>1d</span>

                          </div>
                        </a>

                      </div>
                      <div class="list-group-item">
                        <a class="border-bottom-0 notification-unread  notification notification-flush" href="#!">
                          <div class="notification-avatar">
                            <div class="avatar avatar-xl me-3">
                              <img class="rounded-circle" src="{{ url_for('static', path='theme/assets/img/logos/oxford.png') }}" alt="" />

                            </div>
                          </div>
                          <div class="notification-body">
                            <p class="mb-1"><strong>University of Oxford</strong> created an event : "Causal Inference Hilary 2019"</p>
                            <span class="notification-time"><span class="me-2" role="img" aria-label="Emoji">✌️</span>1w</span>

                          </div>
                        </a>

                      </div>
                      <div class="list-group-item">
                        <a class="border-bottom-0 notification notification-flush" href="#!">
                          <div class="notification-avatar">
                            <div class="avatar avatar-xl me-3">
                              <img class="rounded-circle" src="{{ url_for('static', path='theme/assets/img/team/10.jpg') }}" alt="" />

                            </div>
                          </div>
                          <div class="notification-body">
                            <p class="mb-1"><strong>James Cameron</strong> invited to join the group: United Nations International Children's Fund</p>
                            <span class="notification-time"><span class="me-2" role="img" aria-label="Emoji">🙋‍</span>2d</span>

                          </div>
                        </a>

                      </div>
                    </div>
                  </div>
                  <div class="card-footer text-center border-top"><a class="card-link d-block" href="{{ url_for('static', path='theme/app/social/notifications.html') }}">View all</a></div>
                </div>
              </div>

            </li>
            #}

            <li class="nav-item dropdown"><a class="nav-link pe-0 ps-2" id="navbarDropdownUser" role="button"
                data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                <div class="avatar avatar-xl">
                  <img class="rounded-circle" src="/me/avatar" alt="" />

                </div>
              </a>
              <div class="dropdown-menu dropdown-caret dropdown-caret dropdown-menu-end py-0"
                aria-labelledby="navbarDropdownUser">
                <div class="bg-white dark__bg-1000 rounded-2 py-2">
                  <a class="dropdown-item fw-bold" href="#!"><span>{{ user.full_name }} <br /><small>{{
                        user.username }}</small></span></a>

                  <div class="dropdown-divider"></div>
                  <a class="dropdown-item"
                    href="/logout">Logout</a>
                </div>
              </div>
            </li>
          </ul>
        </nav>
        {% endif %}
        {# tab content goes here #}
        {% include tab_template %}
        {% if not embed: %}
        <footer class="footer">
          <div class="row g-0 justify-content-between fs-10 mt-4 mb-3">
            <div class="col-12 col-sm-auto text-center">
              <p class="mb-0 text-600">build version: {{ build_version }} | build timestamp: {{ build_timestamp }} | created using Falcon Boostrap Theme: <span class="d-none d-sm-inline-block">
                </span><br class="d-sm-none" /> 2024 &copy; <a href="https://themewagon.com">Themewagon</a></p>
            </div>
            <div class="col-12 col-sm-auto text-center">
              <p class="mb-0 text-600">Theme version v3.20.0</p>
            </div>
          </div>
        </footer>
        {% endif %}
      </div>
    </div>
  </main>
  <!-- ===============================================-->
  <!--    End of Main Content-->
  <!-- ===============================================-->



  <!-- ===============================================-->
  <!--    JavaScripts-->
  <!-- ===============================================-->
  <script src="{{ url_for('static', path='htmx.1.9.12.min.js') }}"></script>
  <script src="{{ url_for('static', path='theme/vendors/popper/popper.min.js') }}"></script>
  <script src="{{ url_for('static', path='theme/vendors/bootstrap/bootstrap.min.js') }}"></script>
  <script src="{{ url_for('static', path='theme/vendors/anchorjs/anchor.min.js') }}"></script>
  <script src="{{ url_for('static', path='theme/vendors/is/is.min.js') }}"></script>
  <script src="{{ url_for('static', path='theme/vendors/fontawesome/all.min.js') }}"></script>
  <script src="{{ url_for('static', path='theme/vendors/lodash/lodash.min.js') }}"></script>
  <script src="https://polyfill.io/v3/polyfill.min.js?features=window.scroll"></script>
  <script src="{{ url_for('static', path='theme/vendors/list.js/list.min.js') }}"></script>
  <script src="{{ url_for('static', path='theme/assets/js/theme.js') }}"></script>

</body>

</html>
