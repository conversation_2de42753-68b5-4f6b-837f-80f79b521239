<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Scraper Statuses</title>
    <link rel="stylesheet"
          href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-icons/1.11.1/font/bootstrap-icons.min.css">
    <link href="https://cdn.datatables.net/v/dt/jq-3.7.0/dt-2.1.8/b-3.2.0/sb-1.8.1/sp-2.3.3/sl-2.1.0/datatables.min.css"
          rel="stylesheet">
    <link href="https://cdn.datatables.net/datetime/1.5.5/css/dataTables.dateTime.min.css" rel="stylesheet">
    <style>
        .green {
            color: #36b37e;
        }

        .red {
            color: #e63757;
        }

        .yellow {
            color: yellow;
        }

        .light-blue {
            color: #00b8d9
        }

        .purple {
            color: #6554c0
        }

        .truncate {
            max-width: 100px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
    </style>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.30.1/moment.min.js"></script>
    <script src="https://cdn.datatables.net/v/dt/jq-3.7.0/dt-2.1.8/b-3.2.0/sb-1.8.1/sp-2.3.3/sl-2.1.0/datatables.min.js"></script>
    <script src="https://cdn.datatables.net/datetime/1.5.5/js/dataTables.dateTime.min.js"></script>

    <script>
        const renderDate = function (data) {
            if (!data) {
                return "-";
            }
            const date = new Date(data);
            if (isNaN(date)) {
                throw new Error("Invalid date format");
            }
            const humanizedStr = moment(date).fromNow();
            return `<span data-bs-toggle="tooltip" title="${date.toISOString()}">${humanizedStr}</span>`;
        }
        const renderState = function (data, type, item) {
            const blockedTimerThreshold = new Date(Date.now() - 60 * 60 * 1000); // 60 minutes ago
            const inProgress = ["STARTED", "SCHEDULED"];
            let colorClass;
            let content = item.state;

            if (item.state === "FINISHED") {
                colorClass = "green";
            } else if (inProgress.includes(item.state) && new Date(item.updated_at) < blockedTimerThreshold) {
                colorClass = "red";
                content = "STALE";
            } else if (item.state === "UNCONFIGURED" && new Date(item.updated_at) < blockedTimerThreshold) {
                colorClass = "yellow";
            } else if (item.state === "MANUALLY_UNBLOCKED") {
                colorClass = "yellow";
            } else if (inProgress.includes(item.state)) {
                colorClass = "yellow";
            } else if (["DISABLED", "MANUALLY_BLOCKED"].includes(item.state)) {
                colorClass = "gray";
            } else {
                colorClass = "red";
            }

            if (item.state === "FAILED" && item.last_fail_reason) {
                content = item.last_fail_reason;
            }

            return `<span class="${colorClass}">${content}</span>`;
        }
        const renderActionButtons = function (data, type, item) {
            return `
                        <button disabled onclick="retry('${item.organization_id}')" data-bs-toggle="tooltip"
                                title="Retry">
                            <i class="bi bi-arrow-repeat"></i>
                        </button>
                        <a href="${elasticLink(item.last_operation_id)}" target="_blank" rel="noopener noreferrer">
                            <button data-bs-toggle="tooltip" title="Open logs in Elastic">
                                <i class="bi bi-card-text"></i>
                            </button>
                        </a>
                        <a href="${operationHistoryLink(item.organization_id, item.source)}">
                            <button data-bs-toggle="tooltip" title="View Operation History">
                                <i class="bi bi-clock-history"></i>
                            </button>
                        </a>
                        <button onclick="toggleBlock('${item.organization_id}', '${item.user_id}', '${item.source}', '${item.state}', this)"
                                data-bs-toggle="tooltip"
                                title="${item.state === 'MANUALLY_BLOCKED' ? 'Unblock state updates' : 'Block state updates'}">
                            <i class="bi ${item.state === 'MANUALLY_BLOCKED' ? 'bi-unlock-fill text-success' : 'bi-lock-fill text-danger'}"></i>
                        </button>`
        }
        const elasticLink = function (operationId) {
            return `https://indiebi.kb.westeurope.azure.elastic-cloud.com/s/dpt/app/discover#/?_g=(filters:!(),refreshInterval:(pause:!t,value:60000),time:(from:now-1y%2Fd,to:now))&_a=(breakdownField:log.level,columns:!(message),dataSource:(dataViewId:'logs-*',type:dataView),density:compact,filters:!((meta:(alias:!n,disabled:!f,index:'dataset-logs-*-*',key:service.name,negate:!f,params:(query:scraper-service),type:phrase),query:(match_phrase:(service.name:scraper-service))),(meta:(alias:!n,disabled:!f,index:'dataset-logs-*-*',key:s2.operation_id,negate:!f,params:(query:'${operationId}'),type:phrase),query:(match_phrase:(s2.operation_id:'${operationId}')))),headerRowHeight:-1,hideChart:!f,interval:auto,query:(language:kuery,query:''),rowHeight:-1,sort:!(!('@timestamp',desc)))`
        }
        const operationHistoryLink = function (organizationId, source) {
            return `/scraper_operation_history/?tableState=%7B%22searchBuilder%22%3A%7B%22criteria%22%3A%5B%7B%22condition%22%3A%22%3D%22%2C%22data%22%3A%22Organization+ID%22%2C%22origData%22%3A%22organization_id%22%2C%22type%22%3A%22string%22%2C%22value%22%3A%5B%22${organizationId}%22%5D%7D%2C%7B%22condition%22%3A%22%3D%22%2C%22data%22%3A%22Source%22%2C%22origData%22%3A%22source%22%2C%22type%22%3A%22string%22%2C%22value%22%3A%5B%22${source}%22%5D%7D%5D%2C%22logic%22%3A%22AND%22%7D%7D`
        }
        $(document).ready(function () {
            $('#statuses').DataTable({
                columns: [
                    {data: 'organization_id', title: 'Org ID', width: '4rem'},
                    {
                        sortable: false,
                        searchable: false,
                        data: null,
                        title: 'Org',
                        render: function (data, type, item) {
                            return `${item.organization_name}`;
                        }
                    },
                    {data: 'user_id', title: 'User ID', visible: false},
                    {
                        sortable: false,
                        searchable: false,
                        data: null,
                        title: 'Email',
                        render: function (data, type, item) {
                            return `${item.user_email}`;
                        }
                    },
                    {data: 'source', title: 'Source'},
                    {data: 'updated_at', title: 'Updated', searchable: false, type: 'date', render: renderDate},
                    {data: 'consecutive_failed_scrape_count', title: 'Fail Count', searchable: false, type: 'num'},
                    {data: 'last_success_timestamp', title: 'Last Success', searchable: false, type: 'date', render: renderDate},
                    {data: 'last_fail_timestamp', title: 'Last Fail', type: 'date', searchable: false, render: renderDate},
                    {data: 'state', title: 'State', render: renderState},
                    {
                        data: 'last_fail_reason',
                        title: 'Last fail reason',
                        visible: false
                    },
                    {
                        data: 'last_operation_id',
                        title: 'Last operation ID',
                        visible: false,
                        searchable: false
                    },
                    {
                        data: null,
                        title: 'Actions',
                        orderable: false,
                        sortable: false,
                        searchable: false,
                        render: renderActionButtons,
                        width: '10rem'
                    },
                ],
                ajax: {
                    url: '/scraper_statuses/data'
                },
                serverSide: true,
                stateSave: true,
                scrollX: true,
                pageLength: 20,
                order: [[5, "desc"]],
                stateSaveCallback: function (_settings, data) {
                    var searchBuilderState = data.searchBuilder;
                    var state = JSON.stringify({searchBuilder: searchBuilderState});
                    var url = new URL(window.location);
                    url.searchParams.set('tableState', state);
                    window.history.replaceState(null, '', url);
                },
                stateLoadCallback: function (_settings) {
                    var url = new URL(window.location);
                    var state = url.searchParams.get('tableState');
                    if (state) {
                        var parsedState = JSON.parse(state);
                        return {
                            time: new Date().getTime(),
                            start: 0,
                            searchBuilder: parsedState.searchBuilder
                        };
                    }
                    return null;
                },
                layout: {
                    topStart: {
                        searchBuilder: {
                            columns: [0, 2, 4, 5, 6, 7, 8, 9, 10, 11]
                        }
                    },
                    bottomStart: [
                        {pageLength: {menu: [10, 20, 50, 100, 500]},},
                        'info'
                    ],
                    bottomEnd: 'paging'
                }
            });
        });

        function retry(organizationId) {
            console.log('retry ' + organizationId);
        }

        function toggleBlock(organizationId, userId, source, state, button) {
            console.log(organizationId, userId, source, state);

            // Disable the button to prevent multiple clicks
            button.disabled = true;

            fetch(state === 'MANUALLY_BLOCKED' ? '/scraper_statuses/unblock' : '/scraper_statuses/block', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({organization_id: organizationId, user_id: userId, source: source}),
            })
                .then(response => {
                    if (response.ok) {
                        console.log("OK");
                        // Reload the page after a successful request
                        location.reload();
                    } else {
                        console.error('Failed to toggle block state');
                        button.disabled = false; // Re-enable the button if the request fails
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    button.disabled = false; // Re-enable the button if an error occurs
                });
        }
    </script>
</head>

<body>
<table id="statuses" class="display" style="width:100%"></table>
</body>

</html>
