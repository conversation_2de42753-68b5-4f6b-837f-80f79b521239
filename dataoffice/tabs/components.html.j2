{#
Jinja2 macros for frequently used components.
Instead of copy-pasting html, we can use these macros.
See https://jinja.palletsprojects.com/en/3.0.x/templates/#macros

Using macros:
{% from "components.html.j2" import tab_header %}
{{ tab_header('Hello!') }}
#}

{% macro tab_header(title, subtitle='', icon='fas fa-home') -%}
<div class="d-flex mb-4 mt-3"><span class="fa-stack me-2 ms-n1">
    <i class="fas fa-circle fa-stack-2x text-300"></i><i
      class="fa-inverse fa-stack-1x {{ icon }}"></i></span>
  <div class="col">
    <h5 class="mb-0 text-primary position-relative"><span class="bg-200 dark__bg-1100 pe-3">{{ title }}</span><span
        class="border position-absolute top-50 translate-middle-y w-100 start-0 z-n1"></span></h5>
    <p class="mb-0">{{ subtitle }}</p>
  </div>
</div>
{%- endmacro %}
