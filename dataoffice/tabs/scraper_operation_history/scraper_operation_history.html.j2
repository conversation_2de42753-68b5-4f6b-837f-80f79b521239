<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Scraper Operation History</title>
    <link rel="stylesheet"
          href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-icons/1.11.1/font/bootstrap-icons.min.css">
    <link href="https://cdn.datatables.net/v/dt/jq-3.7.0/dt-2.1.8/b-3.2.0/sb-1.8.1/sp-2.3.3/sl-2.1.0/datatables.min.css"
          rel="stylesheet">
    <link href="https://cdn.datatables.net/datetime/1.5.5/css/dataTables.dateTime.min.css" rel="stylesheet">
    <style>
        .green {
            color: #36b37e;
        }

        .red {
            color: #e63757;
        }

        .yellow {
            color: yellow;
        }

        .light-blue {
            color: #00b8d9
        }

        .purple {
            color: #6554c0
        }
    </style>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.30.1/moment.min.js"></script>
    <script src="https://cdn.datatables.net/v/dt/jq-3.7.0/dt-2.1.8/b-3.2.0/sb-1.8.1/sp-2.3.3/sl-2.1.0/datatables.min.js"></script>
    <script src="https://cdn.datatables.net/datetime/1.5.5/js/dataTables.dateTime.min.js"></script>

    <script>

        const renderExecutionTime = function (data, type, item) {
            if (item.start_timestamp && item.end_timestamp) {
                const start = moment(item.start_timestamp, "YYYY-MM-DD HH:mm:ss.SSSZ");
                const end = moment(item.end_timestamp, "YYYY-MM-DD HH:mm:ss.SSSZ");
                const duration = moment.duration(end.diff(start));
                return `<span data-bs-toggle="tooltip" title="${duration.humanize()}">${Math.floor(duration.asHours()).toString().padStart(2, '0')}:${duration.minutes().toString().padStart(2, '0')}:${duration.seconds().toString().padStart(2, '0')}</span>`;
            }
            return '-';
        };
        const renderDate = function (data) {
            if (!data) {
                return "-";
            }
            const date = new Date(data);
            if (isNaN(date)) {
                throw new Error("Invalid date format");
            }
            const humanizedStr = moment(date).fromNow();
            return `<span data-bs-toggle="tooltip" title="${date.toISOString()}">${humanizedStr}</span>`;
        }
        const renderState = function (data, type, item) {
            const blockedTimerThreshold = new Date(Date.now() - 60 * 60 * 1000); // 60 minutes ago
            const inProgress = ["STARTED", "SCHEDULED"];
            let colorClass;
            let content = item.state;

            if (item.state === "FINISHED") {
                colorClass = "green";
            } else if (inProgress.includes(item.state) && new Date(item.updated_at) < blockedTimerThreshold) {
                colorClass = "red";
                content = "STALE";
            } else if (item.state === "UNCONFIGURED" && new Date(item.updated_at) < blockedTimerThreshold) {
                colorClass = "yellow";
            } else if (item.state === "MANUALLY_UNBLOCKED") {
                colorClass = "yellow";
            } else if (inProgress.includes(item.state)) {
                colorClass = "yellow";
            } else if (["DISABLED", "MANUALLY_BLOCKED"].includes(item.state)) {
                colorClass = "gray";
            } else {
                colorClass = "red";
            }

            if (item.state === "FAILED" && item.fail_reason) {
                content = item.fail_reason;
            }

            return `<span class="${colorClass}">${content}</span>`;
        }
        const renderActions = function (data, type, item) {
            return `<a href="${elasticLink(item.operation_id)}" target="_blank" rel="noopener noreferrer"><button class="btn btn-secondary" ${!item.operation_id ? 'disabled' : ''} data-bs-toggle="tooltip" title="Open logs in Elastic"><i class="bi bi-card-text"></i></button></a>`;
        }
        const elasticLink = function (operationId) {
            return `https://indiebi.kb.westeurope.azure.elastic-cloud.com/s/dpt/app/discover#/?_g=(filters:!(),refreshInterval:(pause:!t,value:60000),time:(from:now-1y%2Fd,to:now))&_a=(breakdownField:log.level,columns:!(message),dataSource:(dataViewId:'logs-*',type:dataView),density:compact,filters:!((meta:(alias:!n,disabled:!f,index:'dataset-logs-*-*',key:service.name,negate:!f,params:(query:scraper-service),type:phrase),query:(match_phrase:(service.name:scraper-service))),(meta:(alias:!n,disabled:!f,index:'dataset-logs-*-*',key:s2.operation_id,negate:!f,params:(query:'${operationId}'),type:phrase),query:(match_phrase:(s2.operation_id:'${operationId}')))),headerRowHeight:-1,hideChart:!f,interval:auto,query:(language:kuery,query:''),rowHeight:-1,sort:!(!('@timestamp',desc)))`
        }
        $(document).ready(function () {
            new DataTable('#operations', {
                order: [[3, "desc"]],
                columns: [
                    {data: 'operation_id', title: 'Operation ID'},
                    {data: 'organization_id', title: 'Organization ID'},
                    {data: 'source', title: 'Source'},
                    {data: 'updated_at', title: 'Updated At', searchable: false, type: 'date', render: renderDate},
                    {
                        data: 'start_timestamp',
                        title: 'Start timestamp',
                        searchable: false,
                        type: 'date',
                        render: renderDate
                    },
                    {
                        data: 'end_timestamp',
                        title: 'End timestamp',
                        searchable: false,
                        type: 'date',
                        render: renderDate
                    },
                    {
                        data: null,
                        title: 'Execution time',
                        searchable: false,
                        orderable: false,
                        render: renderExecutionTime
                    },
                    {data: 'state', title: 'State', render: renderState},
                    {
                        data: 'fail_reason',
                        title: 'Last fail reason',
                        visible: false,
                        searchable: false,
                        sortable: false,
                        orderable: false
                    },
                    {data: null, title: 'Actions', searchable: false, orderable: false, render: renderActions},
                ],
                serverSide: true,
                ajax: {
                    url: '/scraper_operation_history/data',
                },
                stateSave: true,
                pageLength: 20,
                stateSaveCallback: function (settings, data) {
                    var searchBuilderState = data.searchBuilder;
                    var state = JSON.stringify({searchBuilder: searchBuilderState});
                    var url = new URL(window.location);
                    url.searchParams.set('tableState', state);
                    window.history.replaceState(null, '', url);
                },
                stateLoadCallback: function (settings) {
                    var url = new URL(window.location);
                    var state = url.searchParams.get('tableState');
                    if (state) {
                        var parsedState = JSON.parse(state);
                        return {
                            time: new Date().getTime(),
                            start: 0,
                            searchBuilder: parsedState.searchBuilder
                        };
                    }
                    return null;
                },
                layout: {
                    topStart: {
                        searchBuilder: {
                            columns: [0, 1, 2, 3, 4, 5, 7, 8]
                        }
                    },
                    bottomStart: [
                        {pageLength: {menu: [10, 20, 50, 100, 500]},},
                        'info'
                    ],
                    bottomEnd: 'paging'
                }
            });
        });

        function retry(organizationId) {
            console.log('retry ' + organizationId);
        }
    </script>
</head>

<body>
    <table id="operations" class="display" style="width:100%"></table>
</body>

</html>
