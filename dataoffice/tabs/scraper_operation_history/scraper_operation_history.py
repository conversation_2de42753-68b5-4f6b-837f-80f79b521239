from dataoffice.connectors import scrapers_service
from dataoffice.tab import Tab, TabDependencies
from fastapi import Request

tab = Tab(
    title="Operation History",  # Tab title (as shown in the sidebar)
    template="scraper_operation_history/scraper_operation_history.html.j2",  # Default template for the tab
    prefix="/scraper_operation_history",  # URL prefix for the tab
    section="Scrapers",  # Section in the sidebar where the tab is shown
    icon="fa fa-clock",
)


@tab.routes.get("/")
async def index(deps: TabDependencies, request: Request):
    return tab.render(deps, {})


# just a proxy to the scrapers_service
@tab.routes.get("/data")
async def data(request: Request):
    return await scrapers_service.get_scraper_operation_history_datatable(
        request.url.query
    )
