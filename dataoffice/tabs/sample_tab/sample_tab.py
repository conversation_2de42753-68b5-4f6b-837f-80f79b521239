from dataoffice.tab import Tab, TabDependencies

tab = Tab(
    title="Sample Tab",  # Tab title (as shown in the sidebar)
    template="sample_tab/sample_tab.html.j2",  # Default template for the tab
    prefix="/sample-tab",  # URL prefix for the tab
    section="Sample pages",  # Section in the sidebar where the tab is shown
)


# add routes to the tab using the tab.routes object, which is a fastapi APIRouter
# the TabDependencies is a FastAPI dependency that provides common parameters required by tabs
# feel free to add more parameters to endpoints as you see fit, in the usual FastAPI way
# use tab.render() to render a full dataoffice page with the tab template specified above embedded.
# you can also add a number of other routes to the tab, that will handle dynamic JS requests or have deeper navigation in the tab.
# include TabDependencies in all endpoints to make sure the tab is available only to logged in users.


@tab.routes.get("/")
def index(deps: TabDependencies):
    return tab.render(deps, {})


@tab.routes.get("/error")
def error(deps: TabDependencies):
    raise Exception("This is a test error")
