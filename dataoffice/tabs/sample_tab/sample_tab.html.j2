{# This is a Jinja2 template. Template is filled with variables from tab.render()
on the backend before being sent to the client. #}
{# Look at other tabs, theme documentation and Boostrap 5 documentation to have an idea
on what visual components to put here. #}
<div class="card">
  <div class="card-body overflow-hidden p-lg-6">
    <div class="row align-items-center">
      <div class="col-lg-6"><img class="img-fluid"
          src="{{ url_for('static', path='theme/assets/img/icons/spot-illustrations/21.png') }}" alt="" />
      </div>
      <div class="col-lg-6 ps-lg-4 my-5 text-center text-lg-start">
        <h3 class="text-primary">Sample Tab</h3>
        <p class="lead">This is a sample dataoffice tab.</p><a class="btn btn-falcon-primary"
          href="{{ url_for('static', path='theme/documentation/getting-started.html') }}">Getting started</a>
      </div>
    </div>
  </div>
</div>
