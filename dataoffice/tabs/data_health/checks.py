from operator import itemgetter

import dateutil.parser
from dataoffice.connectors import (
    dataset_manager,
    pipeline_manager,
    report_service,
    scrapers_service,
)
from dataoffice.tabs.data_health.check_base import (
    CheckResult,
    DataHealthCheck,
    DataHealthJob,
    check,
    info,
    table,
)
from dataoffice.tabs.data_health.http_zip import preview_zip_file_async
from dataoffice.templating.htmx_response import humanize_date


@check("PowerBI Capacity")
async def check_powerbi_capacity(_: DataHealthJob, result: CheckResult):
    capacities = await dataset_manager.get_capacities()
    capacity_list = ""
    active_count = 0
    paused_count = 0
    unknown_count = 0
    for capacity in capacities:
        state_with_icon = "❗Unknown"
        if capacity.state == "Active":
            state_with_icon = "✅ Active"
            active_count += 1
        elif capacity.state == "Paused" and capacity.is_default_for_releases:
            state_with_icon = "⚙️ Paused (Release Capacity, OK to be paused)"
        elif capacity.state == "Paused":
            state_with_icon = "⏸️ Paused"

            paused_count += 1
        else:
            unknown_count += 1
        capacity_list += f" - **{capacity.name}** ({capacity.id}) Tier: {capacity.tier} State: {state_with_icon}\n"

    if paused_count > 0:
        result.fail(f"{paused_count} capacitites are paused", data=capacity_list)
        return
    if unknown_count > 0:
        result.fail(
            f"f{unknown_count} capacitites are in an unknown state", data=capacity_list
        )
        return
    result.success(f"{active_count} capacities active", data=capacity_list)


@check("PowerBI Capacity CPU Usage")
async def check_powerbi_capacity_cpu_usage(_: DataHealthJob, result: CheckResult):
    result.success("(TODO)")


@check("Report Freshness")
async def check_report_freshness(job: DataHealthJob, result: CheckResult):
    reports = await report_service.get_reports(
        report_service.ReportSearchParams(
            studio_id=job.user_legacy_id, limit=10, sort_by=["-upload_date"]
        )
    )

    if len(reports.data) == 0:
        result.fail("No reports found!")
        return

    last_report = reports.data[0]

    reports_table = table(
        ["ID", "Filename", "Source", "Date From", "Date To", "Uploaded"],
        [
            [
                str(report.id),
                report.original_name,
                report.source,
                str(report.date_from),
                str(report.date_to),
                humanize_date(report.upload_date),
            ]
            for report in reports.data
        ],
    )

    last_report_url = await report_service.download_report(last_report.id)

    result.success(
        f"Last report uploaded {humanize_date(last_report.upload_date)} ({reports.count} total)",
        data=f"#### Last 10 reports: \n\n {reports_table} Use _Manage reports_ tab to find more.\n\n TODO: link to report search\n\nLast report url: [link]({last_report_url})",
    )

    if last_report.original_name.endswith(".zip"):
        download_url = await report_service.download_report(last_report.id)
        preview = await preview_zip_file_async(download_url)
        preview_markdown = f"#### {last_report.original_name} contents:\n"
        for filename, file_bytes in preview.items():
            preview_markdown += (
                f"\n\n ##### {filename} \n\n```\n{file_bytes.decode()}\n```"
            )
        job.info(
            "Last Report Preview",
            message="Preview is available.",
            data=preview_markdown,
        )


@info("Shard Info")
async def info_shard(job: DataHealthJob, result: CheckResult):
    job.shard = await dataset_manager.get_active_shard(job.user_legacy_id)

    result.info(
        f"Active shard: `{job.shard.workspace_id}`",
        data=job.shard,
        data_type="keyvalue",
    )


@check("Last Successful PowerBI Refresh")
async def check_last_sucessful_pipeline(job: DataHealthJob, result: CheckResult):
    assert job.shard is not None
    execution_status_response = await pipeline_manager.get_execution_status(
        {
            "target": f'{{"shard_id": "{job.shard.workspace_id}"}}',
            "job_types": "pbi_refresh",
        }
    )

    if len(execution_status_response.data) < 1:
        result.fail(f"Pipeline for shard_id={job.shard.workspace_id} did not run")
        return

    execution_status = execution_status_response.data[0]

    pipeline_timestamp = execution_status.success_timestamp

    result.success(
        f"Last successful _pbi_refresh_: **{humanize_date(pipeline_timestamp)}**",
        data=execution_status,
        data_type="keyvalue",
    )


@check("Shard Timestamp Freshness")
async def check_shard_timestamp_freshness(job: DataHealthJob, result: CheckResult):
    assert job.shard is not None
    result.success(
        f"Shard refreshed **{humanize_date(job.shard.last_refresh_timestamp)}**"
    )


@info("PowerBI Link")
async def info_powerbi_link(job: DataHealthJob, result: CheckResult):
    assert job.shard is not None
    url = f"https://app.powerbi.com/groups/{job.shard.workspace_id}/reports"
    result.info(
        "",
        data={"url": url, "workspaceId": job.shard.workspace_id},
        data_type="powerbi_link",
    )


@check("Shard Data Freshness")
async def check_shard_data_freshness(job: DataHealthJob, result: CheckResult):
    query_result = await dataset_manager.dax_query(
        job.user_legacy_id,
        "EVALUATE {MAXX(fact_sales, fact_sales[date])}",
    )

    shard_last_date_str = query_result[0]["[Value]"]
    shard_last_date = dateutil.parser.parse(shard_last_date_str)

    result.success(f"Last date in PowerBI shard: **{humanize_date(shard_last_date)}**")


@info("Source Statuses")
async def source_statuses(job: DataHealthJob, result: CheckResult):
    if job.organization_id is None:
        result.fail(f"Organization id was not found for user {job.user_id}")
        return

    scraper_data = await scrapers_service.get_organization_scraper_statuses(
        job.organization_id
    )
    statuses: list = [
        {
            "organization_id": item.organization_id,
            "source": item.source,
            "sourceName": scrapers_service.sourceNameMapping[item.source],
            "state": item.state,
            "updated_at": item.updated_at.replace(tzinfo=None).isoformat() + "Z",
            "user_id": item.user_id,
            "last_fail_reason": item.last_fail_reason,
        }
        for item in scraper_data
    ]
    any_failed = any(status["state"] == "FAILED" for status in statuses)

    if any_failed:
        result.fail(
            message="Some sources have failed or stopped states.",
            data=sorted(statuses, key=itemgetter("source")),
            data_type="source_statuses",
        )
    else:
        result.info(
            message="",
            data=sorted(statuses, key=itemgetter("source")),
            data_type="source_statuses",
        )


DataHealthCheckGraph = list[DataHealthCheck | list[DataHealthCheck]]

all_checks: DataHealthCheckGraph = [
    check_powerbi_capacity,
    check_powerbi_capacity_cpu_usage,
    check_report_freshness,
    info_shard,
    [  # subgroups are executed in parallel
        check_last_sucessful_pipeline,
        check_shard_timestamp_freshness,
        info_powerbi_link,
        check_shard_data_freshness,
        source_statuses,
    ],
]
