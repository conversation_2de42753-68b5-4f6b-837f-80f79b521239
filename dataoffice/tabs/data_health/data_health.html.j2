{% from "components.html.j2" import tab_header %}

{{ tab_header('Data Health <small class="text-muted">BETA</small>'|safe, 'Quickly review common issues for a user', 'fas
fa-heartbeat') }}


<div class="card" id="data-health-app" v-cloak>
  <div class="card-body overflow-hidden p-lg-5">
    <div class="row">
      <div class="col">
        <div class="mb-3">
          <template v-if="selectedUserLoading">
            <div class="mb-2">
              <img :src="spinnerImage" alt="loading" width="48" />
              Loading...
            </div>
          </template>
          <template v-else-if="selectedUser">
            <div class="mb-2">Inspect data health issues for:</div>
            <div>
              <span class="badge rounded-pill bg-info text-dark me-2 px-3">${ selectedUser.user_id }</span>
              <span class="badge rounded-pill bg-warning text-dark me-2 px-3">${ selectedUser.user_legacy_id }</span>
              <span class="fw-bolder me-2">
                ${ selectedUser.company_name } / ${ selectedUser.user_first_name } ${ selectedUser.user_last_name }
              </span>
              <span class="text-muted me-2">${ selectedUser.user_email }</span>
              <button type="button" @click="selectedUser = undefined; job = undefined; setHistory()"
                class="btn-close btn-close-white" aria-label="Close"></button>
            </div>
          </template>
          <template v-else>
            <label for="user-search" class="form-label">Inspect data health issues for:</label>
            <div class="input-group mb-3">
              <input v-model="userSearchInput" @input="getUserHint" class="form-control" type="text" id="user-search"
                placeholder="Type ID, email, name or company to search...">
              <span class="input-group-text bg-transparent text-white" v-show="activeHintRequest">
                <span class="spinner-border spinner-border-sm" role="status">
                  <span class="visually-hidden">Loading...</span>
                </span>
              </span>
            </div>
            <p v-if="userSearchHintsError" v-html="userSearchHintsError" class="text-danger"></p>
            <div class="list-group my-3" id="userSearchHints">
              <a v-for="item in userSearchHints" @click.prevent="selectedUser = item"
                class="list-group-item list-group-item-action bg-transparent text-white" href="#">
                <span class="badge rounded-pill bg-info text-dark me-2 px-3">${ item.user_id }</span>
                <span class="badge rounded-pill bg-warning text-dark me-2 px-3">${ item.user_legacy_id }</span>
                <span class="fw-bolder me-2">${ item.company_name } / ${ item.user_first_name } ${ item.user_last_name
                  }</span>
                <span class="text-muted me-2">${ item.user_email }</span>
              </a>
            </div>
          </template>
        </div>
        <div class="mb-3">
          <div class="accordion my-3" v-if="job" id="job-messages">
            <div v-for="(message, index) in job.messages" class="accordion-item bg-dark text-light border-dark">
              <h5 class="accordion-header border-dark">
                <button class="accordion-button bg-transparent text-light shadow-none" type="button"
                  data-bs-toggle="collapse" :data-bs-target="'#message-'+index" aria-expanded="false"
                  aria-controls="'message-'+index">
                  <i class="me-3 fas fa-info-circle text-light" v-if="message.status === 'info'"></i>
                  <i class="me-3 fas fa-times-circle text-danger" v-if="message.status === 'fail'"></i>
                  <i class="me-3 fas fa-check-circle text-success" v-if="message.status === 'pass'"></i>
                  <div class="me-3 spinner-border spinner-border-sm" role="status" v-if="message.status === 'running'">
                    <span class="visually-hidden">Loading...</span>
                  </div>
                  <span class="fw-semibold text-secondary pe-3">${ message.title }</span> <span
                    v-html="message.message_html"></span>
                </button>
              </h5>
              <div :id="'message-'+index" class="accordion-collapse collapse" data-bs-parent="#job-messages">
                <div class="accordion-body">
                  <div class="me-2" v-if="message.data_type === 'text'" v-html="message.data_html"></div>
                  <div class="me-2" v-if="message.data_type === 'keyvalue'">
                    <span v-for="(value, key) in message.data_html">
                      <strong>${ key }:</strong> <span v-html="value"></span>
                      <br />
                    </span>
                  </div>
                  <div class="me-2" v-if="message.data_type === 'exception'">
                    <pre>${ message.data } </pre>
                  </div>
                  <div class="me-2" v-if="message.data_type === 'request_error'">
                    ${message.data.method} ${ message.data.url }
                    <br />
                    <span v-if="message.status_code"><strong>${message.data.status_code}</strong>
                      ${message.data.status_code_text}</span>
                    <br />
                    <pre>${ message.data.response } </pre>
                  </div>
                  <div class="me-2" v-if="message.data_type === 'powerbi_link'">
                    <a :href="message.data.url" target="_blank">Open PowerBI Workspace: ${message.data.url}</a>
                    <br />
                    <button @click="addDebugAdmins(message.data.workspaceId)"
                      class="btn btn-falcon-warning me-1 mt-3 mb-3" type="button">
                      Add Debug Admins
                    </button>
                    <br />
                    <span v-if="debugAdminsStatus" class="text-muted">${ debugAdminsStatus }<br /></span>
                    <strong>Add Debug Admins:</strong> assign permissions in PowerBI to specific users to allow manual
                    debugging of the workspace.
                    The user list is currently hardcoded in Dataset Manager. See
                    <a target="_blank"
                      href="https://gitlab.com/bluebrick/indiebi/data-platform-team/dataset-manager/-/blob/main/dataset_manager/config.py">
                      here
                    </a>
                    for a full list.

                  </div>
                  <div class="me-2" v-if="message.data_type === 'source_statuses'">
                    <div v-if="message.data.length === 0" class="accordion-body">
                      <div class="text-center text-muted py-4">
                        <i class="fas fa-info-circle me-2"></i>
                        No source statuses available for this user
                      </div>
                    </div>
                    <div class="row g-3">
                      <div v-for="source in message.data" class="col-md-6 col-lg-4">
                        <div class="d-flex align-items-center">
                          <i class="fas fa-circle me-2" :class="'state-' + source.state.toLowerCase()"></i>
                          <div>
                            <div class="fw-bold">${source.sourceName} - <span
                                :class="'state-' + source.state.toLowerCase()">${source.state}</span></div>
                            <div class="small text-muted">
                              Last update: ${ new Date(source.updated_at).toLocaleString() }
                            </div>
                            <div v-if="source.last_fail_reason" class="small text-muted"></div>
                            Last fail reason: <span
                              :class="'state-' + source.state.toLowerCase()">${source.last_fail_reason} </span>
                          </div>

                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="text-muted me-2" v-if="message.time_elapsed_human != '0ms'">
                    ${ message.status === 'running' ? 'Running for' : 'Done in'} ${ message.time_elapsed_human }
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="mb-3">
        <button v-show="selectedUser && (!job || job.finished)" type="button" class="btn btn-falcon-primary"
          @click="runChecks">${ job ? "Run checks again" : "Run checks"}</button>
      </div>
      <div class="mb-3" v-show="job && !job.finished">
        <img :src="spinnerImage" alt="loading" width="48" />
        Loading...
      </div>
    </div>
  </div>
</div>
</div>

<style>
  /* Status colors - text */
  .state-finished {
    color: #22c55e;
  }

  .state-failed {
    color: #ef4444;
  }

  .state-started {
    color: #3b82f6;
  }

  .state-scheduled {
    color: #a855f7;
  }

  .state-stopped {
    color: #f97316;
  }

  .state-unconfigured {
    color: #94a3b8;
  }

  .state-deleted {
    color: #64748b;
  }
</style>

<script type="module">
  import { createApp } from 'https://unpkg.com/vue@3/dist/vue.esm-browser.js'
  const delay = ms => new Promise(res => setTimeout(res, ms));
  function randomSpinnerImage() {
    return `/static/spinners/${Math.floor(Math.random() * 6) + 1}.gif`
  }

  async function retryWithDelay(func, initDelay, maxDelay, exponential) {
    // call func in a loop until func returns a truthy value.
    // after each call, wait a certain amount of milliseconds, starting from initDelay
    // and finishing with maxDelay, increasing exponential times after each attempt.
    let delayMs = initDelay
    while (true) {
      const result = await func()
      if (result) {
        return
      }
      await delay(delayMs)
      delayMs = Math.min(maxDelay, delayMs * exponential)
    }
  }

  const app = createApp({
    data() {
      return {
        userSearchInput: '',
        job: undefined,
        userSearchHints: [],
        userSearchHintsError: undefined,
        selectedUser: undefined,
        selectedUserLoading: false,
        activeHintRequest: undefined,
        spinnerImage: randomSpinnerImage(),
        debugAdminsStatus: undefined,
      }
    },
    methods: {
      async runChecks() {

        this.setHistory()

        this.debugAdminsStatus = undefined

        let response = await fetch("/data-health/jobs", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            user_id: this.selectedUser.user_id,
            user_legacy_id: this.selectedUser.user_legacy_id
          })
        })

        let oldJobStr = await response.text()
        this.job = JSON.parse(oldJobStr)

        while (!this.job.finished) {
          await retryWithDelay(async () => {
            response = await fetch(`/data-health/jobs/${this.job.key}`)
            const jobStr = await response.text()
            if (jobStr !== oldJobStr) {
              oldJobStr = jobStr
              this.job = JSON.parse(jobStr)
              return true
            }
          }, 100, 5000, 1.2)
        }
      },
      async getUserHint() {
        try {
          this.activeHintRequest = this.userSearchInput
          const response = await fetch(`/data-health/user-hint?input_text=${this.userSearchInput}`)
          if (response.redirected) {
            window.location = '/login'
          }
          if (!response.ok) {
            this.userSearchHintsError = `Failed to get user hints. Are you connected to VPN?<pre>${await response.text()}</pre>`
            return
          }
          const userSearchHints = await response.json()
          if (this.activeHintRequest === this.userSearchInput) {
            this.userSearchHints = userSearchHints.slice(0, 10)
            this.userSearchHintsError = undefined
          }
        } catch (e) {
          e.re
          console.log(e)
        } finally {
          if (this.activeHintRequest === this.userSearchInput) {
            this.activeHintRequest = undefined
          }
        }
      },
      async setUser(userId) {
        const response = await fetch(`/data-health/user-hint?input_text=${userId}`)
        const userSearchHints = await response.json()
        this.selectedUser = userSearchHints[0]
        this.runChecks()
      },
      setHistory() {
        const url = new URL(location);
        if (this.selectedUser) {
          url.hash = `#user=${this.selectedUser.user_id}`;
        } else {
          url.hash = ''
        }
        history.pushState({}, "", url);
      },
      async loadHistory(doReload) {
        if (window.location.hash && window.location.hash.length > 1) {
          try {
            this.selectedUserLoading = true;
            const urlParams = new URLSearchParams(window.location.hash.slice(1));
            const userId = urlParams.get('user')
            if (userId) {
              await this.setUser(userId)
            }
          } finally {
            this.selectedUserLoading = false;
          }
        } else if (doReload) {
          location.reload()
        }
      },
      async addDebugAdmins(workspaceId) {
        this.debugAdminsStatus = "Loading..."
        try {
          const response = await fetch(`/data-health/add-debug-admins/${workspaceId}`, { method: "POST" })
          if (response.ok) {
            this.debugAdminsStatus = "OK"
          } else {
            this.debugAdminsStatus = `Failed to add debug admins: ${response.status} ${await response.text()}`
          }
        } catch (e) {
          this.debugAdminsStatus = `Failed to add debug admins: ${e}`
        }
      }
    },
    async beforeMount() {
      await this.loadHistory(false)
      window.onpopstate = async (event) => {
        await this.loadHistory(true)
      }
    }
  })

  // to avoid conflicts with jinja2
  app.config.compilerOptions.delimiters = ['${', '}']
  app.mount('#data-health-app')
</script>
