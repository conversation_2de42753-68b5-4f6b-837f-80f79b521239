import asyncio
import datetime
import random

import fastapi
import httpx
from dataoffice.connectors import dataset_manager, user_service
from dataoffice.tab import Tab, TabDependencies
from dataoffice.tabs.data_health.check_base import DataHealthJob, perform_check
from dataoffice.tabs.data_health.checks import all_checks
from pydantic import BaseModel

tab = Tab(
    title="Data Health",
    template="data_health/data_health.html.j2",
    prefix="/data-health",
    section="Client Issues",
    icon="fas fa-heartbeat",
)


class DataHealthJobRequest(BaseModel):
    user_id: str
    user_legacy_id: int


_active_jobs: dict[int, DataHealthJob] = {}


class UserHint(BaseModel):
    user_id: str
    user_legacy_id: int
    user_email: str
    user_first_name: str
    user_last_name: str
    company_name: str


@tab.routes.get("/")
async def index(deps: TabDependencies):
    return tab.render(deps, {})


async def run_data_health_job(job: <PERSON>HealthJob):
    try:
        for check in all_checks:
            if isinstance(check, list):
                # subgroups run in parallel
                successes = await asyncio.gather(
                    *[perform_check(job, subcheck) for subcheck in check]
                )
                if not all(successes):
                    break
            else:
                succeeded = await perform_check(job, check)
                if not succeeded:
                    break

    finally:
        job.finished = datetime.datetime.now()


def _delete_expired_jobs():
    now = datetime.datetime.now()
    keys_to_delete: list[int] = []
    for key, job in _active_jobs.items():
        if job.finished is not None and (now - job.finished).total_seconds() > 120:
            keys_to_delete.append(key)

    for key in keys_to_delete:
        del _active_jobs[key]


def _random_key() -> int:
    while True:
        key = random.randint(1000, 9999)
        if key not in _active_jobs:
            return key


@tab.routes.post("/jobs")
async def new_data_health_job(
    body: DataHealthJobRequest,
    background_tasks: fastapi.BackgroundTasks,
):
    _delete_expired_jobs()

    key = _random_key()
    organization_id = await user_service.get_organization_id_for_user(body.user_id)
    new_job = DataHealthJob(
        created=datetime.datetime.now(),
        key=key,
        user_id=body.user_id,
        user_legacy_id=body.user_legacy_id,
        organization_id=organization_id,
    )
    _active_jobs[key] = new_job
    background_tasks.add_task(run_data_health_job, new_job)

    return new_job


@tab.routes.get("/jobs/{key}")
async def get_job(key: int):
    job = _active_jobs.get(key)
    if job is None:
        raise fastapi.HTTPException(status_code=404, detail=f"Job {key} not found")

    return job


@tab.routes.get("/user-hint")
async def user_hint(input_text: str) -> list[UserHint]:
    def _build_hint(user: user_service.User):
        return UserHint(
            user_id=user.id,
            user_legacy_id=user.legacy_id,
            user_email=user.email,
            user_first_name=user.first_name,
            user_last_name=user.last_name,
            company_name=user.company_name,
        )

    user_hints: list[UserHint] = []

    try:
        if input_text.startswith("u-"):
            # if the user types in something that looks like a user ID,
            # and it matches a user exactly,
            # make sure to put that result at the top of the list
            user = await user_service.get_user(input_text)
            if user.exists:
                user_hints.append(_build_hint(user))

        users = await user_service.search_users(input_text, offset=0, limit=5)

        user_hints += [_build_hint(user) for user in users]

        return user_hints
    except httpx.ConnectError as e:
        raise fastapi.HTTPException(503, str(e))
    except httpx.HTTPError as e:
        raise fastapi.HTTPException(503, str(e))


@tab.routes.post("/add-debug-admins/{workspace_id}")
async def add_debug_admins(
    workspace_id: str,
):
    await dataset_manager.add_debug_admins(workspace_id)
