import asyncio
import concurrent.futures
import os
from abc import abstractmethod
from io import BytesIO
from typing import IO, Any, Iterable, List
from zipfile import ZipFile

import httpx
from typing_extensions import Buffer

_client = httpx.Client()


def _get_content_length(url: str):
    response = _client.head(url)
    response.raise_for_status()
    return int(response.headers.get("content-length"))


def _get_range(url: str, range_start: int, range_end: int) -> bytes:
    # print(f"GET {range_start} {range_end}")
    headers = {
        "Range": f"bytes={range_start}-{range_end}",
        "x-ms-version": "2024-05-04",
    }
    response = _client.get(url, headers=headers)
    response.raise_for_status()
    return response.content


PAGE_SIZE = 16 * 1024


class SeekableFileBase(IO[bytes]):
    """
    Stub implementation of a file-like object that's readable and seekable, but not writeable.
    Descendants need to only implement the seek(), tell() and read() functions,
    compliant with the file-object protocol.
    """

    def readable(self) -> bool:
        return True

    def seekable(self) -> bool:
        return True

    @abstractmethod
    def seek(self, offset: int, whence: int = 0) -> int: ...

    @abstractmethod
    def tell(self) -> int: ...

    @abstractmethod
    def read(self, size: int = -1) -> bytes: ...

    @property
    def mode(self) -> str:
        return "r"

    @property
    def name(self) -> str:
        raise NotImplementedError()

    def close(self) -> None:
        pass

    @property
    def closed(self) -> bool:
        return False

    def fileno(self) -> int:
        raise NotImplementedError()

    def flush(self) -> None:
        pass

    def isatty(self) -> bool:
        return False

    def readline(self, limit: int = -1) -> bytes:
        raise NotImplementedError()

    def readlines(self, hint: int = -1) -> List[bytes]:
        raise NotImplementedError()

    def truncate(self, size: int | None = -1) -> int:
        raise NotImplementedError()

    def writable(self) -> bool:
        return False

    def write(self, s: Buffer) -> int:
        raise NotImplementedError()

    def writelines(self, lines: Iterable[Buffer]) -> None:
        raise NotImplementedError()

    def __enter__(self) -> "IO[bytes]":
        return self

    def __exit__(self, type: Any, value: Any, traceback: Any) -> None:
        self.close()

    def __iter__(self):
        return self

    def __next__(self) -> bytes:
        result = self.read()
        if len(result) == 0:
            raise StopIteration()
        return result


class HttpFile(SeekableFileBase):
    """
    A read-only, file-like object that converts IO operations into HTTP requests on a given URL.
    The targeted HTTP server must support `Range` headers.
    """

    def __init__(self, url: str):
        self.url = url
        self._pages: dict[int, bytes] = {}
        self._cursor = 0
        self._length = -1

    def seek(self, offset: int, whence: int = 0) -> int:
        if whence == os.SEEK_SET:
            self._cursor = offset
        if whence == os.SEEK_CUR:
            self._cursor += offset
        if whence == os.SEEK_END:
            self._cursor = self._get_length() + offset

        # print(f"SEEK {offset} {whence} -> {self._cursor}")

        return self._cursor

    def tell(self) -> int:
        # print(f"TELL {self._cursor}")
        return self._cursor

    def read(self, size: int = -1) -> bytes:
        # print("READ", size)
        if size == -1:
            size = self._get_length() - self._cursor

        end = self._cursor + size
        buf = BytesIO()

        while self._cursor < end:
            current_page = self._cursor // PAGE_SIZE
            offset_on_page = self._cursor % PAGE_SIZE
            end_on_page = min(PAGE_SIZE, end - current_page * PAGE_SIZE)
            page = self._load_page(current_page)
            # print(
            #     f"{self._cursor=} {end=} {current_page=} {offset_on_page=} {end_on_page=}"
            # )
            data = page[offset_on_page:end_on_page]
            self._cursor += len(data)
            buf.write(data)

        # print(f"Read {len(buf.getvalue())} bytes")
        return buf.getvalue()

    @property
    def name(self) -> str:
        return ""

    def _get_length(self):
        if self._length == -1:
            self._length = _get_content_length(self.url)
        return self._length

    def _load_page(self, page_number: int):
        page = self._pages.get(page_number)
        if not page:
            page_start = page_number * PAGE_SIZE
            page_end = min(self._get_length(), page_start + PAGE_SIZE)
            page = _get_range(self.url, page_start, page_end)
            self._pages[page_number] = page
        return page


def preview_zip_file(url: str):
    """
    Previews a ZIP file available over HTTP at `url`.
    Returns a dictionary, where keys are filenames inside the zip, and values are the first 1024 bytes of each file.
    Uses `HEAD` and `GET` with `Range:` header to minimize the content that's actually downloaded, so even multi-MB ZIP files
    can be previewed quickly.
    """

    data: dict[str, bytes] = {}

    with ZipFile(HttpFile(url)) as f:
        for zipentry in f.filelist:
            with f.open(zipentry) as zipentry_f:
                data[zipentry.filename] = zipentry_f.read(1024)

    return data


_pool = concurrent.futures.ThreadPoolExecutor()


async def preview_zip_file_async(url: str):
    loop = asyncio.get_running_loop()
    return await loop.run_in_executor(_pool, preview_zip_file, url)
