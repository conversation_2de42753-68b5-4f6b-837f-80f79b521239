import datetime
import http.client
import json
import traceback
from io import <PERSON><PERSON>
from typing import Any, Awaitable, Callable, Literal, cast

import httpx
from dataoffice.connectors.dataset_manager import Shard
from dataoffice.templating.htmx_response import humanize_date
from markdown_it import MarkdownIt
from pydantic import BaseModel, computed_field

MessageStatus = Literal["pass", "fail", "info", "running"]
DataType = Literal[
    "text",
    "keyvalue",
    "exception",
    "request_error",
    "powerbi_link",
    "source_statuses",
]


_markdown = MarkdownIt("commonmark", {"breaks": True, "html": True}).enable("table")
_markdown.add_render_rule("table_open", lambda *_: '<table class="table">')


class CheckResult(BaseModel):
    message: str | None = None
    status: MessageStatus = "pass"
    data_type: DataType = "text"
    data: Any | None = None
    title: str
    created: datetime.datetime
    finished: datetime.datetime | None = None

    def _update(
        self, status: MessageStatus, message: str, data: Any, data_type: DataType
    ):
        self.status = status
        self.message = message
        self.data = data
        self.data_type = data_type

    def info(self, message: str, data: Any = None, data_type: DataType = "text"):
        self._update("info", message, data, data_type)

    def success(self, message: str, data: Any = None, data_type: DataType = "text"):
        self._update("pass", message, data, data_type)

    def fail(self, message: str, data: Any = None, data_type: DataType = "text"):
        self._update("fail", message, data, data_type)

    def progress(self, message: str, data: Any = None, data_type: DataType = "text"):
        self._update("running", message, data, data_type)

    def time_elapsed(self) -> float:
        end_time = self.finished or datetime.datetime.now()
        return (end_time - self.created).total_seconds()

    @computed_field
    def message_html(self) -> str:
        return _markdown.renderInline(self.message or "")

    @computed_field
    def data_html(self) -> str | dict[str, str] | None:
        if self.data is None:
            return None
        if isinstance(self.data, str):
            return _markdown.render(self.data)
        if self.data_type == "keyvalue":
            if isinstance(self.data, BaseModel):
                items = self.data.model_dump().items()
            elif isinstance(self.data, dict):
                items = cast(dict[str, Any], self.data).items()  # type: ignore
            else:
                items = self.data

            def _render_item(value: Any | None):
                if value is None:
                    value = "_None_"
                if isinstance(value, datetime.datetime):
                    value = humanize_date(value)
                return _markdown.renderInline(str(value))

            return {key: _render_item(value) for key, value in items}
        return None

    @computed_field
    def time_elapsed_human(self) -> str:
        if not self.finished:
            # don't show time elapsed for live jobs,
            # because this messes up client-side change detection
            return ""
        seconds = self.time_elapsed()
        if seconds < 1.5:
            return f"{seconds*1000:.0f}ms"
        return f"{seconds:.2f}s"


class DataHealthJob(BaseModel):
    user_id: str
    organization_id: str | None
    user_legacy_id: int
    messages: list[CheckResult] = []
    created: datetime.datetime
    finished: datetime.datetime | None = None
    key: int

    shard: Shard | None = None

    def info(
        self,
        title: str,
        data: Any,
        data_type: DataType = "text",
        message: str | None = None,
    ):
        self.messages.append(
            CheckResult(
                title=title,
                message=message,
                data=data,
                created=datetime.datetime.now(),
                status="info",
                data_type=data_type,
            )
        )


DataHealthCheck = Callable[[DataHealthJob, CheckResult], Awaitable[None]]


def check(title: str) -> Callable[[DataHealthCheck], DataHealthCheck]:
    def decorator(check: DataHealthCheck) -> DataHealthCheck:
        setattr(check, "__title", title)
        setattr(check, "__type", "check")
        return check

    return decorator


def info(title: str) -> Callable[[DataHealthCheck], DataHealthCheck]:
    def decorator(check: DataHealthCheck) -> DataHealthCheck:
        setattr(check, "__title", title)
        setattr(check, "__type", "info")
        return check

    return decorator


async def perform_check(job: DataHealthJob, check: DataHealthCheck):
    result = CheckResult(
        title=getattr(check, "__title"),
        created=datetime.datetime.now(),
        status="running",
        data_type="text",
        data=None,
    )

    try:
        job.messages.append(result)
        await check(job, result)
        return True
    except httpx.HTTPStatusError as e:
        result.status = "fail"
        host = e.request.url.netloc.decode()
        result.message = (
            f"Service {host} responded with an error: {e.response.status_code}"
        )

        response_text = e.response.text
        try:
            # if response is valid JSON, pretty print it; otherwise, leave it as text
            response_json = json.loads(response_text)
            response_text = json.dumps(response_json, indent=4)
        except json.JSONDecodeError:
            pass

        result.data = {
            "url": str(e.request.url),
            "method": e.request.method,
            "status_code": e.response.status_code,
            "status_code_text": http.client.responses[e.response.status_code],
            "response": response_text,
        }
        result.data_type = "request_error"

    except httpx.RequestError as e:
        result.status = "fail"
        host = e.request.url.netloc.decode()
        result.message = f"Could not get a response from {host}."
        result.data = {
            "url": str(e.request.url),
            "method": e.request.method,
            "response": str(e),
        }
        result.data_type = "request_error"
    except Exception as e:
        result.status = "fail"
        result.message = f"Unexpected error: {e}"
        result.data = traceback.format_exc()
        result.data_type = "exception"
    finally:
        result.finished = datetime.datetime.now()

    return False


def table(headers: list[str], rows: list[list[str]]):
    """
    Convenience function to render a markdown table.
    """
    with StringIO() as io:
        io.write(f"| {' | '.join(headers)} |\n")
        io.write("| --- " * len(headers) + "|\n")
        for row in rows:
            io.write(f"| {' | '.join(row)} |\n")
        io.write("\n")
        return io.getvalue()
