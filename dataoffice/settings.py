import logging
import os
from enum import StrEnum
from typing import Annotated, Literal, Type

from pydantic import Field
from pydantic_settings import BaseSettings, SettingsConfigDict


class SettingsEnv(StrEnum):
    test = "test"
    local = "local"
    dev = "dev"
    prod = "prod"


class BaseAppSettings(BaseSettings):
    # url that Dataoffice is served on
    dataoffice_url: str = "http://localhost:8666"
    env_files: list[str] = [".env"]

    # credentials for the Entra App Registration that allows Dataoffice to do auth
    auth_client_id: str
    auth_tenant_id: str
    auth_client_secret: str

    # secret key used for signing session cookies; should be a long random string
    cookie_secret_key: str

    # url and api keys for the services that Dataoffice depends on
    pipeline_manager_url: str
    pipeline_manager_key: str

    user_service_url: str
    user_service_key: str

    dataset_manager_url: str
    dataset_manager_key: str

    report_service_url: str
    report_service_key: str

    scraper_service_url: str
    scraper_service_key: str

    # url where cloud-reports publishes execution reports
    cloud_reports_report_url: str

    default_connection_timeout: int = 10

    build_version: str = "local"
    build_timestamp: str = "-"


class ProdSettings(BaseAppSettings):
    env: Literal[SettingsEnv.prod] = SettingsEnv.prod
    env_files: list[str] = [".env", ".env.prod"]
    model_config = SettingsConfigDict(env_file=env_files, env_file_encoding="utf-8")

    pipeline_manager_url: str = "https://pipeline-manager.indiebi.com"
    user_service_url: str = "https://user-service-v2.indiebi.com"
    dataset_manager_url: str = "https://dataset-manager.indiebi.com"
    report_service_url: str = "https://report-service.indiebi.com"
    scraper_service_url: str = "https://scraper-service.indiebi.com"
    cloud_reports_report_url: str = "https://cloud-reports-report.indiebi.com"


class DevSettings(BaseAppSettings):
    env: Literal[SettingsEnv.dev] = SettingsEnv.dev
    env_files: list[str] = [".env", ".env.dev"]
    model_config = SettingsConfigDict(env_file=env_files, env_file_encoding="utf-8")

    pipeline_manager_url: str = "https://pipeline-manager.indiebi.dev"
    user_service_url: str = "https://user-service-v2.indiebi.dev"
    dataset_manager_url: str = "https://dataset-manager.indiebi.dev"
    report_service_url: str = "https://report-service.indiebi.dev"
    scraper_service_url: str = "https://scraper-service.indiebi.dev"
    cloud_reports_report_url: str = "https://cloud-reports-report.indiebi.dev"


class LocalSettings(DevSettings):
    env: Literal[SettingsEnv.local] = SettingsEnv.local  # type: ignore
    env_files: list[str] = [".env", ".env.local"]
    model_config = SettingsConfigDict(env_file=env_files, env_file_encoding="utf-8")

    scraper_service_key: str = "ApiTest123!"
    scraper_service_url: str = "http://127.0.0.1:8000"


Settings = Annotated[
    ProdSettings | DevSettings | LocalSettings,
    Field(discriminator="env"),
]

_settings_map: dict[SettingsEnv, Type[Settings]] = {
    SettingsEnv.local: LocalSettings,
    SettingsEnv.dev: DevSettings,
    SettingsEnv.prod: ProdSettings,
}


_settings: Settings | None = None


def settings() -> Settings:
    # having settings being lazy loaded makes it easier to swap
    # out the settings object for testing.
    # Instantiating Settings should fail if some of the required
    # vars are not defined, but we want tests to be able to inject their own settings.
    global _settings
    if _settings is None:
        env = os.getenv("ENV", SettingsEnv.dev)
        if env not in _settings_map:
            raise ValueError(
                f"Unknown environment: {env}. Available: {list(_settings_map.keys())}"
            )

        SettingClass = _settings_map[env]
        _settings = SettingClass()  # type: ignore

        log_about_settings_being_loaded(env, _settings.env_files)

    return _settings


def log_about_settings_being_loaded(env: SettingsEnv, env_files: list[str]):
    RESET = "\033[0m"
    RED = "\033[91m"
    BOLD = "\033[1m"

    files = f"{RESET}, {RED}".join(env_files)

    # set to 'uvicorn' so it will be visible in uvicorn logs
    logger = logging.getLogger("uvicorn")
    logger.info(
        f"{BOLD}Settings loaded for:{RESET} {RED}{env.upper()}{RESET} (from files: {RED}{files}{RESET})"
    )
