import asyncio

import humps
from dataoffice.api.api_tags import ApiT<PERSON>
from dataoffice.api.utils import strip_managed_partner_suffix
from dataoffice.connectors.report_service import get_organization_coverage
from dataoffice.connectors.user_service import search_organizations
from fastapi import APIRouter
from pydantic import BaseModel, RootModel

router = APIRouter(prefix="/api/managed-partners/report-coverage")


class Period(BaseModel):
    dateFrom: str
    dateTo: str


class OrganizationCoverageModel(BaseModel):
    organizationId: str
    organizationName: str
    coverage: dict[str, list[Period]]


class OrganizationCoverageModelResponse(RootModel[list[OrganizationCoverageModel]]):
    model_config = {
        "json_schema_extra": {
            "example": [
                {
                    "organizationId": "org-1",
                    "organizationName": "Organization 1",
                    "coverage": {
                        "steam_sales": [
                            {"dateFrom": "2020-01-01", "dateTo": "2024-12-31"}
                        ],
                        "playstation_sales": [
                            {"dateFrom": "2020-01-01", "dateTo": "2020-01-10"},
                            {"dateFrom": "2020-01-20", "dateTo": "2024-12-31"},
                        ],
                    },
                },
                {
                    "organizationId": "org-2",
                    "organizationName": "Organization 2",
                    "coverage": {
                        "epic_sales": [
                            {"dateFrom": "2021-01-01", "dateTo": "2024-12-31"}
                        ],
                    },
                },
            ]
        }
    }


@router.get(
    "/",
    response_model=OrganizationCoverageModelResponse,
    tags=[ApiTag.MANAGED_PARTNERS],
    summary="Get report coverage for managed partners",
)
async def get_report_coverage() -> OrganizationCoverageModelResponse:
    # TODO: Implement pagination for organizations, before we will get 1000 managed partners
    organizations = await search_organizations("Managed", 0, 1000)

    coverage_tasks = [get_organization_coverage(org.id) for org in organizations]
    coverage_results = await asyncio.gather(*coverage_tasks)

    results = []
    for org, coverage in zip(organizations, coverage_results):
        results.append(
            {
                "organizationId": org.id,
                "organizationName": strip_managed_partner_suffix(org.name),
                "coverage": {
                    source: [humps.camelize(period) for period in periods]
                    for source, periods in coverage.items()
                },
            }
        )

    return OrganizationCoverageModelResponse(root=results)
