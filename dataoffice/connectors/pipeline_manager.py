import enum
from datetime import datetime
from typing import Any

import httpx
from dataoffice.connectors.common import (
    PaginatedResponse,
    ServiceHealth,
    check_service_health,
    get_model,
)
from dataoffice.settings import settings
from pydantic import BaseModel

_client = httpx.AsyncClient(
    base_url=settings().pipeline_manager_url,
    headers={"x-api-key": settings().pipeline_manager_key},
    timeout=settings().default_connection_timeout,
)


class PipelineExecutionStatus(BaseModel):
    id: int
    target_text: str
    last_success_output: dict[str, Any] | None = None
    latest_error: dict[str, Any] | None = None
    requested_job_guid: str | None = None
    active_job_guid: str | None = None
    trigger_timestamp: datetime | None = None
    success_timestamp: datetime | None = None
    request_timestamp: datetime | None = None
    try_count: int | None = None
    job_type: str | None = None


class PipelineStatus(str, enum.Enum):
    RUNNING = "RUNNING"
    SUCCESS = "SUCCESS"
    FAILED = "FAILED"


class Pipeline(BaseModel):
    guid: str
    event_name: str
    pipeline_definition_name: str
    event_params: dict
    creation_timestamp: datetime
    finished_timestamp: datetime | None = None
    status: PipelineStatus


async def get_execution_status(
    params: dict[str, Any] | None = None,
) -> PaginatedResponse[PipelineExecutionStatus]:
    return await get_model(
        _client, PaginatedResponse[PipelineExecutionStatus], "/execution-status", params
    )


async def get_pipelines_for_event(event_name: str, offset: int, limit: int):
    return await get_model(
        _client,
        PaginatedResponse[Pipeline],
        "/pipeline",
        params={"event_names": event_name, "offset": offset, "limit": limit},
    )


async def trigger_event(event_name: str, params: dict[str, Any]):
    response = await _client.post(
        "/scheduler/event", json={"name": event_name, "params": params}
    )
    response.raise_for_status()


async def check_health() -> ServiceHealth:
    return await check_service_health(_client)
