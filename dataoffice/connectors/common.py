from typing import Any, Type

import httpx
from pydantic import BaseModel, TypeAdapter


class PaginatedResponse[T](BaseModel):
    data: list[T]
    count: int


class ServiceHealth(BaseModel):
    is_healthy: bool
    error: str | None = None


async def check_service_health(client: httpx.AsyncClient) -> ServiceHealth:
    try:
        response = await client.get("/health")
        response.raise_for_status()
        return ServiceHealth(is_healthy=True)
    except httpx.HTTPStatusError as e:
        return ServiceHealth(
            is_healthy=False, error=f"HTTP error: {e.response.status_code}"
        )
    except httpx.RequestError as e:
        return ServiceHealth(
            is_healthy=False,
            error=f"Request error: {str(e)}. Url: '{client.base_url}'",
        )


async def get_model[T](
    client: httpx.AsyncClient,
    model: Type[T],
    path: str,
    params: dict[str, Any] | None = None,
) -> T:
    if params:
        params = {k: v for k, v in params.items() if v is not None}
    response = await client.get(path, params=params)
    response.raise_for_status()
    return TypeAdapter(model).validate_json(response.content)
