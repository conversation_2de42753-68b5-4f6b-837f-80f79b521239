from datetime import date, datetime
from typing import Dict, List, Sequence, TypedDict

import httpx
from dataoffice.connectors.common import ServiceHealth, check_service_health, get_model
from dataoffice.settings import settings
from pydantic import BaseModel

_client = httpx.AsyncClient(
    base_url=settings().report_service_url,
    headers={"x-api-key": settings().report_service_key},
    timeout=settings().default_connection_timeout,
)


class ReportSearchParams(BaseModel):
    ids: list[int] | None = None
    studio_id: int | None = None
    portals: list[str] | None = None
    upload_type: str | None = None
    sources: list[str] | None = None
    offset: int = 0
    limit: int | None
    sort_by: list[str] | None = ["-upload_date"]


class Report(BaseModel):
    id: int
    studio_id: int
    organization_id: str | None
    source: str
    file_path_raw: str
    original_name: str
    portal: str
    date_from: date | None
    date_to: date | None
    no_data: bool
    upload_date: datetime
    state: str
    upload_type: str


class ReportSearchResultPage(BaseModel):
    data: Sequence[Report]
    count: int


class CoveragePeriod(TypedDict):
    date_from: str
    date_to: str


class OrganizationCoverage(Dict[str, List[CoveragePeriod]]):
    pass


async def get_reports(params: ReportSearchParams) -> ReportSearchResultPage:
    return await get_model(
        _client,
        ReportSearchResultPage,
        "/reports",
        params=params.model_dump(exclude_unset=True),
    )


async def download_report(report_id: int) -> str:
    return await get_model(_client, str, f"reports/{report_id}/download-url")


async def get_organization_coverage(organization_id: str) -> OrganizationCoverage:
    response = await _client.get(f"/reports/scrape-coverage/{organization_id}")
    response.raise_for_status()
    return response.json()


async def check_health() -> ServiceHealth:
    return await check_service_health(_client)
