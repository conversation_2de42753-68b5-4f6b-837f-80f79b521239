import math
import os
from datetime import datetime

import psutil
from dataoffice.connectors import (
    dataset_manager,
    pipeline_manager,
    report_service,
    scrapers_service,
    user_service,
)
from dateutil.relativedelta import relativedelta


async def get_services_health():
    user_service_health = await user_service.check_health()
    pipeline_manager_health = await pipeline_manager.check_health()
    dataset_manager_health = await dataset_manager.check_health()
    report_service_health = await report_service.check_health()
    scrapers_service_health = await scrapers_service.check_health()

    services_health = {
        "userService": user_service_health.model_dump(),
        "pipelineManager": pipeline_manager_health.model_dump(),
        "datasetManager": dataset_manager_health.model_dump(),
        "reportService": report_service_health.model_dump(),
        "scrapersService": scrapers_service_health.model_dump(),
    }

    unhealthy_services = [
        name for name, status in services_health.items() if not status["is_healthy"]
    ]
    total_services = len(services_health)
    healthy_services = total_services - len(unhealthy_services)

    return {
        "status": "ok" if healthy_services == total_services else "error",
        "details": services_health,
    }


def get_uptime():
    start_time = datetime.fromtimestamp(psutil.Process(os.getpid()).create_time())
    now = datetime.now()
    uptime = relativedelta(now, start_time)
    uptime_in_seconds = math.floor((now - start_time).total_seconds())

    return {
        "seconds": uptime_in_seconds,
        "readable": f"{uptime.years} years, {uptime.months} months, {uptime.days} days, {uptime.hours} hours, {uptime.minutes} minutes, {uptime.seconds} seconds",
    }
