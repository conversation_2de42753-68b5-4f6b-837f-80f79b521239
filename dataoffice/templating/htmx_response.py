from datetime import datetime, timedelta, timezone
from typing import Any, Mapping

import arrow
import fastapi
import jinja2
from dataoffice.connectors.scrapers_service import (
    ScraperStatusProjection,
)
from fastapi.templating import Jinja2Templates

_templates: Jinja2Templates | None = None


def operation_logs_elastic_link(operation_id: str):
    return f"https://indiebi.kb.westeurope.azure.elastic-cloud.com/s/dpt/app/discover#/?_g=(filters:!(),refreshInterval:(pause:!t,value:60000),time:(from:now-1y%2Fd,to:now))&_a=(breakdownField:log.level,columns:!(message),dataSource:(dataViewId:'logs-*',type:dataView),density:compact,filters:!((meta:(alias:!n,disabled:!f,index:'dataset-logs-*-*',key:service.name,negate:!f,params:(query:scraper-service),type:phrase),query:(match_phrase:(service.name:scraper-service))),(meta:(alias:!n,disabled:!f,index:'dataset-logs-*-*',key:s2.operation_id,negate:!f,params:(query:'{operation_id}'),type:phrase),query:(match_phrase:(s2.operation_id:'{operation_id}')))),headerRowHeight:-1,hideChart:!f,interval:auto,query:(language:kuery,query:''),rowHeight:-1,sort:!(!('@timestamp',desc)))"


def humanize_date(date: datetime | None):
    """
    Returns a Bootstrap-compatible HTML snippet that represents
    the date a neat & human-readable way.
    """
    if date is None:
        return "-"
    assert isinstance(
        date, datetime
    )  # to prevent unsafe strings leaking through to HTML
    humanized_str = arrow.get(date).humanize()

    return f'<span data-bs-toggle="tooltip" title="{date.strftime("%Y-%m-%d %H:%M:%S.%f")[:-3] + "Z"}">{humanized_str}</span>'


def format_timestamp(date: datetime | None):
    """
    Returns a Bootstrap-compatible HTML snippet that represents
    the date a neat & human-readable way.
    """
    if date is None:
        return "-"
    assert isinstance(
        date, datetime
    )  # to prevent unsafe strings leaking through to HTML
    return date.strftime("%Y-%m-%d %H:%M:%S.%f")[:-3] + "Z"


def display_scraper_status(item: ScraperStatusProjection) -> str:
    blocked_timer_threshold = datetime.now(timezone.utc) - timedelta(minutes=60)
    in_progress = ["STARTED", "SCHEDULED", "CONFIGURED"]

    match (item.state, item.updated_at < blocked_timer_threshold):
        case ("FINISHED", _):
            color_class = "green"
        case (state, True) if state in in_progress:
            color_class = "red"
            item.state = "STALE"
        case ("UNCONFIGURED", True) | ("MANUALLY_UNBLOCKED", _):
            color_class = "yellow"
        case ("STARTED", False):
            color_class = "light-blue"
        case ("SCHEDULED", False):
            color_class = "purple"
        case ("CONFIGURED", False):
            color_class = "light-blue"
        case ("DISABLED", _) | ("MANUALLY_BLOCKED", _):
            color_class = "gray"
        case _:
            color_class = "red"

    content = (
        item.last_fail_reason
        if item.state == "FAILED" and item.last_fail_reason
        else item.state
    )

    return f'<td class="{color_class}">{content}</td>'


def get_template(template: str):
    global _templates
    if _templates is None:
        _templates = Jinja2Templates("dataoffice/tabs")
        _templates.env.filters["humanize_date"] = humanize_date
        _templates.env.filters["operation_logs_elastic_link"] = (
            operation_logs_elastic_link
        )
        _templates.env.filters["format_timestamp"] = format_timestamp
        _templates.env.filters["display_scraper_status"] = display_scraper_status
    return _templates.get_template(template)


class HTMXResponse(fastapi.Response):
    media_type = "text/html"

    def __init__(
        self,
        request: fastapi.Request,
        template: str,
        context: dict[str, Any] | None = None,
        status_code: int = 200,
        headers: Mapping[str, str] | None = None,
    ) -> None:
        self.template: jinja2.Template = get_template(template)
        self.context = context or {}
        self.context["request"] = request
        super().__init__(None, status_code, headers)

    def render(self, content: Any) -> bytes:
        return self.template.render(**self.context).encode()
