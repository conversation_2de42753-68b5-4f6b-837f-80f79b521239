from dataclasses import dataclass
from typing import Annotated, Any

from dataoffice import sidebar
from dataoffice.auth import ActiveSessionDependency
from dataoffice.settings import settings
from dataoffice.templating.htmx_response import HTMXResponse
from fastapi import APIRouter, Depends, Request


def _tab_dependencies(
    request: Request, session: ActiveSessionDependency, embed: bool = False
):
    return request, session, embed


TabDependencies = Annotated[
    tuple[Request, ActiveSessionDependency, bool], Depends(_tab_dependencies)
]


@dataclass
class Tab:
    title: str
    template: str
    prefix: str
    section: str
    icon: str = "fas fa-star"

    def __post_init__(self):
        self.routes = APIRouter(prefix=self.prefix)

    def render(self, deps: TabDependencies, context: dict[str, Any] | None):
        if len(deps) == 2:
            request, session = deps
            embed = False
        else:
            request, session, embed = deps
        context = context or {}
        context["sidebar"] = sidebar.get_sidebar()
        context["user"] = session.user
        context["title"] = f"{context.get('title') or self.title} - Dataoffice"
        context["tab_template"] = self.template
        context["build_version"] = settings().build_version
        context["build_timestamp"] = settings().build_timestamp
        context["env"] = settings().env
        context["embed"] = embed
        return HTMXResponse(request, "layout.html.j2", context=context)

    def as_sidebar_item(self):
        return sidebar.Item(
            title=self.title,
            href=self.prefix or "/",
            icon=self.icon,
            section=self.section,
        )
