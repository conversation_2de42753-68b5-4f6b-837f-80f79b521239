{
    "[python]": {
        "editor.codeActionsOnSave": {
            "source.fixAll": "explicit",
        },
        "editor.defaultFormatter": "charliermarsh.ruff",
        "editor.formatOnSave": true,
        "editor.rulers": [
            88,
        ]
    },
    "html.format.wrapAttributes": "preserve",
    "html.format.wrapLineLength": 0,
    "editor.formatOnSave": true,
    "editor.trimAutoWhitespace": true,
    "files.exclude": {
        "**/.pytest_cache": true,
        "**/__pycache__": true,
        "**/.ruff_cache": true,
    },
    "files.insertFinalNewline": true,
    "files.trimTrailingWhitespace": true,
    "python.analysis.autoFormatStrings": true,
    "python.analysis.autoImportCompletions": true,
    "python.analysis.inlayHints.callArgumentNames": "partial",
    "python.analysis.inlayHints.functionReturnTypes": true,
    "python.analysis.inlayHints.pytestParameters": true,
    "python.analysis.inlayHints.variableTypes": true,
    "python.analysis.typeCheckingMode": "basic",
    "python.testing.pytestArgs": [
        "-vvv",
        "--random-order",
    ],
    "python.testing.pytestEnabled": true,
    "python.testing.unittestEnabled": false,
    "ruff.lint.args": [
        // For better debugging expirience. After commenting out parts of code, vscode
        // will not remove the imports, which will be needed in case of uncommenting
        // the code. Ruff will remove unsued imports while running within a git hook
        "--unfixable=F401",
    ],
    "workbench.colorCustomizations": {
        "activityBar.activeBackground": "#ef8259",
        "activityBar.background": "#ef8259",
        "activityBar.foreground": "#15202b",
        "activityBar.inactiveForeground": "#15202b99",
        "activityBarBadge.background": "#a8f7bd",
        "activityBarBadge.foreground": "#15202b",
        "commandCenter.border": "#15202b99",
        "sash.hoverBorder": "#ef8259",
        "statusBar.background": "#ea5f2b",
        "statusBar.foreground": "#15202b",
        "statusBarItem.hoverBackground": "#ce4714",
        "statusBarItem.remoteBackground": "#ea5f2b",
        "statusBarItem.remoteForeground": "#15202b",
        "titleBar.activeBackground": "#ea5f2b",
        "titleBar.activeForeground": "#15202b",
        "titleBar.inactiveBackground": "#ea5f2b99",
        "titleBar.inactiveForeground": "#15202b99"
    },
    "peacock.color": "#ea5f2b",
    "typescript.referencesCodeLens.showOnAllFunctions": true,
    "typescript.referencesCodeLens.enabled": true,
    "javascript.referencesCodeLens.showOnAllFunctions": true,
    "javascript.referencesCodeLens.enabled": true,
    "cSpell.words": [
        "dataoffice"
    ],
    "[vue]": {
      "editor.defaultFormatter": "esbenp.prettier-vscode"
    },
    "[typescript]": {
      "editor.defaultFormatter": "esbenp.prettier-vscode"
    },
    "[javascript]": {
      "editor.defaultFormatter": "esbenp.prettier-vscode"
    },
    "[html]": {
      "editor.defaultFormatter": "esbenp.prettier-vscode"
    }
}
