exclude: '^(frontend/public/theme/|static/)'

repos:
  # Backend
  - repo: https://github.com/python-poetry/poetry
    rev: 1.8.3
    hooks:
      - id: poetry-check
        stages: [pre-push]
      - id: poetry-lock
        stages: [pre-push]
      - id: poetry-install
        stages: [pre-push]

  - repo: https://github.com/astral-sh/ruff-pre-commit
    rev: v0.4.5
    hooks:
      - id: ruff
        name: "Backend: lint"
        args: [--fix]
        stages: [pre-commit]
      - id: ruff-format
        name: "Backend: format"
        stages: [pre-commit]

  - repo: local
    hooks:
      - id: tests-unit
        name: "Backend: unit tests"
        entry: poe test-unit
        language: system
        pass_filenames: false
        stages: [pre-commit]
        files: ^dataoffice/.*$

      - id: tests-integration
        name: "Backend: integration tests"
        entry: poe test-integration
        language: system
        pass_filenames: false
        files: ^dataoffice/.*$

  # Frontend
  - repo: local
    hooks:
      - id: npm-lint
        name: "Frontend: lint"
        entry: bash -c "cd frontend && npm run lint"
        language: system
        pass_filenames: false
        stages: [pre-commit]
        files: ^frontend/.*\.(js|ts|vue)$

      - id: npm-lint-stylelint
        name: "Frontend: lint: stylelint"
        entry: bash -c "cd frontend && npm run lint:css"
        language: system
        pass_filenames: false
        stages: [pre-commit]
        files: ^frontend/.*\.(css|scss|vue)$

      - id: npm-format
        name: "Frontend: format"
        entry: bash -c "cd frontend && npm run format"
        language: system
        pass_filenames: false
        stages: [pre-commit]
        files: ^frontend/.*\.(js|ts|vue|css|json|md)$

      - id: npm-test-unit
        name: "Frontend: unit tests"
        entry: bash -c "cd frontend && npm run test:unit"
        language: system
        pass_filenames: false
        stages: [pre-commit]
        files: ^frontend/.*\.(js|ts|vue|css|json|md)$

      - id: npm-type-check
        name: "Frontend: type-check"
        entry: bash -c "cd frontend && npm run type-check"
        language: system
        pass_filenames: false
        stages: [pre-commit]
        files: ^frontend/.*\.(ts|vue)$

  # General
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.6.0
    hooks:
      - id: trailing-whitespace
        stages: [pre-commit]

      - id: end-of-file-fixer
        stages: [pre-commit]

  - repo: https://gitlab.com/bmares/check-json5
    rev: v1.0.0
    hooks:
      - id: check-json5
        name: vscode settings files
        stages: [pre-commit]
        files: .vscode/.*\.json$
