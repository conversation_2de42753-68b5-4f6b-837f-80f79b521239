import json
from datetime import datetime, timezone

import pytest
import respx
from dataoffice.main import app
from dataoffice.settings import settings
from fastapi.testclient import TestClient
from httpx import Response
from starlette.requests import Request
from starlette.routing import Router
from starlette.staticfiles import StaticFiles


@pytest.fixture
def mock_request():
    request = Request(
        {
            "type": "http",
            "method": "GET",
            "headers": [(b"host", b"testserver")],
            "scheme": "http",
            "server": ("testserver", 80),
            "path": "/",
            "query_string": b"",
        }
    )
    router = Router()
    router.mount("/static", StaticFiles(directory="static"), name="static")
    request.scope["router"] = router
    return request


@pytest.mark.asyncio
@respx.mock
async def test_report_coverage_endpoint_full_coverage(mock_request):
    # Mock user-service /organization/search
    org_id = "o-123"
    org_name = "Test Org (Managed Partner)"
    respx.get(f"{settings().user_service_url}/organization/search").mock(
        return_value=Response(
            200,
            content=json.dumps(
                {
                    "data": [
                        {
                            "id": org_id,
                            "name": org_name,
                            "legacy_id": 1,
                            "created_at": datetime.now(timezone.utc).isoformat(),
                        }
                    ],
                    "count": 1,
                }
            ),
        )
    )

    respx.get(f"{settings().report_service_url}/reports/scrape-coverage/{org_id}").mock(
        return_value=Response(
            200,
            content=json.dumps(
                {"steam_sales": [{"date_from": "2020-01-01", "date_to": "2024-12-31"}]}
            ),
        )
    )

    client = TestClient(app)
    response = client.get("/api/managed-partners/report-coverage/")
    assert response.status_code == 200
    data = response.json()

    assert data == [
        {
            "organizationId": org_id,
            "organizationName": "Test Org",
            "coverage": {
                "steam_sales": [{"dateFrom": "2020-01-01", "dateTo": "2024-12-31"}]
            },
        }
    ]
