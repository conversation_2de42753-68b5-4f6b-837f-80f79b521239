from fastapi.testclient import TestClient


def test_tab_requires_auth(anonymous_client: TestClient):
    response = anonymous_client.get("/sample-tab/", follow_redirects=False)
    assert response.status_code == 307
    assert response.headers["location"] == "/login?next=/sample-tab/"


def test_tab_returns_html(authed_client: TestClient):
    response = authed_client.get("/sample-tab/", follow_redirects=False)
    assert response.status_code == 200
    assert response.headers["content-type"] == "text/html; charset=utf-8"
