from typing import Literal

import dataoffice.settings
from dataoffice.settings import BaseAppSettings, SettingsConfigDict, SettingsEnv


class TestSettings(BaseAppSettings):
    env: Literal[SettingsEnv.test] = SettingsEnv.test
    env_files: list[str] = [".env.test"]
    model_config = SettingsConfigDict(env_file=env_files, env_file_encoding="utf-8")

    auth_client_id: str = "test"
    auth_tenant_id: str = "test"
    auth_client_secret: str = "test"
    cookie_secret_key: str = "test"
    pipeline_manager_url: str = "https://pipeline-manager.indiebi.test"
    pipeline_manager_key: str = "test"
    user_service_url: str = "https://user-service-v2.indiebi.test"
    user_service_key: str = "test"
    dataset_manager_url: str = "https://dataset-manager.indiebi.test"
    dataset_manager_key: str = "test"
    report_service_url: str = "https://report-service.indiebi.test"
    report_service_key: str = "test"
    dataoffice_url: str = "https://dataoffice.indiebi.test"
    cloud_reports_report_url: str = "https://cloud-reports-report.indiebi.test"
    scraper_service_url: str = "https://scraper-service.indiebi.test"
    scraper_service_key: str = "test"


# settings are imported first and immediately overwritten with test settings
# so that modules can still use settings in module scope that executes on import
# ruff: noqa: E402
dataoffice.settings._settings = TestSettings()


import json
from datetime import datetime

import pytest
from dataoffice import auth, main
from dataoffice.services.clients_statuses_service import UsersCache, get_users_cache
from fastapi.testclient import TestClient


@pytest.fixture
def anonymous_client():
    main.app.dependency_overrides[get_users_cache] = lambda: UsersCache()

    yield TestClient(main.app)

    main.app.dependency_overrides = {}


class FakeAuthSession(auth.AuthSession):
    def is_active(self):
        return True

    def get_access_token(self):
        return "test-token"


auth_session = FakeAuthSession(
    public_id="test",
    user=auth.User(username="test", full_name="Test User"),
)


@pytest.fixture
def authed_client(anonymous_client: TestClient):
    main.app.dependency_overrides[auth.active_session] = lambda: auth_session
    main.app.dependency_overrides[get_users_cache] = lambda: UsersCache()

    yield anonymous_client

    main.app.dependency_overrides = {}


@pytest.fixture
def date_time_encoder():
    class DateTimeEncoder(json.JSONEncoder):
        def default(self, o):
            if isinstance(o, datetime):
                return o.isoformat()
            return super().default(o)

    return DateTimeEncoder
