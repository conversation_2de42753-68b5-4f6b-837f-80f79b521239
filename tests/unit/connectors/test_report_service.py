import pytest
import respx
from dataoffice.connectors.report_service import get_organization_coverage
from dataoffice.settings import settings
from httpx import Response


@pytest.mark.asyncio
@respx.mock
async def test_get_organization_coverage():
    org_id = "o-rsFPhw"
    expected_coverage = {
        "steam_sales": [{"date_from": "2010-01-01", "date_to": "2024-05-15"}],
        "playstation_sales": [{"date_from": "2000-01-01", "date_to": "2025-04-17"}],
    }
    base_url = settings().report_service_url
    respx.get(f"{base_url}/reports/scrape-coverage/{org_id}").mock(
        return_value=Response(200, json=expected_coverage)
    )
    result = await get_organization_coverage(org_id)
    assert result == expected_coverage
