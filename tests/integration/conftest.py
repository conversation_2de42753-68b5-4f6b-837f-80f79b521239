from typing import Literal

import dataoffice.settings
import pytest
from dataoffice.settings import DevSettings, SettingsEnv
from pydantic_settings import SettingsConfigDict

SCP_TEST_ACCOUNT_ORG_ID = "o-rsFPhw"
SCP_TEST_ACCOUNT_USER_ID = "u-mp09bS"


class TestSettings(DevSettings):
    env: Literal[SettingsEnv.test] = SettingsEnv.test  # type: ignore
    env_files: list[str] = [".env", ".env.dev", ".env.integration"]
    model_config = SettingsConfigDict(env_file=env_files, env_file_encoding="utf-8")

    auth_client_id: str = "test"
    auth_client_secret: str = "test"
    auth_tenant_id: str = "test"
    cookie_secret_key: str = "test"
    dataset_manager_key: str = "test"
    pipeline_manager_key: str = "test"

    # Keys below are currently used by integrations tests, and they are loaded from .env.dev file,
    # or from env variables when they are running on CI.
    report_service_key: str
    scraper_service_key: str
    user_service_key: str


# settings are imported first and immediately overwritten with dev settings
# so that modules can still use settings in module scope that executes on import
# Why TestSettings based on DevSettings? Because integrations tests are run against real dev environment,
# with assumption that data for test_user_id and test_organization_id will not change.
# ruff: noqa: E402
dataoffice.settings._settings = TestSettings()  # type: ignore


@pytest.fixture
def test_organization_id():
    return SCP_TEST_ACCOUNT_ORG_ID


@pytest.fixture
def test_user_id():
    return SCP_TEST_ACCOUNT_USER_ID
