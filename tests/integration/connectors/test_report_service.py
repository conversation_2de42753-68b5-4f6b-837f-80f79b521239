import pytest
from dataoffice.connectors.report_service import get_organization_coverage


@pytest.mark.asyncio(scope="session")
async def test_get_organization_coverage_for_scp(test_organization_id):
    coverage = await get_organization_coverage(test_organization_id)
    assert coverage == {
        "playstation_sales": [
            {
                "date_from": "2000-01-01",
                "date_to": "2025-05-03",
            },
        ],
        "playstation_wishlist_actions": [
            {
                "date_from": "2000-01-01",
                "date_to": "2025-05-03",
            },
        ],
    }
