import datetime

import pytest
from dataoffice.connectors.user_service import (
    User,
    get_organization_id_for_user,
    get_user,
    search_organizations,
    search_users,
)

fromISO = datetime.datetime.fromisoformat


@pytest.mark.asyncio(scope="session")
async def test_get_user(test_user_id):
    user = await get_user(test_user_id)

    assert user.model_dump() == {
        "email": "<EMAIL>",
        "first_name": "SCP",
        "last_name": "Test",
        "company_name": "SCP Test",
        "id": test_user_id,
        "legacy_id": 10833,
        "verified": True,
        "created_at": fromISO("2025-05-03T19:21:54.186311+00:00"),
    }


@pytest.mark.asyncio(scope="session")
async def test_search_users(test_user_id):
    users = await search_users("scp-test-account", 0, 10)
    assert isinstance(users, list)
    assert all(isinstance(user, User) for user in users)
    assert len(users) > 0

    test_user = next(user for user in users if user.id == test_user_id)
    assert test_user.model_dump() == {
        "email": "<EMAIL>",
        "first_name": "SCP",
        "last_name": "Test",
        "company_name": "SCP Test",
        "id": test_user_id,
        "legacy_id": 10833,
        "verified": True,
        "created_at": fromISO("2025-05-03T19:21:54.186311+00:00"),
    }


@pytest.mark.asyncio(scope="session")
async def test_get_organization_id_for_user(test_user_id, test_organization_id):
    organization_id = await get_organization_id_for_user(test_user_id)
    assert organization_id == test_organization_id


@pytest.mark.asyncio(scope="session")
async def test_search_organization_for_scp_account():
    orgs = await search_organizations("SCP Test", 0, 10)
    assert isinstance(orgs, list)
    org = next((o for o in orgs if o.id == "o-rsFPhw"), None)
    assert org is not None
    assert org.name == "SCP Test"
