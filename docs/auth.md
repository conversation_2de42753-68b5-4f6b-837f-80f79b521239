- all code relathed to authentication is in `dataoffice/auth.py`
- we're using Entra ID OAuth2 Authorization Code Flow through MSAL for Python library, allowing us to login to Dataoffice using M365 accounts. See [https://learn.microsoft.com/en-us/entra/identity-platform/msal-authentication-flows#authorization-code](https://learn.microsoft.com/en-us/entra/identity-platform/msal-authentication-flows#authorization-code)
- when logging in, we call `initiate_auth_code_flow()` which generates an URI that we redirect the user to, which loads an MS-managed UI for logging in.
- the login screen calls `/oauth/callback` endpoint with parameters that allow us to authenticate and retrieve access and refresh tokens.
- Datoffice assigns session cookies to every client, `using from starlette.middleware.sessions.SessionMiddleware`.
- tokens obtained from MSAL are assigned to sessions are stored in web service memory (`dataoffice.auth._sessions` dictionary)
- currently, when the service restarts, all sessions are lost and all clients need to log in again. But if we persist `_sessions` somehow, sessions can survive restarts or be shared between multiple instances of Dataoffice running under a load balancer.
- `auth.py` exposes `ActiveSessionDependency` type which can be used as a FastAPI dependency annotation for all requests that require the user to be logged in.
