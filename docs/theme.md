## Theme

This project uses the Falcon theme, a paid theme bought from Bootstrap Marketplace.
Invoice and source files are available in `theme/` directory.

Online documentation for the theme:
[https://prium.github.io/falcon/v3.20.0/documentation/getting-started.html](https://prium.github.io/falcon/v3.20.0/documentation/getting-started.html)


## Demo site
The whole theme demo site is also unpacked and linked in the Dataoffice menu under "Theme Examples"

Use it to quickly browse through available demos.

Feel free to shamelessly copy-paste stuff from the sample pages.

The demo can be viewed here: [https://dataoffice.indiebi.dev/static/theme/index.html](https://dataoffice.indiebi.dev/static/theme/index.html)

## Macros
Additionally, components that are often used can be extracted as Jinja2 macros
and put here: `dataoffice/tabs/components.html.j2`. Feel free to add more.

## Icons
This theme uses FontAwesome 5.14.2.
Here's an online viewer of avaialable icons:
[https://fontawesome.com/v5/search?o=r&m=free](https://fontawesome.com/v5/search?o=r&m=free)

Embed an icon like this:
``` html
<i class="fas fa-wine-glass-alt"> </i>
```
