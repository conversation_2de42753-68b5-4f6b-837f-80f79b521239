/*
    Attention!
    This app is using Bootstrap 5. Normally, you should not need to add custom CSS.
    Most of the time, you can use Bootstrap classes to style your components.

    However, if you need to add custom CSS, you can do it here - as a last resort.
    Preferably add a comment justifying why you need to add custom CSS instead of using Bootstrap classes.
*/

/* v-cloak is used by Vue to prevent flickering while Vue is loading. */
/* see https://vuejs.org/api/built-in-directives.html#v-cloak */
[v-cloak] {
  display: none;
}

@media (width >= 1200px) {
  .logo-container {
    height: 5rem;
  }
}

.logo-container .navbar-brand {
  margin: auto;
}

.logo-env-tag {
  position: relative;
  right: 18px;
  margin-top: -12px;
  font-size: 12px;
  font-family: var(--falcon-font-sans-serif);
  font-weight: bold;
}

:root[data-env="prod"][data-bs-theme="dark"] .dataoffice-logo {
  content: url("/static/logo-dark_prod.png");
}

:root[data-env="prod"][data-bs-theme="light"] .dataoffice-logo {
  content: url("/static/logo-light_prod.png");
}

:root[data-env="dev"][data-bs-theme="dark"] .dataoffice-logo {
  content: url("/static/logo-dark_dev.png");
}

:root[data-env="dev"][data-bs-theme="light"] .dataoffice-logo {
  content: url("/static/logo-light_dev.png");
}

:root[data-env="local"][data-bs-theme="dark"] .dataoffice-logo {
  content: url("/static/logo-dark_local.png");
}

:root[data-env="local"][data-bs-theme="light"] .dataoffice-logo {
  content: url("/static/logo-light_local.png");
}

.env-tag {
  text-transform: uppercase;
  color: grey;
}

.logo-env-tag {
  position: relative;
  right: 18px;
  margin-top: -12px;
  font-size: 12px;
  font-family: var(--falcon-font-sans-serif);
  font-weight: bold;
}

:root[data-env="prod"] .env-tag {
  color: #e63757;
}

:root[data-env="dev"] .env-tag {
  color: #2c7be5;
}

:root[data-env="local"] .env-tag {
  color: #8c51ea;
}

:root[data-env="prod"] .env-tag::before {
  content: "prod";
}

:root[data-env="dev"] .env-tag::before {
  content: "dev";
}

:root[data-env="local"] .env-tag::before {
  content: "local";
}
